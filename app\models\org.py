from datetime import datetime

from sqlalchemy import Column, Integer, String, Boolean, DateTime
from app.db.database import Base

class Org(Base):
    __tablename__ = "org_info"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(64), nullable=False, comment="部门名称")
    org_code = Column(String(50), nullable=False, unique=True, comment="部门编码")
    sort = Column(Integer, nullable=False, comment="显示排序")
    owner_user_id = Column(Integer, nullable=False, comment="负责人用户id")
    status = Column(Boolean, nullable=True, comment="部门状态")
    parent_id = Column(Integer, default=-1, comment="上一级部门id")
    parent_org_code = Column(String(255), default=-1, comment="上一级部门编码")
    can_sub_query = Column(Integer, nullable=False, comment="1 可查询子孙部分数据 2 不可")
    create_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='创建时间')
    update_time= Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='修改时间')

class UserToOrg(Base):
    __tablename__ = "user_to_org"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True)
    org_code = Column(String(50), nullable=False, index=True)
    primary_flag = Column(Boolean, nullable=False, comment="1 主部门")

class OrgToRole(Base):
    __tablename__ = "org_to_role"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    org_code = Column(String(50), nullable=False, index=True)
    role_id = Column(Integer, nullable=False, index=True)
    org_id = Column(Integer, nullable=False, index=True)