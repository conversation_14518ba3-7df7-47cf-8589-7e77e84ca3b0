#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试重构后的函数
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from app.services.scene_qz.scene_qz_service import SceneQzService


async def simple_test():
    """简单测试"""
    print("开始简单测试...")
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 测试重构后的函数
        result = await SceneQzService.cluster_cases_with_shared_entities(
            page=1,
            page_size=5,
            db=db
        )
        
        print(f"测试结果:")
        print(f"  总数: {result['total']}")
        print(f"  页码: {result['page']}")
        print(f"  每页大小: {result['page_size']}")
        print(f"  返回条目数: {len(result['items'])}")
        
        if result['items']:
            print(f"  第一个聚类:")
            first_cluster = result['items'][0]
            print(f"    案件类型: {first_cluster['case_type']}")
            print(f"    共享实体数: {len(first_cluster['involved'])}")
            print(f"    案件数: {len(first_cluster['cases'])}")
        
        print("测试完成！")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(simple_test())
