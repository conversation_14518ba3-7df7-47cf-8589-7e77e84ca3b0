<template>
  <div class="bigscreen">
    <PageHeader title="菜单管理" subtitle="MENU MANAGE" />
    <div class="content-wrapper">
      <div class="search-section">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" class="search-form common-search" label-width="120px">
          <div class="search-left">
          <el-form-item label="菜单名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入菜单名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="菜单状态" clearable>
              <el-option  label="正常" value="1" />
              <el-option  label="停用" value="0" />
            </el-select>
          </el-form-item>
        </div>
          <el-form-item class="search-right">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

    <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" class="tool-btn">
            新增
          </el-button>
          <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll" class="tool-btn">
            展开/折叠
          </el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table
          v-if="refreshTable"
          :data="menuList"
          row-key="id"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column
            prop="name"
            label="菜单名称"
            :show-overflow-tooltip="true"
            width="160"
          ></el-table-column>
          <el-table-column prop="icon" label="图标" align="center" width="120">
            <template slot-scope="scope">
              <svg-icon :icon-class="scope.row.icon" />
            </template>
          </el-table-column>
          <el-table-column prop="sort" label="排序" width="120"></el-table-column>
          <!-- <el-table-column
            prop="perms"
            label="权限标识"
            :show-overflow-tooltip="true"
          ></el-table-column> -->
          <el-table-column
            prop="component_name"
            label="组件路径"
            :show-overflow-tooltip="true"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
              <el-tag v-else type="danger">停用</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="创建时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" class="view-btn" @click="handleUpdate(scope.row)">
                修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-plus" class="view-btn" @click="handleAdd(scope.row)">
                新增
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" class="view-btn" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
    </div>
    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open"  append-to-body  custom-class="screen-dialog self-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级菜单" prop="parent_id">
              <treeselect
                v-model="form.parent_id"
                :options="menuOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="is_catalog">
              <el-radio-group v-model="form.is_catalog">
                <el-radio :label="0">目录</el-radio>
                <el-radio :label="1">菜单</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单图标" prop="icon">
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
                popper-class="icon-select-popover"
              >
                <IconSelect ref="iconSelect" @selected="selected" :active-icon="form.icon" />
                <el-input slot="reference" v-model="form.icon" placeholder="点击选择图标" readonly>
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    style="width: 25px;"
                  />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number v-model="form.sort" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.is_catalog == '1'">
            <el-form-item prop="component_name">
              <el-input v-model="form.component_name" placeholder="请输入组件名称" />
              <span slot="label">
                <el-tooltip
                  content="暂无实际用途，建议保持唯一性"
                  placement="top"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
                组件名称
              </span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.is_catalog == '1'">
          <el-col :span="12" >
            <el-form-item prop="is_link">
              <span slot="label">
                <el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                是否外链
              </span>
              <el-radio-group v-model="form.is_link">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item prop="web_path">
              <span slot="label">
                <el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                <i class="el-icon-question"></i>
                </el-tooltip>
                路由地址
              </span>
              <el-input v-model="form.web_path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12" v-if="form.is_catalog == '1'">
            <el-form-item prop="component">
              <span slot="label">
                <el-tooltip
                  content="访问的组件路径，如：`system/user/index`，默认在`views`目录下"
                  placement="top"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
                组件路径
              </span>
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.is_catalog == '1'">
            <el-form-item prop="cache">
              <span slot="label">
                <el-tooltip
                  content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致"
                  placement="top"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
                是否缓存
              </span>
              <el-radio-group v-model="form.cache">
                <el-radio :label="0">不缓存</el-radio>
                <el-radio :label="1">缓存</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="status">
              <span slot="label">
                <el-tooltip
                  content="选择停用则路由将不会出现在侧边栏，也不能被访问"
                  placement="top"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
                菜单状态
              </span>
              <el-radio-group v-model="form.status">
                <el-radio :label="0">停用</el-radio>
                <el-radio :label="1">正常</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" class="ensure-btn">确 定</el-button>
        <el-button @click="cancel" class="cancel-btn">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import { error } from "jquery";
import PageHeader from "@/components/PageHeader.vue";

export default {
  name: "Menu",
  // dicts: ['sys_show_hide', 'sys_normal_disable'],
  components: { Treeselect, IconSelect,PageHeader },
  data() {
    return {
      sys_show_hide: [],
      sys_normal_disable: [],
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        name: undefined,
        visible: undefined,
      },
      // 表单参数
      form: {
        id: undefined,
        parent_id: -1,
        name: undefined,
        icon: undefined,
        is_catalog: 0,
        sort: undefined,
        web_path: undefined,
        is_link: 0,
        cache: 1,
        visible: 0,
        status: 1,
        is_affix: 0,
        is_iframe: 0
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: "菜单名称不能为空", trigger: "blur" }],
        sort: [{ required: true, message: "菜单顺序不能为空", trigger: "blur" }],
        web_path: [{ required: true, message: "路由地址不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 选择图标
    selected(name) {
      this.form.icon = name;
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      listMenu(this.queryParams).then(response => {
        this.menuList = this.handleTree(response.data, "id");
        this.loading = false;
      }).catch(err => {
          console.log(err);
        });
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listMenu().then(response => {
        console.log("编码",response.code);
        this.menuOptions = [];
        const menu = { id: -1, name: "主类目", children: [] };
        menu.children = this.handleTree(response.data, "id");
        this.menuOptions.push(menu);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        parent_id: -1,
        name: undefined,
        icon: undefined,
        web_path: undefined,
        is_catalog: 0,
        sort: undefined,
        is_link: 0,
        cache: 1,
        visible: 0,
        status: 1,
        is_affix: 0,
        is_iframe: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.getTreeselect()
      if (row != null && row.id) {
        this.form.parent_id = row.id;
      } else {
        this.form.parent_id = -1;
      }
      this.open = true;
      this.title = "添加菜单";
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getMenu(row.id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改菜单";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateMenu(this.form).then(response => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMenu({...this.form,is_affix:0,is_iframe:0}).then(response => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
            }).catch(err => {
              console.log(err);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的数据项？')
        .then(function () {
          return delMenu(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="less" scoped>
.app-container {
  background-color: white;
  height: 100%;
  padding: 50px 10px;
  
}

</style>

<style lang="less">
/* 全局样式 */
.icon-select-popover {
  background-color: #112A3F !important;
  color: rgba(255, 255, 255, 0.8) !important;
  
  .el-input__inner {
    background-color: #112A3F;
    color: rgba(255, 255, 255, 0.8);
  }
}
.self-dialog {
  width:800px
}
</style>
