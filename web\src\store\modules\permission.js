import { getUserMenu } from '@/api/menu'
import { asyncRoutes, constantRoutes } from '@/router'

/**
 * 使用 meta.role 判断当前用户是否有权限
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
//   if (route.meta && route.meta.roles) {
//     return roles.some(role => route.meta.roles.includes(role))
//   } else {
//     return true
//   }
return true
}

/**
 * 通过递归过滤异步路由表
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    // 如果传入的是空数组，则完全清空路由
    if (routes.length === 0) {
      state.routes = []
    } else {
      state.routes = constantRoutes.concat(routes)
    }
  }
}

const actions = {
  GenerateRoutes({ commit }) {
    return new Promise((resolve, reject) => {
      getUserMenu().then(response => {
        const { data } = response
        // 将动态路由作为子路由添加到布局组件中
        const layoutRoute = asyncRoutes.find(route => route.path === '/')
        if (layoutRoute) {
          layoutRoute.children = data.map(item => {
            // 处理组件路径
            const componentPath = item.component ? item.component.replace(/\.vue$/, '') : null
            const routeName = item.component_name || `route_${Math.random().toString(36).substr(2, 9)}`
            
            return {
              path: item.web_path || `/${routeName.toLowerCase()}`,
              component: item.children && item.children.length > 0 
                ? () => import('@/views/Layout/ParentView.vue')  // 使用布局组件
                : componentPath ? () => import(`@/views/${componentPath}`) : null,
              name: routeName,
              title: item.name,
              meta: {
                title: item.name,
                icon: item.icon,
                noCache: !item.cache,
                affix: item.is_affix === 1,
                requiresAuth: true
              },
              children: item.children ? item.children.map(child => {
                const childComponentPath = child.component ? child.component.replace(/\.vue$/, '') : null
                const childRouteName = child.component_name || `route_${Math.random().toString(36).substr(2, 9)}`
                return {
                  path: child.web_path || `/${childRouteName.toLowerCase()}`,
                  component: childComponentPath ? () => import(`@/views/${childComponentPath}`) : null,
                  name: childRouteName,
                  meta: {
                    title: child.name,
                    icon: child.icon,
                    noCache: !child.cache,
                    affix: child.is_affix === 1,
                    requiresAuth: true
                  }
                }
              }) : []
            }
          })
        }
        
        commit('SET_ROUTES', asyncRoutes)
        resolve(asyncRoutes)
      }).catch(error => {
        console.log(error)
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
} 