<template>
  <div class="event-multi-case">
    <div class="search-section">
      <el-form
        :inline="true"
        :model="searchForm"
        class="common-search search-form"
        label-width="120px"
      >
        <div class="search-left">
          <div class="entity-list">
            <div v-for="(entity, index) in visibleEntities" :key="entity.uid" class="entity-item">
              <el-form-item label="身份证号">
                <el-input
                  v-model="entity.id_card"
                  placeholder="输入身份证号"
                  style="width: 240px"
                ></el-input>
              </el-form-item>
              <el-form-item label="姓名">
                <el-input
                  v-model="entity.name"
                  placeholder="输入姓名"
                  style="width: 160px"
                ></el-input>
              </el-form-item>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                @click="removeEntity(entity)"
                v-if="searchForm.entities.length > 1"
              ></el-button>
            </div>
            <el-button type="primary" icon="el-icon-plus" @click="addEntity" class="add-entity-btn">
              添加人员
            </el-button>
            <!-- <el-button type="text" @click="showBatchInput = true" style="margin-left: 16px;">批量粘贴</el-button> -->
          </div>
          <el-form-item label="案件类型">
            <el-select
              v-model="searchForm.case_type"
              placeholder="请选择案件类型"
              style="width: 240px"
            >
              <el-option label="涉黄" value="涉黄"></el-option>
              <el-option label="涉赌" value="涉赌"></el-option>
              <el-option label="涉毒" value="涉毒"></el-option>
              <el-option label="盗窃" value="盗窃"></el-option>
              <el-option label="抢劫" value="抢劫"></el-option>
              <el-option label="诈骗" value="诈骗"></el-option>
              <el-option label="强奸" value="强奸"></el-option>
              <el-option label="斗殴" value="斗殴"></el-option>
              <el-option label="家庭纠纷" value="家庭纠纷"></el-option>
              <el-option label="情感纠纷" value="情感纠纷"></el-option>
              <el-option label="经济纠纷" value="经济纠纷"></el-option>
              <el-option label="邻里纠纷" value="邻里纠纷"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="search-right">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
      <el-button
        v-if="searchForm.entities.length > 2"
        type="text"
        @click="showAllEntities = !showAllEntities"
        class="toggle-entity-btn"
      >
        {{ showAllEntities ? "收起" : "展开更多" }}
      </el-button>
    </div>
    <el-dialog v-model="showBatchInput" title="批量粘贴" width="400px">
      <el-input
        type="textarea"
        v-model="batchInput"
        :rows="8"
        placeholder="每行一组，格式：身份证号,姓名 或 身份证号 姓名"
      />
      <template #footer>
        <el-button @click="showBatchInput = false">取消</el-button>
        <el-button type="primary" @click="handleBatchInput">导入</el-button>
      </template>
    </el-dialog>
    <div class="sub-total">共找到{{ total }}组一事多发</div>
    <div class="case-list">
      <div v-for="item in eventList" :key="item.case" class="case-card">
        <div class="card-header">
          <span class="card-title">{{ item.case_type }} - {{ item.case }}</span>
          <span class="card-id">报警时间: {{ formatDateTime(item.police_time) }}</span>
        </div>
        <div class="card-body">
          <div class="case-table table-container">
            <el-table :data="item.roles" style="width: 100%" size="small">
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column prop="id_card" label="身份证号" width="200" />
              <el-table-column prop="role" label="角色类型" />
            </el-table>
          </div>
          <div
            v-if="item.other_involved && item.other_involved.length > 0"
            class="other-involved table-container"
          >
            <div class="section-title">其他涉案人员</div>
            <el-table :data="item.other_involved" style="width: 100%" size="small">
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column prop="id_card" label="身份证号" width="200" />
              <el-table-column prop="role" label="角色类型" />
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { searchCasesWithSharedEntities } from "@/api/caseAnalyze";

export default {
  name: "EventMultiCase",
  data() {
    return {
      searchForm: {
        entities: [{ id_card: "", name: "", uid: Date.now() + "-" + Math.random() }],
        case_type: "家庭纠纷",
      },
      showAllEntities: true,
      showBatchInput: false,
      batchInput: "",
      eventList: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  computed: {
    visibleEntities() {
      if (this.showAllEntities) {
        return this.searchForm.entities;
      } else {
        return this.searchForm.entities.slice(0, 2);
      }
    },
  },
  methods: {
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "";
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    addEntity() {
      this.searchForm.entities.push({
        id_card: "",
        name: "",
        uid: Date.now() + "-" + Math.random(),
      });
    },

    removeEntity(entity) {
      const idx = this.searchForm.entities.indexOf(entity);
      if (idx > -1) this.searchForm.entities.splice(idx, 1);
    },

    async fetchEventList() {
      try {
        const params = {
          ...this.searchForm,
          page: this.page,
          page_size: this.pageSize,
        };

        const response = await searchCasesWithSharedEntities(params);

        if (response.code === 200) {
          const { items, total } = response.data;
          this.total = total;
          this.eventList = items;
        } else {
          this.$message.error(response.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取一事多发数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchEventList();
    },

    handleCurrentChange(val) {
      this.page = val;
      this.fetchEventList();
    },

    handleSearch() {
      // 验证至少有一个人员信息填写完整
      const hasValidEntity = this.searchForm.entities.some(entity => entity.id_card && entity.name);

      if (!hasValidEntity) {
        this.$message.warning("请至少填写一组完整的身份证号和姓名");
        return;
      }

      this.page = 1;
      this.fetchEventList();
    },

    resetSearch() {
      this.searchForm = {
        entities: [{ id_card: "", name: "", uid: Date.now() + "-" + Math.random() }],
        case_type: "",
      };
      this.page = 1;
      this.eventList = [];
      this.total = 0;
    },

    handleBatchInput() {
      const lines = this.batchInput.split("\n");
      const newEntities = [];
      lines.forEach(line => {
        let [id_card, name] = line.split(",");
        if (!name && line.trim()) {
          [id_card, name] = line.trim().split(/\s+/);
        }
        if (id_card && name) {
          newEntities.push({
            id_card: id_card.trim(),
            name: name.trim(),
            uid: Date.now() + "-" + Math.random(),
          });
        }
      });
      if (newEntities.length) {
        this.searchForm.entities = newEntities;
        this.$message.success("批量导入成功");
      } else {
        this.$message.warning("未识别到有效数据");
      }
      this.showBatchInput = false;
      this.batchInput = "";
    },
  },
};
</script>

<style lang="less" scoped>
.event-multi-case {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 500px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  .entity-list {
    width: inherit !important;
  }
  .search-section {
    padding: 24px 32px 8px 32px;
    border-radius: 8px;
    margin-bottom: 18px;
    flex-shrink: 0;
    .el-form-item {
      margin-right: 24px;
      .el-input,
      .el-select {
        color: #fff;
      }
    }

    .entity-list {
      margin-bottom: 16px;
      padding-bottom: 8px;
      position: relative;
      overflow: visible;

      .entity-item {
        margin-bottom: 8px;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        display: flex;
        align-items: center;
      }
    }
  }

  .sub-total {
    color: #bfc8e2;
    font-size: 14px;
    margin-bottom: 20px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    display: inline-block;
    flex-shrink: 0;
  }

  .case-list {
    flex: 1;
    padding-right: 10px;
    margin-bottom: 20px;
    overflow: visible;
  }

  .case-card {
    background: #22304a;
    border-radius: 8px;
    margin-bottom: 24px;
    padding: 18px 24px 12px 24px;
    .card-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      padding: 16px 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
      }

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        flex: 1;
        min-width: 300px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 16px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      .card-id {
        color: #bfc8e2;
        font-size: 14px;
        padding: 4px 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
      }
    }

    .card-body {
      .other-involved {
        margin-top: 16px;

        .section-title {
          color: #bfc8e2;
          font-size: 14px;
          margin-bottom: 12px;
          padding-left: 8px;
          border-left: 2px solid #409eff;
        }
      }
    }
  }

  .pagination-container {
    padding: 0 20px;
    text-align: right;
    background: #18233a;
    flex-shrink: 0;
  }

  .add-entity-btn {
    margin-top: 8px;
    width: 120px;
    height: 32px;
    display: block;
    position: relative;
    z-index: 2;
  }
  .toggle-entity-btn {
    margin-top: 8px;
    margin-left: 0;
    color: #409eff;
    font-size: 14px;
    position: relative;
    z-index: 2;
    display: block;
  }
}
</style>
