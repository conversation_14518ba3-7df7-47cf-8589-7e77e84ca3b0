from typing import List, Dict, Set, Tuple
from sqlalchemy.orm import Session
from app.models.org import Org, UserToOrg, OrgToRole
from app.models.menu import <PERSON>u, UserToRole as UTR, RoleMenuPermission
from collections import defaultdict

class PermissionService:
    def __init__(self, db: Session):
        self.db = db
        self._org_cache = {}  # 缓存组织信息
        self._menu_cache = {}  # 缓存菜单信息
        self._org_children_cache = defaultdict(list)  # 缓存组织的子组织

    def _build_org_cache(self):
        """构建组织缓存"""
        if not self._org_cache:
            orgs = self.db.query(Org).all()
            for org in orgs:
                self._org_cache[org.id] = org
                if org.parent_id != -1:
                    self._org_children_cache[org.parent_id].append(org.id)

    def _get_org_children(self, org_id: int) -> List[int]:
        """获取组织的所有子组织ID"""
        if not self._org_cache:
            self._build_org_cache()
        
        children = []
        to_process = [org_id]
        
        while to_process:
            current = to_process.pop()
            direct_children = self._org_children_cache[current]
            children.extend(direct_children)
            to_process.extend(direct_children)
        
        return children

    def get_user_orgs(self, user_id: int) -> Tuple[int, List[int]]:
        """
        获取用户的主部门ID和其他部门ID列表
        返回: (主部门ID, [其他部门ID列表])
        """
        user_orgs = self.db.query(UserToOrg).filter(UserToOrg.user_id == user_id).all()
        primary_org_id = None
        other_org_ids = []
        
        for user_org in user_orgs:
            org = self.db.query(Org).filter(Org.org_code == user_org.org_code).first()
            if org:
                if user_org.primary_flag:
                    primary_org_id = org.id
                else:
                    other_org_ids.append(org.id)
        
        return primary_org_id, other_org_ids

    def get_queryable_org_ids(self, org_ids: List[int]) -> List[int]:
        """
        获取可查询的组织ID列表（包括子组织）
        """
        if not self._org_cache:
            self._build_org_cache()
        
        queryable_org_ids = set()
        
        for org_id in org_ids:
            org = self._org_cache.get(org_id)
            if org and org.can_sub_query:
                # 如果可以查询子组织，添加所有子组织ID
                children = self._get_org_children(org_id)
                queryable_org_ids.update([org_id] + children)
            else:
                # 如果不能查询子组织，只添加当前组织ID
                queryable_org_ids.add(org_id)
        
        return list(queryable_org_ids)

    def get_user_roles(self, user_id: int) -> List[int]:
        """
        获取用户的所有角色ID（包括通过组织继承的角色）
        """
        # 获取用户直接关联的角色
        direct_roles = self.db.query(UTR.role_id).filter(UTR.user_id == user_id).all()
        direct_role_ids = {role[0] for role in direct_roles}
        
        # 获取用户所在组织的角色
        primary_org_id, other_org_ids = self.get_user_orgs(user_id)
        org_role_ids = set()
        
        if primary_org_id:
            org_roles = self.db.query(OrgToRole.role_id).filter(OrgToRole.org_id == primary_org_id).all()
            org_role_ids.update(role[0] for role in org_roles)
        
        for org_id in other_org_ids:
            org_roles = self.db.query(OrgToRole.role_id).filter(OrgToRole.org_id == org_id).all()
            org_role_ids.update(role[0] for role in org_roles)
        
        # 合并并去重
        return list(direct_role_ids.union(org_role_ids))

    def get_user_menus(self, user_id: int) -> List[Dict]:
        """
        获取用户的菜单树
        """
        # 获取用户的所有角色ID
        role_ids = self.get_user_roles(user_id)
        if not role_ids:
            return []
        
        # 获取角色对应的菜单ID
        menu_ids = self.db.query(RoleMenuPermission.menu_id)\
            .filter(RoleMenuPermission.role_id.in_(role_ids))\
            .distinct()\
            .all()
        menu_ids = [menu[0] for menu in menu_ids]
        
        # 获取菜单详情
        menus = self.db.query(Menu)\
            .filter(Menu.id.in_(menu_ids), Menu.status == True)\
            .order_by(Menu.sort)\
            .all()
        
        # 构建菜单树
        menu_dict = {menu.id: {
            "id": menu.id,
            "name": menu.name,
            "sort": menu.sort,
            "is_link": menu.is_link,
            "link_url": menu.link_url,
            "is_catalog": menu.is_catalog,
            "web_path": menu.web_path,
            "component": menu.component,
            "component_name": menu.component_name,
            "status": menu.status,
            "cache": menu.cache,
            "visible": menu.visible,
            "is_iframe": menu.is_iframe,
            "is_affix": menu.is_affix,
            "parent_id": menu.parent_id,
            "children": []
        } for menu in menus}
        
        # 构建树形结构
        root_menus = []
        for menu in menus:
            menu_data = menu_dict[menu.id]
            if menu.parent_id is None:
                root_menus.append(menu_data)
            else:
                parent = menu_dict.get(menu.parent_id)
                if parent:
                    parent["children"].append(menu_data)
        
        return root_menus

    def get_user_permissions(self, user_id: int) -> Dict:
        """
        获取用户的权限信息，包括组织信息和菜单树
        """
        primary_org_id, other_org_ids = self.get_user_orgs(user_id)
        queryable_org_ids = self.get_queryable_org_ids([primary_org_id] + other_org_ids)
        menus = self.get_user_menus(user_id)
        
        return {
            "primary_org_id": primary_org_id,
            "other_org_ids": other_org_ids,
            "queryable_org_ids": queryable_org_ids,
            "menus": menus
        } 