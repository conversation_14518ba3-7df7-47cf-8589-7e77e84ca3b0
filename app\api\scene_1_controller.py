from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from typing import Optional, List  # 添加这行导入

from fastapi import APIRouter, File, UploadFile, Depends, Form
from fastapi import BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sqlalchemy import func, text
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.db.database import get_db  # 修改导入路径
from app.log.log_utils import LogUtils
from app.models.police_record import PoliceRecord, police_record_search_order
from app.services.auth_service import AuthService
from app.services.db_service import DatabaseService
from app.services.excel_service import ExcelService
from app.services.llm_service import LLMService
from app.services.scene_1_service import Scene1Service
from app.services.user import org_service
from app.services.user.org_service import OrgService

router = APIRouter()

# 创建全局线程池执行器
executor = ThreadPoolExecutor()


@router.post("/file")
async def upload_file(
        background_tasks: BackgroundTasks,
        file: UploadFile = File(...),
        case_type: int = Form(0),
        db: Session = Depends(get_db)
):
    try:
        # 检查文件类型
        if not file.filename.endswith('.xlsx'):
            return create_response(code=400, message="只支持.xlsx格式的文件")
        # 读取文件内容
        contents = await file.read()

        # 解析Excel
        records = await ExcelService.parse_excel(contents, ['处警日期时间', '接警时间'])

        dbRecords = []

        # 逐条保存记录
        for record in records:
            # 转换字段名从中文到英文
            english_data = DatabaseService.map_fields_to_english(record, DatabaseService.FIELD_MAPPING)

            # 检查记录是否存在
            existing_record = db.query(PoliceRecord).filter(
                PoliceRecord.police_number == english_data['police_number'],
                PoliceRecord.source == 1
            ).first()
            exists = existing_record is not None
            user = UserContext.get_current()
            if exists:
                db_record = Scene1Service.unload_update_record(db, english_data, case_type, user.id, existing_record)
                # 将db_record的值除了id都赋予给existing_record
                dbRecords.append(db_record)
            else:
                db_record = Scene1Service.upload_create_record(db, english_data, case_type, user.id)
                dbRecords.append(db_record)
        # LLM处理
        if dbRecords:  # 只有当dbRecords不为空时才进行LLM处理
            background_tasks.add_task(LLMService.process_batch, dbRecords)
        return create_response(data={"processed": True})
    except Exception as e:
        LogUtils.error(f"处理失败: {str(e)}")
        return create_response(code=500, message=f"处理失败: {str(e)}")


class AnalysisInputData(BaseModel):
    ids: List[int]  # 明确指定ids是一个整数列表


@router.post("/police_record_analysis")
async def police_record_analysis(
        data: AnalysisInputData,
        background_tasks: BackgroundTasks,
        db: Session = Depends(get_db),
):
    try:
        query = db.query(PoliceRecord)
        query = query.filter(PoliceRecord.id.in_(data.ids))
        query = query.filter(PoliceRecord.analysis_status.in_([0, 3]))
        results = query.all()
        background_tasks.add_task(LLMService.process_batch, results)
        return create_response(message="处理成功")

    except Exception as e:
        LogUtils.error(f"处理失败: {str(e)}")
        return create_response(code=500, message=f"处理失败: {str(e)}")


@router.post("/search")
async def search_records(
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        police_number: Optional[str] = None,
        police_unit: Optional[str] = None,
        alarm_form: Optional[str] = None,
        alarm_category: Optional[str] = None,
        police_category: Optional[str] = None,
        police_results: Optional[str] = None,
        analysis_level: Optional[str] = None,
        check_results: Optional[str] = None,
        alarm_content: Optional[str] = None,
        analysis_status: Optional[str] = None,
        case_type: int = 0,
        page: int = 1,
        page_size: int = 10,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    query = DatabaseService.build_search_query(
        db, start_time, end_time, police_number, police_unit,
        alarm_form, alarm_category, police_category, police_results,
        analysis_level, check_results, alarm_content, case_type, analysis_status
    )

    # 计算总记录数
    total_count = query.count()
    skip = (page - 1) * page_size
    query = query.order_by(police_record_search_order(), PoliceRecord.police_time.desc()).offset(skip).limit(page_size)

    results = query.all()
    return create_response(data={
        "records": results,
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": (total_count + page_size - 1) // page_size
    })


@router.post("/export")
async def export_records(
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        police_number: Optional[str] = None,
        police_unit: Optional[str] = None,
        alarm_form: Optional[str] = None,
        alarm_category: Optional[str] = None,
        police_category: Optional[str] = None,
        police_results: Optional[str] = None,
        analysis_level: Optional[str] = None,
        check_results: Optional[str] = None,
        alarm_content: Optional[str] = None,
        analysis_status: Optional[str] = None,
        case_type: int = 0,
        db: Session = Depends(get_db)
):
    """
    导出警务记录到Excel文件
    """
    try:
        # 使用 DatabaseService 构建查询
        query = DatabaseService.build_search_query(
            db, start_time, end_time, police_number, police_unit, alarm_form, alarm_category, police_category,
            police_results, analysis_level, check_results, alarm_content, case_type, analysis_status
        )

        results = query.all()
        # 调用ExcelService导出数据
        excel_content = ExcelService.export_excel(results)

        # 生成文件名
        filename = f"police_records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 返回文件流
        return StreamingResponse(
            iter([excel_content]),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # 修复拼写错误
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except Exception as e:
        LogUtils.error(f"导出失败: {str(e)}")
        return create_response(code=500, message=f"导出失败: {str(e)}")


@router.get("/statistics/sum")
async def get_statistics(
        db: Session = Depends(get_db)
):
    try:
        # 统计表中所有数据总数
        total_count = db.query(PoliceRecord).count()

        # 统计表中analysis_status不为0的总数
        analyzed_count = db.query(PoliceRecord).filter(PoliceRecord.analysis_status != 0).count()

        return create_response(data={
            "total_count": total_count,
            "analyzed_count": analyzed_count
        })
    except Exception as e:
        LogUtils.error(f"获取统计信息失败: {str(e)}")
        return create_response(code=500, message=f"获取统计信息失败: {str(e)}")


@router.get("/statistics/analysis_level")
async def get_analysis_level_statistics(
        db: Session = Depends(get_db)
):
    try:
        # 统计表中analysis_level为red、yellow、blue的总数
        red_count = db.query(PoliceRecord).filter(PoliceRecord.analysis_level == 'red').count()
        yellow_count = db.query(PoliceRecord).filter(PoliceRecord.analysis_level == 'yellow').count()
        blue_count = db.query(PoliceRecord).filter(PoliceRecord.analysis_level == 'blue').count()

        # 计算总数
        total_count = red_count + yellow_count + blue_count

        # 计算占比率并转换为带百分号的字符串，保留三位小数
        red_ratio = f"{red_count / total_count * 100:.3f}%" if total_count > 0 else "0.000%"
        yellow_ratio = f"{yellow_count / total_count * 100:.3f}%" if total_count > 0 else "0.000%"
        blue_ratio = f"{blue_count / total_count * 100:.3f}%" if total_count > 0 else "0.000%"

        return create_response(data={
            "red_count": red_count,
            "yellow_count": yellow_count,
            "blue_count": blue_count,
            "red_ratio": red_ratio,
            "yellow_ratio": yellow_ratio,
            "blue_ratio": blue_ratio
        })
    except Exception as e:
        LogUtils.error(f"获取分析级别统计信息失败: {str(e)}")
        return create_response(code=500, message=f"获取分析级别统计信息失败: {str(e)}")


@router.get("/statistics/details")
async def get_details(
        db: Session = Depends(get_db)
):
    try:
        # 查询所有记录的指定字段
        results = db.query(
            PoliceRecord.alarm_details,
            PoliceRecord.analysis_level,
            PoliceRecord.analysis_details
        ).all()

        # 将结果转换为字典列表
        details = [
            {
                "alarm_details": record.alarm_details,
                "analysis_level": record.analysis_level,
                "analysis_details": record.analysis_details
            }
            for record in results
        ]

        return create_response(data=details)
    except Exception as e:
        LogUtils.error(f"获取详情失败: {str(e)}")
        return create_response(code=500, message=f"获取详情失败: {str(e)}")


@router.get("/statistics/police_unit")
async def get_police_unit_statistics(
        db: Session = Depends(get_db)
):
    try:
        # 统计表中各个police_unit的总数
        results = db.query(
            PoliceRecord.police_unit,
            func.count(PoliceRecord.police_unit).label('count')
        ).group_by(PoliceRecord.police_unit).all()

        # 将结果转换为字典列表
        police_unit_stats = [
            {
                "police_unit": result.police_unit,
                "count": result.count
            }
            for result in results
        ]

        return create_response(data=police_unit_stats)
    except Exception as e:
        LogUtils.error(f"获取police_unit统计信息失败: {str(e)}")
        return create_response(code=500, message=f"获取police_unit统计信息失败: {str(e)}")


@router.post("/login")
async def dify_login() -> dict:
    try:
        auth_result = AuthService.dify_login()
        return create_response(data=auth_result)
    except Exception as e:
        LogUtils.error(f"获取token异常: {str(e)}")
        return create_response(code=500, message="获取token异常")


@router.post("/sync_police_record")
async def sync_police_record(
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        db: Session = Depends(get_db)
):
    try:
        # 将YYYYMMDD格式转换为时间戳
        start_date_str = datetime.strptime(start_date, '%Y%m%d').strftime('%Y%m%d%H%M%S')
        end_date_str = datetime.strptime(end_date, '%Y%m%d').strftime('%Y%m%d%H%M%S')
        # 2. 根据sql模版组装where语句带上起始时间和结束时间
        # 根据处警时间进行同步
        # xgsj(修改时间 202504021123)、rownumber fh_rksj
        sql_template = """
            SELECT *
            FROM (
                SELECT 
                    a.jqlb as case_type,
                    b.cjbh as police_number,
                    b.cjdw as police_unit,
                    b.cjrqsj as police_time,
                    a.jjsj as alarm_time,
                    a.bjr_xm as call_name,
                    a.bjxs as alarm_form,
                    a.bjdh as alarm_call,
                    a.jjdhlb as alarm_category,
                    b.cjlb as police_category,
                    b.cjr as police_name,
                    b.ldclsj as leader_approval_time,
                    b.cjjg as police_results,
                    a.bjnr as alarm_content,
                    b.cjxxqk as alarm_details,
                    b.fh_rksj as fh_rksj,
                    a.cjbs as cjbs,
                    b.cjdw as cjdw,
                    ROW_NUMBER() OVER (
                        PARTITION BY b.cjbh 
                        ORDER BY b.fh_rksj DESC
                    ) as rn
                FROM ods_jcj_jjxx a
                JOIN ods_jcj_cjxx b ON a.jjbh = b.jjbh
                WHERE b.xgsj BETWEEN :start_time AND :end_time
                    AND (a.jqlb LIKE '01%' OR a.jqlb LIKE '02%')
            ) t
            WHERE rn = 1
        """
        sql = text(sql_template)
        # 3. 执行sql语句查询返回数组对象
        results = db.execute(sql, {"start_time": start_date_str, "end_time": end_date_str}).fetchall()

        # 将结果转换为字典列表
        data = [dict(row._asdict()) for row in results]

        # 字典值查询
        sql_template_dic = """
        SELECT DISTINCT code, item 
        FROM ods_dic 
        WHERE dic_id in ('170','99','179')
        """
        sql_dic = text(sql_template_dic)
        results_dic = db.execute(sql_dic).fetchall()
        # 将结果转换为字典列表
        data_dic = {row.code: row.item for row in results_dic}

        # 4. 循环遍历每条记录
        user = UserContext.get_current()
        new_count = 0
        # 获取映射表org_map表数据
        org_map = OrgService(db).get_org_map(db)
        for record in data:
            # 如果不存在处警结果 或 处警结果为未审核，就不同步
            if record['police_results'] is None or record['police_results'] == '99':
                continue

            exist_record = db.query(PoliceRecord).filter(
                PoliceRecord.police_number == record['police_number'],
                PoliceRecord.source == 0
            ).first()

            ## 若存在重复数据，取最新入库时间数据再次同步
            if exist_record:
                ods_fh_rksj = int(record.get('fh_rksj', 0))
                existing_fh_rksj = int(exist_record.fh_rksj.timestamp()) if exist_record.fh_rksj else 0
                if ods_fh_rksj > existing_fh_rksj:
                    db.delete(exist_record)  # 使用 db.delete() 删除单条记录
                    db.commit()  # 提交删除操作
                else:
                    continue

            police_results = data_dic.get(record['police_results'], record['police_results'])
            analysis_status = 0
            if "刑事案件" in police_results:
                analysis_status = 4

            # 创建新记录
            new_record = PoliceRecord(
                user_id=user.id,
                case_type=0,
                police_number=record['police_number'],
                police_unit=data_dic.get(record['police_unit'], record['police_unit']),
                police_time=record['police_time'],
                alarm_time=record['alarm_time'],
                call_name=record['call_name'],
                alarm_form=data_dic.get(record['alarm_form'], record['alarm_form']),
                alarm_call=record['alarm_call'],
                alarm_category=data_dic.get(record['alarm_category'], record['alarm_category']),
                police_category=data_dic.get(record['police_category'], record['police_category']),
                police_name=record['police_name'],
                police_results=police_results,
                alarm_content=record['alarm_content'],
                alarm_details=record['alarm_details'],
                upload_time=datetime.now(),
                check_results=record.get('check_results', None),
                analysis_status=analysis_status,
                analysis_level='white',
                analysis_details=None,
                fh_rksj=datetime.fromtimestamp(int(record.get('fh_rksj', 0))) if record.get('fh_rksj') else None,
                org_code=org_map.get(record['cjdw'], "0000")
            )

            # 判断case_type是否为01开头（刑事类警情）或02开头（行政（治安）警情）
            if record['case_type'].startswith('02'):
                new_record.case_type = 1

            # 保存新记录
            db.add(new_record)
            new_count += 1

            # 每处理100条记录提交一次事务
            if new_count % 100 == 0:
                db.commit()

        # 提交剩余的事务
        db.commit()
        return create_response(message=f"同步完成: 新增{new_count}条记录。")

    except Exception as e:
        LogUtils.error(f"同步数据失败: {str(e)}")
        return create_response(code=500, message=f"同步数据失败: {str(e)}")
