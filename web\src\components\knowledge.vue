<template>
  <div class="knowledge">
    <el-dialog
      title="知识库"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      custom-class="abnormal-dialog screen-dialog knowledge-dialog bigscreen"
      append-to-body
      height="800"
    >
      <div class="knowledge-content">
        <div class="knowledge-header">
          <div class="knowledge-header-title">文档</div>
          <div class="knowledge-header-tip">
            知识库的所有文件都在这里显示，整个知识库都可以链接到Dify引用或通过Chat插件进行索引。
          </div>
        </div>
        <div class="knowledge-search">
          <!-- <el-input v-model="search" placeholder="请输入内容" style="width: 300px" clearable /> -->
          <!-- <el-button type="primary" @click="handleSearch">搜索</el-button> -->
          <el-upload
            class="upload-excel"
            action="1233122312"
            :http-request="handleUpload"
            :show-file-list="false"
            :before-upload="beforeUploadPDF"
            :accept="`${fileType.join(',')},.md`"
          >
            <el-button type="primary">
              <i class="el-icon-upload"></i>
              <span>添加文件</span>
            </el-button>
          </el-upload>
        </div>
        <div class="knowledge-table">
          <el-table
            :data="tableData"
            style="width: 100%"
            :header-cell-style="headerStyle"
            :cell-style="cellStyle"
            :row-class-name="rowClassName"
            :row-key="row => row.id"
            @selection-change="handleSelectionChange"
            ref="table"
            height="500"
          >
            <!-- <el-table-column
              type="selection"
              width="50"
              align="center"
              :selectable="checkSelectable"
            ></el-table-column> -->
            <el-table-column type="index" label="#序号" width="150"></el-table-column>
            <el-table-column prop="doc_name" label="名称" show-overflow-tooltip></el-table-column>
            <!-- <el-table-column label="操作" width="150" align="center">
              <template slot-scope="scope"></template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
    </el-dialog>
    <!-- <el-dialog
      title="上传文件"
      :visible.sync="uploadDialogVisible"
      width="500px"
      :before-close="handleUploadCancel"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      custom-class="abnormal-dialog screen-dialog knowledge-dialog bigscreen upload-dialog"
      append-to-body
    >
      <div class="upload-container">
        <el-upload
          class="upload-box"
          multiple
          :before-upload="beforeUploadPDF"
          :file-list="uploadFiles"
          action="123"
          drag
          :auto-upload="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip" slot="tip">支持上传PDF，PNG文件，单个文件不超过100MB</div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleUploadPDF">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
<script>
import { get_datasets, upload_file, upload_dataset } from "@/api/chat";
import { fileType } from "@/constants";
export default {
  name: "Knowledge",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    namespace: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      search: "",
      loading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      selectedRows: [],
      selectedRowIds: [],
      uploadDialogVisible: false,
      uploadFiles: [],
      fileType,
      has_more: true,
    };
  },
  computed: {
    agent_id() {
      return this.$store.getters[`${this.namespace}/agent_id`];
    },
    session_id() {
      return this.$store.getters[`${this.namespace}/session_id`];
    },
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
      },
      immediate: true,
    },
  },
  mounted() {
    this.loadData();
    this.$nextTick(() => {
      const tableBodyWrapper = this.$refs.table.$el.querySelector(".el-table__body-wrapper");
      if (tableBodyWrapper) {
        tableBodyWrapper.addEventListener("scroll", this.handleTableScroll);
      }
    });
  },
  beforeDestroy() {
    const tableBodyWrapper = this.$refs.table.$el.querySelector(".el-table__body-wrapper");
    if (tableBodyWrapper) {
      tableBodyWrapper.removeEventListener("scroll", this.handleTableScroll);
    }
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleSearch() {},
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#FFFFFFcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    rowClassName({ row }) {
      return "";
    },
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleUploadFile() {
      this.uploadDialogVisible = true;
    },
    handleUploadCancel() {
      this.uploadDialogVisible = false;
      this.uploadFiles = [];
    },
    beforeUploadPDF(file) {
      const isTypeOk = this.fileType.includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 50;

      if (!isTypeOk) {
        this.$message.error(`只能上传 ${this.fileType.join(",")} 文件!`);
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 50MB!");
        return false;
      }
      return true;
    },
    async handleUpload(options) {
      try {
        this.uploadFiles = options.file;
        // 开启全局加载状态

        const formData = new FormData();
        if (this.namespace == "office") {
          this.$loading({
            lock: true,
            text: "文件上传中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          // 文秘办公
          formData.append("file", this.uploadFiles);
          formData.append("session_id", this.session_id);
          formData.append("agent_id", this.agent_id);

          const res = await upload_file(formData);
          if (res.code === 200 && res.data && res.data.success) {
            console.log("文件上传成功");
            const formDataDataset = new FormData();
            formDataDataset.append("file_name", res.data.file_name);
            formDataDataset.append("file_id", res.data.document_id);
            formDataDataset.append("upload_type", "file_id");
            upload_dataset(this.agent_id, formDataDataset)
              .then(res => {
                if (res.code === 200 && res.data) {
                  this.$message.success("文件上传成功");
                  this.loadData();
                }
              })
              .catch(err => {
                console.log("上传知识库失败");
                this.$message.error("文件上传失败" || err.message);
              })
              .finally(err => {
                this.$loading().close();
              });
          } else {
            throw new Error(res.message || "文件上传失败");
          }
        } else if (this.namespace === "law" || this.namespace === "legal") {
          this.$loading({
            lock: true,
            text: "文件上传中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          // 法条运用以及法律法规
          const formDataDataset = new FormData();
          formDataDataset.append("file", this.uploadFiles);
          formDataDataset.append("upload_type", "file");
          upload_dataset(this.agent_id, formDataDataset)
            .then(res => {
              if (res.code === 200 && res.data) {
                this.$message.success("文件上传成功");
                this.loadData();
              }
            })
            .catch(err => {
              console.log("上传知识库失败");
              this.$message.error("文件上传失败" || err.message);
            })
            .finally(err => {
              this.$loading().close();
            });
        }
      } catch (error) {
        // 只有在实际上传过程中出现错误时才提示
        if (error.toString() !== "cancel") {
          console.error("文件上传失败:", error);
        }
      } finally {
        this.loading = false;
        this.uploadFiles = null;
      }
    },
    loadData() {
      // console.log("this.currentPage", this.currentPage);
      get_datasets(this.agent_id, {
        limit: this.pageSize,
        page: this.currentPage,
      })
        .then(res => {
          if (res.code === 200 && res.data) {
            if (this.currentPage === 1) {
              this.tableData = res.data.datasets;
            } else {
              this.tableData = [...this.tableData, ...res.data.datasets];
            }
            this.has_more = res.data.has_more;
          }
        })
        .catch(err => {
          this.$message.error(err.message || "查询失败");
        });
    },
    handleTableScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      // 当滚动到距离底部20px时触发加载
      if (scrollHeight - scrollTop - clientHeight < 20) {
        if (this.has_more && !this.loading) {
          this.currentPage += 1;
          this.loadData();
        }
      }
    },
  },
};
</script>

<style lang="less">
.knowledge-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  width: 40% !important;
  height: auto !important;
  padding: 0 !important;
  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
.upload-dialog {
  // height: 515px !important;
  // background: rgba(0, 24, 51, 0.95) !important;
  // border: 1px solid rgba(0, 255, 255, 0.2) !important;
  // border-radius: 8px !important;
  // height: 60% !important;
  // backdrop-filter: blur(20px) !important;
  // box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
  // .el-dialog__header {
  //   padding: 15px 20px !important;
  //   background: linear-gradient(
  //     180deg,
  //     rgba(0, 48, 102, 0.8) 0%,
  //     rgba(0, 48, 102, 0.6) 100%
  //   ) !important;
  //   border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  //   border-radius: 8px 8px 0 0 !important;
  // }

  .upload-container {
    border-radius: 6px;
    background-color: transparent;

    .el-upload-list__item-name {
      color: #ffffff;
    }

    .el-upload-list {
      overflow-y: auto;
      height: 200px;
      padding-bottom: 5px;

      /* 滚动条样式 */
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, rgba(0, 85, 255, 0.6), rgba(0, 192, 255, 0.6));
        border-radius: 4px;
        box-shadow: inset 0 0 4px rgba(0, 192, 255, 0.3);
      }

      &::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, rgba(0, 85, 255, 0.8), rgba(0, 192, 255, 0.8));
        box-shadow: inset 0 0 6px rgba(0, 192, 255, 0.5);
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 25, 60, 0.3);
        border-radius: 4px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      }
    }

    .upload-box {
      .el-upload__tip {
        margin-top: 10px;
        font-size: 14px;
        color: #fff !important;
      }

      .el-upload {
        width: 100%;
        color: #fff !important;
      }

      .el-upload-dragger {
        width: 100%;
        padding: 40px 20px;
        background-color: transparent !important;
        border: 1px dashed #409eff;

        .el-icon-upload {
          font-size: 60px;
          color: #409eff;
          margin-bottom: 20px;
        }

        .el-upload__text {
          font-size: 16px;
          color: #fff;

          em {
            color: #409eff;
            font-style: normal;
          }
        }

        .el-upload__tip {
          margin-top: 10px;
          font-size: 14px;
          color: #fff !important;
        }
      }
    }
    .el-dialog__body {
      height: 400px !important;
      overflow-y: hidden !important;
    }
  }
}
</style>
<style scoped lang="less">
.knowledge-dialog {
  .knowledge-content {
    .knowledge-header {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      .knowledge-header-title {
        font-size: 20px;
        color: #fff;
        margin-bottom: 10px;
      }
      .knowledge-header-tip {
        font-size: 14px;
        color: #fff;
      }
    }
    .knowledge-search {
      margin-top: 10px;
      display: flex;
      gap: 10px;
      :deep(.el-input__inner) {
        color: rgba(255, 255, 255, 0.7);
        border: none;
        background-color: #152d43;
        border: 1px solid rgba(0, 255, 255, 0.2);
        color: #ffffff;
        &:hover {
          border-color: #00ffff;
        }
      }
      :deep(.el-button) {
        color: #071827;
        background-color: #00ffff;
        border-color: #00ffff;
      }
    }
    .knowledge-table {
      margin-top: 10px;
      .pagination-container {
        margin-top: 20px;
        flex-shrink: 0;
        display: flex;
        justify-content: flex-end;
        :deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
          background-color: rgba(0, 255, 255, 0.2);
          color: #00ffff;
          border: 1px solid #00ffff;
        }

        :deep(.el-pagination.is-background .el-pager li) {
          background-color: rgba(0, 48, 102, 0.3);
          color: #ffffff;
          border: 1px solid rgba(0, 255, 255, 0.1);
          margin: 0 3px;
        }

        :deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
          color: #00ffff;
          border-color: #00ffff;
        }

        :deep(.el-pagination.is-background .btn-next),
        :deep(.el-pagination.is-background .btn-prev) {
          background-color: rgba(0, 48, 102, 0.3);
          color: #ffffff;
          border: 1px solid rgba(0, 255, 255, 0.1);
        }

        :deep(.el-pagination.is-background .btn-next:hover),
        :deep(.el-pagination.is-background .btn-prev:hover) {
          color: #00ffff;
          border-color: #00ffff;
        }

        :deep(.el-pagination__total),
        :deep(.el-pagination__jump),
        :deep(.el-pagination__sizes) {
          color: #ffffff;
        }
      }
      :deep(.el-table) {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        background-color: transparent !important;
        min-height: 0;
        .el-table__header {
          colgroup {
            col[name="gutter"] {
              width: 0;
            }
          }
        }

        .el-table__header-wrapper {
          flex-shrink: 0;
        }
        .el-table__empty-block {
          min-height: 400px;
        }

        .el-table__body-wrapper {
          flex: 1;
          height: 0 !important;
        }

        /* 全局对话框滚动条美化 */
        .el-dialog__body::-webkit-scrollbar,
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        .el-dialog__body::-webkit-scrollbar-thumb,
        .el-table__body-wrapper::-webkit-scrollbar-thumb {
          background: linear-gradient(180deg, rgba(0, 180, 255, 0.5), rgba(0, 255, 255, 0.5));
          border-radius: 3px;
          box-shadow: inset 0 0 3px rgba(0, 255, 255, 0.2);
          transition: background 0.3s ease-in-out;
        }

        .el-dialog__body::-webkit-scrollbar-thumb:hover,
        .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(180deg, rgba(0, 180, 255, 0.8), rgba(0, 255, 255, 0.8));
          box-shadow: inset 0 0 5px rgba(0, 255, 255, 0.4);
        }

        .el-dialog__body::-webkit-scrollbar-track,
        .el-table__body-wrapper::-webkit-scrollbar-track {
          background: rgba(0, 48, 102, 0.2);
          border-radius: 3px;
        }

        .el-dialog__body::-webkit-scrollbar-corner,
        .el-table__body-wrapper::-webkit-scrollbar-corner {
          background: transparent;
        }
      }
    }
    .knowledge-item {
      .knowledge-item-title {
        font-size: 16px;
        color: #fff;
      }
    }
  }
}
</style>
