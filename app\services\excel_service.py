import io
from typing import List, Dict

import pandas as pd
from fastapi import HTT<PERSON>Ex<PERSON>
from openpyxl import Workbook
from openpyxl.styles import PatternFill

from app.log.log_utils import LogUtils
from app.models.police_record import ANALYSIS_STATUS_MAPPING
from app.models.police_record import PoliceRecord
from app.services.db_service import DatabaseService


class ExcelService:
    # 字段映射配置
    FIELD_MAPPING = {
        **DatabaseService.FIELD_MAPPING,  # 继承 DatabaseService 的字段映射
        'analysis_details': '辅助分析理由',
        'analysis_status': '分析状态',
        'check_results': '核查结果',
    }

    # 颜色配置
    COLOR_MAPPING = {
        'red': 'FF9999',  # 柔和的浅红
        'yellow': 'FFF9B3',  # 温暖的淡黄
        'white': 'FFFFFF',  # 纯白色
        'blue': '99CCFF'  # 清新的浅蓝
    }

    @staticmethod
    async def parse_excel(file_contents: bytes, time_fields: list[str]) -> List[Dict]:
        try:
            df = pd.read_excel(io.BytesIO(file_contents))
            # 定义需要转换的时间字段，从外部传入，复用代码
            # time_fields = ['处警日期时间', '接警时间']
            # 将时间字段从 Excel 序列号转换为 datetime 类型
            for field in time_fields:
                # 如果字段是 float 类型，先转换为字符串并去除小数点
                if pd.api.types.is_float_dtype(df[field]):
                    df[field] = df[field].astype(str).str.replace(r'\.0$', '', regex=True)

                # 将时间字段从字符串转换为 datetime 类型，处理空值
                df[field] = pd.to_datetime(df[field], format='%Y%m%d%H%M%S', errors='coerce')

            # 将 DataFrame 中的所有 NaN 值替换为 None
            df = df.replace({pd.NA: None})
            df = df.replace({float('nan'): None})
            df = df.where(pd.notna(df), None)
            # 确保所有浮点型 NaN 也被替换
            for col in df.columns:
                if pd.api.types.is_float_dtype(df[col]):
                    df[col] = df[col].apply(lambda x: None if pd.isna(x) else x)

            return df.to_dict('records')
        except Exception as e:
            LogUtils.error(f"Excel解析失败: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Excel文件解析失败: {str(e)}")

    @staticmethod
    def export_excel(records: List[PoliceRecord]) -> bytes:
        """
        导出Excel文件
        :param query_filters: 查询过滤条件
        :return: Excel文件的二进制内容
        """
        try:

            # 创建工作簿
            wb = Workbook()
            ws = wb.active

            # 写入表头
            headers = ['序号'] + list(ExcelService.FIELD_MAPPING.values())
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据并设置颜色
            for row_idx, record in enumerate(records, 2):
                # 写入序号
                ws.cell(row=row_idx, column=1, value=row_idx - 1)

                # 写入数据
                for col_idx, field in enumerate(ExcelService.FIELD_MAPPING.keys(), 2):  # 从第2列开始
                    value = getattr(record, field)
                    # 当 field 为 analysis_status 时转换为字符串
                    if field == 'analysis_status':
                        value = ANALYSIS_STATUS_MAPPING.get(value, '未知状态')
                    ws.cell(row=row_idx, column=col_idx, value=value)

                # 设置行颜色
                if hasattr(record, 'analysis_level') and record.analysis_level in ExcelService.COLOR_MAPPING:
                    color = ExcelService.COLOR_MAPPING[record.analysis_level]
                    fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
                    for col in range(1, len(headers) + 1):
                        ws.cell(row=row_idx, column=col).fill = fill

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return output.getvalue()

        except Exception as e:
            LogUtils.error(f"Excel导出失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Excel导出失败: {str(e)}")
