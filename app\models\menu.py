from datetime import datetime

from sqlalchemy import Column, Integer, String, Boolean, DateTime
from app.db.database import Base

class Menu(Base):
    __tablename__ = "menu_info"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(64), nullable=False)
    sort = Column(Integer, nullable=True)
    is_link = Column(Boolean, nullable=False)
    link_url = Column(String(255), nullable=True)
    is_catalog = Column(Boolean, nullable=False)
    web_path = Column(String(128), nullable=True)
    component = Column(String(128), nullable=True)
    component_name = Column(String(50), nullable=True)
    status = Column(Boolean, nullable=False)
    cache = Column(Boolean, nullable=False)
    visible = Column(Boolean, nullable=False)
    is_iframe = Column(Boolean, nullable=False)
    is_affix = Column(Boolean, nullable=False)
    parent_id = Column(Integer, nullable=True, index=True)
    icon = Column(String(128), nullable=True)
    create_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='创建时间')
    update_time= Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='修改时间')