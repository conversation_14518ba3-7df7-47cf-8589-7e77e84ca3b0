import inspect
import logging
import traceback

# 配置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class LogUtils:

    @staticmethod
    def info(msg: str, include_stack: bool = False):
        if include_stack:
            caller_frame = inspect.currentframe().f_back
            caller_info = f" - 调用位置: {inspect.getframeinfo(caller_frame).filename}:{inspect.getframeinfo(caller_frame).lineno}"
            logger.info(f"{msg}{caller_info}")
        else:
            logger.info(msg)

    @staticmethod
    def error(msg: str, exc_info: bool = True):
        if exc_info:
            stack_trace = traceback.format_exc()
            if stack_trace != "NoneType: None\n":  # 有异常时
                logger.error(f"{msg}\n堆栈信息: {stack_trace}")
            else:  # 无异常但需要显示调用位置
                caller_frame = inspect.currentframe().f_back
                caller_info = f" - 调用位置: {inspect.getframeinfo(caller_frame).filename}:{inspect.getframeinfo(caller_frame).lineno}"
                logger.error(f"{msg}{caller_info}")
        else:
            logger.error(msg)

    @staticmethod
    def warning(msg: str, include_stack: bool = True):
        if include_stack:
            caller_frame = inspect.currentframe().f_back
            caller_info = f" - 调用位置: {inspect.getframeinfo(caller_frame).filename}:{inspect.getframeinfo(caller_frame).lineno}"
            logger.warning(f"{msg}{caller_info}")
        else:
            logger.warning(msg)

    @staticmethod
    def debug(msg: str, include_stack: bool = True):
        if include_stack:
            caller_frame = inspect.currentframe().f_back
            caller_info = f" - 调用位置: {inspect.getframeinfo(caller_frame).filename}:{inspect.getframeinfo(caller_frame).lineno}"
            logger.debug(f"{msg}{caller_info}")
        else:
            logger.debug(msg)
