<template>
  <div class="case-analysis bigscreen">
    <PageHeader title="刑事案件立而不侦巡查" subtitle="Case Analysis" />

    <div class="content-wrapper">
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form common-search">
          <div class="search-left" :class="[showMoreSearch && 'has-height']">
            <el-form-item label="案件编号">
              <el-input v-model="searchForm.ajbh" placeholder="请输入案件编号"></el-input>
            </el-form-item>
            <el-form-item label="案件名称">
              <el-input v-model="searchForm.ajmc" placeholder="请输入案件名称"></el-input>
            </el-form-item>
            <el-form-item label="分析结论">
              <el-select v-model="searchForm.analysis_result" placeholder="请选择分析结论" clearable style="width: 180px">
                <el-option label="无问题" value="无问题"></el-option>
                <el-option label="犯罪嫌疑人不明确" value="犯罪嫌疑人不明确"></el-option>
                <el-option label="犯罪嫌疑人未到案" value="犯罪嫌疑人未到案"></el-option>
                <el-option label="立而不侦" value="立而不侦"></el-option>
                <el-option label="立而不侦预警" value="立而不侦预警"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分析状态">
              <el-select v-model="searchForm.analysis_status" placeholder="请选择分析状态" clearable style="width: 180px">
                <el-option label="已同步" value="init"></el-option>
                <el-option label="分析中" value="processing"></el-option>
                <el-option label="分析完成" value="completed"></el-option>
                <el-option label="分析失败" value="failed"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="立案时间">
              <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"
                style="width: 380px" @change="handleDateRangeChange"></el-date-picker>
            </el-form-item>

          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <div @click="toggleMoreSearch" class="el-button_text">
                {{ !showMoreSearch ? '收起' : '展开' }}
                <i v-show="!showMoreSearch" class="el-icon-arrow-up"></i>
                <i v-show="showMoreSearch" class="el-icon-arrow-down"></i>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button type="primary" @click="handleSyncData" class="tool-btn">
            <i class="el-icon-refresh"></i>
            同步数据
          </el-button>
          <el-button type="primary" @click="handleBatchAnalysis" :disabled="selectedCases.length === 0" class="tool-btn">
            <i class="el-icon-s-operation"></i>
            批量分析
          </el-button>
          <el-button type="primary" @click="handleExport" class="tool-btn">
            <i class="el-icon-download"></i>
            导出数据
          </el-button>
        </div>
      </div>
      <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          :row-class-name="rowClassName" v-loading="loading" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="60" align="center" :selectable="checkSelectable"></el-table-column>
          <el-table-column type="expand" width="28">
            <template slot-scope="props">
              <div class="detail-container">
                <div class="detail-content">
                  <div class="detail-section">
                    <div class="section-title">基本信息</div>
                    <div class="detail-grid">
                      <div class="detail-item">
                        <span class="item-label">案件编号：</span>
                        <span class="item-value">{{ props.row.ajbh }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">案件名称：</span>
                        <span class="item-value">{{ props.row.ajmc }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">案件类别：</span>
                        <span class="item-value">{{ props.row.ajlb }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">案件状态：</span>
                        <span class="item-value">{{ props.row.ajzt }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">受理单位：</span>
                        <span class="item-value">{{ props.row.sldw }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">主办单位：</span>
                        <span class="item-value">{{ props.row.zbdwmc }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">主办人：</span>
                        <span class="item-value">{{ props.row.zbrxm }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">受理时间：</span>
                        <span class="item-value">{{ formatDateTime(null, null, props.row.slsj) }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">立案时间：</span>
                        <span class="item-value">{{ formatDateTime(null, null, props.row.lasj) }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">分析结论：</span>
                        <span class="item-value">{{ props.row.analysis_result || '暂无' }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">分析状态：</span>
                        <span class="item-value">
                          <el-tag :type="getStatusType(props.row.analysis_status)">
                            {{ getStatusText(props.row.analysis_status) }}
                          </el-tag>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="detail-section">
                    <div class="section-title">内容概要</div>
                    <div class="summary-container">
                      <template v-if="props.row.summary">
                        <div v-for="(value, key) in formatSummary(props.row.summary)" :key="key" class="summary-item">
                          <div class="summary-label">{{ key }}</div>
                          <div class="summary-value">
                            <template v-if="Array.isArray(value)">
                              <template v-if="value.length === 0">
                                <span class="empty-value">-</span>
                              </template>
                              <template v-else>
                                <div v-for="(item, index) in value" :key="index" class="array-item">
                                  <template v-if="typeof item === 'object' && item !== null">
                                    <div v-for="(subValue, subKey) in item" :key="subKey" class="array-sub-item">
                                      <span class="sub-label">{{ subKey }}：</span>
                                      <template v-if="Array.isArray(subValue)">
                                        <template v-if="subValue.length === 0">
                                          <span class="empty-value">-</span>
                                        </template>
                                        <template v-else>
                                          <el-tag v-for="(arrayItem, arrayIndex) in subValue" :key="arrayIndex"
                                            size="small" class="summary-tag">
                                            {{ formatValue(arrayItem) }}
                                          </el-tag>
                                        </template>
                                      </template>
                                      <template v-else>
                                        <span class="sub-value">{{ formatValue(subValue) }}</span>
                                      </template>
                                    </div>
                                  </template>
                                  <template v-else>
                                    <el-tag size="small" class="summary-tag">
                                      {{ formatValue(item) }}
                                    </el-tag>
                                  </template>
                                </div>
                              </template>
                            </template>
                            <template v-else-if="typeof value === 'object' && value !== null">
                              <div v-for="(subValue, subKey) in value" :key="subKey" class="summary-sub-item">
                                <span class="sub-label">{{ subKey }}：</span>
                                <template v-if="Array.isArray(subValue)">
                                  <template v-if="subValue.length === 0">
                                    <span class="empty-value">-</span>
                                  </template>
                                  <template v-else>
                                    <div v-for="(item, index) in subValue" :key="index" class="array-item">
                                      <template v-if="typeof item === 'object' && item !== null">
                                        <div v-for="(nestedValue, nestedKey) in item" :key="nestedKey"
                                          class="array-sub-item">
                                          <span class="nested-label">{{ nestedKey }}：</span>
                                          <template v-if="Array.isArray(nestedValue)">
                                            <template v-if="nestedValue.length === 0">
                                              <span class="empty-value">-</span>
                                            </template>
                                            <template v-else>
                                              <el-tag v-for="(arrayItem, arrayIndex) in nestedValue" :key="arrayIndex"
                                                size="small" class="summary-tag">
                                                {{ formatValue(arrayItem) }}
                                              </el-tag>
                                            </template>
                                          </template>
                                          <template v-else>
                                            <span class="nested-value">{{ formatValue(nestedValue) }}</span>
                                          </template>
                                        </div>
                                      </template>
                                      <template v-else>
                                        <el-tag size="small" class="summary-tag">
                                          {{ formatValue(item) }}
                                        </el-tag>
                                      </template>
                                    </div>
                                  </template>
                                </template>
                                <template v-else-if="typeof subValue === 'object' && subValue !== null">
                                  <div class="nested-object">
                                    <div v-for="(nestedValue, nestedKey) in subValue" :key="nestedKey"
                                      class="nested-item">
                                      <span class="nested-label">{{ nestedKey }}：</span>
                                      <template v-if="Array.isArray(nestedValue)">
                                        <template v-if="nestedValue.length === 0">
                                          <span class="empty-value">-</span>
                                        </template>
                                        <template v-else>
                                          <el-tag v-for="(item, index) in nestedValue" :key="index" size="small"
                                            class="summary-tag">
                                            {{ formatValue(item) }}
                                          </el-tag>
                                        </template>
                                      </template>
                                      <template v-else>
                                        <span class="nested-value">{{ formatValue(nestedValue) }}</span>
                                      </template>
                                    </div>
                                  </div>
                                </template>
                                <template v-else>
                                  <span class="sub-value">{{ formatValue(subValue) }}</span>
                                </template>
                              </div>
                            </template>
                            <template v-else>
                              {{ formatValue(value) }}
                            </template>
                          </div>
                        </div>
                      </template>
                      <div v-else class="no-data">暂无内容概要</div>
                    </div>
                  </div>
                  <div class="detail-section" v-if="props.row.conclusion">
                    <div class="section-title">分析结论</div>
                    <div class="detail-text">{{ props.row.conclusion }}</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号" width="70">
          </el-table-column>
          <el-table-column prop="ajbh" label="案件编号" width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="ajmc" label="案件名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="ajlb" label="案件类别" width="120" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="ajfab" label="案件副类别" width="120" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="ajzt" label="案件状态" width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.ajzt }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="ajly" label="案件来源" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="bacx" label="办案程序" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sldw" label="受理单位" show-overflow-tooltip></el-table-column>
          <el-table-column prop="zbdwmc" label="主办单位" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="zbrxm" label="主办人" width="120" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="slsj" label="受理时间" width="180" align="center" show-overflow-tooltip :formatter="formatDateTime"></el-table-column> -->
          <el-table-column prop="lasj" label="立案时间" width="180" align="center" show-overflow-tooltip
            :formatter="formatDateTime"></el-table-column>
          <el-table-column prop="analysis_result" label="分析结论" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="analysis_status" label="分析状态" width="120" align="center">
            <template slot-scope="scope">
              <!-- <el-tag :type="getStatusType(scope.row.analysis_status)">
                {{ getStatusText(scope.row.analysis_status) }}
              </el-tag> -->
              <StatusView :status-option="formatAnalysisStatus(scope.row)" />
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.analysis_status === 'completed'"
                type="text"
                size="small"
                @click="handleViewConclusion(scope.row)"
                class="view-btn"
              >
                <i class="el-icon-view"></i>
                查看结论
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>
    </div>

    <!-- 查看结论弹框 -->
    <el-dialog title="分析结论" :visible.sync="conclusionDialogVisible" width="60%" :before-close="handleConclusionClose"
      custom-class="conclusion-dialog" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true">
      <div class="conclusion-content">
        <div class="conclusion-text">{{ currentCase?.conclusion || '暂无分析结论' }}</div>
      </div>
    </el-dialog>

    <!-- 月份范围对话框 -->
    <DateRangeDialog :visible.sync="dateRangeDialogVisible" :restrictToPastYears="2" @confirm="handleDateRangeConfirm"
      @cancel="handleDateRangeCancel" @close="handleDateRangeClose"></DateRangeDialog>
  </div>
</template>

<script>
import { getSpyingList, syncSpying, batchAnalysis, exportSpyingList } from "@/api/spying";
import PageHeader from "@/components/PageHeader.vue";
import DateRangeDialog from "@/components/DateRangeDialog.vue";
import StatusView from "@/components/StatusView.vue";

export default {
  name: "CaseAnalysis",
  components: {
    PageHeader,
    DateRangeDialog,
    StatusView
  },
  data() {
    return {
      searchForm: {
        ajbh: "",
        ajmc: "",
        analysis_result: "",
        analysis_status: "",
        start_date: "",
        end_date: "",
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
      total: 0,
      conclusionDialogVisible: false,
      currentCase: null,
      selectedCases: [],
      dateRangeDialogVisible: false,
      dateRange: [],
      showMoreSearch: false,
      analysisFormatMap: {
        "processing": { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        "completed": { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        "failed": { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
        "init": { text: "已同步", color: "#409EFF", iconType: 'refresh', bgColor: "rgba(245,108,108,0.1)" },
        "init": { text: "待分析", color: "#E6A23C", iconType: 'clock', bgColor: "rgba(245,108,108,0.1)" },
      },
    };
  },
  created() {
    this.loadTableData();
  },
  methods: {
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.analysis_status];
      return status || {};
    },
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchForm.ajbh) {
          params.ajbh = this.searchForm.ajbh;
        }
        if (this.searchForm.ajmc) {
          params.ajmc = this.searchForm.ajmc;
        }
        if (this.searchForm.analysis_result) {
          params.analysis_result = this.searchForm.analysis_result;
        }
        if (this.searchForm.analysis_status) {
          params.analysis_status = this.searchForm.analysis_status;
        }
        if (this.searchForm.start_date) {
          params.start_date = this.searchForm.start_date;
        }
        if (this.searchForm.end_date) {
          params.end_date = this.searchForm.end_date;
        }

        const res = await getSpyingList(params);
        if (res.code === 200) {
          this.tableData = res.data.items || [];
          this.total = res.data.total || 0;
        } else {
          throw new Error(res.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;
      }
    },
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#ffffffcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    rowClassName({ row }) {
      if (row.analysis_status === "failed") {
        return "warning-row";
      }
      return "";
    },
    async handleSearch() {
      this.currentPage = 1;
      // Make sure date range values are updated in searchForm
      if (this.dateRange && this.dateRange.length === 2) {
        this.searchForm.start_date = this.dateRange[0];
        this.searchForm.end_date = this.dateRange[1];
      }
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        ajbh: "",
        ajmc: "",
        analysis_result: "",
        analysis_status: "",
        start_date: "",
        end_date: "",
      };
      this.dateRange = []; // 重置时间范围控件
      this.handleSearch();
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      try {
        const date = new Date(cellValue);
        if (isNaN(date.getTime())) return cellValue; // 如果转换失败，返回原始值

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error("日期格式化错误:", error);
        return cellValue; // 发生错误时返回原始值
      }
    },
    getStatusType(status) {
      switch (status) {
        case "init":
          return "info";
        case "processing":
          return "warning";
        case "completed":
          return "success";
        case "failed":
          return "danger";
        default:
          return "";
      }
    },
    getStatusText(status) {
      switch (status) {
        case "init":
          return "已同步";
        case "processing":
          return "分析中";
        case "completed":
          return "分析完成";
        case "failed":
          return "分析失败";
        default:
          return "未知状态";
      }
    },
    async handleSyncData() {
      this.dateRangeDialogVisible = true;
    },
    async handleDateRangeConfirm(dateRange) {
      try {
        this.loading = true;
        const params = {
          start_date: dateRange[0], // 添加日期为当月第一天
          end_date: dateRange[1]    // 添加日期为当月第一天
        };
        const res = await syncSpying(params);
        if (res.code === 200) {
          this.$message.success("数据同步成功");
          await this.loadTableData();
        } else {
          throw new Error(res.message || "数据同步失败");
        }
      } catch (error) {
        console.error("数据同步失败:", error);

      } finally {
        this.loading = false;
        this.dateRangeDialogVisible = false;
      }
    },
    handleDateRangeCancel() {
      this.dateRangeDialogVisible = false;
    },
    handleDateRangeClose() {
      this.dateRangeDialogVisible = false;
    },
    checkSelectable(row) {
      return row.analysis_status === 'init' || row.analysis_status === 'failed';
    },
    handleSelectionChange(selection) {
      this.selectedCases = selection;
    },
    async handleBatchAnalysis() {
      try {
        if (this.selectedCases.length === 0) {
          this.$message.warning('请选择要分析的案件');
          return;
        }

        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              确认对选中的 ${this.selectedCases.length} 个案件进行批量分析吗？
            </div>
          </div>
        `,
          "分析确认",
          {
            confirmButtonText: "确定分析",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const ajbhes = this.selectedCases.map(item => item.ajbh);
          const res = await batchAnalysis(ajbhes);
          if (res.code === 200) {
            this.$message.success("批量分析任务已启动");
            await this.loadTableData();
            this.selectedCases = [];
          } else {
            throw new Error(res.message || "批量分析失败");
          }
        } else {
          this.$message.info("已取消分析");
        }
      } catch (error) {
        if (error.toString() !== "cancel" && error.toString() !== "close") {
          console.error("批量分析失败:", error);

        }
      } finally {
        this.loading = false;
      }
    },
    handleViewConclusion(row) {
      if (!row) {
        this.$message.warning('无法获取案件信息');
        return;
      }
      this.currentCase = row;
      this.conclusionDialogVisible = true;
    },
    handleConclusionClose(done) {
      done();
    },
    formatValue(value) {
      if (value === null || value === undefined) return '-';
      if (typeof value === 'object') {
        try {
          return JSON.stringify(value);
        } catch (error) {
          return String(value);
        }
      }
      return String(value);
    },
    formatCaseStatus(status) {
      if (!status) return '-';
      const statusMap = {
        '111': '起诉',
        '109': '撤案'
      };
      return statusMap[status] || '其它';
    },
    formatSummary(summary) {
      try {
        const data = typeof summary === 'string' ? JSON.parse(summary) : summary;
        // 处理案件状态的特殊情况
        if (data['案件状态'] && Array.isArray(data['案件状态'])) {
          data['案件状态'] = data['案件状态'].map(item => {
            if (typeof item === 'object' && item['案件状态']) {
              return {
                ...item,
                '案件状态': item['案件状态']
              };
            }
            return item;
          });
        }
        return data;
      } catch (error) {
        console.error('解析摘要数据失败:', error);
        return {};
      }
    },
    handleDateRangeChange(value) {
      if (value) {
        this.searchForm.start_date = value[0];
        this.searchForm.end_date = value[1];
      } else {
        this.searchForm.start_date = '';
        this.searchForm.end_date = '';
      }
      // 移除自动搜索
      // this.handleSearch();
    },
    async handleExport() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchForm.ajbh) {
          params.ajbh = this.searchForm.ajbh;
        }
        if (this.searchForm.ajmc) {
          params.ajmc = this.searchForm.ajmc;
        }
        if (this.searchForm.analysis_result) {
          params.analysis_result = this.searchForm.analysis_result;
        }
        if (this.searchForm.analysis_status) {
          params.analysis_status = this.searchForm.analysis_status;
        }
        if (this.searchForm.start_date) {
          params.start_date = this.searchForm.start_date;
        }
        if (this.searchForm.end_date) {
          params.end_date = this.searchForm.end_date;
        }

        const res = await exportSpyingList(params);

        // 创建下载链接
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `立而不侦巡查数据_${new Date().getTime()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('导出成功');
      } catch (error) {
        console.error("导出失败：", error);
      } finally {
        this.loading = false;
      }
    },
    toggleMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
    },
  },
};
</script>

<style scoped>
.case-analysis {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  height: 110px;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 10px;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

.pagination-container {
  margin-top: 20px;
  margin-right: 60px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* Element UI 样式覆盖 */
:deep(.el-table) {
  background-color: transparent !important;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  background-color: transparent !important;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
  height: 0 !important;
}

:deep(.el-table__header) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table__header tr th) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-bottom: 2px solid rgba(0, 255, 255, 0.2);
  color: #00ffff;
  font-size: 16px;
  font-weight: bold;
}

:deep(.el-table__body tr) {
  background-color: transparent !important;
}

:deep(.el-table__body tr td) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  color: #ffffff;
  font-size: 14px;
  border-color: rgba(0, 255, 255, 0.1);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-table__row) {
  transition: all 0.3s;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0, 48, 102, 0.3);
}

/* 详情展开样式 */
.detail-container {
  padding: 20px;
  background: rgba(0, 48, 102, 0.2);
  border-radius: 4px;
  margin: 10px 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
}

.section-title {
  color: #00ffff !important;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 15px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #00ffff;
  border-radius: 2px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.item-label {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
  font-size: 14px;
  white-space: nowrap;
}

.item-value {
  color: #ffffff;
  font-size: 14px;
}

.detail-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
  text-align: justify;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  color: #ffffff !important;
  padding-right: 8px;
  font-size: 14px;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 1);
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}

/* 按钮样式 */
:deep(.el-button) {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
  height: 32px;
  padding: 0 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 80px;
  gap: 5px;
}

:deep(.el-button:hover) {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  color: #00ffff;
}

:deep(.el-button:disabled) {
  background: rgba(0, 48, 102, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
  color: rgba(0, 255, 255, 0.4);
  cursor: not-allowed;
  opacity: 0.8;
}

:deep(.el-button:disabled:hover) {
  background: rgba(0, 48, 102, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
  color: rgba(0, 255, 255, 0.4);
}

/* 查看结论按钮 */
.view-btn {
  color: #00ffff;
  background: transparent;
  border: none;
  padding: 0;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 16px;
}

.view-btn:hover {
  color: #ffffff;
  background: transparent;
}

.view-btn i {
  font-size: 16px;
}

/* 分页样式 */
:deep(.el-pagination) {
  padding: 0;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

/* 加载样式 */
:deep(.el-loading-mask) {
  background-color: rgba(0, 24, 51, 0.9) !important;
}

:deep(.el-loading-spinner .el-loading-text) {
  color: #00ffff !important;
}

:deep(.el-loading-spinner .path) {
  stroke: #00ffff !important;
}

/* 警告行样式 */
:deep(.warning-row) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.warning-row td) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.el-table--striped .el-table__body tr.warning-row td) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr.warning-row:hover > td) {
  background-color: rgba(255, 0, 0, 0.3) !important;
}

/* 结论弹框样式 */
:deep(.conclusion-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  height: 60% !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}

:deep(.conclusion-dialog .el-dialog__header) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.conclusion-dialog .el-dialog__title) {
  color: #00ffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

:deep(.conclusion-dialog .el-dialog__body) {
  padding: 20px !important;
  background: rgba(0, 24, 51, 0.4) !important;
  height: 90% !important;
  overflow-y: auto !important;
}

.conclusion-content {
  padding: 20px;
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
}

.conclusion-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
  text-align: justify;
  white-space: pre-wrap;
}

.summary-container {
  padding: 10px;
}

.summary-item {
  margin-bottom: 15px;
  padding: 10px;
  background: rgba(0, 48, 102, 0.2);
  border-radius: 4px;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

.summary-label {
  color: #00ffff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
  padding-left: 8px;
  border-left: 3px solid #00ffff;
}

.summary-value {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
}

.summary-sub-item {
  margin: 8px 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.sub-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  min-width: 100px;
}

.sub-value {
  color: #ffffff;
  font-size: 14px;
}

.summary-tag {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.3);
  color: #00ffff;
  margin: 2px;
}

.summary-tag:hover {
  background: rgba(0, 255, 255, 0.2);
}

.no-data {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  padding: 20px;
  font-size: 14px;
}

.nested-object {
  background: rgba(0, 48, 102, 0.1);
  border-radius: 4px;
  padding: 8px;
  margin-top: 4px;
}

.nested-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin: 4px 0;
}

.nested-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  min-width: 80px;
}

.nested-value {
  color: #ffffff;
  font-size: 14px;
}

.empty-value {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

:deep(.el-icon-arrow-right) {
  color: #00ffff;
}

.array-item {
  margin: 4px 0;
  padding: 4px;
  background: rgba(0, 48, 102, 0.1);
  border-radius: 4px;
}

.array-sub-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin: 4px 0;
  padding-left: 12px;
}

.array-sub-item .sub-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  min-width: 80px;
}

.array-sub-item .sub-value {
  color: #ffffff;
  font-size: 14px;
}

:deep(.el-range-editor .el-range-input) {
  background: none;
  color: white;
}
</style>
