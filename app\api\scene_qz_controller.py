from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Body, Query, Depends
from pydantic import BaseModel, Field
from sqlalchemy import null
from app.common.response_com import create_response
from app.log.log_utils import LogUtils
from app.services.scene_qz.scene_qz_service import SceneQzService, get_scene_qz_service

from app.core.context_handle import UserContext

router = APIRouter()


class EntitySearchParams(BaseModel):
    """实体查询参数模型"""
    id_card: Optional[str] = None
    name: str
    page: Optional[int] = 1
    page_size: Optional[int] = 10

class CaseSearchParams(BaseModel):
    """案件查询参数模型（支持多实体）"""
    entities: List[Dict[str, Optional[str]]] = Field(
        ..., 
        description="实体列表，每个实体包含 'id_card'（可选）和 'name'（必填）",
        example=[{"id_card": "123456789012345678", "name": "张三"}, {"id_card": null, "name": "李四"}]
    )
    case_type: str
    page: Optional[int] = 1
    page_size: Optional[int] = 10

class LocationSearchParams(BaseModel):
    """基于经纬度查询案件参数模型"""
    longitude: float = Field(..., description="经度", ge=-180, le=180)
    latitude: float = Field(..., description="纬度", ge=-90, le=90)
    radius: float = Field(1000, description="查询半径（米），默认1000米", ge=0)
    page: Optional[int] = 1
    page_size: Optional[int] = 10

@router.post("/scene_qz/search_by_entity")
async def search_cases_by_entity(
        search_params: EntitySearchParams = Body(...),
) -> Dict[str, Any]:
    try:
        result = await SceneQzService.search_cases_by_entity(
            search_params.id_card,
            search_params.name,
            search_params.page,
            search_params.page_size
        )
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"根据实体查询案件异常: {str(e)}")
        return create_response(
            message=f"根据实体查询案件异常: {str(e)}",
            code=500
        )

@router.get("/scene_qz/cluster_entities")
async def cluster_entities(
        page: Optional[int] = Query(1, description="页码"),
        page_size: Optional[int] = Query(10, description="每页数量"),
        start_time: Optional[str] = Query(None, description="开始时间，格式为 YYYY-MM-DD HH:MM:SS"),
        end_time: Optional[str] = Query(None, description="结束时间，格式为 YYYY-MM-DD HH:MM:SS")
) -> Dict[str, Any]:
    try:
        result = await SceneQzService.cluster_entities(
            page,
            page_size,
            start_time,
            end_time
        )
        return create_response(
            message="聚类成功",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"实体聚类异常: {str(e)}")
        return create_response(
            message=f"实体聚类异常: {str(e)}",
            code=500
        )

@router.post("/scene_qz/search_cases_with_shared_entities")
async def search_cases_with_shared_entities(
        search_params: CaseSearchParams = Body(...),
) -> Dict[str, Any]:
    try:
        # 验证每个实体的 name 字段是否非空
        for entity in search_params.entities:
            if not entity.get("name"):
                raise ValueError("每个实体必须包含 'name' 字段")
        result = await SceneQzService.search_cases_with_shared_entities(
            search_params.entities,
            search_params.case_type,
            search_params.page,
            search_params.page_size
        )
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"根据实体和案件类型查询案件异常: {str(e)}")
        return create_response(
            message=f"根据实体和案件类型查询案件异常: {str(e)}",
            code=500
        )

@router.get("/scene_qz/cluster_cases_with_shared_entities")
async def cluster_cases_with_shared_entities(
        page: Optional[int] = Query(1, description="页码"),
        page_size: Optional[int] = Query(10, description="每页数量"),
        start_time: Optional[str] = Query(None, description="开始时间，格式为 YYYY-MM-DD HH:MM:SS"),
        end_time: Optional[str] = Query(None, description="结束时间，格式为 YYYY-MM-DD HH:MM:SS")
) -> Dict[str, Any]:
    try:
        result = await SceneQzService.cluster_cases_with_shared_entities(
            page,
            page_size,
            start_time,
            end_time
        )
        return create_response(
            message="聚类成功",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"具有共享实体的案件聚类异常: {str(e)}")
        return create_response(
            message=f"具有共享实体的案件聚类异常: {str(e)}",
            code=500
        )

@router.post("/scene_qz/search_by_location")
async def search_cases_by_location(
        search_params: LocationSearchParams = Body(...),
) -> Dict[str, Any]:
    try:
        result = await SceneQzService.search_cases_by_location(
            search_params.longitude,
            search_params.latitude,
            search_params.radius,
            search_params.page,
            search_params.page_size
        )
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"根据位置查询案件异常: {str(e)}")
        return create_response(
            message=f"根据位置查询案件异常: {str(e)}",
            code=500
        )
    
@router.get("/scene_qz/periodic_stats")
async def get_periodic_stats(
        start_time: Optional[str] = Query(None, description="开始时间，格式为 YYYY-MM-DD HH:MM:SS"),
        end_time: Optional[str] = Query(None, description="结束时间，格式为 YYYY-MM-DD HH:MM:SS")
) -> Dict[str, Any]:
    try:
        result = await SceneQzService.get_periodic_stats(
            start_time,
            end_time
        )
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"获取周期性案件统计信息异常: {str(e)}")
        return create_response(
            message=f"获取周期性案件统计信息异常: {str(e)}",
            code=500
        )

@router.post("/scene_qz/sync")
async def scene_qz_sync(
        start_date: str = Body(..., embed=True),
        end_date: str = Body(..., embed=True),
        user_id: str = Depends(UserContext.get_current_id),
):
    """
    同步场景5的案件数据
    参数:
        start_date: 开始日期(YYYYMMDD)
        end_date: 结束日期(YYYYMMDD)
    """
    try:
        # 调用服务层方法进行同步
        scene_qz_service = await get_scene_qz_service()
        result = await scene_qz_service.scene_qz_data_sync(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
        )

        if not result:
            return create_response(code=500, message="同步失败")

        return create_response(
            data=result,
            message="同步成功"
        )
    except Exception as e:
        LogUtils.error(f"场景5数据同步出错: {str(e)}")
        return create_response(code=500, message=f"同步出错: {str(e)}")
