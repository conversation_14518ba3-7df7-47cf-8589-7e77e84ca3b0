from collections import defaultdict
from typing import List, Optional, Dict, Set, Any


from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.models.menu import Menu
from app.schemas.menu_schema import MenuCreate, MenuUpdate


class MenuService:
    def __init__(self, db: Session):
        self.db = db
        self._menu_cache = {}  # 缓存菜单信息
        self._menu_children_cache = defaultdict(list)  # 缓存菜单的子菜单

    def _build_menu_cache(self):
        """构建菜单缓存"""
        if not self._menu_cache:
            menus = self.db.query(Menu).all()
            for menu in menus:
                self._menu_cache[menu.id] = menu
                if menu.parent_id is not None:
                    self._menu_children_cache[menu.parent_id].append(menu.id)

    def get_menu(self, menu_id: int) :
        row=self.db.query(Menu).filter(Menu.id == menu_id).first()
        if not row:
            return create_response(code=500,message=f"菜单不存在")
        menu_dict = {
            'id': row.id,
            'name': row.name,
            'sort': row.sort,
            'parent_id': row.parent_id,
            'is_link':  1 if row.is_link else 0,
            'link_url': row.link_url,
            'web_path': row.web_path,
            'component': row.component,
            'component_name': row.component_name,
            'status':  1 if row.status else 0,
            'cache':  1 if row.cache else 0,
            'visible':  1 if row.visible else 0,
            'is_iframe':  1 if row.is_iframe else 0,
            'is_affix':   1 if row.is_affix else 0,
            'icon':  row.icon,
            'is_catalog':  1 if row.is_catalog else 0,
            'children': []
        }
        return create_response(code=200, data=menu_dict)

    def get_menus(self) -> Any | None:
        sql = text("""
            SELECT DISTINCT  *
            FROM menu_info mi 
            WHERE mi.id IN (
                SELECT menu_id 
                FROM role_menu_permission rmp 
                WHERE role_id IN (
                    SELECT role_id as roleId FROM user_to_role utr WHERE user_id = :userId
                    UNION
                    SELECT role_id as roleId FROM org_to_role otr 
                    WHERE org_code IN (SELECT org_code FROM user_to_org uto WHERE user_id = :userId)
                     UNION SELECT id FROM role_info WHERE code = 'NORMAL_USER'
                )
            ) AND mi.status = 1
            ORDER BY sort
        """)
        userId=UserContext.get_current_id()
        result = self.db.execute(sql,{"userId":userId}).fetchall()
        if not result:
            return []

        # 将查询结果转换为字典列表
        menu_list = []
        for row in result:
            menu_dict = {
                'id': row.id,
                'name': row.name,
                'sort': row.sort,
                'parent_id': row.parent_id,
                'is_link': row.is_link,
                'link_url': row.link_url,
                'web_path': row.web_path,
                'component': row.component,
                'component_name': row.component_name,
                'status': row.status,
                'cache': row.cache,
                'visible': row.visible,
                'is_iframe': row.is_iframe,
                'is_affix': row.is_affix,
                'icon': row.icon,
                'is_catalog': 1 if row.is_catalog else 0,
                'children': []
            }
            menu_list.append(menu_dict)

        # 构建树形结构
        def build_tree(menus: List[Dict], parent_id: int = -1) -> List[Dict]:
            tree = []
            for menu in menus:
                if menu['parent_id'] == parent_id:
                    children = build_tree(menus, menu['id'])
                    if children:
                        menu['children'] = children
                    tree.append(menu)
            return sorted(tree, key=lambda x: x['sort'])

        # 返回构建好的树形结构
        return build_tree(menu_list)

    # def get_TotalMenu(self, skip: int = 0, limit: int = 100) -> Any | None:
    #     result =self.db.query(Menu).all();
    #
    #     if not result:
    #         return [];
    #     # 将查询结果转换为字典列表
    #     menu_list = []
    #     for row in result:
    #         menu_dict = {
    #             'id': row.id,
    #             'name': row.name,
    #             'sort': row.sort,
    #             'parent_id': row.parent_id,
    #             'is_link': row.is_link,
    #             'link_url': row.link_url,
    #             'web_path': row.web_path,
    #             'component': row.component,
    #             'component_name': row.component_name,
    #             'status': row.status,
    #             'cache': row.cache,
    #             'visible': row.visible,
    #             'is_iframe': row.is_iframe,
    #             'is_affix': row.is_affix,
    #             'icon': row.icon,
    #
    #             'children': []
    #         }
    #         menu_list.append(menu_dict)
    #
    #     # 构建树形结构
    #     def build_tree(menus: List[Dict], parent_id: int = -1) -> List[Dict]:
    #         tree = []
    #         for menu in menus:
    #             if menu['parent_id'] == parent_id:
    #                 children = build_tree(menus, menu['id'])
    #                 if children:
    #                     menu['children'] = children
    #                 tree.append(menu)
    #         return sorted(tree, key=lambda x: x['sort'])
    #
    #     # 返回构建好的树形结构
    #     return build_tree(menu_list)

    def get_TotalMenu(self):
        try:
            result = self.db.query(Menu).order_by(Menu.sort).all()
            if not result:
                return create_response(code=500, data=[])
            # 将查询结果转换为字典列表
            menu_list = []
            for row in result:
                menu_dict = {
                    'id': row.id,
                    'name': row.name,
                    'sort': row.sort,
                    'parent_id': row.parent_id,
                    'is_link':  1 if  row.is_link else 0,
                    'link_url': row.link_url,
                    'web_path': row.web_path,
                    'component': row.component,
                    'component_name': row.component_name,
                    'status': 1 if row.status else 0,
                    'cache':  1 if row.cache else 0,
                    'visible': 1 if row.visible else 0,
                    'is_iframe': 1 if row.is_iframe else 0,
                    'is_affix': 1 if row.is_affix else 0 ,
                    'icon': row.icon,
                    'is_catalog': 1 if row.is_catalog else 0,
                    'children': []
                }
                menu_list.append(menu_dict)

            return create_response(code=200, data=menu_list)
        except Exception as e:
            return create_response(code=500, message=f"str(e)")


    def create_menu(self, menu: MenuCreate) :
        try:
            # 检查菜单名称是否已存在
            existing_menu = self.db.query(Menu).filter(Menu.name == menu.name).first()
            if existing_menu:
                raise Exception("菜单名称重复")
            # 创建新菜单
            try:
                # 创建新菜单
                db_menu = Menu(**menu.dict())
                self.db.add(db_menu)
                self.db.commit()
                self.db.refresh(db_menu)
                return create_response(code=200, data=db_menu)
            except SQLAlchemyError as e:
                self.db.rollback()
                raise Exception("新增失败")
        except Exception as e:
            return create_response(code=500, message=f"{str(e)}")

    def update_menu(self, menu: MenuUpdate):
        try:
            # 查询菜单是否存在
            db_menu = self.db.query(Menu).filter(Menu.id == menu.id).first()
            if not db_menu:
                return create_response(code=500, message=f"菜单不存在")
            # 检查菜单名称是否重复
            if menu.name:
                existing_menu = self.db.query(Menu).filter(
                    Menu.name == menu.name,
                    Menu.id != menu.id
                ).first()
                if existing_menu:
                    return create_response(code=500, message=f"菜单名称重复")

            # 更新菜单信息
            for key, value in menu.dict(exclude_unset=True).items():
                setattr(db_menu, key, value)

            self.db.commit()
            self.db.refresh(db_menu)
            return create_response(code=200, data=db_menu)

        except Exception as e:
            self.db.rollback()
            return create_response(code=500, message=f"菜单更新失败{str(e)}")

    def delete_menu(self, menu_id: int):
        try:
            # 查询菜单是否存在
            db_menu = self.db.query(Menu).filter(Menu.id == menu_id).first()
            if not db_menu:
                raise Exception(detail="菜单不存在")

            try:
                # 删除菜单
                self.db.delete(db_menu)
                self.db.commit()
                return create_response(code=200)

            except SQLAlchemyError as e:
                self.db.rollback()
                return create_response(
                    code=500,
                    message=f"数据库操作失败: {str(e)}"
                )

        except Exception as e:
            return create_response(
                code=500,
                message=f"删除菜单失败: {str(e)}"
            )

