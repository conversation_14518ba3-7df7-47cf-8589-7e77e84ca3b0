from datetime import datetime
from typing import Dict, List, Any, Optional

from fastapi import Depends, Body, APIRouter, File, Form, UploadFile
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.db.database import get_db
from app.log.log_utils import LogUtils
from app.services.scene5.scene_5_service import get_scene_5_service, Scene5Service

router = APIRouter()

"""
电子卷宗控制器
"""


@router.post("/scene5/sync")
async def scene_5_sync(
        start_date: str = Body(..., embed=True),
        end_date: str = Body(..., embed=True),
        user_id: str = Depends(UserContext.get_current_id),
):
    """
    同步场景5的案件数据
    参数:
        start_date: 开始日期(YYYYMMDD)
        end_date: 结束日期(YYYYMMDD)
    """
    try:
        # 调用服务层方法进行同步
        scene_5_service = await get_scene_5_service()
        result = await scene_5_service.scene_5_data_sync(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
        )

        if not result:
            return create_response(code=500, message="同步失败")

        return create_response(
            data=result,
            message="同步成功"
        )
    except Exception as e:
        LogUtils.error(f"场景5数据同步出错: {str(e)}")
        return create_response(code=500, message=f"同步出错: {str(e)}")


@router.post("/scene_5/analyze")
async def scene_5_analyze(ids: List[int] = Body(..., embed=True)) -> Dict[str, Any]:
    """
    Args:
        ajbhes: 案件编号

    Returns:
        Dict[str, Any]: 分析结果
    """
    try:
        # 异步调用，指令发送完成即可
        scene_5_service = await get_scene_5_service()
        res = await scene_5_service.scene_5_analyze_with_ocr(
            ids=ids
        )
        return create_response(
            message="分析指令已发送",
            data=res
        )
    except Exception as e:
        return create_response(
            message=f"分析异常: {str(e)}",
            code=500
        )


class Scene5SearchParams(BaseModel):
    """立而不侦场景查询参数模型"""
    ajbh: Optional[str] = None
    ajmc: Optional[str] = None  # 案件名称
    analysis_result: Optional[str] = None  # 分析结论
    aj_type: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    page: Optional[int] = 1
    page_size: Optional[int] = 10


@router.post("/scene_5/search")
async def scene_5_search(
        search_params: Scene5SearchParams = Body(...),
) -> Dict[str, Any]:
    try:
        result = await Scene5Service.scene_5_search(search_params.__dict__)
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        return create_response(
            message=f"查询异常: {str(e)}",
            code=500
        )


@router.get("/scene_5/get/detail")
async def scene_5_search(
        id: int
) -> Dict[str, Any]:
    try:
        result = await Scene5Service.scene_5_get_detail(id)
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        return create_response(
            message=f"查询异常: {str(e)}",
            code=500
        )


@router.post("/scene_5/upload")
async def upload(
        aj_type: str = Form(...),
        pdf_file: UploadFile = File(...),
        db: Session = Depends(get_db)
):
    """
    同时创建案件信息并上传PDF文件，将PDF转换为图片保存
    """
    try:
        result = await Scene5Service.upload(
            aj_type=aj_type,
            pdf_file=pdf_file,
            db=db
        )

        return create_response(
            message="卷宗PDF文件上传成功",
            data=result
        )
    except Exception as e:
        return create_response(
            message=f"卷宗PDF文件上传:{str(e)}",
            code=500
        )
