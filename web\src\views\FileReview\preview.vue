<template>
  <div class="preview-container">
    <div class="preview-header">
      <h2>{{ caseName }} - 在线审阅</h2>
    </div>
    <div class="preview-content">
      <!-- 左侧目录 -->
      <div class="preview-sidebar" :class="{ 'compressed': sidebarCollapsed }">
        <div class="sidebar-header">
          <span>目录</span>
        </div>
        <div class="sidebar-content">
          <el-tree
            :data="tocData"
            :props="defaultProps"
            @node-click="handleNodeClick"
            node-key="page"
            highlight-current
            :default-expanded-keys="tocData.map(item => item.page)"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span class="node-content">
                <span class="node-title">
                  <i :class="data.icon"></i>
                  <span>{{ node.label }}</span>
                </span>
              </span>
              <el-tag size="mini" type="danger" v-if="data.pageLabel">
                {{ data.pageLabel }}
              </el-tag>
            </span>
          </el-tree>
        </div>
        <!-- 右侧边缘的折叠按钮 -->
        <el-button 
          type="text" 
          size="mini" 
          class="collapse-btn"
          @click="toggleSidebar" 
        >
          <i :class="sidebarCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
        </el-button>
      </div>
      
      <!-- 中间图片预览 -->
      <div class="preview-main">
        <div class="pdf-container">
          <div class="image-viewer" ref="imageViewer" :class="{'fullscreen': isFullscreen}">
            <div v-if="loading" class="loading-container">
              <i class="el-icon-loading"></i>
              <p>图片加载中...</p>
            </div>
            <div v-else-if="imgUrls.length > 0" class="image-container">
              <img 
                :src="processImageUrl(imgUrls[pageNum - 1])" 
                :style="{transform: `scale(${scale})`,height:'100%'}"
                class="preview-image"
                alt="文档图片"
                @load="handleImageLoad"
              />
            </div>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <p>暂无图片数据</p>
            </div>
            <div v-if="isFullscreen" class="fullscreen-controls">
              <el-button @click="toggleFullscreen" circle type="primary">
                <i class="el-icon-close"></i>
              </el-button>
            </div>
            
            <!-- 右侧垂直工具栏 -->
            <!-- <div class="vertical-toolbar">
              <el-button circle size="mini" title="批注">
                <i class="el-icon-edit"></i>
              </el-button>
              <el-button circle size="mini" title="提取">
                <i class="el-icon-scissors"></i>
              </el-button>
              <el-button circle size="mini" title="复制">
                <i class="el-icon-document-copy"></i>
              </el-button>
              <el-button circle size="mini" title="下载">
                <i class="el-icon-download"></i>
              </el-button>
              <el-button circle size="mini" title="搜索">
                <i class="el-icon-search"></i>
              </el-button>
              <el-button circle size="mini" title="旋转">
                <i class="el-icon-refresh"></i>
              </el-button>
              <el-button circle size="mini" title="全屏">
                <i class="el-icon-full-screen"></i>
              </el-button>
            </div> -->
            
            <!-- 右下角缩放控制 -->
            <div class="zoom-controls-vertical" :class="{'fullscreen-zoom': isFullscreen}">
              <el-button circle size="mini" @click="zoomIn" :disabled="scale >= 3">
                <i class="el-icon-zoom-in"></i>
              </el-button>
              <el-button circle size="mini" @click="zoomOut" :disabled="scale <= 0.5">
                <i class="el-icon-zoom-out"></i>
              </el-button>
            </div>
          </div>
          
          <!-- 底部控制栏 -->
          <div class="pdf-controls">
            <div class="pagination-box">
              <el-button class="nav-button first-page" @click="toFirstPage" :disabled="pageNum <= 1">
                <i class="el-icon-d-arrow-left"></i>
              </el-button>
              <el-button class="nav-button" @click="prevPage" :disabled="pageNum <= 1">
                <i class="el-icon-arrow-left"></i>
              </el-button>
              
              <div class="page-counter">
                <el-input
                  v-model.number="inputPage"
                  class="page-input"
                  size="mini"
                  @keyup.enter.native="jumpToPage"
                  @blur="handleInputBlur"
                ></el-input>
                <span class="separator">/</span>
                <span class="total-pages">{{ numPages }}</span>
              </div>
              
              <el-button class="nav-button" @click="nextPage" :disabled="pageNum >= numPages">
                <i class="el-icon-arrow-right"></i>
              </el-button>
              <el-button class="nav-button last-page" @click="toLastPage" :disabled="pageNum >= numPages">
                <i class="el-icon-d-arrow-right"></i>
              </el-button>
              
              <el-divider direction="vertical"></el-divider>
              
              <!-- <el-button class="text-mode-btn" type="text">
                <i class="el-icon-document"></i>
                <span>文本模式</span>
              </el-button> -->
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧问题清单 -->
      <div class="preview-issues" :class="{ 'compressed': issuesCollapsed }">
        <div class="issues-header">
          <span>问题清单</span>
        </div>
        <!-- 左侧边缘的折叠按钮 -->
        <el-button 
          type="text" 
          size="mini" 
          class="collapse-btn"
          @click="toggleIssues" 
        >
          <i :class="issuesCollapsed ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"></i>
        </el-button>
        <div class="issues-content">
          <!-- 问题分类信息 -->
          <div class="issue-categories">
            <div class="category-row">
              <div class="category-label">类型:</div>
              <div class="category-items">
                <el-tag 
                  size="small" 
                  :type="item.type" 
                  v-for="item in errorSummary.types" 
                  :key="item.name"
                >
                  {{ item.name }} ({{ item.count }})
                </el-tag>
              </div>
            </div>
            
            <!-- <div class="category-row">
              <div class="category-label">程度:</div>
              <div class="category-items">
                <el-tag size="small" type="danger" v-for="item in errorSummary.severities" :key="item.name">{{ item.name }}</el-tag>
              </div>
            </div>
            
            <div class="category-row">
              <div class="category-label">状态:</div>
              <div class="category-items">
                <el-tag size="small" type="info" v-for="item in errorSummary.statuses" :key="item.name">{{ item.name }}</el-tag>
              </div>
            </div> -->
          </div>
          
          <!-- 变更状态记录 -->
          <div class="issue-list">
            <div class="section-title">受案登记表</div>
            <div class="issue-item" v-for="item in errorList" :key="item.section">
              <div class="issue-header">
                <span class="issue-label">{{ item.section }}</span>
                <el-tag size="mini" :type="getTagTypeForIssue(item)">{{ getIssueType(item) }}</el-tag>
              </div>
              <div class="issue-description">
                <p>{{ item.issues[0].description }}</p>
              </div>
              <!-- <div class="issue-actions">
                <el-button size="mini">程序违法</el-button>
                <el-button size="mini" type="danger">一般</el-button>
                <el-button size="mini" type="warning">{{ item.issues[0].score }}分</el-button>
              </div> -->
              <div class="issue-footer">
                <!-- <span class="issue-comment"><i class="el-icon-chat-dot-square"></i> 问题记录</span> -->
                <!-- <span class="issue-attachment"><i class="el-icon-document"></i> 查看附件</span> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRecordDetail } from "@/api/file";
import { data } from "jquery";

export default {
  name: 'FilePreview',
  data() {
    return {
      caseName: '',
      caseCode: '',
      mdId: 1,
      numPages: 0,
      pageNum: 1,
      inputPage: 1,
      scale: 1.0,
      tocData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      errorSummary: {
        types: [],
        severities: [],
        statuses: []
      },
      errorList: [],
      loading: false,
      isFullscreen: false,
      pageNotes: {},
      imgUrls: [],
      fileDetail: null,
      sidebarCollapsed: false,
      issuesCollapsed: false
    };
  },
  created() {
    this.mdId = this.$route.query.md_id;
    this.caseName = this.$route.query.case_name || '';
    this.loadDocumentData();
    // 添加键盘导航事件监听
    window.addEventListener('keydown', this.handleKeyDown);
  },
  beforeDestroy() {
    // 移除键盘导航事件监听
    window.removeEventListener('keydown', this.handleKeyDown);
  },
  methods: {
    async loadDocumentData() {
      this.loading = true;
      
      try {
        // 调用API获取卷宗详情
        const res = await getRecordDetail({ id: this.mdId });
        if (res.code === 200) {
          this.fileDetail = res.data;
          this.caseCode = this.fileDetail.case_code;
          this.caseName = this.fileDetail.case_name;
          
          // 设置图片URL数组
          if (this.fileDetail.src && this.fileDetail.src.length > 0) {
            this.imgUrls = this.fileDetail.src;
            this.numPages = this.imgUrls.length;
          }
          
          // 处理目录数据
          this.processTocData();
          
          // 处理问题清单数据
          this.processErrorData();
        } else {
          this.$message.error(res.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取卷宗详情失败', error);
      } finally {
        this.loading = false;
      }
    },
    processTocData() {
      // 处理目录数据
      let treeArr = []
      if (this.fileDetail && this.fileDetail.analysis_page) {
        treeArr = this.fileDetail.analysis_page.map(item => {
          // 计算页码范围
          const firstPage = parseInt(item.pageArray[0]) + 1;
          const lastPage = item.pageArray.length > 1 ? 
            parseInt(item.pageArray[item.pageArray.length - 1]) + 1 : firstPage;
          const pageRange = firstPage === lastPage ? `(${firstPage})` : `(${firstPage}-${lastPage})`;
          
          // 获取批注信息
          const comment = this.getCommentForSection(item.title);
          let pageLabel = ""
          if(item.pageArray.length > 0){
            pageLabel = item.pageArray[0]
          }
          if(item.pageArray.length>1){
            pageLabel+="-"+item.pageArray[item.pageArray.length-1]
          }
          return {
            label: item.title,
            page: firstPage, // 页码从1开始显示
            status: item.status,
            icon: 'el-icon-document',
            pageRange: pageRange,
            comment: comment,
            pageLabel: pageLabel,
          };
        });
      }
      // 这里需要显示顶级的目录
      this.tocData = [{
        label: "卷宗",
        page: '', // 页码从1开始显示
        status: "",
        icon: "el-icon-folder",
        pageLabel:'共1卷',
        children:[{
            label: this.caseName,
            page: '', // 页码从1开始显示
            status: "",
            icon: "el-icon-folder-opened",
            pageLabel: '共'+this.numPages+"页",
            children:treeArr
        }]
      }]
    },
    processErrorData() {
      // 处理问题清单数据
      if (this.fileDetail && this.fileDetail.analysis_error) {
        // 按类型分组错误
        const groupedErrors = {};
        
        this.fileDetail.analysis_error.forEach(error => {
          if (!groupedErrors[error.title]) {
            groupedErrors[error.title] = [];
          }
          
          groupedErrors[error.title].push({
            label: error.child_type === 'system_doc' || error.child_type === 'rg_doc' ? 
              `缺少《${error.title}》` : error.title,
            description: error.content,
            type: this.getErrorTypeText(error.type),
            severity: this.getErrorSeverity(error.type),
            score: this.getErrorScore(error.type),
            reason: error.reason || ''
          });
        });
        
        // 转换为数组格式
        this.errorList = Object.keys(groupedErrors).map(title => {
          return {
            section: title,
            issues: groupedErrors[title]
          };
        });
        
        // 统计错误类型
        this.calculateErrorSummary();
      }
    },
    getErrorTypeText(type) {
      switch (type) {
        case 'cl_lack':
          return '材料缺失';
        case 'other':
          return '格式错误';
        default:
          return '其他问题';
      }
    },
    getErrorSeverity(type) {
      switch (type) {
        case 'cl_lack':
          return '严重';
        case 'other':
          return '一般';
        default:
          return '轻微';
      }
    },
    getErrorScore(type) {
      switch (type) {
        case 'cl_lack':
          return 3;
        case 'other':
          return 1;
        default:
          return 1;
      }
    },
    calculateErrorSummary() {
      // 计算错误统计信息
      const types = {};
      const severities = {};
      const statuses = {
        '待查询': 0,
        '已认定': this.fileDetail.analysis_error.length,
        '已整改': 0
      };
      
      this.fileDetail.analysis_error.forEach(error => {
        const typeText = this.getErrorTypeText(error.type);
        const severityText = this.getErrorSeverity(error.type);
        
        types[typeText] = (types[typeText] || 0) + 1;
        severities[severityText] = (severities[severityText] || 0) + 1;
      });
      
      this.errorSummary = {
        types: Object.keys(types).map(key => ({
          name: key,
          count: types[key],
          type: key === '材料缺失' ? 'danger' : 'info'
        })),
        severities: Object.keys(severities).map(key => ({
          name: key,
          count: severities[key],
          type: key === '严重' ? 'danger' : key === '一般' ? 'warning' : 'info'
        })),
        statuses: Object.keys(statuses).map(key => ({
          name: key,
          count: statuses[key],
          type: key === '已认定' ? 'success' : key === '已整改' ? 'danger' : 'info'
        }))
      };
    },
    getTotalScore() {
      // 计算总分
      if (!this.fileDetail || !this.fileDetail.analysis_error) {
        return 0;
      }
      
      return this.fileDetail.analysis_error.reduce((sum, error) => {
        return sum + this.getErrorScore(error.type);
      }, 0);
    },
    handleImageLoad() {
      // 图片加载完成后触发
      this.loading = false;
    },
    prevPage() {
      if (this.pageNum <= 1) {
        return;
      }
      this.pageNum--;
      this.inputPage = this.pageNum;
    },
    nextPage() {
      if (this.pageNum >= this.numPages) {
        return;
      }
      this.pageNum++;
      this.inputPage = this.pageNum;
    },
    zoomIn() {
      if (this.scale >= 3) return;
      this.scale += 0.25;
    },
    zoomOut() {
      if (this.scale <= 0.5) return;
      this.scale -= 0.25;
    },
    handleNodeClick(data) {
      // 处理目录点击事件，跳转到对应页面
      if (data.page && data.page > 0 && data.page <= this.numPages) {
        this.pageNum = data.page;
        this.inputPage = data.page;
      }
    },
    handleKeyDown(event) {
      // 添加键盘导航
      switch(event.key) {
        case 'ArrowLeft': // 左箭头键：上一页
          this.prevPage();
          break;
        case 'ArrowRight': // 右箭头键：下一页
          this.nextPage();
          break;
        case '+': // 加号键：放大
          this.zoomIn();
          break;
        case '-': // 减号键：缩小
          this.zoomOut();
          break;
      }
    },
    // 切换全屏模式
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    toFirstPage() {
      this.pageNum = 1;
      this.inputPage = 1;
    },
    toLastPage() {
      this.pageNum = this.numPages;
      this.inputPage = this.numPages;
    },
    // 跳转到指定页
    jumpToPage() {
      let targetPage = parseInt(this.inputPage);
      if (isNaN(targetPage) || targetPage < 1) {
        targetPage = 1;
      } else if (targetPage > this.numPages) {
        targetPage = this.numPages;
      }
      
      this.pageNum = targetPage;
      this.inputPage = targetPage;
    },
    // 处理输入框失去焦点
    handleInputBlur() {
      this.inputPage = this.pageNum;
    },
    // 获取章节的批注信息
    getCommentForSection(title) {
      if (!this.fileDetail || !this.fileDetail.analysis_error) {
        return null;
      }
      
      // 查找与章节标题相关的错误
      const relatedErrors = this.fileDetail.analysis_error.filter(error => 
        error.title === title || error.title.includes(title)
      );
      
      if (relatedErrors.length > 0) {
        return relatedErrors[0].content || "存在问题";
      }
      
      return null;
    },
    // 获取特定页面的批注信息
    getCommentForPage(title, pageNum) {
      // 这里可以实现更详细的页面级批注逻辑
      // 如果没有具体到页面的批注数据，可以返回null
      return null;
    },
    // 获取问题类型
    getIssueType(item) {
      return item.issues && item.issues.length > 0 ? item.issues[0].type : '未知';
    },
    // 获取标签类型
    getTagTypeForIssue(item) {
      const type = this.getIssueType(item);
      return type === '材料缺失' ? 'danger' : type === '格式错误' ? 'warning' : 'info';
    },
    // 切换左侧侧边栏
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },
    
    // 切换右侧问题清单
    toggleIssues() {
      this.issuesCollapsed = !this.issuesCollapsed;
    },
    // 处理URL，去除域名
    processImageUrl(url) {
      if (!url) return '';
      try {
        // const urlObj = new URL(url);
        return "/apx/api/scene_5/process_image_url?url="+url //+urlObj.pathname + urlObj.search + urlObj.hash;
      } catch (e) {
        return url;
      }
    }
  },
  computed: {
    totalScore() {
      return this.getTotalScore();
    }
  }
};
</script>

<style lang="less" scoped>
.preview-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  
  .preview-header {
    padding: 10px 20px;
    background-color: #fff;
    border-bottom: 1px solid #dcdfe6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    h2 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }
  
  .preview-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 20px;
    gap: 20px;
    
    .preview-sidebar {
      width: 400px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      position: relative;
      font-size: 16px;
      &.compressed {
        width: 300px;
      }
      
      .sidebar-header {
        padding: 10px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
        font-weight: bold;
        color: #303133;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .sidebar-content {
        flex: 1;
        overflow: auto;
        padding: 10px;
        
        .custom-tree-node {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          width: 100%;
          
          .node-content {
            flex: 1;
            overflow: hidden;
            
            .node-title {
              display: flex;
              align-items: center;
              
              i {
                margin-right: 5px;
                font-size: 16px;
                color: #909399;
              }
              
              .error-node {
                color: #f56c6c;
                font-weight: bold;
              }
              
              .page-range {
                margin-left: 8px;
                color: #909399;
                font-size: 12px;
              }
            }
            
            .node-comment {
              margin-top: 4px;
              font-size: 12px;
              color: #E6A23C;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              padding-left: 21px;
              font-style: italic;
            }
          }
          
          .el-tag {
            margin-left: 8px;
            flex-shrink: 0;
          }
        }
        
        /deep/ .el-tree-node__content {
          height: auto;
          padding: 5px 0;
        }
      }
      
      .collapse-btn {
        position: absolute;
        top: 50%;
        right: -16px;
        transform: translateY(-50%);
        z-index: 10;
        background-color: #f0f2f5;
        border-radius: 50%;
        padding: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        i {
          font-size: 14px;
          color: #606266;
        }
        
        &:hover {
          background-color: #e3e7ed;
          i {
            color: #409EFF;
          }
        }
      }
    }
    
    .preview-main {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .pdf-container {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        height: 100%;
        position: relative;
        
        .pdf-controls {
          display: flex;
          align-items: center;
          padding: 5px 0;
          background-color: transparent;
          justify-content: center;
          position: absolute;
          bottom: 5px;
          left: 0;
          right: 0;
          z-index: 100;
          
          .pagination-box {
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 4px;
            padding: 0 10px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
            
            .nav-button {
              font-size: 14px;
              color: #606266;
              background: transparent;
              border: none;
              padding: 8px 12px;
              margin: 0 2px;
              
              &:hover:not(:disabled) {
                color: #409EFF;
                background-color: #ecf5ff;
              }
              
              &:disabled {
                color: #c0c4cc;
                background-color: transparent;
                cursor: not-allowed;
              }
              
              &.first-page, &.last-page {
                font-size: 12px;
              }
            }
            
            .page-counter {
              display: flex;
              align-items: center;
              margin: 0 10px;
              
              .page-input {
                width: 40px;
                
                /deep/ .el-input__inner {
                  text-align: center;
                  padding: 0 5px;
                  height: 28px;
                  line-height: 28px;
                }
              }
              
              .separator {
                margin: 0 8px;
                color: #606266;
              }
              
              .total-pages {
                color: #606266;
                font-size: 14px;
              }
            }
            
            .el-divider {
              height: 20px;
              margin: 0 8px;
            }
            
            .text-mode-btn {
              display: flex;
              align-items: center;
              font-size: 14px;
              padding: 8px 12px;
              color: #606266;
              
              i {
                margin-right: 5px;
                font-size: 16px;
              }
              
              &:hover {
                color: #409EFF;
              }
            }
          }
        }
        
        .image-viewer {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f7fa;
          min-height: 500px;
          overflow: hidden;
          position: relative;
          transition: all 0.3s ease;
          flex: 1;
          
          .image-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            overflow: auto;
            padding-bottom: 40px;
          }
          
          .preview-image {
            max-width: 100%;
            transition: transform 0.3s ease;
            transform-origin: center center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: move;
          }
          
          &.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            background-color: rgba(0, 0, 0, 0.9);
            padding: 0;
            
            .image-container {
              max-height: 100vh;
              padding-bottom: 80px; /* 在全屏模式下为导航条预留更多空间 */
              
              &::-webkit-scrollbar {
                width: 8px;
                height: 8px;
              }
              
              &::-webkit-scrollbar-thumb {
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
              }
              
              &::-webkit-scrollbar-track {
                background-color: rgba(0, 0, 0, 0.3);
                border-radius: 4px;
              }
            }
            
            .preview-image {
              max-height: calc(100vh - 80px); /* 在全屏模式下为导航条预留更多空间 */
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
            }
            
            .fullscreen-controls {
              position: absolute;
              top: 20px;
              right: 20px;
              z-index: 1001;
            }
          }
          
          .fullscreen-controls {
            position: absolute;
            top: 20px;
            right: 20px;
          }
          
          /* 右下角缩放控制 */
          .zoom-controls-vertical {
            position: absolute;
            bottom: 80px; /* 调整位置，避免与底部导航条重叠 */
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 90;
            opacity: 0.7;
            transition: opacity 0.3s ease;
            
            &:hover {
              opacity: 1;
            }
            
            &.fullscreen-zoom {
              bottom: 100px; /* 全屏模式下更加远离底部 */
              right: 40px;
              
              .el-button {
                background-color: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.3);
                color: #ffffff;
                
                &:hover {
                  background-color: rgba(255, 255, 255, 0.3);
                }
                
                &:active {
                  background-color: rgba(255, 255, 255, 0.4);
                }
                
                &:disabled {
                  background-color: rgba(255, 255, 255, 0.1);
                  color: rgba(255, 255, 255, 0.5);
                }
              }
            }
            
            .el-button {
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
              margin-left: 0px !important;
            }
          }
          
          .loading-container, .no-image {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #909399;
            
            i {
              font-size: 36px;
              margin-bottom: 16px;
            }
            
            p {
              margin: 0;
            }
          }
        }
      }
    }
    
    .preview-issues {
      width: 600px;
      min-width: 300px;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      position: relative;
      
      &.compressed {
        width: 300px;
      }
      
      .issues-header {
        padding: 10px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
        font-weight: bold;
        color: #303133;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .issues-content {
        flex: 1;
        overflow: auto;
        padding: 10px;
        font-size: 16px;
        .issue-categories {
          margin-bottom: 15px;
          
          .category-row {
            display: flex;
            margin-bottom: 10px;
            align-items: center;
            
            .category-label {
              width: 50px;
              font-weight: bold;
              color: #606266;
            }
            
            .category-items {
              display: flex;
              gap: 8px;
              flex-wrap: wrap;
            }
          }
        }
        
        .issue-list {
          margin-top: 15px;
          padding-top: 15px;
          border-top: 1px solid #dcdfe6;
          
          .section-title {
            font-weight: bold;
            color: #606266;
            margin-bottom: 10px;
            background-color: #ebeef5;
            padding: 8px;
            border-radius: 4px;
          }
          
          .issue-item {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            
            .issue-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              
              .issue-label {
                font-weight: bold;
                color: #303133;
              }
              
              .el-tag {
                margin-left: 8px;
              }
            }
            
            .issue-description {
              margin-bottom: 10px;
              
              p {
                margin: 0;
                color: #606266;
                font-size: 13px;
                line-height: 1.4;
                
                &.issue-reason {
                  color: #E6A23C;
                  margin-top: 5px;
                  font-style: italic;
                }
              }
            }
            
            .issue-actions {
              display: flex;
              gap: 8px;
              justify-content: flex-start;
              flex-wrap: wrap;
              margin-bottom: 8px;
              
              .el-button {
                font-size: 12px;
                padding: 5px 8px;
                margin-left: 0;
              }
            }
            
            .issue-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-top: 1px dashed #dcdfe6;
              padding-top: 8px;
              
              .issue-attachment, .issue-comment {
                color: #409EFF;
                font-size: 12px;
                cursor: pointer;
                
                i {
                  margin-right: 4px;
                }
                
                &:hover {
                  color: #66b1ff;
                }
              }
            }
          }
        }
      }
      
      .collapse-btn {
        position: absolute;
        top: 50%;
        left: -16px;
        transform: translateY(-50%);
        z-index: 10;
        background-color: #f0f2f5;
        border-radius: 50%;
        padding: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        i {
          font-size: 14px;
          color: #606266;
        }
        
        &:hover {
          background-color: #e3e7ed;
          i {
            color: #409EFF;
          }
        }
      }
    }
  }
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: #f5f7fa;
  border-radius: 3px;
}
</style> 