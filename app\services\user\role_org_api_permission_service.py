from sqlalchemy.orm import Session
from app.models.role_org_api_permission import RoleOrgApi<PERSON>ermission
from app.schemas.role_org_api_permission_schema import RoleOrgApiPermissionCreate, RoleOrgApiPermissionUpdate

class RoleOrgApiPermissionService:
    def __init__(self, db: Session):
        self.db = db

    def get_permissions(self, skip: int = 0, limit: int = 100):
        return self.db.query(RoleOrgApiPermission).offset(skip).limit(limit).all()

    def get_permission(self, permission_id: int):
        return self.db.query(RoleOrgApiPermission).filter(RoleOrgApiPermission.id == permission_id).first()

    def create_permission(self, permission: RoleOrgApiPermissionCreate):
        db_permission = RoleOrgApiPermission(**permission.dict())
        self.db.add(db_permission)
        self.db.commit()
        self.db.refresh(db_permission)
        return db_permission

    def update_permission(self, permission_id: int, permission: RoleOrgApiPermissionUpdate):
        db_permission = self.get_permission(permission_id)
        if not db_permission:
            return None
        
        update_data = permission.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_permission, key, value)
            
        self.db.commit()
        self.db.refresh(db_permission)
        return db_permission

    def delete_permission(self, permission_id: int):
        db_permission = self.get_permission(permission_id)
        if not db_permission:
            return False
        
        self.db.delete(db_permission)
        self.db.commit()
        return True

    def get_permissions_by_role(self, role_id: int):
        return self.db.query(RoleOrgApiPermission).filter(RoleOrgApiPermission.role_id == role_id).all()

    def get_permissions_by_org(self, org_code: str):
        return self.db.query(RoleOrgApiPermission).filter(RoleOrgApiPermission.org_code == org_code).all()