<template>
  <div class="record-check bigscreen" >
    <PageHeader title="笔录巡查" subtitle="Record Check" />

    <div class="content-wrapper">
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form common-search" label-width="80px">
          <div class="search-left" :class="[showMoreSearch && 'has-height']">
            <el-form-item label="被询问人">
              <el-input v-model="searchForm.sary_xm" placeholder="请输入被询（讯）问人"></el-input>
            </el-form-item>
            <el-form-item label="案件编号">
              <el-input v-model="searchForm.ajbh" placeholder="请输入案件编号"></el-input>
            </el-form-item>
            <el-form-item label="笔录地点">
              <el-input v-model="searchForm.bldd" placeholder="请输入笔录地点"></el-input>
            </el-form-item>
            <el-form-item label="询问人" label-width="80px">
              <el-input v-model="searchForm.mjyxm" placeholder="请输入询问人"></el-input>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyyMMdd"
                @change="handleTimeRangeChange"></el-date-picker>
            </el-form-item>
            <el-form-item label="分析状态">
              <el-select v-model="searchForm.analysis_status" placeholder="请选择分析状态" clearable>
                <el-option label="未分析" :value="0"></el-option>
                <el-option label="分析中" :value="1"></el-option>
                <el-option label="分析完成" :value="2"></el-option>
                <el-option label="分析失败" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分析结果">
              <el-select v-model="searchForm.check_status" placeholder="请选择分析结果" clearable>
                <el-option label="无问题" :value="0"></el-option>
                <el-option label="有问题" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <div @click="toggleMoreSearch" class="el-button_text">
                {{ !showMoreSearch ? '收起' : '展开' }}
                <i v-show="!showMoreSearch" class="el-icon-arrow-up"></i>
                <i v-show="showMoreSearch" class="el-icon-arrow-down"></i>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button type="primary" @click="handleSyncData" class="tool-btn">
            <i class="el-icon-refresh"></i>
            同步数据
          </el-button>
          <el-button type="primary" @click="handleBatchAnalysis" :disabled="selectedRecords.length === 0" 
            class="batch-analysis-btn tool-btn">
            <i class="el-icon-s-operation"></i>
            批量分析
          </el-button>
          <el-upload class="upload-pdf" action="122333" :http-request="handleUploadPDF" :show-file-list="false" 
            :before-upload="beforeUploadPDF" accept=".pdf">
            <el-button type="primary" size="small" class="tool-btn">
              <i class="el-icon-upload2"></i>
              上传PDF
            </el-button>
          </el-upload>
        </div>
      </div>

      <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          :row-class-name="rowClassName" class="el-table--enable-row-hover" v-loading="loading" @selection-change="handleSelectionChange"
          height="calc(100vh - 350px)">
          <el-table-column type="selection" width="60" align="center" :selectable="checkSelectable"></el-table-column>
          <el-table-column type="expand" width="28">
            <template slot-scope="props">
              <div class="detail-container">
                <div class="detail-header">
                  <span class="detail-title">详细信息</span>
                </div>
                <div class="detail-content">
                  <div class="detail-section">
                    <div class="section-title">基本信息</div>
                    <div class="detail-grid">
                      <div class="detail-item">
                        <span class="item-label">案件编号：</span>
                        <span class="item-value">{{ props.row.ajbh }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">被询（讯）问人：</span>
                        <span class="item-value">{{ props.row.sary_xm }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">笔录地点：</span>
                        <span class="item-value">{{ props.row.bldd }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">询问人1：</span>
                        <span class="item-value">{{ props.row.mjyxm }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">询问人2：</span>
                        <span class="item-value">{{ props.row.mjexm }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">记录人：</span>
                        <span class="item-value">{{ props.row.jlrxm }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">开始时间：</span>
                        <span class="item-value">
                          {{ formatDateTime(props.row, null, props.row.kssj) }}
                        </span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">结束时间：</span>
                        <span class="item-value">
                          {{ formatDateTime(props.row, null, props.row.jssj) }}
                        </span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">分析状态：</span>
                        <span class="item-value">
                          <!-- <el-tag :type="getStatusType(props.row.analysis_status)">
                            {{ getStatusText(props.row.analysis_status) }}
                          </el-tag> -->
                          <StatusView :status-option="formatAnalysisStatus(props.row)"></StatusView>
                        </span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">分析结果：</span>
                        <span class="item-value">
                          <div v-if="props.row.analysis_status === 2" class="check-result" :class="props.row.check_status === 0 ? 'check-result-success' : 'check-result-error'">
                            {{ props.row.check_status === 0 ? '无问题' : '有问题' }}
                          </div>
                          <span v-else>-</span>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="detail-section">
                    <div class="section-title">问答环节</div>
                    <div class="detail-text" v-html="formattedText(props.row.blnr)"></div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号" width="70">
          </el-table-column>
          <el-table-column prop="ajbh" label="案件编号" width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="bldd" label="笔录地点" show-overflow-tooltip></el-table-column>
          <el-table-column prop="mjyxm" label="询问人1" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="mjexm" label="询问人2" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="jlrxm" label="记录人" width="120" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column
            prop="blnr"
            label="问答环节"
            min-width="200"
            show-overflow-tooltip>
          </el-table-column> -->
          <el-table-column prop="kssj" label="开始时间" width="180" align="center" show-overflow-tooltip
            :formatter="formatDateTime"></el-table-column>
          <el-table-column prop="jssj" label="结束时间" width="180" align="center" show-overflow-tooltip
            :formatter="formatDateTime"></el-table-column>
          <el-table-column prop="analysis_status" label="分析状态" width="120" align="center">
            <!-- <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.analysis_status)">
                {{ getStatusText(scope.row.analysis_status) }}
              </el-tag>
            </template> -->
            <template slot-scope="scope">
              <StatusView :statusOption="formatAnalysisStatus(scope.row)"></StatusView>
            </template>
          </el-table-column>
          <el-table-column prop="check_status" label="分析结果" width="120" align="center">
            <template slot-scope="scope">
              <!-- <el-tag v-if="scope.row.analysis_status === 2"
                :type="scope.row.check_status === 0 ? 'success' : 'danger'">
                {{ scope.row.check_status === 0 ? '无问题' : '有问题' }}
              </el-tag> -->
              <div v-if="scope.row.analysis_status === 2" class="check-result" :class="scope.row.check_status === 0 ? 'check-result-success' : 'check-result-error'">
                {{ scope.row.check_status === 0 ? '无问题' : '有问题' }}
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button v-if="calcException(scope.row.detection_result)" type="text" size="small"
                @click="handleAbnormalView(scope.row)" class="abnormal-btn">
                <!-- <i class="el-icon-warning"></i> -->
                问题查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>
    </div>

    <!-- 添加异常查看弹框 -->
    <el-dialog title="问题展示" :visible.sync="abnormalDialogVisible" width="60%" :before-close="handleAbnormalClose"
      custom-class="abnormal-dialog screen-dialog" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true"
      :fullscreen="isFullscreen">
      <div slot="title" class="dialog-title">
        <div class="title-left">
          <span>问题展示</span>
          <el-button type="text" class="fullscreen-btn" @click="toggleFullscreen">
            <i :class="isFullscreen ? 'el-icon-zoom-out' : 'el-icon-full-screen'"></i>
          </el-button>
        </div>
      </div>
      <!-- 在最上方添加下载按钮 -->
      <div class="download-container">
        <el-button type="primary" size="small" class="download-btn-top tool-btn" @click="handleDownload">
          <i class="el-icon-download"></i>
          下载
        </el-button>
        <el-button type="primary" size="small" class="print-btn-top tool-btn" @click="handlePrint">
          <i class="el-icon-printer"></i>
          打印
        </el-button>
      </div>

      <div class="abnormal-content common-scroll" id="printArea">

        <!-- 笔录概述部分 -->
        <div class="overview-section" v-if="currentRecord && currentRecord.detection_result">
          <div class="section-header">
            <div class="section-title">笔录概述：</div>
          </div>
          <div class="overview-content">
            {{ JSON.parse(currentRecord.detection_result).blnr_role?.reason || '暂无概述信息' }}
          </div>
        </div>

        <div class="section-title">基础信息问题:</div>
        <!-- 字段展示区域 -->
        <div class="phase-section">
          <el-table :data="basicInfoTableData" :header-cell-style="abnormalHeaderStyle" :cell-style="abnormalCellStyle"
            style="width: 100%;" border>
            <el-table-column prop="field" label="问题名称" width="200" align="center"></el-table-column>
            <el-table-column prop="reason" label="问题描述"></el-table-column>
          </el-table>
        </div>

        <!-- 问答异常展示区域 -->
        <div class="qa-abnormal-section">
          <div class="section-title">笔录内容问题：</div>
          <div class="abnormal-table">
            <el-table :data="abnormalTableData" :header-cell-style="abnormalHeaderStyle" :cell-style="abnormalCellStyle"
              style="width: 100%;" border>
              <el-table-column prop="part" label="问题名称" width="200" align="center"
                class-name="first-column"></el-table-column>
              <el-table-column prop="reason" label="问题描述">
                <template slot-scope="scope">
                  <div v-html="scope.row.reason" @click="scrollToTarget" style="cursor: pointer"></div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 问答异常对照区域 -->
        <div class="qa-compare-section">
          <div class="section-title">问答展示：</div>
          <div class="compare-content">
            <div class="compare-text" v-html="errorTextAll"></div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加PDF分析结果对话框 -->
    <el-dialog :title="'分析结果'" :visible.sync="pdfAnalysisDialogVisible" :close-on-click-modal="false"
      class="pdf-analysis-dialog" width="85%" top="5vh">
      <div class="analysis-wrapper">
        <!-- 进度条部分 -->
        <div v-if="isAnalyzing" class="progress-container">
          <el-progress :percentage="Math.floor(analysisProgress)" :stroke-width="20" :format="percent => `${percent}%`"
            :color="['#0055ff', '#00c0ff']" class="analysis-progress"></el-progress>

          <div class="progress-text">分析进度: {{ Math.floor(analysisProgress) }}%</div>
        </div>

        <!-- 分析过程列表 -->
        <div class="process-list" v-if="processes.length > 0">
          <div class="list-header">
            <i class="el-icon-s-operation"></i>
            <span>分析过程</span>
          </div>

          <div v-for="(item, index) in processes" :key="index" class="process-item">
            <div class="node-header" v-if="item.type === 'node'">
              <el-tag size="mini" class="node-tag">{{ index + 1 }}</el-tag>
              <span class="node-text">{{ item.text }}</span>
            </div>
          </div>
        </div>

        <!-- 最终分析结果 -->
        <div class="final-result" v-if="analysisResult">
          <div class="list-header">
            <i class="el-icon-document"></i>
            <span>分析完成</span>
          </div>
          <!-- <pre class="result-content">{{ analysisResult }}</pre> -->
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="pdfAnalysisDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <DateRangeDialog :visible.sync="dateRangeDialogVisible" :dateRange="dateRange" title-label="请选择登记时间范围"
      @confirm="handleDateRangeConfirm" @cancel="handleDateRangeCancel" @close="handleDateRangeClose" />
  </div>
</template>

<script>
import { getRecordList, uploadTranscriptPDF, syncRecordData, batchAnalyzeRecords } from "@/api/record";
import { runPdfAnalysisWorkflow } from "@/api/chat";
import PageHeader from "@/components/PageHeader.vue";
import DateRangeDialog from "@/components/DateRangeDialog.vue";
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, BorderStyle, AlignmentType, VerticalAlign, HeightRule, ShadingType, TableLayoutType } from 'docx';
import { saveAs } from 'file-saver';
import StatusView from "@/components/StatusView.vue";

export default {
  name: "RecordCheck",
  components: {
    PageHeader,
    DateRangeDialog,
    StatusView
  },
  data() {
    return {
      searchForm: {
        sary_xm: "",
        ajbh: "",
        bldd: "",
        mjyxm: "",
        analysis_status: null,
        check_status: null,
        timeRange: [],
        kssj: "",
        jssj: ""
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
      total: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", start);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", start);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", start);
            },
          },
        ],
      },
      abnormalDialogVisible: false,
      abnormalTableData: [
        {
          part: "异常部分1",
          reason: "异常原因1",
        },
        {
          part: "异常部分2",
          reason: "异常原因2",
        },
      ],
      currentRecord: null,
      autoRefreshTimer: null,
      pdfAnalysisDialogVisible: false,
      isAnalyzing: false,
      analysisResult: "",
      currentMdId: null,
      processes: [],
      processingTimeout: null,
      analysisProgress: 0,
      maxAnalysisTime: 180000, // 最大分析时间3分钟
      errorTextAll: "",
      isFullscreen: false,
      basicInfoTableData: [],
      selectedRecords: [],
      dateRangeDialogVisible: false,
      dateRange: [],
      showMoreSearch: false,
      analysisFormatMap: {
        0: { text: "待分析", color: "#E6A23C", iconType: 'clock', bgColor: "rgba(245,108,108,0.1)" },
        1: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        2: { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
        4: { text: "已同步", color: "#409EFF", iconType: 'refresh', bgColor: "rgba(245,108,108,0.1)" },
      },
      checkFormatMap: {
        "无问题": { iconColor: "#999999", iconType: 'speck' },
        "有问题": { iconColor: "#F56C6C", iconType: 'speck' },
      },
    };
  },
  created() {
    this.loadTableData();
    // 使用实例属性保存定时器ID，便于后续清除
  },
  beforeDestroy() {
    // 清除定时器
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
    }
  },
  methods: {
    formatCheckResults(row) {
      let str = row.check_status==0?'无问题':'有问题';
      let status = this.checkFormatMap[str];
      status = status || {};
      status.text = str
      return status || {};
    },
    // 分析状态格式化
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.analysis_status];
      return status || {};
    },
    calcException(detection_result) {
      if (detection_result) {
        try {
          let result = JSON.parse(detection_result);
          if (result.check_status === 1) {
            return true;
          }
        } catch (error) {
          console.error('解析检测结果失败:', error);
        }
      }
      return false;
    },
    formattedText(text) {
      return text.replace(/\n/g, "<br>");
    },
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchForm.timeRange && this.searchForm.timeRange.length === 2) {
          params.kssj = this.searchForm.timeRange[0];
          params.jssj = this.searchForm.timeRange[1];
        }
        if (this.searchForm.sary_xm) {
          params.sary_xm = this.searchForm.sary_xm;
        }
        if (this.searchForm.ajbh) {
          params.ajbh = this.searchForm.ajbh;
        }
        if (this.searchForm.bldd) {
          params.bldd = this.searchForm.bldd;
        }
        if (this.searchForm.mjyxm) {
          params.asker = this.searchForm.mjyxm;
        }
        if (this.searchForm.analysis_status !== null) {
          params.analysis_status = this.searchForm.analysis_status;
        }
        if (this.searchForm.check_status !== null) {
          params.check_status = this.searchForm.check_status;
        }

        const res = await getRecordList(params);
        if (res.code === 200) {
          this.tableData = res.data.records || [];
          this.total = res.data.total || 0;
        } else {
          throw new Error(res.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;

      }
    },
    headerStyle() {
      return {
        backgroundColor: "#1C3349",
        color: "#FFFFFFcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#FFFFFFb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    rowClassName({ row }) {
      if (!row.detection_result) {
        return "";
      }
      try {
        let result = JSON.parse(row.detection_result);
        if (result.check_status === 1 || row.check_status === 1) {
          return "warning-row";
        }
      } catch (e) {
        console.error("解析detection_result失败:", e);
      }
      return "";
    },
    async handleSearch() {
      this.currentPage = 1;
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        sary_xm: "",
        ajbh: "",
        bldd: "",
        mjyxm: "",
        analysis_status: null,
        check_status: null,
        timeRange: [],
        kssj: "",
        jssj: ""
      };
      this.handleSearch();
    },
    async exportData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchForm.location) {
          params.location = this.searchForm.location;
        }
        if (this.searchForm.interrogator) {
          params.interrogator = this.searchForm.interrogator;
        }
        if (this.searchForm.interviewee) {
          params.interviewee = this.searchForm.interviewee;
        }

        const res = await exportRecords(params);
        if (
          res.type === "application/vnd.ms-excel" ||
          res.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ) {
          // 创建Blob对象
          const blob = new Blob([res], { type: res.type });

          // 创建下载链接
          const downloadElement = document.createElement("a");
          const href = window.URL.createObjectURL(blob);
          downloadElement.href = href;
          downloadElement.download = `笔录数据_${new Date().toLocaleDateString()}.xlsx`;

          // 触发下载
          document.body.appendChild(downloadElement);
          downloadElement.click();
          document.body.removeChild(downloadElement);
          window.URL.revokeObjectURL(href);

          this.$message.success("导出成功");
        } else {
          throw new Error(res.message || "导出失败");
        }
      } catch (error) {
        console.error("导出失败：", error);
      } finally {
        this.loading = false;
      }
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },
    beforeUpload(file) {
      const isExcel =
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("只能上传 Excel 文件!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    async handleUpload(options) {
      try {
        const file = options.file;
        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              笔录数据导入成功后系统会自动处理并展示数据，确认导入笔录数据吗？
            </div>
          </div>
        `,
          "导入确认",
          {
            confirmButtonText: "确定导入",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const formData = new FormData();
          formData.append("file", file);

          const res = await uploadRecords(formData);
          if (res.code === 200 && res.data && res.data.processed) {
            this.$message.success("文件上传成功");
            await this.loadTableData();
          } else {
            throw new Error(res.message || "文件上传失败");
          }
        } else {
          this.$message.info("已取消导入");
        }
      } catch (error) {
        if (error.toString() !== "cancel") {
          console.error("文件上传失败:", error);

        }
      } finally {
        this.loading = false;
      }
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      const date = new Date(cellValue);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    goToHome() {
      this.$router.push("/");
    },
    async handleAbnormalView(row) {
      try {
        this.errorTextAll = "";
        this.currentRecord = row;
        // 将计算属性改为方法调用，并在这里获取异常数据
        this.abnormalTableData = this.getAbnormalTableData(row);
        // 设置基础信息表格数据
        this.basicInfoTableData = this.getBasicInfoTableData(row);
        // 重置全屏状态
        this.isFullscreen = false;
        this.abnormalDialogVisible = true;

        // 重置滚动位置和弹框大小
        this.$nextTick(() => {
          const dialogBody = document.querySelector('.abnormal-dialog .el-dialog__body');
          if (dialogBody) {
            dialogBody.scrollTop = 0;
          }
          // 重置弹框大小
          const dialog = document.querySelector('.abnormal-dialog');
          if (dialog) {
            dialog.style.width = '60%';
            dialog.style.marginTop = '15vh';
          }
        });
      } catch (error) {
        console.error("显示异常数据失败：", error);
        this.$message.error("显示异常数据失败");
      }
    },
    handleAbnormalClose(done) {
      done();
    },
    abnormalHeaderStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#00ffff",
        fontSize: "14px",
        fontWeight: "bold",
        textAlign: "center",
      };
    },
    abnormalCellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffff",
        fontSize: "14px",
      };
    },
    getStatusType(status) {
      // 根据分析状态返回相应的类型
      switch (status) {
        case 0:
          return "info";
        case 1:
          return "warning";
        case 2:
          return 'success'
        case 3:
          return 'danger'
        default:
          return "";
      }
    },
    getStatusText(status) {
      // 根据分析状态返回相应的文本
      switch (status) {
        case 0:
          return "未分析";
        case 1:
          return "分析中";
        case 2:
          return '分析完成'
        case 3:
          return '分析失败'
        default:
          return "未知状态";
      }
    },
    getTimeCheckText(status) {
      switch (status) {
        case "TIME_FORMAT_ERROR":
          return "时间解析异常";
        case "START_END_NULL":
          return "开始、结束时间均为空";
        case "START_NULL":
          return "开始时间为空，结束时间不为空";
        case "END_NULL":
          return "开始时间不为空，结束时间为空";
        case "TIME_LIMIT":
          return "时间不够15分钟";
        default:
          return "时间校验异常";
      }
    },
    getLocationCheckText(status) {
      switch (status) {
        case "NULL":
          return "地点为空";
        case "CONTENT_MISS":
          return "地点缺失";
        default:
          return "地点校验异常";
      }
    },
    beforeUploadPDF(file) {
      const isPDF = file.type === "application/pdf";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isPDF) {
        this.$message.error("只能上传 PDF 文件!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    async handleUploadPDF(options) {
      try {
        const file = options.file;
        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              PDF上传成功后系统会自动处理并展示数据，确认上传PDF文件吗？
            </div>
          </div>
        `,
          "上传确认",
          {
            confirmButtonText: "确定上传",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const formData = new FormData();
          formData.append("pdf_file", file);

          const config = {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          };

          const res = await uploadTranscriptPDF(formData, config);
          if (res.code === 200) {
            this.$message.success(res.message || "PDF上传成功");
            let dataId = res.data;

            // 打开分析对话框并开始分析
            this.currentMdId = dataId;
            this.showPdfAnalysis(dataId);

            // 重新加载表格数据
            await this.loadTableData();
          } else {
            throw new Error(res.message || "PDF上传失败");
          }
        } else {
          this.$message.info("已取消上传");
        }
      } catch (error) {
        if (error.toString() !== "cancel") {
          console.error("PDF上传失败:", error);

        }
      } finally {
        this.loading = false;
      }
    },
    async showPdfAnalysis(mdId) {
      this.pdfAnalysisDialogVisible = true;
      this.isAnalyzing = true;
      this.analysisResult = "";
      this.processes = [];
      this.analysisProgress = 0;

      // 清除可能存在的上一次超时
      if (this.processingTimeout) {
        clearTimeout(this.processingTimeout);
      }

      // 设置超时处理
      // this.processingTimeout = setTimeout(() => {
      //   if (this.isAnalyzing) {
      //     this.isAnalyzing = false
      //     this.$message.warning('分析时间过长，已自动停止。请检查文件大小或尝试重新上传。')
      //     this.processes.push({
      //       type: 'node',
      //       text: '⚠️ 分析时间过长，已自动停止。请检查文件大小或尝试重新上传。'
      //     })
      //   }
      // }, this.maxAnalysisTime)

      // 显示进度的定时器
      const progressInterval = setInterval(() => {
        if (this.isAnalyzing && this.analysisProgress < 95) {
          this.analysisProgress += 1;
        } else {
          clearInterval(progressInterval);
        }
      }, 800);

      try {
        const response = await runPdfAnalysisWorkflow(mdId);

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let partialLine = "";

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          partialLine += chunk;

          // 按换行符分割处理
          const lines = partialLine.split("\n");
          partialLine = lines.pop(); // 保存未完成的行

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) continue;

            if (trimmedLine.startsWith("data: ")) {
              try {
                const jsonData = JSON.parse(trimmedLine.slice(6));

                // 处理不同类型的事件
                if (jsonData.event === "node_started") {
                  // 节点开始事件 - 添加节点信息
                  const nodeTitle = jsonData.data.title || "未命名节点";
                  this.processes.push({
                    type: "node",
                    text: nodeTitle,
                  });

                  // 根据节点进度更新总体进度
                  if (jsonData.data.index && jsonData.data.index > 0) {
                    this.analysisProgress = Math.min(95, jsonData.data.index * 5);
                  }
                } else if (jsonData.event === "workflow_finished") {
                  // 工作流完成事件 - 取最终结果
                  if (jsonData.data && jsonData.data.outputs) {
                    const finalResult =
                      jsonData.data.outputs.result ||
                      JSON.stringify(jsonData.data.outputs, null, 2);
                    this.analysisResult = finalResult;
                  }
                  this.isAnalyzing = false;
                  this.analysisProgress = 100;
                } else if (jsonData.event === "workflow_failed") {
                  // 工作流失败事件
                  this.$message.error("分析失败: " + (jsonData.data.error || "未知错误"));
                  this.isAnalyzing = false;
                }
              } catch (e) {
                console.error("JSON解析错误:", e, trimmedLine);
              }
            }
          }
        }
      } catch (error) {
        console.error("PDF分析失败:", error);
        this.$message.error("PDF分析失败");
        this.isAnalyzing = false;
      } finally {
        // 清除超时计时器
        if (this.processingTimeout) {
          clearTimeout(this.processingTimeout);
          this.processingTimeout = null;
        }

        // 清除进度定时器
        clearInterval(progressInterval);

        this.isAnalyzing = false;
        this.analysisProgress = 100;
      }
    },
    handlePdfAnalysisClose() {
      // 在分析过程中不允许关闭
      if (this.isAnalyzing) {
        this.$message.warning("分析过程中无法关闭，请等待分析完成或点击取消分析");
        return;
      }
      this.pdfAnalysisDialogVisible = false;
    },
    scrollToTarget(event) {
      if (event.target.dataset.action) {
        const element = document.getElementById(event.target.dataset.action);
        if (!element) return;
        element.scrollIntoView({
          behavior: "smooth",
        });
      }
    },
    // 将计算属性转换为方法
    getAbnormalTableData(record) {
      if (!record || !record.detection_result) {
        return [];
      }
      const result = JSON.parse(record.detection_result);
      const abnormalData = [];

      // 检查blnr_content
      if (result.blnr_content && result.blnr_content.status === "NULL") {
        abnormalData.push({
          part: "问答环节内容",
          reason: "问答环节为空",
        });
      }

      // 检查blnr_chart
      if (result.blnr_chart && result.blnr_chart.status === "ERROR") {
        this.errorTextAll = result.blnr_chart.final_text;
        if (!result.blnr_chart || !result.blnr_chart.jump_result) {
          result.blnr_chart.jump_result = [];
        }
        let htmlChart = "";
        for (let i = 0; i < result.blnr_chart.jump_result.length; i++) {
          htmlChart += `${result.blnr_chart.jump_result[i]}` + "<br>";
        }
        abnormalData.push({
          part: "错别字",
          reason: htmlChart,
        });
      }

      // 检查blnr_punctuation
      if (result.blnr_punctuation && result.blnr_punctuation.status === "ERROR") {
        let htmlChart = "";
        if (!result.blnr_punctuation || !result.blnr_punctuation.sentences) {
          result.blnr_punctuation.sentences = [];
        }
        for (let i = 0; i < result.blnr_punctuation.sentences.length; i++) {
          htmlChart +=
            `${result.blnr_punctuation.sentences[i]} (${result.blnr_punctuation.amend[i]})` +
            "<br>";
        }
        abnormalData.push({
          part: "标点符号",
          reason: htmlChart,
        });
      }

      return abnormalData;
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    getBasicInfoStatusType(status) {
      switch (status) {
        case 'RIGHT':
          return 'success';
        case 'NULL':
          return 'warning';
        case 'ERROR':
          return 'danger';
        case 'VICTIM':
          return 'info';
        default:
          return '';
      }
    },
    getBasicInfoStatusText(status) {
      switch (status) {
        case 'RIGHT':
          return '正常';
        case 'NULL':
          return '为空';
        case 'ERROR':
          return '异常';
        case 'VICTIM':
          return '笔录概述';
        default:
          return '未知';
      }
    },
    getBasicInfoTableData(record) {
      if (!record || !record.detection_result) {
        return [];
      }
      const result = JSON.parse(record.detection_result);
      const tableData = [];

      // 添加询问人1信息
      if (result.mjyxm && result.mjyxm.status !== 'RIGHT') {
        tableData.push({
          field: '询问人1',
          status: result.mjyxm.status,
          reason: result.mjyxm.status === 'NULL' ? '为空' : '异常'
        });
      }

      // 添加询问人2信息
      if (result.mjexm && result.mjexm.status !== 'RIGHT') {
        tableData.push({
          field: '询问人2',
          status: result.mjexm.status,
          reason: result.mjexm.status === 'NULL' ? '为空' : '异常'
        });
      }

      // 添加记录人信息
      if (result.jlrxm && result.jlrxm.status !== 'RIGHT') {
        tableData.push({
          field: '记录人',
          status: result.jlrxm.status,
          reason: result.jlrxm.status === 'NULL' ? '为空' : '异常'
        });
      }
      // 添加记录人信息
      if (result.tel_num && result.tel_num.status !== 'RIGHT') {
        tableData.push({
          field: '手机号',
          status: result.tel_num.status,
          reason: result.tel_num.status === 'NULL' ? '为空' : '异常'
        });
      }
      // 添加时间校验信息
      if (result.timecheck && result.timecheck.status !== 'RIGHT') {
        tableData.push({
          field: '时间',
          status: 'ERROR',
          reason: this.getTimeCheckText(result.timecheck.status)
        });
      }

      // 添加地点校验信息
      if (result.bldd && result.bldd.status !== 'RIGHT') {
        tableData.push({
          field: '询（讯）问地点',
          status: result.bldd.status,
          reason: result.bldd.reason
        });
      }

      // 添加户籍校验信息
      if (result.hjszd && result.hjszd.status !== 'RIGHT') {
        tableData.push({
          field: '户籍地',
          status: result.hjszd.status,
          reason: result.hjszd.reason
        });
      }

      // 添加现住址校验信息
      if (result.xzz && result.xzz.status !== 'RIGHT') {
        tableData.push({
          field: '现住址',
          status: result.xzz.status,
          reason: result.xzz.reason
        });
      }


      return tableData;
    },
    checkSelectable(row) {
      return row.analysis_status === 0 || row.analysis_status === 3;
    },
    handleSelectionChange(selection) {
      this.selectedRecords = selection;
    },
    async handleSyncData() {
      this.dateRangeDialogVisible = true;
    },
    async handleDateRangeConfirm(dateRange) {
      try {
        this.loading = true;
        const params = {
          start_date: dateRange[0],
          end_date: dateRange[1]
        };
        const res = await syncRecordData(params);
        if (res.code === 200) {
          this.$message.success("数据同步成功");
          await this.loadTableData();
        } else {
          throw new Error(res.message || "数据同步失败");
        }
      } catch (error) {
        console.error("数据同步失败:", error);

      } finally {
        this.loading = false;
        this.dateRangeDialogVisible = false;
      }
    },
    handleDateRangeCancel() {
      this.dateRangeDialogVisible = false;
    },
    handleDateRangeClose() {
      this.dateRangeDialogVisible = false;
    },
    async handleBatchAnalysis() {
      try {
        if (this.selectedRecords.length === 0) {
          this.$message.warning('请选择要分析的笔录');
          return;
        }

        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              确认对选中的 ${this.selectedRecords.length} 个笔录进行批量分析吗？
            </div>
          </div>
        `,
          "分析确认",
          {
            confirmButtonText: "确定分析",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const mdIds = this.selectedRecords.map(item => item.id);
          const res = await batchAnalyzeRecords(mdIds);
          if (res.code === 200) {
            this.$message.success("批量分析任务已启动");
            await this.loadTableData();
            this.selectedRecords = [];
          } else {
            throw new Error(res.message || "批量分析失败");
          }
        } else {
          this.$message.info("已取消分析");
        }
      } catch (error) {

      } finally {
        this.loading = false;
      }
    },
    async handleDownload() {
      try {
        // 创建文档
        const doc = new Document({
          sections: [{
            properties: {},
            children: [
              new Paragraph({
                text: "问题展示报告",
                heading: HeadingLevel.HEADING_1,
                spacing: {
                  after: 200,
                  line: 360,
                },
              }),
              // 添加笔录概述
              new Paragraph({
                text: "笔录概述：",
                heading: HeadingLevel.HEADING_2,
                spacing: {
                  before: 200,
                  after: 100,
                },
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: this.currentRecord?.detection_result ?
                      JSON.parse(this.currentRecord.detection_result).blnr_role?.reason || '暂无概述信息' :
                      '暂无概述信息',
                  }),
                ],
                spacing: {
                  after: 200,
                },
              }),
              // 添加基础信息问题
              new Paragraph({
                text: "基础信息问题：",
                heading: HeadingLevel.HEADING_2,
                spacing: {
                  before: 200,
                  after: 100,
                },
              }),
              // 创建基础信息表格 - 修改为使用固定宽度和明确的边框样式
              new Table({
                width: {
                  size: 9000, // 使用更大的固定宽度
                  type: WidthType.DXA,
                },
                columnWidths: [3000, 6000], // 添加固定列宽
                layout: TableLayoutType.FIXED, // 使用固定布局模式
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  bottom: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  left: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  right: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  insideHorizontal: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  insideVertical: { style: BorderStyle.SINGLE, size: 2, color: "000000" }
                },
                // 为整个表格设置居中对齐
                alignment: AlignmentType.CENTER,
                rows: [
                  new TableRow({
                    // 设置表头行高
                    height: {
                      value: 400,
                      rule: HeightRule.ATLEAST,
                    },
                    tableHeader: true, // 标记为表头行
                    children: [
                      new TableCell({
                        // 设置单元格垂直对齐和填充
                        verticalAlign: VerticalAlign.CENTER,
                        margins: {
                          top: 100,
                          bottom: 100,
                          left: 150,
                          right: 150,
                        },
                        children: [
                          new Paragraph({
                            text: "问题名称",
                            alignment: AlignmentType.CENTER,
                            style: "Strong", // 使用预定义的粗体样式
                          })
                        ],
                        width: {
                          size: 3000, // 使用固定宽度
                          type: WidthType.DXA,
                        },
                        // 为表头设置背景色
                        shading: {
                          type: ShadingType.CLEAR,
                          color: "auto",
                          fill: "E7E6E6",
                        },
                      }),
                      new TableCell({
                        verticalAlign: VerticalAlign.CENTER,
                        margins: {
                          top: 100,
                          bottom: 100,
                          left: 150,
                          right: 150,
                        },
                        children: [
                          new Paragraph({
                            text: "问题描述",
                            alignment: AlignmentType.CENTER,
                            style: "Strong",
                          })
                        ],
                        width: {
                          size: 6000, // 使用固定宽度
                          type: WidthType.DXA,
                        },
                        shading: {
                          type: ShadingType.CLEAR,
                          color: "auto",
                          fill: "E7E6E6",
                        },
                      }),
                    ],
                  }),
                  ...this.basicInfoTableData.map(item =>
                    new TableRow({
                      height: {
                        value: 350,
                        rule: HeightRule.ATLEAST,
                      },
                      children: [
                        new TableCell({
                          verticalAlign: VerticalAlign.CENTER,
                          margins: {
                            top: 100,
                            bottom: 100,
                            left: 150,
                            right: 150,
                          },
                          children: [
                            new Paragraph({
                              text: item.field,
                              alignment: AlignmentType.LEFT,
                            })
                          ],
                          width: {
                            size: 3000,
                            type: WidthType.DXA,
                          },
                        }),
                        new TableCell({
                          verticalAlign: VerticalAlign.CENTER,
                          margins: {
                            top: 100,
                            bottom: 100,
                            left: 150,
                            right: 150,
                          },
                          children: [
                            new Paragraph({
                              text: item.reason,
                              alignment: AlignmentType.LEFT,
                            })
                          ],
                          width: {
                            size: 6000,
                            type: WidthType.DXA,
                          },
                        }),
                      ],
                    })
                  ),
                ],
              }),
              // 添加笔录内容问题
              new Paragraph({
                text: "笔录内容问题：",
                heading: HeadingLevel.HEADING_2,
                spacing: {
                  before: 200,
                  after: 100,
                },
              }),
              // 创建笔录内容问题表格 - 同样修改为使用固定宽度和明确的边框样式
              new Table({
                width: {
                  size: 9000, // 使用更大的固定宽度
                  type: WidthType.DXA,
                },
                columnWidths: [3000, 6000], // 添加固定列宽
                layout: TableLayoutType.FIXED, // 使用固定布局模式
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  bottom: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  left: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  right: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  insideHorizontal: { style: BorderStyle.SINGLE, size: 2, color: "000000" },
                  insideVertical: { style: BorderStyle.SINGLE, size: 2, color: "000000" }
                },
                // 为整个表格设置居中对齐
                alignment: AlignmentType.CENTER,
                rows: [
                  new TableRow({
                    height: {
                      value: 400,
                      rule: HeightRule.ATLEAST,
                    },
                    tableHeader: true, // 标记为表头行
                    children: [
                      new TableCell({
                        verticalAlign: VerticalAlign.CENTER,
                        margins: {
                          top: 100,
                          bottom: 100,
                          left: 150,
                          right: 150,
                        },
                        children: [
                          new Paragraph({
                            text: "问题名称",
                            alignment: AlignmentType.CENTER,
                            style: "Strong", // 使用预定义的粗体样式
                          })
                        ],
                        width: {
                          size: 3000, // 使用固定宽度
                          type: WidthType.DXA,
                        },
                        // 为表头设置背景色
                        shading: {
                          type: ShadingType.CLEAR,
                          color: "auto",
                          fill: "E7E6E6",
                        },
                      }),
                      new TableCell({
                        verticalAlign: VerticalAlign.CENTER,
                        margins: {
                          top: 100,
                          bottom: 100,
                          left: 150,
                          right: 150,
                        },
                        children: [
                          new Paragraph({
                            text: "问题描述",
                            alignment: AlignmentType.CENTER,
                            style: "Strong",
                          })
                        ],
                        width: {
                          size: 6000,
                          type: WidthType.DXA,
                        },
                        shading: {
                          type: ShadingType.CLEAR,
                          color: "auto",
                          fill: "E7E6E6",
                        },
                      }),
                    ],
                  }),
                  ...this.abnormalTableData.map(item =>
                    new TableRow({
                      height: {
                        value: 350,
                        rule: HeightRule.ATLEAST,
                      },
                      children: [
                        new TableCell({
                          verticalAlign: VerticalAlign.CENTER,
                          margins: {
                            top: 100,
                            bottom: 100,
                            left: 150,
                            right: 150,
                          },
                          children: [
                            new Paragraph({
                              text: item.part,
                              alignment: AlignmentType.LEFT,
                            })
                          ],
                          width: {
                            size: 3000,
                            type: WidthType.DXA,
                          },
                        }),
                        new TableCell({
                          verticalAlign: VerticalAlign.CENTER,
                          margins: {
                            top: 100,
                            bottom: 100,
                            left: 150,
                            right: 150,
                          },
                          children: this.convertSimpleHtmlToParagraphs(item.reason),
                          width: {
                            size: 6000,
                            type: WidthType.DXA,
                          },
                        }),
                      ],
                    })
                  ),
                ],
              }),
              // 添加问答展示
              new Paragraph({
                text: "问答展示：",
                heading: HeadingLevel.HEADING_2,
                spacing: {
                  before: 200,
                  after: 100,
                },
              }),
              ...this.convertHtmlToParagraphs(this.errorTextAll),
            ],
          }],
        });

        // 生成文档
        const blob = await Packer.toBlob(doc);

        // 生成文件名
        const fileName = `问题展示报告_${this.currentRecord?.bldd || '未知笔录'}_${new Date().toLocaleDateString()}.docx`;

        // 保存文件
        saveAs(blob, fileName);

        this.$message.success('文档下载成功');
      } catch (error) {
        console.error('下载文档失败:', error);
        this.$message.error('下载文档失败');
      }
    },

    // 打印功能实现
    handlePrint() {
      const content = document.getElementById('printArea').innerHTML;
      // 函数：递归移除行内样式和类
      // const removeStylesAndClasses = (html) => {
      //   const div = document.createElement('div');
      //   div.innerHTML = html;

      //   // 递归移除样式和类
      //   const removeAttributes = (el) => {
      //     el.removeAttribute('style'); // 移除行内样式
      //     el.removeAttribute('class'); // 移除类名
      //     Array.from(el.children).forEach(removeAttributes); // 递归处理子元素
      //   };
      //   removeAttributes(div);

      //   return div.innerHTML;
      // };
      const removeStylesAndClasses = (html) => {
        // 使用 DOMParser 解析 HTML 字符串
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 递归移除样式和类
        const removeAttributes = (el) => {
          el.removeAttribute('style'); // 移除行内样式
          el.removeAttribute('class');
          if (el.outerHTML == '<th></th>') {
            let parent = el.parentNode;
            parent.removeChild(el);
            return
          }
          Array.from(el.children).forEach(removeAttributes); // 递归处理子元素
        };

        Array.from(doc.body.children).forEach(removeAttributes); // 递归处理文档中的每个元素

        return doc.body.innerHTML;
      };
      // 清除样式后的内容
      const cleanedContent = removeStylesAndClasses(content);
      const printWindow = window.open();
      printWindow.document.write(`
          <html>
            <head>
              <title>打印预览</title>
              <style>
                @page {
                  size: A4 portrait;
                  margin: 20mm;
                }

                body {
                  font-family: Arial, sans-serif;
                  margin: 0;
                  padding: 0;
                  font-size: 14px;
                }

                table {
                  width: 100%;
                  border-collapse: collapse;
                  page-break-inside: auto;
                  table-layout: inherit !important;
                }
                thead {
                  display: table-header-group; /* 表头在每页显示 */
                }

                tr {
                  page-break-inside: avoid; /* 避免分页拆行 */
                  page-break-after: auto;
                }
                  td:{
                    width: 100px;
                  }

                th, td {
                  width: 200px;
                  border: 1px solid #333;
                  padding: 6px 10px;
                  word-break: break-word;
                  box-sizing: border-box; /* 确保边框包含在宽度内 */
                }
                
               
                
                /* 确保表格中的内容不会溢出 */
                table, th, td {
                  max-width: 100%;
                }
                h1 {
                  text-align: center;
                  margin-bottom: 20px;
                }
              </style>
            </head>
            <body>
              <h1>问题展示报告</h1>
              ${cleanedContent}
            </body>
          </html>
        `);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();

    },

    // 将HTML转换为Word文档支持的段落
    convertHtmlToParagraphs(htmlContent) {
      if (!htmlContent) return [new Paragraph('')];

      // 先处理所有红色背景的元素，添加特殊标记
      let processedHtml = htmlContent;

      // 用简单的搜索和替换识别常见的红色文本模式
      // 1. 替换<span class="abnormal-text">...</span>
      processedHtml = processedHtml.replace(/<span\s+class="abnormal-text"[^>]*>(.*?)<\/span>/gi,
        '<span data-red-text="true">$1</span>');

      // 2. 替换<span style="color: red">...</span> 和 <span style="color: #ff4d4f">...</span>
      processedHtml = processedHtml.replace(/<span\s+style="[^"]*color:\s*(red|#ff4d4f|#ff0000)[^"]*"[^>]*>(.*?)<\/span>/gi,
        '<span data-red-text="true">$2</span>');

      // 3. 替换带红色背景的文本
      processedHtml = processedHtml.replace(/<span\s+style="[^"]*background-color:\s*(red|#ff4d4f|#ff0000)[^"]*"[^>]*>(.*?)<\/span>/gi,
        '<span data-red-bg="true">$2</span>');

      // 3.1 替换特定模式的带红色背景的文本 (如 <span style="background: rgba(255, 0, 0, 0.2)")
      processedHtml = processedHtml.replace(/<span\s+style="[^"]*background(?:-color)?:\s*rgba?\(\s*255\s*,\s*0\s*,\s*0[^"]*"[^>]*>(.*?)<\/span>/gi,
        '<span data-red-bg="true">$1</span>');

      // 3.2 替换带有warning-row类的元素
      processedHtml = processedHtml.replace(/<([^>]+)class="[^"]*warning-row[^"]*"[^>]*>(.*?)<\/\1>/gi,
        '<span data-red-bg="true">$2</span>');

      // 3.3 替换可能的内联样式组合
      processedHtml = processedHtml.replace(/<([^>]+)style="[^"]*color:\s*(red|#ff0000|#ff4d4f)[^"]*background(?:-color)?:\s*(red|#ff0000|rgba?\([^)]*\))[^"]*"[^>]*>(.*?)<\/\1>/gi,
        '<span data-red-bg-text="true">$4</span>');

      // 3.4 尝试检测更多可能的红色背景模式
      processedHtml = processedHtml.replace(/<font\s+color="red"[^>]*>(.*?)<\/font>/gi,
        '<span data-red-text="true">$1</span>');

      // 3.5 特别处理 error-text-def 类，这是背景为红色的文本
      processedHtml = processedHtml.replace(/<([^>]+)class="[^"]*error-text-def[^"]*"[^>]*>(.*?)<\/\1>/gi,
        '<span data-red-bg="true">$2</span>');

      // 3.6 处理可能的嵌套情况
      processedHtml = processedHtml.replace(/<span class="error-text-def"[^>]*>(.*?)<\/span>/gi,
        '<span data-red-bg="true">$1</span>');

      // 3.7 处理可能的带有data-v属性的error-text-def
      processedHtml = processedHtml.replace(/<([^>]+)class="[^"]*error-text-def[^"]*"[^>]*data-v[^>]*>(.*?)<\/\1>/gi,
        '<span data-red-bg="true">$2</span>');

      // 3.8 处理特定属性选择器的样式 [data-v...].error-text-def
      processedHtml = processedHtml.replace(/<span[^>]*>(.*?)<\/span>/gi, (match, content) => {
        if (match.includes('error-text-def')) {
          return `<span data-red-bg="true">${content}</span>`;
        }
        return match;
      });

      // 3.9 尝试捕获带有内联style属性的元素中包含background-color:red的情况
      processedHtml = processedHtml.replace(/<([^>]+)style=["'][^"']*background(?:-color)?:\s*(?:red|#ff0000|#ff4d4f|rgba?\(\s*255\s*,\s*0\s*,\s*0[^"']*)["'][^>]*>(.*?)<\/\1>/gi,
        '<span data-red-bg="true">$2</span>');

      // 3.10 扫描并处理所有元素，寻找可能的红色背景
      const tempScanElement = document.createElement('div');
      tempScanElement.innerHTML = processedHtml;
      const allElements = tempScanElement.querySelectorAll('*');
      allElements.forEach(el => {
        // 检查类名
        if (el.className && (
          el.className.includes('error-text-def') ||
          el.className.includes('warning-row'))) {
          el.setAttribute('data-red-bg', 'true');
        }

        // 检查内联样式
        const style = el.getAttribute('style');
        if (style && (
          style.includes('background-color: red') ||
          style.includes('background-color:#ff0000') ||
          style.includes('background-color:#ff4d4f') ||
          style.includes('background:red') ||
          style.includes('background: red') ||
          style.includes('background-color: rgb(255, 0, 0)') ||
          style.includes('background-color: rgba(255, 0, 0'))) {
          el.setAttribute('data-red-bg', 'true');
        }
      });
      processedHtml = tempScanElement.innerHTML;

      // 将HTML内容按<br>标签分割成多个段落
      const paragraphs = [];
      const htmlParts = processedHtml.split(/<br\s*\/?>/i);

      for (const part of htmlParts) {
        if (!part.trim()) continue;

        // 创建一个DOM元素来解析标记后的HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = part;

        // 提取所有标记的节点
        const redTextElements = tempDiv.querySelectorAll('[data-red-text="true"]');
        const redBgElements = tempDiv.querySelectorAll('[data-red-bg="true"]');
        const redBgTextElements = tempDiv.querySelectorAll('[data-red-bg-text="true"]');
        const boldElements = tempDiv.querySelectorAll('b, strong');

        // 如果没有任何标记元素，简单处理
        if (redTextElements.length === 0 && redBgElements.length === 0 && redBgTextElements.length === 0 && boldElements.length === 0) {
          const textContent = tempDiv.textContent.replace(/\s+/g, ' ').trim();
          if (textContent) {
            paragraphs.push(new Paragraph(textContent));
          }
          continue;
        }

        // 为所有标记元素添加唯一标识
        let markedHtml = tempDiv.innerHTML;

        redTextElements.forEach((el, index) => {
          const text = el.textContent;
          markedHtml = markedHtml.replace(
            new RegExp(`<span data-red-text="true">${this.escapeRegExp(text)}</span>`, 'g'),
            `###RED_TEXT_${index}###${text}###RED_TEXT_END###`
          );
        });

        redBgElements.forEach((el, index) => {
          const text = el.textContent;
          markedHtml = markedHtml.replace(
            new RegExp(`<span data-red-bg="true">${this.escapeRegExp(text)}</span>`, 'g'),
            `###RED_BG_${index}###${text}###RED_BG_END###`
          );
        });

        redBgTextElements.forEach((el, index) => {
          const text = el.textContent;
          markedHtml = markedHtml.replace(
            new RegExp(`<span data-red-bg-text="true">${this.escapeRegExp(text)}</span>`, 'g'),
            `###RED_BG_TEXT_${index}###${text}###RED_BG_TEXT_END###`
          );
        });

        // 重新处理加粗标签
        markedHtml = markedHtml.replace(/<b>(.*?)<\/b>/gi, '###BOLD###$1###BOLD_END###');
        markedHtml = markedHtml.replace(/<strong>(.*?)<\/strong>/gi, '###BOLD###$1###BOLD_END###');

        // 移除所有剩余的HTML标签
        markedHtml = markedHtml.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();

        // 使用更可靠的方法提取和处理标记文本
        const segments = [];
        let position = 0;

        // 辅助函数，提取普通文本片段
        const addTextSegment = (end) => {
          if (end > position) {
            const text = markedHtml.substring(position, end).trim();
            if (text) {
              segments.push({
                type: 'normal',
                text: text
              });
            }
          }
        };

        // 一个个处理所有标记
        while (position < markedHtml.length) {
          // 查找下一个标记的开始
          const redTextStart = markedHtml.indexOf('###RED_TEXT_', position);
          const redBgStart = markedHtml.indexOf('###RED_BG_', position);
          const redBgTextStart = markedHtml.indexOf('###RED_BG_TEXT_', position);
          const boldStart = markedHtml.indexOf('###BOLD###', position);

          // 找到最早的标记
          let nextMarkStart = -1;
          let markType = '';

          if (redTextStart !== -1 && (nextMarkStart === -1 || redTextStart < nextMarkStart)) {
            nextMarkStart = redTextStart;
            markType = 'redText';
          }
          if (redBgStart !== -1 && (nextMarkStart === -1 || redBgStart < nextMarkStart)) {
            nextMarkStart = redBgStart;
            markType = 'redBg';
          }
          if (redBgTextStart !== -1 && (nextMarkStart === -1 || redBgTextStart < nextMarkStart)) {
            nextMarkStart = redBgTextStart;
            markType = 'redBgText';
          }
          if (boldStart !== -1 && (nextMarkStart === -1 || boldStart < nextMarkStart)) {
            nextMarkStart = boldStart;
            markType = 'bold';
          }

          // 如果没有找到更多标记，添加剩余文本并结束
          if (nextMarkStart === -1) {
            addTextSegment(markedHtml.length);
            break;
          }

          // 添加标记前的普通文本
          addTextSegment(nextMarkStart);

          // 基于标记类型，找到相应的结束标记并提取内容
          let endTag, contentStart, contentEnd;

          switch (markType) {
            case 'redText':
              endTag = '###RED_TEXT_END###';
              // 找到真正的内容开始位置（跳过标记ID）
              contentStart = markedHtml.indexOf('###', nextMarkStart + 3) + 3;
              contentEnd = markedHtml.indexOf('###RED_TEXT_END###', contentStart);

              if (contentEnd !== -1) {
                const text = markedHtml.substring(contentStart, contentEnd).trim();
                segments.push({
                  type: 'redText',
                  text: text
                });
                position = contentEnd + endTag.length;
              } else {
                // 如果找不到结束标记，跳过这个标记
                position = nextMarkStart + 10;
              }
              break;

            case 'redBg':
              endTag = '###RED_BG_END###';
              // 找到真正的内容开始位置（跳过标记ID）
              contentStart = markedHtml.indexOf('###', nextMarkStart + 3) + 3;
              contentEnd = markedHtml.indexOf('###RED_BG_END###', contentStart);

              if (contentEnd !== -1) {
                const text = markedHtml.substring(contentStart, contentEnd).trim();
                segments.push({
                  type: 'redBg',
                  text: text
                });
                position = contentEnd + endTag.length;
              } else {
                // 如果找不到结束标记，跳过这个标记
                position = nextMarkStart + 10;
              }
              break;

            case 'redBgText':
              endTag = '###RED_BG_TEXT_END###';
              // 找到真正的内容开始位置（跳过标记ID）
              contentStart = markedHtml.indexOf('###', nextMarkStart + 3) + 3;
              contentEnd = markedHtml.indexOf('###RED_BG_TEXT_END###', contentStart);

              if (contentEnd !== -1) {
                const text = markedHtml.substring(contentStart, contentEnd).trim();
                segments.push({
                  type: 'redBgText',
                  text: text
                });
                position = contentEnd + endTag.length;
              } else {
                // 如果找不到结束标记，跳过这个标记
                position = nextMarkStart + 10;
              }
              break;

            case 'bold':
              endTag = '###BOLD_END###';
              contentStart = nextMarkStart + '###BOLD###'.length;
              contentEnd = markedHtml.indexOf(endTag, contentStart);

              if (contentEnd !== -1) {
                const text = markedHtml.substring(contentStart, contentEnd).trim();
                segments.push({
                  type: 'bold',
                  text: text
                });
                position = contentEnd + endTag.length;
              } else {
                // 如果找不到结束标记，跳过这个标记
                position = nextMarkStart + 10;
              }
              break;

            default:
              // 如果是未知标记，跳过
              position = nextMarkStart + 10;
          }
        }

        // 将段落转换为TextRun对象
        const runs = [];
        segments.forEach(segment => {
          switch (segment.type) {
            case 'normal':
              runs.push(new TextRun(segment.text));
              break;

            case 'redText':
              runs.push(new TextRun({
                text: segment.text,
                color: "FF0000"
              }));
              break;

            case 'redBg':
              runs.push(new TextRun({
                text: segment.text,
                highlight: "red",
                shading: {
                  type: "solid",
                  color: "FF0000",
                  fill: "FFDDDD"
                }
              }));
              break;

            case 'redBgText':
              runs.push(new TextRun({
                text: segment.text,
                color: "FF0000",
                highlight: "red",
                shading: {
                  type: "solid",
                  color: "FF0000",
                  fill: "FFDDDD"
                }
              }));
              break;

            case 'bold':
              runs.push(new TextRun({
                text: segment.text,
                bold: true
              }));
              break;
          }
        });

        // 如果有处理后的内容，创建段落
        if (runs.length > 0) {
          paragraphs.push(new Paragraph({ children: runs }));
        } else {
          // 如果无法解析，添加原始文本（确保清理所有标记）
          const cleanText = tempDiv.textContent.replace(/###RED_BG_\d+###|###RED_BG_END###|###RED_TEXT_\d+###|###RED_TEXT_END###|###RED_BG_TEXT_\d+###|###RED_BG_TEXT_END###|###BOLD###|###BOLD_END###/g, '');
          paragraphs.push(new Paragraph(cleanText));
        }
      }

      // 如果没有段落，添加一个空段落
      if (paragraphs.length === 0) {
        paragraphs.push(new Paragraph(''));
      }

      return paragraphs;
    },

    // 辅助函数：转义正则表达式中的特殊字符
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    },
    // 修改stripHtml方法，保留换行符
    stripHtml(html) {
      if (!html) return '';
      return html
        .replace(/<br\s*\/?>/gi, '<br>') // 统一处理br标签
        .replace(/<[^>]*>/g, '') // 移除其他HTML标签
        .replace(/&nbsp;/g, ' '); // 处理空格
    },
    toggleMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
    },
    handleTimeRangeChange(val) {
      if (val && val.length === 2) {
        this.searchForm.kssj = val[0];
        this.searchForm.jssj = val[1];
      } else {
        this.searchForm.kssj = "";
        this.searchForm.jssj = "";
      }
    },
    // 添加一个更简单的HTML转换函数，用于表格内容，避免复杂的HTML解析
    convertSimpleHtmlToParagraphs(htmlContent) {
      if (!htmlContent) return [new Paragraph('')];

      // 移除HTML标签，保留基本文本
      const plainText = htmlContent.replace(/<[^>]*>/g, ' ').replace(/&nbsp;/g, ' ').trim();

      // 按换行符分割
      const lines = plainText.split(/\r?\n/).filter(line => line.trim().length > 0);

      if (lines.length === 0) return [new Paragraph('')];

      // 为每行创建段落
      return lines.map(line => {
        return new Paragraph({
          text: line.trim(),
          spacing: {
            before: 80,
            after: 80,
          }
        });
      });
    },
  },
  computed: {
    // 移除getAbnormalTableDataX计算属性
  },
};
</script>

<style scoped>
.record-check {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

:deep(.el-tooltip__popper.is-light) {
  background: rgba(0, 48, 102, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

:deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow) {
  border-bottom-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow::after) {
  border-bottom-color: rgba(0, 48, 102, 0.95) !important;
}

.search-form {
  width: 100%;
}

.search-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 10px;
}

.search-row.hidden {
  height: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
  pointer-events: none;
}

.toggle-search-text {
  color: #00ffff;
  padding: 5px 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  user-select: none;
  margin-left: 10px;
}

.toggle-search-text:hover {
  color: #ffffff;
}

.toggle-search-text i {
  transition: transform 0.3s;
}

.toggle-search-text i.rotate {
  transform: rotate(180deg);
}

.button-group-right {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-left: auto;
}

.button-group-right .el-button {
  margin-left: 0;
}

.search-row-buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  color: #ffffff !important;
  padding-right: 8px;
  font-size: 14px;
  white-space: nowrap;
  width: 80px !important;
  text-align: right !important;
}

@media screen and (max-width: 1200px) {
  :deep(.el-form-item) {
    /* max-width: calc(33.33% - 10px); */
  }
}

@media screen and (max-width: 992px) {
  :deep(.el-form-item) {
    /* max-width: calc(50% - 10px); */
  }
}

@media screen and (max-width: 768px) {
  :deep(.el-form-item) {
    max-width: 100%;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

:deep(.el-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
  height: 0 !important;
}

.pagination-container {
  margin-top: 20px;
  margin-right: 60px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

.search-left {
  height: 170px;
}

/* Element UI 样式覆盖 */
:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 1);
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}

:deep(.el-table) {
  background-color: transparent !important;
  flex: 1;
  min-height: 0;
}

:deep(.el-table__header) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0, 48, 102, 0.3);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}


:deep(.el-table td, .el-table th.is-leaf) {
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  background-color: rgba(0, 48, 102, 0.3) !important;
}

:deep(.el-table__row) {
  transition: all 0.3s;
}

:deep(.el-table__header-wrapper) {
  background-color: transparent !important;
}

:deep(.el-table__header tr th) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-bottom: 2px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-table__body tr) {
  background-color: transparent !important;
}

:deep(.el-table__body tr td) {
  background-color: rgba(0, 48, 102, 0.3) !important;
}




:deep(.el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__expand-icon) {
  color: #00ffff;
}

:deep(.el-table__expand-icon .el-icon) {
  font-size: 16px;
}

:deep(.el-table__expand-icon--expanded) {
  transform: rotate(90deg);
}

:deep(.el-table__expand-icon:hover) {
  color: #ffffff;
}

.pagination-container {
  margin-top: 20px;
}

:deep(.el-pagination) {
  padding: 0;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

:deep(.el-select:hover .el-input__inner) {
  border-color: #00ffff;
}

:deep(.el-select .el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.1);
  color: #ffffff;
}

:deep(.el-select-dropdown) {
  background-color: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}

.button-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.upload-excel {
  display: inline-block;
}

:deep(.el-upload) {
  display: inline-block;
}

:deep(.el-icon-upload2),
:deep(.el-icon-download) {
  font-size: 16px;
}

/* 添加tooltip样式 */
:deep(.el-tooltip__popper) {
  background-color: rgba(0, 48, 102, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  color: #ffffff !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
  max-width: 400px !important;
  line-height: 1.5 !important;
}

:deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow) {
  border-top-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow::after) {
  border-top-color: rgba(0, 48, 102, 0.95) !important;
}

:deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow) {
  border-bottom-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow::after) {
  border-bottom-color: rgba(0, 48, 102, 0.95) !important;
}

:deep(.el-form-item__label) {
  color: #ffffff !important;
}

/* :deep(.el-form-item__content) {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-left: 0 !important;
} */

/* Element UI 样式覆盖 */
:deep(.el-loading-mask) {
  background-color: rgba(0, 24, 51, 0.9) !important;
}

:deep(.el-loading-spinner .el-loading-text) {
  color: #00ffff !important;
}

:deep(.el-loading-spinner .path) {
  stroke: #00ffff !important;
}

:deep(.el-date-editor.el-input__inner) {
  width: 240px;
}

:deep(.el-date-editor--datetime.el-input__inner) {
  width: 240px;
}

:deep(.el-date-editor .el-range-input) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel) {
  background: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-picker-panel__content) {
  color: #ffffff;
}

:deep(.el-date-table th) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-date-table td.available:hover) {
  color: #00ffff;
}

:deep(.el-date-table td.current:not(.disabled)) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
}

:deep(.el-picker-panel__shortcut) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel__shortcut:hover) {
  color: #00ffff;
}

:deep(.el-date-picker__header-label) {
  color: #ffffff;
}

:deep(.el-date-picker__header-label:hover) {
  color: #00ffff;
}

:deep(.el-picker-panel__icon-btn) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel__icon-btn:hover) {
  color: #00ffff;
}

:deep(.el-form--inline .el-form-item__label) {
  padding-right: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  float: inline-start;
}

:deep(.el-button [class*="el-icon-"] + span) {
  margin-left: 4px;
}

:deep(.el-select .el-input__inner) {
  padding-right: 25px;
}

:deep(.el-date-editor .el-range__icon) {
  line-height: 24px;
}

.abnormal-btn {
  color: #ff9900;
  background: transparent;
  border: none;
  padding: 0;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 16px;
}

.abnormal-btn:hover {
  color: #ffb84d;
  background: transparent;
}

.abnormal-btn i {
  font-size: 16px;
}

/* 异常弹框样式 */
:deep(.abnormal-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}
/* 
:deep(.abnormal-dialog .el-dialog__header) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg,
      rgba(0, 48, 102, 0.8) 0%,
      rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.abnormal-dialog .el-dialog__title) {
  color: #00ffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

:deep(.abnormal-dialog .el-dialog__body) {
  padding: 20px !important;
  background: rgba(0, 24, 51, 0.4) !important;
  height: 90% !important;
  overflow-y: auto !important;
} */

.abnormal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 900px;
  overflow-y: auto;
}

.is-fullscreen .abnormal-content {
  max-height: 94%;
}

.phase-section {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
}

.phase-row {
  display: flex;
  gap: 15px;
}

.phase-item {
  flex: 1;
  min-width: 0;
}

.phase-title {
  color: #00ffff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.phase-content {
  color: #ffffff;
  font-size: 14px;
}

.abnormal-item {
  color: #ff4d4f;
  margin-bottom: 5px;
  line-height: 1.5;
}

.no-abnormal {
  color: #52c41a;
}

.qa-abnormal-section,
.qa-compare-section {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
}

.section-title {
  color: #00ffff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 15px;
}

.abnormal-table {
  width: 100%;
}

.compare-content {
  background: rgba(0, 24, 51, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
}

.compare-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
}

/* 异常文本高亮 */
.abnormal-text {
  color: #ff4d4f;
  cursor: pointer;
  position: relative;
}

.abnormal-text:hover::after {
  content: attr(data-reason);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 24, 51, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  color: #ffffff;
  z-index: 1;
}

:deep(.warning-row) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.warning-row td) {
  /* background-color: rgba(255, 0, 0, 0.2) !important; */
}

:deep(.el-table--striped .el-table__body tr.warning-row td) {
  /* background-color: rgba(255, 0, 0, 0.2) !important; */
}

:deep(.el-table__fixed-right .el-table__body tr.warning-row td) {
  /* background-color: rgb(83 21 21) !important; */
}

:deep(.el-table--enable-row-hover .el-table__body tr.warning-row:hover > td) {
  /* background-color: rgba(255, 0, 0, 0.3) !important; */
}

/* 添加PDF分析对话框相关样式 */
:deep(.pdf-analysis-dialog) {
  background: rgba(0, 24, 51, 0.98) !important;
  border: 1px solid rgba(0, 180, 255, 0.3) !important;
  border-radius: 6px !important;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.7) !important;
}

:deep(.pdf-analysis-dialog .el-dialog__header) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 32, 80, 0.9), rgba(0, 24, 65, 0.85)) !important;
  border-bottom: 1px solid rgba(0, 180, 255, 0.3) !important;
  border-radius: 6px 6px 0 0 !important;
}

:deep(.pdf-analysis-dialog .el-dialog__title) {
  color: #00c0ff !important;
  font-size: 20px !important;
  font-weight: bold !important;
  text-shadow: 0 0 10px rgba(0, 192, 255, 0.4) !important;
  letter-spacing: 1px !important;
}

:deep(.pdf-analysis-dialog .el-dialog__body) {
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 25, 60, 0.98)) !important;
  padding: 25px !important;
  max-height: 75vh !important;
  overflow-y: auto !important;
}

:deep(.pdf-analysis-dialog .el-dialog__footer) {
  background: linear-gradient(0deg, rgba(0, 32, 80, 0.9), rgba(0, 24, 65, 0.85)) !important;
  border-top: 1px solid rgba(0, 180, 255, 0.2) !important;
  padding: 12px 20px !important;
  border-radius: 0 0 6px 6px !important;
}

.analysis-wrapper {
  min-height: 300px;
  color: #e6f7ff;
}

.progress-container {
  margin-bottom: 25px;
  background: rgba(0, 30, 70, 0.5);
  border: 1px solid rgba(0, 180, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.3);
}

.analysis-progress {
  margin-bottom: 15px;
}

.analysis-progress :deep(.el-progress-bar__outer) {
  background-color: rgba(0, 40, 85, 0.6) !important;
  border-radius: 10px !important;
  height: 24px !important;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.4) !important;
}

.analysis-progress :deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #0055ff, #00c0ff) !important;
  border-radius: 10px !important;
  box-shadow: 0 0 10px rgba(0, 192, 255, 0.5) !important;
  transition: width 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) !important;
}

.analysis-progress :deep(.el-progress-bar__innerText) {
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: bold !important;
  text-shadow: 0 0 6px rgba(0, 0, 0, 0.6) !important;
}

.analysis-progress :deep(.el-progress__text) {
  color: #00c0ff !important;
  font-weight: bold !important;
  font-size: 16px !important;
  text-shadow: 0 0 8px rgba(0, 192, 255, 0.5) !important;
}

.progress-text {
  color: #00c0ff;
  font-size: 18px;
  text-align: center;
  margin: 8px 0;
  font-weight: bold;
  text-shadow: 0 0 6px rgba(0, 192, 255, 0.4);
}

.list-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 40, 85, 0.8), rgba(0, 50, 100, 0.7));
  border-radius: 6px;
  margin-bottom: 15px;
  border-left: 4px solid #00c0ff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.list-header i {
  color: #00c0ff;
  margin-right: 10px;
  font-size: 20px;
  text-shadow: 0 0 8px rgba(0, 192, 255, 0.4);
}

.list-header span {
  color: #00c0ff;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 1px;
  text-shadow: 0 0 8px rgba(0, 192, 255, 0.4);
}

.process-list {
  background: rgba(0, 30, 70, 0.4);
  border: 1px solid rgba(0, 180, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
}

.process-item {
  margin-bottom: 12px;
}

.node-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background: rgba(0, 40, 85, 0.5);
  border-radius: 6px;
  border-left: 3px solid rgba(0, 180, 255, 0.5);
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.node-header:hover {
  background: rgba(0, 50, 100, 0.6);
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.node-tag {
  background-color: rgba(0, 180, 255, 0.25) !important;
  border-color: rgba(0, 180, 255, 0.2) !important;
  color: #00c0ff !important;
  margin-right: 12px;
  font-weight: bold !important;
}

.node-text {
  color: #e6f7ff;
  font-size: 15px;
  line-height: 1.5;
}

.final-result {
  background: rgba(0, 30, 70, 0.4);
  border: 1px solid rgba(0, 180, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
}

.result-content {
  background: rgba(0, 20, 45, 0.7);
  border: 1px solid rgba(0, 180, 255, 0.15);
  border-radius: 6px;
  padding: 20px;
  color: #e6f7ff;
  font-family: "Courier New", monospace;
  font-size: 15px;
  line-height: 1.6;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  max-height: 350px;
  overflow-y: auto;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
.analysis-wrapper::-webkit-scrollbar,
.result-content::-webkit-scrollbar,
.process-list::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.analysis-wrapper::-webkit-scrollbar-thumb,
.result-content::-webkit-scrollbar-thumb,
.process-list::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(0, 85, 255, 0.6), rgba(0, 192, 255, 0.6));
  border-radius: 4px;
  box-shadow: inset 0 0 4px rgba(0, 192, 255, 0.3);
}

.analysis-wrapper::-webkit-scrollbar-thumb:hover,
.result-content::-webkit-scrollbar-thumb:hover,
.process-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(0, 85, 255, 0.8), rgba(0, 192, 255, 0.8));
  box-shadow: inset 0 0 6px rgba(0, 192, 255, 0.5);
}

.analysis-wrapper::-webkit-scrollbar-track,
.result-content::-webkit-scrollbar-track,
.process-list::-webkit-scrollbar-track {
  background: rgba(0, 25, 60, 0.3);
  border-radius: 4px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

/* 优化按钮样式 */
.pdf-analysis-dialog :deep(.el-button) {
  background: linear-gradient(90deg, rgba(0, 85, 255, 0.2), rgba(0, 192, 255, 0.2));
  border: 1px solid rgba(0, 192, 255, 0.6);
  color: #00c0ff;
  padding: 8px 20px;
  font-size: 15px;
  font-weight: bold;
  border-radius: 6px;
  text-shadow: 0 0 5px rgba(0, 192, 255, 0.3);
  transition: all 0.3s;
  letter-spacing: 1px;
}

.pdf-analysis-dialog :deep(.el-button:hover) {
  background: linear-gradient(90deg, rgba(0, 85, 255, 0.3), rgba(0, 192, 255, 0.3));
  border-color: rgba(0, 192, 255, 0.8);
  color: #ffffff;
  box-shadow: 0 0 15px rgba(0, 192, 255, 0.4);
  transform: translateY(-2px);
}

/* PDF分析对话框进度条样式 */
.pdf-analysis-dialog .el-progress-bar__innerText,
.pdf-analysis-dialog .el-progress__text {
  color: #ffffff !important;
  font-weight: bold !important;
  text-shadow: 0 0 6px rgba(0, 0, 0, 0.5) !important;
}

.collapse-btn {
  margin-left: 10px;
}

.node-tag {
  margin-left: 10px;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.item-title {
  font-weight: bold;
}

.item-content {
  margin-top: 5px;
}

.analysis-result-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.section-title {
  font-weight: bold;
}

.is-expanded {
  transform: rotate(90deg);
}

.is-expanded i {
  transition: transform 0.3s ease;
}

/* 简化PDF分析对话框样式 */
.pdf-analysis-dialog {
  /deep/ .el-dialog__body {
    padding: 15px 20px;
  }
}

.analysis-wrapper {
  min-height: 300px;
  max-height: 70vh;
  overflow-y: auto;
}

.section-title {
  margin-top: 10px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #333;
}

.process-list {
  margin-bottom: 20px;
  max-height: 50vh;
  overflow-y: auto;
  padding-right: 10px;

  .process-item {
    margin-bottom: 10px;
  }

  .node-header {
    font-weight: 600;
    padding: 10px 15px;
    color: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    transition: all 0.3s;

    &:hover {
      /* background-color: #e9f5fe; */
    }

    .node-index {
      margin-right: 10px;
    }
  }
}

.final-result {
  border-top: 1px solid #eee;
  padding-top: 15px;
  max-height: 30vh;
  overflow-y: auto;

  .result-content {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    font-family: "Courier New", monospace;
    white-space: pre-wrap;
  }
}

/* 自定义滚动条样式 */
:deep(.pdf-analysis-dialog .el-dialog__body::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.pdf-analysis-dialog .el-dialog__body::-webkit-scrollbar-thumb) {
  background: linear-gradient(180deg, rgba(0, 180, 255, 0.7), rgba(0, 255, 255, 0.7));
  border-radius: 4px;
}

:deep(.pdf-analysis-dialog .el-dialog__body::-webkit-scrollbar-track) {
  background: rgb(2, 35, 90, 0.9) !important;
  border-radius: 4px;
}

/* 更好的滚动效果 */
.analysis-wrapper,
.process-list,
.final-result,
.result-content {
  scroll-behavior: smooth;
}

/* PDF分析对话框进度条样式 */
.pdf-analysis-dialog .el-progress-bar__innerText,
.pdf-analysis-dialog .el-progress__text {
  color: #ffffff !important;
  font-weight: bold !important;
  text-shadow: 0 0 4px rgba(0, 255, 255, 0.4) !important;
}

/* 为进度条提供更好的视觉效果 */
.pdf-analysis-dialog .el-progress-bar__outer {
  background-color: rgba(0, 48, 102, 0.5) !important;
  border-radius: 8px !important;
  height: 20px !important;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
}

.pdf-analysis-dialog .el-progress-bar__inner {
  background: linear-gradient(90deg, #00b4ff, #00ffff) !important;
  border-radius: 8px !important;
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.4) !important;
  transition: width 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) !important;
}

/* 确保所有弹窗内容可滚动 */
.el-dialog__body {
  max-height: 80vh;
  overflow-y: auto;
}

:deep(.error-text-def) {
  background-color: red !important;
}

.dialog-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 40px;
  /* 为关闭按钮留出空间 */
}

.title-left {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.title-left span {
  font-size: 24px !important;
  font-weight: bold;
  color: #00ffff;
}

.fullscreen-btn {
  color: #00ffff;
  font-size: 20px;
  padding: 0;
  transition: all 0.3s;
  margin-left: auto;
  margin-right: 30px;
}

.fullscreen-btn:hover {
  color: #ffffff;
  transform: scale(1.1);
}

.download-btn {
  color: #00ffff;
  font-size: 20px;
  padding: 0;
  transition: all 0.3s;
  margin-left: 10px;
}

.download-btn:hover {
  color: #ffffff;
  transform: scale(1.1);
}

:deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
}

:deep(.el-dialog__close) {
  color: #00ffff;
  font-size: 20px;
}

:deep(.el-dialog__close:hover) {
  color: #ffffff;
  transform: rotate(90deg);
}

:deep(.abnormal-dialog.el-dialog--fullscreen) {
  margin: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-height: 100vh !important;
}

:deep(.abnormal-dialog.el-dialog--fullscreen .el-dialog__body) {
  height: calc(100vh - 120px) !important;
  max-height: calc(100vh - 120px) !important;
}

.overview-section {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.overview-content {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
  text-align: justify;
  padding: 10px;
  background: rgba(0, 24, 51, 0.3);
  border-radius: 4px;
}

.batch-analysis-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
  height: 32px;
  padding: 0 15px;
  min-width: 80px;
}

.batch-analysis-btn:hover {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  color: #00ffff;
}

.batch-analysis-btn:disabled {
  background: rgba(0, 48, 102, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
  color: rgba(0, 255, 255, 0.4);
  cursor: not-allowed;
  opacity: 0.8;
}

.batch-analysis-btn:disabled:hover {
  background: rgba(0, 48, 102, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
  color: rgba(0, 255, 255, 0.4);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  color: #00ffff;
  font-size: 14px;
  font-weight: bold;
  margin: 0;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #00ffff;
  border-radius: 2px;
}

.download-btn {
  color: #00ffff;
  font-size: 16px;
  padding: 0;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.download-btn:hover {
  color: #ffffff;
  transform: scale(1.05);
}

.download-btn i {
  font-size: 18px;
}

/* 添加新的CSS样式 */
.download-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.download-btn-top {
  margin-bottom: 10px;
}

.print-btn-top {
  margin-bottom: 10px;
  margin-left: 10px;
}
</style>
