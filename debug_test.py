#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试重构后的函数
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from sqlalchemy import text


async def debug_test():
    """调试测试"""
    print("开始调试测试...")
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 测试完整的SQL查询
        main_query_sql = """
        WITH pivot_data AS (
            SELECT
                qcf.case_id,
                qcf.entity_id,
                MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
                MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
            FROM qz_case_feature qcf
            JOIN police_records pr ON qcf.case_id = pr.police_number
            WHERE pr.source = 0
              AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
            GROUP BY qcf.case_id, qcf.entity_id
        ),
        -- 找到至少出现在两个案件中的实体
        entities_with_multiple_cases AS (
            SELECT
                ENTITY_NAME,
                ID_CARD,
                COUNT(DISTINCT case_id) as case_count
            FROM pivot_data
            WHERE ENTITY_NAME IS NOT NULL AND entity_id > 0  -- 排除案件级别信息
            GROUP BY ENTITY_NAME, ID_CARD
            HAVING COUNT(DISTINCT case_id) >= 2
        ),
        -- 为每个姓名找到代表性身份证（智能聚类）
        name_id_mapping AS (
            SELECT
                ENTITY_NAME,
                MAX(ID_CARD) as representative_id_card
            FROM entities_with_multiple_cases
            WHERE ID_CARD IS NOT NULL
            GROUP BY ENTITY_NAME
        ),
        -- 为所有共享实体分配统一的聚类键
        shared_entities_with_cluster AS (
            SELECT
                emc.ENTITY_NAME,
                emc.ID_CARD,
                CASE
                    WHEN nim.representative_id_card IS NOT NULL THEN nim.representative_id_card
                    ELSE CONCAT('NO_ID_CARD_', emc.ENTITY_NAME)
                END as cluster_key
            FROM entities_with_multiple_cases emc
            LEFT JOIN name_id_mapping nim ON emc.ENTITY_NAME = nim.ENTITY_NAME
        ),
        -- 获取包含共享实体的案件
        cases_with_shared_entities AS (
            SELECT DISTINCT
                pd.case_id,
                COALESCE(pd.CASE_TYPE, 'UNKNOWN') as CASE_TYPE,
                pr.police_time
            FROM pivot_data pd
            JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            JOIN police_records pr ON pd.case_id = pr.police_number
            WHERE pd.entity_id > 0 AND pr.source = 0
        ),
        -- 统计每个案件包含的共享实体数量
        case_shared_entity_count AS (
            SELECT
                cwse.case_id,
                cwse.CASE_TYPE,
                cwse.police_time,
                COUNT(DISTINCT sec.cluster_key) as shared_entity_count
            FROM cases_with_shared_entities cwse
            JOIN pivot_data pd ON cwse.case_id = pd.case_id
            JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            WHERE pd.entity_id > 0
            GROUP BY cwse.case_id, cwse.CASE_TYPE, cwse.police_time
            HAVING COUNT(DISTINCT sec.cluster_key) >= 2  -- 至少包含2个不同的共享实体
        ),
        -- 获取有效案件的所有实体信息
        case_entities_detail AS (
            SELECT
                csec.case_id,
                csec.CASE_TYPE,
                csec.police_time,
                pd.entity_id,
                pd.ENTITY_NAME,
                pd.ID_CARD,
                pd.ENTITY_TYPE,
                sec.cluster_key,
                CASE WHEN sec.cluster_key IS NOT NULL THEN 1 ELSE 0 END as is_shared_entity
            FROM case_shared_entity_count csec
            JOIN pivot_data pd ON csec.case_id = pd.case_id
            LEFT JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            WHERE pd.entity_id > 0 AND pd.ENTITY_NAME IS NOT NULL
        ),
        -- 先计算每个案件的共享实体组合
        case_shared_entities AS (
            SELECT
                case_id,
                CASE_TYPE,
                police_time,
                GROUP_CONCAT(DISTINCT
                    CONCAT(ENTITY_NAME, '|', COALESCE(ID_CARD, ''))
                    ORDER BY ENTITY_NAME
                    SEPARATOR ';;'
                ) as shared_entities_key
            FROM case_entities_detail
            WHERE is_shared_entity = 1
            GROUP BY case_id, CASE_TYPE, police_time
        ),
        -- 按共享实体组合和案件类型进行聚类
        cluster_summary AS (
            SELECT
                CASE_TYPE,
                shared_entities_key,
                COUNT(DISTINCT case_id) as case_count,
                GROUP_CONCAT(DISTINCT
                    CONCAT(case_id, '|', DATE_FORMAT(police_time, '%Y-%m-%d %H:%i:%s'))
                    ORDER BY police_time DESC
                    SEPARATOR ';;'
                ) as cases_info
            FROM case_shared_entities
            GROUP BY CASE_TYPE, shared_entities_key
            HAVING case_count >= 2
        )
        SELECT * FROM cluster_summary
        ORDER BY case_count DESC
        LIMIT 5 OFFSET 0
        """
        
        params = {'limit': 5, 'offset': 0}
        
        print("执行完整SQL查询...")
        result = db.execute(text(main_query_sql), params)
        clusters = result.fetchall()
        
        print(f"查询结果数量: {len(clusters)}")
        
        if clusters:
            print("查询结果:")
            for i, cluster in enumerate(clusters):
                print(f"  {i+1}. 案件类型: {cluster[0]}")
                print(f"      共享实体: {cluster[1]}")
                print(f"      案件数: {cluster[2]}")
                print(f"      案件信息: {cluster[3]}")
        else:
            print("没有找到符合条件的聚类结果")
            
            # 检查中间步骤
            print("\n检查中间步骤...")
            
            # 检查case_shared_entity_count
            check_sql = """
            WITH pivot_data AS (
                SELECT
                    qcf.case_id,
                    qcf.entity_id,
                    MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
                    MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
                FROM qz_case_feature qcf
                JOIN police_records pr ON qcf.case_id = pr.police_number
                WHERE pr.source = 0
                  AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
                GROUP BY qcf.case_id, qcf.entity_id
            ),
            entities_with_multiple_cases AS (
                SELECT
                    ENTITY_NAME,
                    ID_CARD,
                    COUNT(DISTINCT case_id) as case_count
                FROM pivot_data
                WHERE ENTITY_NAME IS NOT NULL AND entity_id > 0
                GROUP BY ENTITY_NAME, ID_CARD
                HAVING COUNT(DISTINCT case_id) >= 2
            ),
            name_id_mapping AS (
                SELECT
                    ENTITY_NAME,
                    MAX(ID_CARD) as representative_id_card
                FROM entities_with_multiple_cases
                WHERE ID_CARD IS NOT NULL
                GROUP BY ENTITY_NAME
            ),
            shared_entities_with_cluster AS (
                SELECT
                    emc.ENTITY_NAME,
                    emc.ID_CARD,
                    CASE
                        WHEN nim.representative_id_card IS NOT NULL THEN nim.representative_id_card
                        ELSE CONCAT('NO_ID_CARD_', emc.ENTITY_NAME)
                    END as cluster_key
                FROM entities_with_multiple_cases emc
                LEFT JOIN name_id_mapping nim ON emc.ENTITY_NAME = nim.ENTITY_NAME
            ),
            cases_with_shared_entities AS (
                SELECT DISTINCT
                    pd.case_id,
                    COALESCE(pd.CASE_TYPE, 'UNKNOWN') as CASE_TYPE,
                    pr.police_time
                FROM pivot_data pd
                JOIN shared_entities_with_cluster sec ON (
                    pd.ENTITY_NAME = sec.ENTITY_NAME AND
                    (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
                )
                JOIN police_records pr ON pd.case_id = pr.police_number
                WHERE pd.entity_id > 0 AND pr.source = 0
            )
            SELECT
                cwse.case_id,
                cwse.CASE_TYPE,
                COUNT(DISTINCT sec.cluster_key) as shared_entity_count
            FROM cases_with_shared_entities cwse
            JOIN pivot_data pd ON cwse.case_id = pd.case_id
            JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            WHERE pd.entity_id > 0
            GROUP BY cwse.case_id, cwse.CASE_TYPE
            ORDER BY shared_entity_count DESC
            LIMIT 10
            """
            
            result = db.execute(text(check_sql))
            check_results = result.fetchall()
            
            print(f"案件共享实体统计 (前10个):")
            for row in check_results:
                print(f"  案件: {row[0]}, 类型: {row[1]}, 共享实体数: {row[2]}")
        
        print("调试测试完成！")
        
    except Exception as e:
        print(f"调试测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(debug_test())
