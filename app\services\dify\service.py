"""
Dify服务
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple, AsyncIterable

import aiohttp
import httpx

from app.config import settings
from app.log.log_utils import LogUtils


class DifyService:
    """Dify服务"""

    def __init__(self, api_key: str = None):
        """
        初始化Dify服务
        
        Args:
            api_key: API密钥
        """
        self.api_key = api_key
        self.base_url = settings.DIFY_API_PREIX
        self.headers = {
            "Authorization": f"Bearer {api_key}" if api_key else "",
            "Content-Type": "application/json"
        }

    async def upload_file(self, session_id: str, document_data: Dict[str, Any]) -> str:
        """
        上传文件到Dify

        Args:
            file: 上传的文件对象
            user_id: 用户ID，默认为None

        Returns:
            上传结果
        """
        url = f"{self.base_url}/v1/files/upload"

        # 移除Content-Type头，让requests自动设置multipart/form-data
        headers = {
            'Authorization': self.headers['Authorization']
        }

        # 读取文件内容
        file = document_data.get('file_object')
        file_content = await file.read()

        # 如果没有指定文件类型，尝试根据文件名推断

        files = {
            'file': (file.filename, file_content, file.content_type)
        }

        data = {}
        if session_id:
            data['user'] = session_id

        try:
            response = requests.post(url, headers=headers, files=files, data=data)
            if response.status_code == 201:
                result = response.json()
                if result.get('id'):
                    return result.get('id')
                return ''
            else:
                raise Exception(f"HTTP Error: {response.text}")

        except Exception as e:
            LogUtils.error(f"上传文件异常: {str(e)}")
            raise Exception(f"上传文件异常: {str(e)}")
        finally:
            # 确保文件指针回到开始位置，以便后续可能的操作
            await file.seek(0)
            # 确保文件被关闭
            await file.close()

    # 获取指定用户的会话列表
    async def get_conversations(self, user_id, last_id=None, limit=20) -> Tuple[bool, List[Dict[str, Any]]]:
        url = f"{self.base_url}/v1/conversations"
        params = {
            "user": user_id,
            "limit": limit
        }

        if last_id:
            params["last_id"] = last_id

        response = requests.get(url, headers=self.headers, params=params)
        result = _handle_response(response)
        if result.get('data'):
            return result.get('has_more', bool), result.get('data', [])
        return False, []

    async def update_conversation_name(self, conversation_id, name, user_id):
        """
        更新会话名称

        Args:
            agent_id: 代理ID
            conversation_id: 会话ID
            name: 新的会话名称
            user_id: 用户ID

        Returns:
            更新结果
        """
        url = f"{self.base_url}/v1/conversations/{conversation_id}/name"

        payload = {
            "name": name,
            "auto_generate": False,
            "user": user_id
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.headers, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        LogUtils.error(f"更新会话名称失败: {error_text}")
                        raise Exception(f"HTTP Error: {error_text}")

                    result = await response.json()
                    if result:
                        return result.get('name')
                    LogUtils.info(f'更新会话名称成功: {result}')
                    return ''
        except Exception as e:
            LogUtils.error(f"更新会话名称异常: {str(e)}")
            raise Exception(f"更新会话名称异常: {str(e)}")

    async def get_messages(self, user_id, conversation_id, first_id=None, limit=20) -> Tuple[
        bool, List[Dict[str, Any]]]:
        """
        获取会话中的消息列表

        Args:
            user_id: 用户ID
            conversation_id: 会话ID，为空则获取所有会话的消息

        Returns:
            元组(是否有更多消息, 消息列表)
        """
        url = f"{self.base_url}/v1/messages"
        params = {
            "user": user_id,
            "limit": limit
        }
        if conversation_id:
            params["conversation_id"] = conversation_id
        if first_id:
            params["first_id"] = first_id

        response = requests.get(url, headers=self.headers, params=params)
        result = _handle_response(response)
        if result.get('data'):
            return result.get('has_more', False), result.get('data', [])
        return False, []

    async def extract_document_features(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取文档特征

        Args:
            doc_data: 文档数据

        Returns:
            文档特征
        """
        try:
            # 这里应该调用Dify的文档特征提取API
            # 由于没有具体的API，这里只是一个示例
            url = f"{self.base_url}/v1/knowledge-bases/documents/extract-features"

            payload = {
                "document": doc_data
            }

            response = requests.post(url, headers=self.headers, json=payload)

            if response.status_code == 200:
                return response.json().get("data", {})
            else:
                LogUtils.error(f"提取文档特征失败: {response.text}")
                return {}
        except Exception as e:
            LogUtils.error(f"提取文档特征异常: {str(e)}")
            return {}

    async def generate_answer(self,
                              query: str,
                              context: List[Dict[str, Any]],
                              prompt_template: str,
                              system_prompt: str,
                              model_name: str,
                              temperature: str) -> Dict[str, Any]:
        """
        生成回答

        Args:
            query: 用户查询
            context: 上下文信息
            prompt_template: 提示词模板
            system_prompt: 系统提示词
            model_name: 模型名称
            temperature: 温度参数

        Returns:
            生成的回答
        """
        try:
            # 这里应该调用Dify的文本生成API
            # 由于没有具体的API，这里只是一个示例
            url = f"{self.base_url}/v1/chat-messages"

            payload = {
                "query": query,
                "inputs": {},
                "response_mode": "blocking",
                "conversation_id": None,
                "retriever_resources": {
                    "context": context
                }
            }

            response = requests.post(url, headers=self.headers, json=payload)

            if response.status_code == 200:
                data = response.json()
                return {
                    "text": data.get("answer", ""),
                    "sources": data.get("retriever_resources", {}).get("documents", []),
                    "metadata": data
                }
            else:
                LogUtils.error(f"生成回答失败: {response.text}")
                return {"text": "生成回答失败，请稍后重试"}
        except Exception as e:
            LogUtils.error(f"生成回答异常: {str(e)}")
            return {"text": f"生成回答异常: {str(e)}"}

    async def login(self, username: str, password: str) -> Optional[str]:
        """
        登录Dify

        Args:
            username: 用户名
            password: 密码

        Returns:
            API密钥
        """
        try:
            url = settings.DIFY_LOGIN_API_URL

            payload = {
                "email": username,
                "password": password
            }

            response = requests.post(url, json=payload)

            if response.status_code == 200:
                data = response.json()
                return data.get("data", {}).get("api_key")
            else:
                LogUtils.error(f"登录Dify失败: {response.text}")
                return None
        except Exception as e:
            LogUtils.error(f"登录Dify异常: {str(e)}")
            return None

    def get_dataset_by_id(self, dataset_id: str, page: int = 1, limit: int = 20, keyword: str = None) -> Tuple[
        bool, List[Dict[str, Any]]]:
        """
        获取知识库详情
        curl --location --request GET 'http://192.168.201.221/v1/datasets/{dataset_id}/documents' \
        --header 'Authorization: Bearer {api_key}'
        Args:
            dataset_id: 知识库ID

        Returns:
            知识库信息或None
        """
        url = f"{self.base_url}/v1/datasets/{dataset_id}/documents"
        params = {
            "page": page,
            "limit": limit
        }

        if keyword:
            params["keyword"] = keyword

        response = requests.get(url, headers=self.headers, params=params)
        result = _handle_response(response)
        if result.get('data'):
            # 从data中提取id和name字段
            formatted_data = [{
                'id': item.get('id', ''),
                'name': item.get('name', '')
            } for item in result.get('data', [])]
            return result.get('has_more', False), formatted_data
        return False, []

    async def generate_answer_stream(self,
                                     conversation_id: str,
                                     input: Dict[str, Any],
                                     session_id: str,
                                     dataset_context: str,
                                     query_dict: Dict[str, Any] = None) -> AsyncIterable[Dict[str, Any]]:
        """
        流式生成回答

        Args:
            conversation_id: 对话ID
            dataset_context: 知识库上下文信息
            input: chatflow 入参
            session_id: 该agent场景的会话ID
            query_dict: 完整的查询字典，包含file_ids等额外信息

        Yields:
            生成的回答块
        """
        try:
            # 准备API请求
            url = f"{self.base_url}/v1/chat-messages"
            message = query_dict.get("message", "")

            payload = {
                "query": message,
                "inputs": input,
                "response_mode": "streaming",
                "conversation_id": conversation_id,
                "user": session_id
            }

            # 处理file_ids，如果存在的话
            if query_dict and "file_ids" in query_dict and query_dict["file_ids"]:
                files = []
                for file_id in query_dict["file_ids"]:
                    files.append({
                        "type": "document",
                        "transfer_method": "local_file",
                        "upload_file_id": file_id
                    })
                payload['files'] = files

            # 执行流式请求
            async with httpx.AsyncClient() as client:
                async with client.stream("POST", url, headers=self.headers, json=payload) as response:
                    if response.status_code != 200:
                        yield {"error": f"Dify API调用失败: {response.status_code}"}
                        return

                    # 逐行处理响应
                    async for line in response.aiter_lines():
                        # yield  line
                        if line.startswith("data: "):
                            data = line[6:]  # 去除"data: "前缀
                            if data == "[DONE]":
                                break
                            try:
                                chunk_data = json.loads(data)
                                yield chunk_data
                                # event_type = chunk_data.get("event")
                                #
                                # if event_type == "message":
                                #     # 文本块
                                #     yield {"text_chunk": chunk_data.get("answer", "")}
                                # elif event_type == "citation":
                                #     # 引用数据
                                #     yield {"citation": chunk_data.get("citation")}
                                # else:
                                #     # 其他事件类型
                                #     yield chunk_data
                            except json.JSONDecodeError:
                                # 如果不是JSON，作为纯文本处理
                                yield {"text_chunk": data}

        except Exception as e:
            LogUtils.error(f"流式生成回答失败: {str(e)}")
            yield {"error": f"生成回答失败: {str(e)}"}

    # 创建空知识库
    def create_dataset(self,
                       payload: Dict):
        """
        创建知识库。

        :param name: 知识库名称
        :param permission: 权限设置（默认 'only_me'）
        :return: 响应数据
        """
        url = f"{self.base_url}/v1/datasets"

        response = requests.post(url, headers=self.headers, json=payload)
        result = _handle_response(response)
        LogUtils.info(f'创建知识库成功:{result}')
        return result.get('id', '')

    # 通过文件在已有知识库中创建文档
    async def create_document_by_file(self,
                                      dataset_id: str,
                                      document_data: Dict[str, Any],
                                      payload: Dict) -> Dict[str, Any]:
        url = f"{self.base_url}/v1/datasets/{dataset_id}/document/create-by-file"

        headers = {
            'Authorization': self.headers['Authorization']
        }

        # 读取文件内容
        file = document_data.get('file_object')
        file_content = await file.read()

        # 如果没有指定文件类型，尝试根据文件名推断

        files = {
            'file': (file.filename, file_content, file.content_type)
        }

        data = {}
        data['data'] = json.dumps(payload, ensure_ascii=False)

        try:
            response = requests.post(url, headers=headers, files=files, data=data)
            if response.status_code == 200:
                result = response.json()
                if result.get('document'):
                    return {
                        'id': result.get('document').get('id', ''),
                        'batch': result.get('batch', '')
                    }
                return {'batch': result.get('batch', ''), 'id': ''}

        except Exception as e:
            LogUtils.error(f"通过文件创建文档异常: {str(e)}")
            raise Exception(f"通过文件创建文档异常: {str(e)}")
        finally:
            # 确保文件指针回到开始位置，以便后续可能的操作
            await file.seek(0)
            # 确保文件被关闭
            await file.close()

    def create_document_by_text(self,
                                dataset_id,  # 知识库ID，必填
                                payload: Dict
                                ):
        url = f"{self.base_url}/v1/datasets/{dataset_id}/document/create-by-text"
        LogUtils.info(f'创建文档参数:{url}')

        LogUtils.info(f'创建文档参数:{payload}')
        response = requests.post(url, headers=self.headers, json=payload)
        result = _handle_response(response)
        LogUtils.info(f'创建文档成功:{result}')
        if result.get('document'):
            return {'id': result.get('document').get('id', ''), 'batch': result.get('batch', '')}
        return {'batch': result.get('batch', ''), 'id': ''}

    # 任务状态(创建文档)
    def get_indexing_status(self, dataset_id, batch):
        """
        获取文档索引状态。

        :param dataset_id: 知识库 ID
        :param batch: 批次号
        :return: 响应数据
        """
        url = f"{self.base_url}/v1/datasets/{dataset_id}/documents/{batch}/indexing-status"
        response = requests.get(url, headers=self.headers)
        result = _handle_response(response)
        LogUtils.info(f'获取索引状态结果:{result}')
        if result.get('data'):
            return result.get('data', [])[0]['indexing_status']
        return ''

    # 更新已有的分段内容(覆盖)
    async def update_segment(self,
                             dataset_id,  # 知识库ID 不能为空
                             document_id,  # 知识库文档ID 不能为空
                             segment_id,  # 片段ID 不能为空
                             payload: Dict,
                             ):
        LogUtils.info(f'-----更新分段参数dataset_id:{dataset_id},document_id:{document_id},segment_id:{segment_id}')
        url = f"{self.base_url}/v1/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=self.headers, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        LogUtils.error(f"更新分段失败: {error_text}")
                        raise Exception(f"HTTP Error: {error_text}")

                    result = await response.json()
                    if result.get('data'):
                        update_segment_id = result.get('data').get('id')
                        LogUtils.info(
                            f'-------更新分段参数返回dataset_id:{dataset_id},document_id:{document_id},segment_id:{update_segment_id}')
                        return update_segment_id
                    LogUtils.info(f'更新分段成功: {result}')
                    return ''
        except Exception as e:
            LogUtils.error(f"更新分段异常: {str(e)}")
            raise Exception(f"更新分段异常: {str(e)}")

    # 在已有知识库的已有文档中创建片段,返回结果中的id为,可以同时创建多个片段
    def create_segments(self,
                        dataset_id,
                        document_id,
                        payload: Dict,
                        ):
        LogUtils.info(f'-----新增分段参数dataset_id:{dataset_id},document_id:{document_id}')
        url = f"{self.base_url}/v1/datasets/{dataset_id}/documents/{document_id}/segments"
        response = requests.post(url, headers=self.headers, json=payload)
        result = _handle_response(response)
        LogUtils.info(f'新增片段成功:{result}')
        if result.get('data'):
            segment_id = result.get('data', [])[0]['id']
            LogUtils.info(
                f'------新增分段参数dataset_id:{dataset_id},document_id:{document_id},segment_id:{segment_id}')
            return segment_id

        return ''

    # 检索知识库
    def retrieve_dataset(self,
                         dataset_id,
                         payload: Dict,
                         ):
        url = f"{self.base_url}/v1/datasets/{dataset_id}/retrieve"
        response = requests.post(url, headers=self.headers, json=payload)
        result = _handle_response(response)
        if result.get('records'):
            records = result.get('records', [])
            if len(records) > 0:
                record = records[0]
                if record.get('segment'):
                    segment = record.get('segment')
                    if segment.get('content'):
                        content = segment.get('content', '')
                        return content
        return ''

    async def execute_workflow(self,
                               workflow_endpoint: str,
                               inputs: Dict[str, Any],
                               api_key: str,
                               user_id: str = "abc-123",
                               timeout_seconds: int = 180,
                               response_mode: str = "blocking") -> Dict[str, Any]:
        """
        执行Dify工作流调用并返回结果

        Args:
            workflow_endpoint: 工作流端点，如 '/v1/workflows/run'
            inputs: 输入参数，如 {'id': '123'}
            api_key: 特定流程的apikey
            user_id: 用户ID，默认为"abc-123"
            timeout_seconds: 超时时间（秒），默认180秒
            response_mode: 响应模式，默认为"blocking"（阻塞式）

        Returns:
            工作流执行结果
        """

        # 构建请求参数
        url = f"{self.base_url}{workflow_endpoint}"
        payload = {
            'inputs': inputs,
            'response_mode': response_mode,
            'user': user_id,
        }

        # 设置超时
        timeout = aiohttp.ClientTimeout(total=timeout_seconds)
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        try:
            LogUtils.info(f"开始执行Dify工作流: {url}, 参数: {payload}")

            # 发起异步HTTP请求
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                        url=url,
                        headers=headers,
                        json=payload,
                        timeout=timeout
                ) as response:
                    # 检查HTTP状态码
                    if response.status != 200:
                        error_text = await response.text()
                        LogUtils.error(f"Dify工作流执行失败, 状态码: {response.status}, 错误: {error_text}")
                        return {
                            "success": False,
                            "message": f"工作流执行失败，HTTP状态码: {response.status}",
                            "error": error_text
                        }

                    # 获取并解析响应数据
                    response_data = await response.json()

                    # 检查工作流执行状态
                    workflow_status = response_data.get('data', {}).get('status')
                    if workflow_status and workflow_status != 'succeeded':
                        LogUtils.warning(f"Dify工作流执行未成功完成，状态: {workflow_status}")
                        return {
                            "success": False,
                            "message": f"工作流执行未成功完成，状态: {workflow_status}",
                            "data": response_data
                        }

                    # 返回成功结果
                    LogUtils.info(f"Dify工作流执行成功: {url}")
                    return {
                        "success": True,
                        "data": response_data
                    }

        except asyncio.TimeoutError:
            LogUtils.error(f"Dify工作流执行超时: {url}")
            return {
                "success": False,
                "message": f"工作流执行超时（{timeout_seconds}秒）"
            }
        except Exception as e:
            LogUtils.error(f"Dify工作流执行异常: {url}, 错误: {str(e)}")
            return {
                "success": False,
                "message": f"工作流执行异常: {str(e)}"
            }

    async def chatflow(self,
                       chatflow_endpoint: str,
                       api_key: str,
                       message: str,
                       session_id: str = "abc-123",
                       file_ids: List[str] = None,
                       inputs: Dict[str, Any] = None,
                       timeout_seconds: int = 180) -> Dict[str, Any]:
        """
        非流式(blocking模式)调用Dify Chatflow并返回完整结果

        Args:
            chatflow_endpoint: Chatflow端点，如 '/v1/chatflow'
            api_key: 特定场景的API密钥
            message: 用户消息内容
            conversation_id: 对话ID，可选
            session_id: 会话ID，默认为"abc-123"
            file_ids: 文件ID列表，可选
            inputs: 额外的输入参数，可选
            timeout_seconds: 超时时间（秒），默认180秒

        Returns:
            完整的Chatflow调用结果
        """

        # 构建请求参数
        url = f"{self.base_url}{chatflow_endpoint}"

        payload = {
            "query": message,
            "inputs": inputs or {},
            "response_mode": "blocking",  # 使用blocking模式
            "user": session_id,
            "conversation_id": ""
        }

        # 处理文件ID（如果有）
        if file_ids and len(file_ids) > 0:
            files = []
            for file_id in file_ids:
                files.append({
                    "type": "document",
                    "transfer_method": "local_file",
                    "upload_file_id": file_id
                })
            payload["files"] = files

        # 设置请求头和超时
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        timeout = aiohttp.ClientTimeout(total=timeout_seconds)

        try:
            LogUtils.info(f"开始调用Dify Chatflow (blocking模式): {url}, 参数: {payload}")

            # 使用aiohttp执行请求
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                        url=url,
                        headers=headers,
                        json=payload,
                        timeout=timeout
                ) as response:
                    # 检查HTTP状态码
                    if response.status != 200:
                        error_text = await response.text()
                        LogUtils.error(f"Chatflow调用失败, 状态码: {response.status}, 错误: {error_text}")
                        return {
                            "success": False,
                            "message": f"Chatflow调用失败: HTTP {response.status}",
                            "error": error_text
                        }

                    # 获取并解析响应数据
                    response_data = await response.json()

                    # 返回成功结果
                    LogUtils.info(f"Dify Chatflow调用成功: {url}")
                    return {
                        "success": True,
                        "data": response_data
                    }

        except asyncio.TimeoutError:
            LogUtils.error(f"Chatflow调用超时: {url}")
            return {
                "success": False,
                "message": f"Chatflow调用超时（{timeout_seconds}秒）"
            }
        except Exception as e:
            LogUtils.error(f"Chatflow调用异常: {url}, 错误: {str(e)}")
            return {
                "success": False,
                "message": f"Chatflow调用异常: {str(e)}"
            }


import requests


def _handle_response(response):
    """
    处理响应，检查状态码。

    :param response: requests 响应对象
    :return: 响应 JSON 或错误信息
    """
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"HTTP Error: {response.text}")
