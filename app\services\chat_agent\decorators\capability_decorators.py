"""
智能体能力装饰器实现
"""
from typing import Dict, List, Any

from app.log.log_utils import LogUtils
from app.services.chat_agent.agents.base_agent import BaseAgentTemplate
from app.services.chat_agent.base import UserRoleType, AgentOperation, AgentType


class RoleBasedCapabilityDecorator:
    """基于角色的能力控制装饰器"""

    def __init__(self, agent: BaseAgentTemplate, user_context: Dict[str, Any]):
        self.agent = agent
        self.user_context = user_context
        self.role_capabilities = self._get_role_capabilities()
        self.user_role = user_context.get('role', UserRoleType.NORMAL_USER.name)

    def _get_role_capabilities(self) -> Dict[str, List[AgentOperation]]:
        """获取角色能力配置"""
        # 这里可以从配置文件或数据库加载
        return {
            UserRoleType.ADMIN.name: [AgentOperation.PROCESS_CHAT, AgentOperation.HANDLE_DOCUMENT_UPLOAD],
            UserRoleType.SUPER_ADMIN.name: [AgentOperation.PROCESS_CHAT, AgentOperation.HANDLE_DOCUMENT_UPLOAD],
            UserRoleType.NORMAL_USER.name: [AgentOperation.PROCESS_CHAT],
        }

    async def process_chat_request(self,
                                   user_input: Dict[str, Any],
                                   user_context: Dict[str, Any],
                                   conversation_id: str) -> Dict[str, Any]:
        """装饰器方法，在调用原方法前进行权限检查"""
        if AgentOperation.PROCESS_CHAT not in self.role_capabilities.get(self.user_role, []):
            LogUtils.warning(f"用户角色 {self.user_role} 无权执行聊天操作")
            return {
                "success": False,
                "content": "权限不足，无法执行聊天操作",
                "metadata": {}
            }

        return await self.agent.process_chat_request(user_input, user_context, conversation_id)

    async def handle_document_upload(self,
                                     document_data: Dict[str, Any],
                                     user_context: Dict[str, Any],
                                     segment_id: str = None
                                     ) -> Dict[str, Any]:
        # 2. 确定Agent类型
        agent_type = self.agent.agent_config.get("type", "")

        # 3. 针对不同Agent类型检查场景特定权限
        if agent_type in [AgentType.LEGAL_CODE.name,
                          AgentType.DETENTION_CENTRE.name,
                          AgentType.GENERAL.name]:

            """装饰器方法，在调用原方法前进行权限检查"""
            if AgentOperation.HANDLE_DOCUMENT_UPLOAD not in self.role_capabilities.get(self.user_role, []):
                LogUtils.warning(f"用户角色 {self.user_role} 无权执行文档上传操作")
                return {
                    "success": False,
                    "message": "权限不足，无法执行文档上传操作",
                    "data": {}
                }

        return await self.agent.handle_document_upload(document_data, user_context, segment_id)

    async def list_datasets(self, user_context: Dict[str, Any], page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """委托方法"""
        return await self.agent.list_datasets(user_context, page, limit)

    async def start_new_session(self, user_id: int) -> str:
        """委托方法"""
        return await self.agent.start_new_session(user_id)

    async def get_chat_history(self, conversation_id: str, user_id: int, first_id: str = None, limit: int = 20):
        """委托方法"""
        return await self.agent.get_chat_history(conversation_id, user_id, first_id, limit)

    async def user_chat_session(self, user_id: int) -> List[Dict[str, Any]]:
        """委托方法"""
        return await self.agent.user_chat_session(user_id)

    async def list_user_chat_conversations(self, session_id, limit, last_id: str = None):
        """委托方法"""
        return await self.agent.list_user_chat_conversations(session_id, limit, last_id)

    async def handle_file_upload(self, document_data: Dict[str, Any], session_id: str):
        """委托方法"""
        return await self.agent.handle_file_upload(document_data, session_id)
