import request from '@/utils/request'
import { mockGetRecordList } from './mock/recordMock'

// 获取笔录列表
export function getRecordList(data) {
  // 使用模拟数据代替真实API请求
  // return mockGetRecordList(data);
  
  // 原始API调用代码，注释掉
  return request({
    url: '/api/transcript/search',
    method: 'post',
    params: data
  })
}

// 上传笔录PDF文件
export function uploadTranscriptPDF(data, config = {}) {
  return request({
    url: '/api/upload_transcript_pdf',
    method: 'post',
    data,
    ...config
  })
}

// 同步笔录数据
export function syncRecordData(params) {
  return request({
    url: '/api/scene_4/sync',
    method: 'post',
    params
  })
}

// 批量分析笔录
export function batchAnalyzeRecords(recordIds) {
  return request({
    url: '/api/scene_4/analyze',
    method: 'post',
    data: {
      ids: recordIds
    }
  })
} 