from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

SituationAnalysisBase = declarative_base()

class SituationAnalysisRecord(SituationAnalysisBase):
    """态势分析记录数据模型

    用于存储态势分析相关的报告信息
    """
    __tablename__ = 'situation_analysis'

    # User ID
    user_id = Column(Integer, index=True)
    # 记录ID，主键
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    # 报告名称
    report_name = Column(String(255), nullable=False, index=True, comment="报告名称")
    # 统计开始时间
    start_time = Column(DateTime, nullable=False, index=True, comment="统计开始时间")
    # 统计结束时间
    end_time = Column(DateTime, nullable=False, index=True, comment="统计结束时间")
    # 创建报告时间
    created_at = Column(DateTime, nullable=False, server_default=func.now(), index=True, comment="创建时间")
    # 最后修改时间
    updated_at = Column(
        DateTime,
        nullable=True,
        server_default=func.now(),
        onupdate=func.now(),
        index=True,
        comment="最后修改时间"
    )
    # 报告MD内容
    md_content = Column(Text, nullable=True, comment="Markdown格式的报告内容")
    # 报告生成状态（0:进行中 1:已完成）
    status = Column(
        Integer,
        default=0,
        nullable=False,
        index=True,
        comment="报告生成状态"
    )

    # 状态映射字典（与PoliceRecord的ANALYSIS_STATUS_MAPPING格式一致）
    STATUS_MAPPING = {
        0: '进行中',
        1: '已完成'
    }

    def __repr__(self):
        return f"<SituationAnalysisRecord {self.id} {self.report_name}>"
