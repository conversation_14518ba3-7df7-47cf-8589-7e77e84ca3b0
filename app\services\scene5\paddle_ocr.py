import asyncio
from urllib.parse import quote

import aiohttp
from paddleocr import PaddleOCR

from app.log.log_utils import LogUtils
from app.services.scene5.device_manager import DeviceManager

# 设置PaddleOCR并行实例数量
OCR_WORKER_COUNT = 1  # 使用CPU核心数-1作为实例数
OCR_BATCH_SIZE = 10  # 每个OCR实例处理的批次大小


# OCR实例池
class OcrPool:
    def __init__(self, workers: int = 1):
        self.workers = workers
        self._pool = None
        self._ocr_instances = []
        self.device_type, self.device_count = DeviceManager.detect_available_device()
        self._initialize()

    def _initialize(self):
        # 初始化OCR实例
        for i in range(self.workers):
            # 获取设备ID（循环使用可用设备）
            device_id = i % self.device_count
            # 获取OCR配置
            ocr_config = DeviceManager.get_ocr_config(self.device_type, device_id)

            try:
                self._ocr_instances.append(PaddleOCR(**ocr_config))
                LogUtils.info(f"初始化OCR实例 {i + 1}/{self.workers} 在 {self.device_type.upper()} {device_id}")
            except Exception as e:
                LogUtils.error(f"OCR实例初始化失败: {str(e)}")
                raise

        LogUtils.info(f"OCR实例池初始化完成，实例数: {self.workers}，设备类型: {self.device_type.upper()}")

    """
    获取ocr实例
    """

    def get_instance(self):
        if len(self._ocr_instances) == 0:
            return None
        return self._ocr_instances.pop()

    """
    归还ocr实例
    """

    def return_instance(self, ocr_instance: PaddleOCR):
        if not ocr_instance:
            return
        self._ocr_instances.append(ocr_instance)

    def process_image(self, image_bytes):
        ocr_instance = None
        try:
            ocr_instance = self.get_instance()
            if ocr_instance is None:
                LogUtils.error('OCR实例获取失败，请检查实例池状态')
                return None
            return ocr_instance.ocr(image_bytes)
        except Exception as e:
            LogUtils.error(f"OCR处理失败: {str(e)}")
            return None
        finally:
            self.return_instance(ocr_instance)


class PaddleOcrService:
    async def orc_image(self, url: str) -> str:
        """根据URL进行OCR识别"""
        try:
            # 对URL进行编码处理
            url = quote(url, safe=':/?&=')

            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    response.raise_for_status()
                    # 直接读取字节流
                    image_bytes = await response.read()
                    # 处理内存中的字节流，使用OCR池
                    return self._process_image_bytes(image_bytes)

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            LogUtils.error(f"文件下载失败: {str(e)} URL: {url}")
            return ""
        except Exception as e:
            LogUtils.error(f"OCR处理异常: {str(e)}")
            return ""

    def _process_image_bytes(self, image_bytes: bytes) -> str:
        """处理内存中的图像字节流OCR，从实例池获取OCR实例"""
        try:
            # 从OCR池获取实例并处理
            result = ocr_pool.process_image(image_bytes)
            # 处理识别结果
            content = []
            current_row = []
            current_y = None

            for line in result:
                for detection in line:
                    box = detection[0]
                    text = detection[1][0]
                    y_coord = (box[0][1] + box[2][1]) / 2

                    if current_y is None or abs(y_coord - current_y) > 10:
                        if current_row:
                            content.append("***".join(current_row))
                        current_row = [text]
                        current_y = y_coord
                    else:
                        current_row.append(text)

            if current_row:
                content.append("***".join(current_row))

            LogUtils.info("ocr处理结束")

            return "\n".join(content)
        except Exception as e:
            LogUtils.error(f"OCR池处理异常: {str(e)}")
            return ""


# 创建OCR实例池
ocr_pool = OcrPool(OCR_WORKER_COUNT)
paddle_ocr_service = PaddleOcrService()
