from typing import List, Optional

from pydantic import BaseModel, Field
from sqlalchemy import false


class UserCreateRequest(BaseModel):
    username: str = Field(..., min_length=2, max_length=50, description="用户名")
    password: Optional[str] = Field(..., min_length=6, max_length=100, description="密码")
    id_card_number: str = Field(..., min_length=18, max_length=18, description="身份证号（必填且唯一，长度18）")
    police_number: str = Field(...,  min_length=2, max_length=20, description="警号（必填）")
    police_unit: Optional[str] = Field(None, min_length=2, max_length=128, description="所在单位部门（具体到部门）")
    org_code:str = Field(...,  description="机构编号")
    phone_number: str = Field(..., min_length=2, max_length=11, description="联系电话（必填，长度11）")

class UserLoginRequest(BaseModel):
    police_number: str = Field(..., min_length=2, max_length=20, description="警号")
    password: str = Field(..., min_length=6, max_length=100, description="密码")

class UserChangeRequest(BaseModel):
    police_number: str = Field(..., min_length=2, max_length=20, description="警号")
    password: str = Field(..., min_length=6, max_length=100, description="密码")

class TokenResponse(BaseModel):
    access_token: str
    token_type: str


class UserBackendCreateRequest(UserCreateRequest):
    org_code: str = Field(..., description="主部门编码")
    org_codes: List[str] = Field(default_factory=list, description="关联部门ID列表")
    role_ids: List[int] = Field(default_factory=list, description="角色ID列表")
    is_active: bool = Field(default=false, description="是否激活账号")

class UserBackendUpdateRequest(BaseModel):
    org_code: str = Field(..., description="主部门编码")
    org_codes: List[str] = Field(default_factory=list, description="关联部门ID列表")
    role_ids: List[int] = Field(default_factory=list, description="角色ID列表")
    is_active: bool = Field(default=false, description="是否激活账号")
    id:int = Field(..., description="用户id")
    username: str = Field(..., min_length=2, max_length=50, description="用户名")
    id_card_number: str = Field(..., min_length=18, max_length=18, description="身份证号（必填且唯一，长度18）")
    police_number: str = Field(..., min_length=2, max_length=20, description="警号（必填）")
    phone_number: str = Field(..., min_length=2, max_length=11, description="联系电话（必填，长度11）")


class UserBackendQueryRequest(BaseModel):
    page: Optional[int] = 1
    page_size: Optional[int] = 10
    username: Optional[str] = None
    police_number: Optional[str] = None
    phone_number: Optional[str] = None
    id_card_number: Optional[str] = None
    org_codes: Optional[List[str] ]=  None


class UserBackendResetPwdRequest(BaseModel):
    userId:Optional[int] = None ## 用户id
    password:Optional[str] = None ## 新密码
    password2:Optional[str] = None ## 再次确认密码


