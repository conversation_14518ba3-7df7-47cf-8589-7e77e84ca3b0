<template>
  <div class="chat-history" v-show="currentConversation" @scroll="handleScroll">
    <!-- <div v-if="!currentConversation.messages || currentConversation.messages.length === 0" class="empty">暂无消息</div> -->
    <div v-if="loadingMore" class="loading-more">
      <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
      加载更多...
    </div>
    <div class="messages-container">
      <transition-group name="fade">
        <div v-for="msg in messageList" :key="msg.id" class="msg-item" :class="msg.role">
          <div class="msg-content">
            <template v-if="msg.role === 'user'">
              <div
                v-if="msg.message_files && msg.message_files.length > 0"
                class="file-box-wrapper"
              >
                <div
                  class="scroll-button left"
                  v-show="getScrollState(msg.id).canScrollLeft"
                  @click="scrollLeft(msg.id)"
                >
                  <i class="el-icon-arrow-left"></i>
                </div>
                <div
                  class="file-box"
                  :ref="'fileBox_' + msg.id"
                  @scroll="handleFileBoxScroll($event, msg.id)"
                >
                  <template v-for="file in msg.message_files">
                    <div class="file-item" v-if="file.belongs_to == 'user'" :key="file.id">
                      <i class="el-icon-document"></i>
                      <span>{{ file.filename }}</span>
                    </div>
                  </template>
                </div>
                <div
                  class="scroll-button right"
                  v-show="getScrollState(msg.id).canScrollRight"
                  @click="scrollRight(msg.id)"
                >
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
              {{ msg.content }}
              <div class="msg-time">{{ formatTime(msg.created_at) }}</div>
            </template>
            <div v-else style="display: flex">
              <img
                src="@/assets/images/gongan.png"
                alt="avatar"
                style="width: 32px; height: 32px"
              />
              <div
                style="
                  margin-left: 10px;
                  padding: 10px 30px;
                  border-radius: 8px;
                  background: #0f2336;
                "
              >
                <div v-html="renderMarkdown(msg.content)"></div>
                <div class="msg-time">{{ formatTime(msg.created_at) }}</div>
              </div>
            </div>
          </div>
        </div>
      </transition-group>
    </div>
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><i class="el-icon-loading"></i></el-icon>
      正在思考...
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import MarkdownIt from "markdown-it";
import "github-markdown-css";

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
});

export default {
  name: "ChatHistory",
  props: {
    namespace: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loadingMore: false,
      isFirstLoad: true,
      scrollStates: {},
    };
  },
  computed: {
    currentConversation() {
      return this.$store.getters[`${this.namespace}/currentConversation`];
    },
    currentConversationId() {
      return this.$store.getters[`${this.namespace}/currentConversationId`];
    },
    loading() {
      return this.$store.getters[`${this.namespace}/loading`];
    },
    historyHasMore() {
      return this.$store.getters[`${this.namespace}/historyHasMore`];
    },
    messageList() {
      return this.currentConversation?.messages || [];
    },
  },
  watch: {
    "currentConversation.messages": {
      handler(newMessages) {
        console.log("newMessages", newMessages);
        if (newMessages?.length) {
          this.$nextTick(() => {
            if (this.isFirstLoad) {
              this.scrollToBottom();
              this.isFirstLoad = false;
            }
            this.initializeFileBoxScroll();
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    async handleScroll(e) {
      console.log("handleScroll", e);
      const container = e.target;
      // 当滚动到顶部时加载更多
      if (container.scrollTop === 0 && !this.loadingMore && this.historyHasMore) {
        this.loadingMore = true;
        try {
          const firstMessageId = this.messageList[0]?.id;
          if (!firstMessageId) return;

          // 使用 Vuex action 加载历史消息，使用追加模式
          await this.$store.dispatch(`${this.namespace}/fetchConversationHistory`, {
            conversation_id: this.currentConversationId,
            first_id: firstMessageId.split("_")[0],
            append: true,
          });

          // 更新滚动位置，保持在原位置
          this.$nextTick(() => {
            // const newHeight = container.scrollHeight;
            container.scrollTop = 0;
          });
        } catch (error) {
          console.error("加载更多消息失败:", error);
        } finally {
          this.loadingMore = false;
        }
      }
    },
    renderMarkdown(content) {
      if (!content) return "";
      let res = content.replace(/#f8f8f8/g, "#071827");
      res = res.replace(/padding: 8px/g, "padding: 10px 30px");
      return md.render(res);
    },
    formatTime(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    scrollToBottom() {
      const container = document.querySelector(".chat-history");
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    getScrollState(msgId) {
      if (!this.scrollStates[msgId]) {
        const fileBox = this.$refs[`fileBox_${msgId}`]?.[0];
        if (fileBox) {
          const hasOverflow = fileBox.scrollWidth > fileBox.clientWidth;
          this.$set(this.scrollStates, msgId, {
            scrollLeft: fileBox.scrollLeft || 0,
            canScrollLeft: false,
            canScrollRight: hasOverflow,
          });
        } else {
          return { canScrollLeft: false, canScrollRight: false };
        }
      }
      return this.scrollStates[msgId];
    },
    scrollLeft(msgId) {
      const fileBox = this.$refs[`fileBox_${msgId}`]?.[0];
      if (!fileBox) return;
      fileBox.scrollLeft = Math.max(0, fileBox.scrollLeft - 200);
    },
    scrollRight(msgId) {
      const fileBox = this.$refs[`fileBox_${msgId}`]?.[0];
      if (!fileBox) return;
      const maxScroll = fileBox.scrollWidth - fileBox.clientWidth;
      fileBox.scrollLeft = Math.min(maxScroll, fileBox.scrollLeft + 200);
    },
    handleFileBoxScroll(event, msgId) {
      const fileBox = event.target;
      if (!fileBox) return;

      const scrollLeft = fileBox.scrollLeft;
      const hasOverflow = fileBox.scrollWidth > fileBox.clientWidth;
      const canScrollLeft = scrollLeft > 0;
      const canScrollRight =
        hasOverflow && scrollLeft < fileBox.scrollWidth - fileBox.clientWidth - 1;

      this.$set(this.scrollStates, msgId, {
        scrollLeft,
        canScrollLeft,
        canScrollRight,
      });
    },
    initializeFileBoxScroll() {
      this.$nextTick(() => {
        Object.keys(this.$refs).forEach(key => {
          if (key.startsWith("fileBox_")) {
            const msgId = key.split("_")[1];
            const fileBox = this.$refs[key]?.[0];
            if (fileBox) {
              this.handleFileBoxScroll({ target: fileBox }, msgId);
            }
          }
        });
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.scrollToBottom();

      // 监听文件盒子的滚动事件
      this.initializeFileBoxScroll();
    });
  },
  beforeDestroy() {
    // 清理事件监听
    const fileBoxes = document.querySelectorAll(".file-box");
    fileBoxes.forEach(fileBox => {
      fileBox.removeEventListener("scroll", () => this.checkScroll(fileBox));
    });
  },
};
</script>

<style scoped lang="less">
.chat-history {
  flex: 1;
  padding: 20px 20px 0 20px;
  // height: 100%;
  // height: calc(100vh - 400px);
  overflow-y: auto;
  // background: white;
  display: flex;
  flex-direction: column;
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 24, 48, 0.95);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 192, 255, 0.3) 100%);
    border-radius: 4px;
    border: 2px solid rgba(0, 24, 48, 0.95);
    transition: all 0.3s;

    &:hover {
      background: linear-gradient(180deg, rgba(0, 255, 255, 0.5) 0%, rgba(0, 192, 255, 0.5) 100%);
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.3) rgba(0, 24, 48, 0.95);
  .file-box-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    margin: 10px 0;
    padding: 0;
  }

  .scroll-button {
    position: absolute;
    width: 24px;
    height: 24px;
    background: rgba(0, 48, 102, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    transition: all 0.3s ease;
    top: 50%;
    transform: translateY(-50%);

    i {
      color: #00ffff;
      font-size: 16px;
    }

    &:hover {
      background: rgba(0, 48, 102, 1);
    }

    &.left {
      left: 0;
    }

    &.right {
      right: 0;
    }
  }

  .file-box {
    margin: 0;
    padding: 10px 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    overflow-x: auto;
    scroll-behavior: smooth;
    position: relative;
    width: 100%;
    -webkit-overflow-scrolling: touch;

    /* 隐藏滚动条但保持功能 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .file-item {
      display: inline-flex;
      align-items: center;
      padding: 8px;
      background: rgba(0, 48, 102, 0.3);
      border: 1px solid rgba(0, 255, 255, 0.2);
      border-radius: 4px;
      transition: all 0.3s ease;
      // max-width: 200px;
      height: 50px;
      flex-shrink: 0;

      &:hover {
        background: rgba(0, 48, 102, 0.4);
        border-color: rgba(0, 255, 255, 0.3);
      }

      .el-icon-document {
        font-size: 16px;
        color: #00ffff;
        margin-right: 8px;
      }

      .file-name {
        flex: 1;
        color: #ffffff;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 120px;
      }

      .delete-icon {
        font-size: 16px;
        color: #ff4d4f;
        cursor: pointer;
        padding: 4px;
        margin-left: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 77, 79, 0.1);
          transform: scale(1.1);
        }
      }
    }
  }
}

.messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.msg-item {
  margin-bottom: 20px;
  max-width: 80%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.msg-item.user {
  margin-left: auto;
  align-items: flex-end;
}

.msg-item.assistant {
  margin-right: auto;
  align-items: flex-start;
}

.msg-content {
  padding: 12px 16px;
  border-radius: 8px;
  //  background: white;
  /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
  word-break: break-word;

  color: rgba(255, 255, 255, 0.7);
}

.msg-item.user .msg-content {
  background: #0f2336;
  color: rgba(255, 255, 255, 0.7);
}

.msg-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.empty {
  text-align: center;
  color: #909399;
  margin-top: 40px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading {
  text-align: center;
  color: #909399;
  margin-top: 20px;
  padding: 10px;
}

.loading-more {
  text-align: center;
  color: #909399;
  padding: 10px;
  font-size: 14px;
}

/* 优化过渡动画 */
.fade-enter-active {
  transition: all 0.3s ease;
}

.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

:deep(pre) {
  white-space: pre-wrap;
  word-wrap: break-word;
}

:deep(.markdown-body) {
  background: transparent;
  font-size: 14px;
}

:deep(.markdown-body pre) {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
}

:deep(.markdown-body code) {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 4px;
}
</style>
