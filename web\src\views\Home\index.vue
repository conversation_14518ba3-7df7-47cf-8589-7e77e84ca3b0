<template>
  <div class="home-container">
    <!-- 粒子效果容器 -->
    <div class="particle-container">
      <canvas class="rain"></canvas>
      <canvas class="dashed"></canvas>
    </div>
    <div class="q-header">
        <!-- <div class="q-bg-left"></div> -->
         <img src="@/assets/images/header-bg.png" class="q-bg-left">
        <div class="q-bg-container">
            <img src="@/assets/images/maintitle.png"/>
            <p class="q-sub-title">· DeepSeek 智能辅助平台 ·</p>
        </div>
        <!-- <div class="q-bg-right"></div> -->
        <img src="@/assets/images/header-bg.png" class="q-bg-right">
      </div>
      
    <!-- 主内容区域 -->
    <div class="content-area">
      <!-- 顶部标题 -->
      <!-- <div class="header">
        <div class="title-container">
          <h1 class="main-title">法制监督智能助手</h1>
          <p class="sub-title">· DeepSeek 智能辅助平台 ·</p>
        </div>
      </div> -->
      
      <!-- 功能卡片网格 -->
      <div class="card-grid">
        <!-- 第一行卡片 -->
        <div class="card" @click="navigateTo('/case-management')">
          <div class="card-content">
            <div class="card-icon criminal-icon"></div>
            <div class="card-label">
              <div class="label-text">案件类警情流失巡查</div>
              <div class="label-subtext">CASE LOSS PATROL</div>
            </div>
          </div>
        </div>
        
        <div class="card" @click="navigateTo('/record-check')">
          <div class="card-content">
            <div class="card-icon record-icon"></div>
            <div class="card-label">
              <div class="label-text">笔录巡查</div>
              <div class="label-subtext">RECORD INQUIRY</div>
            </div>
          </div>
        </div>
        
        <div class="card" @click="navigateTo('/situation-analysis')">
          <div class="card-content">
            <div class="card-icon analysis-icon"></div>
            <div class="card-label">
              <div class="label-text">态势分析</div>
              <div class="label-subtext">SITUATION ANALYSIS</div>
            </div>
          </div>
        </div>
        
        <div class="card" @click="navigateTo('/stand-firm-without-spying')">
          <div class="card-content">
            <div class="card-icon investigation-icon"></div>
            <div class="card-label">
              <div class="label-text">刑事案件立而不侦巡查</div>
              <div class="label-subtext">CRIMINAL CASES INSPECTION</div>
            </div>
          </div>
        </div>
        
        <!-- 第二行卡片 -->
        <div class="card" @click="navigateTo('/legal-record-check')">
          <div class="card-content">
            <div class="card-icon legal-icon"></div>
            <div class="card-label">
              <div class="label-text">法律文书巡查</div>
              <div class="label-subtext">LEGAL DOCUMENT INSPECTION</div>
            </div>
          </div>
        </div>
        
        <div class="card" @click="navigateTo('/file-review/index')">
          <div class="card-content">
            <div class="card-icon file-icon"></div>
            <div class="card-label">
              <div class="label-text">卷宗审核</div>
              <div class="label-subtext">FILE REVIEW</div>
            </div>
          </div>
        </div>
        
        <div class="card" @click="navigateTo('/amount-warning/index')">
          <div class="card-content">
            <div class="card-icon admin-icon"></div>
            <div class="card-label">
              <div class="label-text">涉案财物预警</div>
              <div class="label-subtext">PROPERTY INVOLVED IN THE CASE</div>
            </div>
          </div>
        </div>
        
        <div class="card" @click="navigateTo('/chat/index')">
          <div class="card-content">
            <div class="card-icon more-icon"></div>
            <div class="card-label">
              <div class="label-text">AI智能助手</div>
              <div class="label-subtext">AI ASSISTANT</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧统计区域 -->
      <div class="stats-sidebar">
        <!-- 人数统计 -->
        <div class="stats-box user-stats" v-if="userRole === 'ADMIN'">
          <div class="stats-box-title">人数统计</div>
          <div class="stats-box-content">
            <div class="user-count-item">
              <div class="user-count-number">{{ online_users }}</div>
              <div class="user-count-label">在线人数</div>
            </div>
            <div class="user-count-item">
              <div class="user-count-number">{{ registered_users }}</div>
              <div class="user-count-label">注册人数</div>
            </div>
          </div>
        </div>
        
        <!-- DeepSeek警情信息分析 -->
        <div class="stats-box deepseek-stats">
          <div class="stats-box-title">DeepSeek 警情信息分析</div>
          <div class="stats-box-content">
            <!-- 红色指标 -->
            <div class="stats-progress-item red" @click="navigateWithFilter('red')">
              <div class="progress-circle">
                <img src="@/assets/images/circle-red.png" class="circle-bg-image" alt="Red Circle" />
                <svg viewBox="0 0 36 36" style="background: transparent; overflow: visible;">
                  <!-- 红色渐变定义 -->
                  <defs style="background: transparent;">
                    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stop-color="#ff2020" />
                      <stop offset="100%" stop-color="#ff8080" />
                    </linearGradient>
                  </defs>
                  <!-- 背景圆环 -->
                  <circle cx="18" cy="18" r="16" fill="none" stroke="rgba(255, 255, 255, 0.08)" stroke-width="2.5"></circle>
                  
                  <!-- 进度圆环 - 使用渐变作为描边颜色 -->
                  <circle 
                    cx="18" 
                    cy="18" 
                    r="16" 
                    fill="none" 
                    stroke="url(#redGradient)" 
                    stroke-width="2.5" 
                    stroke-linecap="round" 
                    stroke-dasharray="50.24, 100.48" 
                    :stroke-dasharray="`${red_ratio * 1.0048}, 100.48`" 
                    transform="rotate(-90 18 18)"
                    class="circle-fill red-fill"
                  />
                </svg>
                <div class="progress-percentage">{{ red_ratio }}%</div>
              </div>
              <div class="progress-info">
                <div class="progress-left">
                  <div class="progress-label">标红数量</div>
                  <div class="progress-sublabel">Number highlighted in red</div>
                </div>
                <div class="progress-value">{{ red_count }}</div>
              </div>
            </div>
            
            <!-- 黄色指标 -->
            <div class="stats-progress-item yellow" @click="navigateWithFilter('yellow')">
              <div class="progress-circle">
                <img src="@/assets/images/circle-yellow.png" class="circle-bg-image" alt="Yellow Circle" />
                <svg viewBox="0 0 36 36" style="background: transparent; overflow: visible;">
                  <!-- 黄色渐变定义 -->
                  <defs style="background: transparent;">
                    <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stop-color="#ffb800" />
                      <stop offset="100%" stop-color="#ffdf80" />
                    </linearGradient>
                  </defs>
                  <!-- 背景圆环 -->
                  <circle cx="18" cy="18" r="16" fill="none" stroke="rgba(255, 255, 255, 0.08)" stroke-width="2.5"></circle>
                  
                  <!-- 进度圆环 - 使用渐变作为描边颜色 -->
                  <circle 
                    cx="18" 
                    cy="18" 
                    r="16" 
                    fill="none" 
                    stroke="url(#yellowGradient)" 
                    stroke-width="2.5" 
                    stroke-linecap="round" 
                    stroke-dasharray="50.24, 100.48" 
                    :stroke-dasharray="`${yellow_ratio * 1.0048}, 100.48`" 
                    transform="rotate(-90 18 18)"
                    class="circle-fill yellow-fill"
                  />
                </svg>
                <div class="progress-percentage">{{ yellow_ratio }}%</div>
              </div>
              <div class="progress-info">
                <div class="progress-left">
                  <div class="progress-label">标黄数量</div>
                  <div class="progress-sublabel">Number highlighted in yellow</div>
                </div>
                <div class="progress-value">{{ yellow_count }}</div>
              </div>
            </div>
            
            <!-- 蓝色指标 -->
            <div class="stats-progress-item blue" @click="navigateWithFilter('blue')">
              <div class="progress-circle">
                <img src="@/assets/images/circle-blue.png" class="circle-bg-image" alt="Blue Circle" />
                <svg viewBox="0 0 36 36" style="background: transparent; overflow: visible;">
                  <!-- 蓝色渐变定义 -->
                  <defs style="background: transparent;">
                    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stop-color="#00b8ff" />
                      <stop offset="100%" stop-color="#80dfff" />
                    </linearGradient>
                  </defs>
                  <!-- 背景圆环 -->
                  <circle cx="18" cy="18" r="16" fill="none" stroke="rgba(255, 255, 255, 0.08)" stroke-width="2.5"></circle>
                  
                  <!-- 进度圆环 - 使用渐变作为描边颜色 -->
                  <circle 
                    cx="18" 
                    cy="18" 
                    r="16" 
                    fill="none" 
                    stroke="url(#blueGradient)" 
                    stroke-width="2.5" 
                    stroke-linecap="round" 
                    stroke-dasharray="50.24, 100.48" 
                    :stroke-dasharray="`${blue_ratio * 1.0048}, 100.48`" 
                    transform="rotate(-90 18 18)"
                    class="circle-fill blue-fill"
                  />
                </svg>
                <div class="progress-percentage">{{ blue_ratio }}%</div>
              </div>
              <div class="progress-info">
                <div class="progress-left">
                  <div class="progress-label">标蓝数量</div>
                  <div class="progress-sublabel">Number highlighted in blue</div>
                </div>
                <div class="progress-value">{{ blue_count }}</div>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </div>
</template>

<script>
import { getUserStats, getAnalysisLevelStats } from '@/api/statistics'
import { CountUp } from 'countup.js'

export default {
  name: 'Home',
  data() {
    return {
      loading: false,
      total_count: 0,
      analyzed_count: 0,
      red_count: 100,   // 默认值与截图匹配
      yellow_count: 100, // 默认值与截图匹配
      blue_count: 100,   // 默认值与截图匹配
      red_ratio: 50,     // 默认值与截图匹配
      yellow_ratio: 50,  // 默认值与截图匹配
      blue_ratio: 50,    // 默认值与截图匹配
      countInstances: {},
      refreshTimer: null,
      rainCtx: null,
      dashedCtx: null,
      animationFrame: null,
      online_users: 1,   // 默认值与截图匹配
      registered_users: 7, // 默认值与截图匹配
      userRole: '',
      circleAnimating: false,
      targetRedRatio: 50,
      targetYellowRatio: 50,
      targetBlueRatio: 50
    }
  },
  created() {
    // 获取用户角色
    const userInfo = this.$store.state.user
    this.userRole = userInfo?.role || ''

    // 如果是管理员，获取用户统计信息
    if (this.userRole === 'ADMIN') {
      this.fetchUserStats()
    }

    this.fetchAnalysisLevelStats()
    this.startRefreshTimer()
  },
  mounted() {
    this.initParticleEffect()
    
    // 页面加载后延迟一点启动进度环动画，给用户更好的视觉体验
    setTimeout(() => {
      this.animateCircles()
    }, 500)
  },
  beforeDestroy() {
    this.stopRefreshTimer()
    this.stopParticleEffect()
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path)
    },
    navigateWithFilter(level) {
      this.$router.push({
        path: '/case-management',
        query: { analysis_level: level }
      })
    },
    showComingSoon() {
      this.$notify({
        title: '功能开发中',
        message: '该功能正在开发中，敬请期待',
        type: 'warning',
        position: 'top-right',
        duration: 3000,
        offset: 50
      })
    },
    startRefreshTimer() {
      this.refreshTimer = setInterval(() => {
        this.fetchAnalysisLevelStats()
      }, 60000)
    },
    stopRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    async fetchAnalysisLevelStats() {
      try {
        const res = await getAnalysisLevelStats()
        if (res.code === 200 && res.data) {
          const redCount = res.data.red_count || 0
          const yellowCount = res.data.yellow_count || 0
          const blueCount = res.data.blue_count || 0
          
          this.targetRedRatio = parseFloat(res.data.red_ratio) || 0
          this.targetYellowRatio = parseFloat(res.data.yellow_ratio) || 0
          this.targetBlueRatio = parseFloat(res.data.blue_ratio) || 0
          
          // 动画更新环形进度条
          this.animateCircles()
          
          // 更新数字计数
          this.animateCounters(redCount, yellowCount, blueCount)
        }
        
        // 模拟数据更新，仅用于演示
        // this.targetRedRatio = Math.floor(Math.random() * 70) + 30
        // this.targetYellowRatio = Math.floor(Math.random() * 70) + 30
        // this.targetBlueRatio = Math.floor(Math.random() * 70) + 30
        
        // const redCount = Math.floor(Math.random() * 100) + 100
        // const yellowCount = Math.floor(Math.random() * 100) + 100
        // const blueCount = Math.floor(Math.random() * 100) + 100
        
        // // 动画更新环形进度条
        // this.animateCircles()
        
        // // 更新数字计数
        // this.animateCounters(redCount, yellowCount, blueCount)
        
      } catch (error) {
        console.error('获取分析等级统计数据失败:', error)
        // this.$message.error('获取分析等级统计数据失败')
      }
    },
    async fetchUserStats() {
      try {
        const res = await getUserStats()
        if (res.code === 200 && res.data) {
          this.online_users = res.data.online_users || 0
          this.registered_users = res.data.total_users || 0
        }
      } catch (error) {
        console.error('获取用户统计信息失败:', error)
        // this.$message.error('获取用户统计信息失败')
      }
    },
    animateElement(element, endValue, decimals = 0) {
      if (!element) return
      
      const id = element.id || Math.random().toString(36).substr(2, 9)
      const options = {
        startVal: 0,
        duration: 2,
        decimals: decimals,
        useEasing: true,
        useGrouping: true
      }
      
      const countUp = new CountUp(element, endValue, options)
      if (!countUp.error) {
        countUp.start()
        this.countInstances[id] = countUp
      } else {
        console.error(countUp.error)
      }
    },
    initParticleEffect() {
      const rainCanvas = document.querySelector('.rain')
      const dashedCanvas = document.querySelector('.dashed')
      
      if (!rainCanvas || !dashedCanvas) return
      
      const rainCtx = rainCanvas.getContext('2d')
      const dashedCtx = dashedCanvas.getContext('2d')
      
      // 设置画布大小
      const resizeCanvas = () => {
        const container = document.querySelector('.particle-container')
        if (!container) return
        
        const rect = container.getBoundingClientRect()
        rainCanvas.width = rect.width
        rainCanvas.height = rect.height
        dashedCanvas.width = rect.width
        dashedCanvas.height = rect.height
      }
      
      resizeCanvas()
      window.addEventListener('resize', resizeCanvas)
      
      // 粒子效果
      class Particle {
        constructor() {
          this.init()
        }
        
        init() {
          this.x = Math.random() * rainCanvas.width
          this.y = rainCanvas.height
          this.vy = -(Math.random() * 1 + 1)
          this.size = Math.random() * 2 + 1
          this.alpha = Math.random() * 0.5 + 0.5
          this.color = this.getRandomColor()
        }
        
        getRandomColor() {
          const colors = [
            { r: 0, g: 255, b: 255 },  // 青色
            { r: 0, g: 195, b: 255 },  // 浅蓝色
            { r: 20, g: 120, b: 156 }  // 深蓝色
          ]
          const color = colors[Math.floor(Math.random() * colors.length)]
          return `rgba(${color.r}, ${color.g}, ${color.b}, ${this.alpha})`
        }
        
        draw() {
          rainCtx.beginPath()
          const gradient = rainCtx.createLinearGradient(this.x, this.y, this.x, this.y - this.size * 10)
          gradient.addColorStop(0, this.color)
          gradient.addColorStop(1, 'rgba(0, 255, 255, 0)')
          rainCtx.fillStyle = gradient
          rainCtx.fillRect(this.x, this.y, this.size, -this.size * 10)
          rainCtx.closePath()
          
          this.update()
        }
        
        update() {
          this.y += this.vy
          
          if (this.y < 0) {
            this.init()
          }
        }
      }
      
      // 初始化粒子
      const particles = []
      const particleCount = 50
      
      for (let i = 0; i < particleCount; i++) {
        setTimeout(() => {
          const particle = new Particle()
          particles.push(particle)
        }, i * 100)
      }
      
      // 动画循环
      const animate = () => {
        rainCtx.clearRect(0, 0, rainCanvas.width, rainCanvas.height)
        dashedCtx.clearRect(0, 0, dashedCanvas.width, dashedCanvas.height)
        
        particles.forEach(particle => particle.draw())
        
        this.animationFrame = requestAnimationFrame(animate)
      }
      
      animate()
    },
    stopParticleEffect() {
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
      window.removeEventListener('resize', this.resizeCanvas)
    },
    animateCircles() {
      if (this.circleAnimating) return
      
      this.circleAnimating = true
      const duration = 1500 // 动画持续时间（毫秒）
      const startTime = performance.now()
      
      // 保存起始值
      const startRedRatio = this.red_ratio
      const startYellowRatio = this.yellow_ratio
      const startBlueRatio = this.blue_ratio
      
      // 动画函数
      const animate = (currentTime) => {
        const elapsedTime = currentTime - startTime
        const progress = Math.min(elapsedTime / duration, 1)
        
        // 应用缓动函数，使动画更自然
        const easeOutProgress = 1 - Math.pow(1 - progress, 3)
        
        // 计算当前值
        this.red_ratio = Math.round(startRedRatio + (this.targetRedRatio - startRedRatio) * easeOutProgress)
        this.yellow_ratio = Math.round(startYellowRatio + (this.targetYellowRatio - startYellowRatio) * easeOutProgress)
        this.blue_ratio = Math.round(startBlueRatio + (this.targetBlueRatio - startBlueRatio) * easeOutProgress)
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          // 确保最终值精确匹配目标值
          this.red_ratio = this.targetRedRatio
          this.yellow_ratio = this.targetYellowRatio
          this.blue_ratio = this.targetBlueRatio
          this.circleAnimating = false
        }
      }
      
      requestAnimationFrame(animate)
      
      // 使用CountUp动画更新百分比数字
      this.animatePercentages()
    },
    animatePercentages() {
      // 为百分比数字添加动画效果
      const options = {
        duration: 1.5,
        useEasing: true,
        suffix: '%'
      }
      
      // 红色百分比
      const redElement = document.querySelector('.red .progress-percentage')
      if (redElement) {
        new CountUp(
          redElement, 
          this.targetRedRatio, 
          { ...options, startVal: parseInt(redElement.textContent) || 0 }
        ).start()
      }
      
      // 黄色百分比
      const yellowElement = document.querySelector('.yellow .progress-percentage')
      if (yellowElement) {
        new CountUp(
          yellowElement, 
          this.targetYellowRatio, 
          { ...options, startVal: parseInt(yellowElement.textContent) || 0 }
        ).start()
      }
      
      // 蓝色百分比
      const blueElement = document.querySelector('.blue .progress-percentage')
      if (blueElement) {
        new CountUp(
          blueElement, 
          this.targetBlueRatio, 
          { ...options, startVal: parseInt(blueElement.textContent) || 0 }
        ).start()
      }
    },
    animateCounters(redCount, yellowCount, blueCount) {
      // 使用CountUp库创建平滑的数字过渡效果
      const options = {
        startVal: this.red_count,
        duration: 1.5,
        useEasing: true,
        useGrouping: true,
      }
      
      // 红色计数器
      const redValueElement = document.querySelector('.red .progress-value')
      if (redValueElement) {
        new CountUp(
          redValueElement, 
          redCount, 
          { ...options, startVal: this.red_count }
        ).start(() => {
          this.red_count = redCount
        })
      }
      
      // 黄色计数器
      const yellowValueElement = document.querySelector('.yellow .progress-value')
      if (yellowValueElement) {
        new CountUp(
          yellowValueElement, 
          yellowCount, 
          { ...options, startVal: this.yellow_count }
        ).start(() => {
          this.yellow_count = yellowCount
        })
      }
      
      // 蓝色计数器
      const blueValueElement = document.querySelector('.blue .progress-value')
      if (blueValueElement) {
        new CountUp(
          blueValueElement, 
          blueCount, 
          { ...options, startVal: this.blue_count }
        ).start(() => {
          this.blue_count = blueCount
        })
      }
    }
  }
}
</script>

<style scoped>
@import './style.css';

/* 添加动画样式 */
@keyframes pulse {
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
}

.progress-circle {
  animation: pulse 3s infinite ease-in-out;
}

.progress-percentage {
  transition: all 0.3s ease-out;
}

.circle-fill {
  transition: stroke-dasharray 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}
</style> 