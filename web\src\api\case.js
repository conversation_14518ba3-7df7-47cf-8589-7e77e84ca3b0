import request from '@/utils/request'

// 获取警情列表
export function getCaseList(data) {
  return request({
    url: '/api/search',
    method: 'post',
    params: data
  })
}

// 导出警情数据
export function exportCases(data) {
  return request({
    url: '/api/export',
    method: 'post',
    params:data,
    responseType: 'blob'
  })
}


// 上传警情数据文件
export function uploadCases(data, config = {}) {
  return request({
    url: '/api/file',
    method: 'post',
    data,
    ...config
  })
} 

export function loginDify() {
  return request({
    url: '/api/login',
    method: 'post'
  })
} 

// 同步警情数据
export function syncCaseData(data) {
  return request({
    url: '/api/sync_police_record',
    method: 'post',
    params: data
  })
} 

// 批量分析案件
export function batchAnalyzeCases(caseIds) {
  return request({
    url: '/api/police_record_analysis',
    method: 'post',
    data: {
      ids: caseIds
    }
  })
} 

// 手动导入案件数据
export function manualImportCases(data, config = {}) {
  return request({
    url: '/api/manual_import',
    method: 'post',
    data,
    ...config
  })
}

// 获取导入文件列表
export function getImportFileList(data) {
  return request({
    url: '/api/import_files',
    method: 'get',
    params: data
  })
} 