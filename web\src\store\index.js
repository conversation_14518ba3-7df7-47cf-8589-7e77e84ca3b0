import Vue from "vue";
import Vuex from "vuex";
import { login, logoutInfo } from "@/api/auth";
import { getToken, removeToken } from "@/utils/auth";
import chat from "./modules/chat";
import office from "./modules/office";
import law from "./modules/law";
import legal from "./modules/legal";
import permission from "./modules/permission";
Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    policeStations: [
      { name: "xxxx派出所", count: 0 },
      { name: "xxxx派出所", count: 0 },
    ],
    user: JSON.parse(localStorage.getItem("user")) || null,
    userRole: JSON.parse(localStorage.getItem("userRole")) || [],
  },
  mutations: {
    updatePoliceStationCount(state, { index, count }) {
      state.policeStations[index].count = count;
    },
    SET_USER(state, user) {
      state.user = user;
      localStorage.setItem("user", JSON.stringify(user));
    },
    SET_USER_ROLE(state, userRole) {
      state.userRole = userRole;
      localStorage.setItem("userRole", JSON.stringify(userRole));
    },
    CLEAR_AUTH(state) {
      state.user = null;
      localStorage.removeItem("user");
    },
  },
  actions: {
    // 登录
    async login({ commit }, userInfo) {
      try {
        const res = await login(userInfo);
        if (res.code === 200) {
          // 修复数据结构解析，确保能正确获取token和user
          // 根据实际API返回结构调整以下代码
          // 方法1: 如果access_token本身就是token字符串
          if (typeof res.data.access_token === "string") {
            let user = { ...userInfo, role: res.data.role };
            commit("SET_USER", user);
          }
        }
        return res;
      } catch (error) {
        console.error("登录失败:", error);
        throw error;
      }
    },
    // 登出
    async logout({ commit, dispatch }) {
      console.log("退出登录");
      try {
        const token = getToken();
        await logoutInfo(token);
        removeToken();
        commit("CLEAR_AUTH");
        // 清除路由状态
        commit("permission/SET_ROUTES", [], { root: true });
        // 重置store
        commit("chat/RESET_STORE");
        commit("office/RESET_STORE");
        commit("law/RESET_STORE");
        commit("legal/RESET_STORE");
      } catch (error) {
        console.error("登出失败:", error);
        removeToken();
        commit("CLEAR_AUTH");
        // 清除路由状态
        commit("permission/SET_ROUTES", [], { root: true });
        // 重置store
        commit("chat/RESET_STORE");
        commit("office/RESET_STORE");
        commit("law/RESET_STORE");
        commit("legal/RESET_STORE");
      }
    },
  },
  getters: {
    isAuthenticated: state => !!state.user,
    permission_routes: state => state.permission.routes,
    userRole: state => state.userRole,
  },
  modules: {
    chat,
    office,
    law,
    legal,
    permission,
  },
});
