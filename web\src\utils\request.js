import axios from "axios";
import { Message } from "element-ui";
import router from "@/router";
import store from "@/store";
import { getToken, removeToken } from "@/utils/auth";

// console.log("前缀"+process.env.VUE_APP_API_BASE_URL)
// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL,
  timeout: 120000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在这里可以添加token等认证信息
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error("请求错误：", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果响应类型是blob，直接返回数据
    if (response.config.responseType === "blob") {
      return response.data;
    }

    const res = response.data;
    // 处理401未授权错误
    if (res.code === 401) {
      Message({
        message: "登录已过期，请重新登录",
        type: "error",
        duration: 5 * 1000,
      });
      // 清除登录状态
      removeToken();
      // 清除路由状态
      store.commit("permission/SET_ROUTES", [], { root: true });
      // 重置store
      store.commit("chat/RESET_STORE");
      store.commit("office/RESET_STORE");
      store.commit("law/RESET_STORE");
      store.commit("legal/RESET_STORE");
      store.commit("CLEAR_AUTH");
      router.push("/login");
      return Promise.reject(new Error("登录已过期，请重新登录"));
    }
    // 处理500错误
    if (res.code === 500) {
      Message({
        message: res.message || "系统错误",
        type: "error",
        duration: 5 * 1000,
      });
      return Promise.reject(new Error(res.message || "系统错误"));
    }
    // 其他情况直接返回响应数据
    return res;
  },
  error => {
    console.error("响应错误：", error);
    if (error.response && error.response.status === 401) {
      Message({
        message: "登录已过期，请重新登录",
        type: "error",
        duration: 5 * 1000,
      });
      removeToken();
      // 清除路由状态
      store.commit("permission/SET_ROUTES", [], { root: true });
      // 重置store
      store.commit("chat/RESET_STORE");
      store.commit("office/RESET_STORE");
      store.commit("law/RESET_STORE");
      store.commit("legal/RESET_STORE");

      store.commit("CLEAR_AUTH");
      router.push("/login");
    } else {
      Message({
        message: error.message || "系统错误",
        type: "error",
        duration: 5 * 1000,
      });
    }
    return Promise.reject(error);
  }
);

export default service;
