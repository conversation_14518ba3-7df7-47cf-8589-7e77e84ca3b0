from sqlalchemy import Column, Integer, String, DateTime, Text, Index, UniqueConstraint, func
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

SacwBaseBase = declarative_base()


class SacwBase(SacwBaseBase):
    """涉案财物记录基类"""
    __abstract__ = True

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    cwbh = Column(String(50), comment='物品编号', index=True)
    cwmc = Column(String(255), comment='物品名称')
    ajmc = Column(String(255), comment='案件名称')
    ajbh = Column(String(24), comment='案件编号', index=True)
    fh_rksj = Column(String(50), comment='入库时间')


class SacwDjrkjl(SacwBase):
    """物品登记入库记录"""
    __tablename__ = 'sacw_djrkjl'

    djsj = Column(String(50), comment='入库时间')
    cwsl = Column(String(24), comment='入库数量')
    gl_fdqx = Column(String(50), comment='保管期限')


class SacwClck(SacwBase):
    """物品处理出库记录"""
    __tablename__ = 'sacw_clck'

    czsj = Column(String(50), comment='操作时间')


class SacwBgyq(SacwBase):
    """物品保管延期记录"""
    __tablename__ = 'sacw_bgyq'

    czsj = Column(String(50), comment='操作时间')
    clqx = Column(String(50), comment='处理期限')

    __table_args__ = (
        Index('idx_clqx', clqx),  # 处理期限查询索引
    )


class ImportedFiles(SacwBaseBase):
    """已导入文件记录表"""
    __tablename__ = 'imported_files'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    file_name = Column(String(255), nullable=False, comment='文件名')
    file_md5 = Column(String(32), nullable=False, comment='文件MD5值')
    file_type = Column(Integer, nullable=False, comment='文件类型：0-案件措施，1-涉案财物')
    import_time = Column(DateTime, nullable=False, server_default=func.now(), comment='导入时间')
    
    __table_args__ = (
        UniqueConstraint('file_md5', name='uk_file_md5'),
        Index('idx_file_md5', file_md5),
    )
