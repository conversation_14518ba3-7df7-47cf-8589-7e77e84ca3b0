"""
聊天智能体处理器工厂
"""
from typing import Dict, Any

from sqlalchemy.orm import Session

from app.config import settings
from app.services.chat_agent.base import AgentType
from app.services.chat_agent.handlers.implementations import (
    DefaultKnowledgeBaseHandler,
    DifyQueryProcessingHandler,
    DifySessionHandler, SecretarialKnowledgeBaseHandler
)
from app.services.chat_agent.handlers.interfaces import (
    KnowledgeBaseHandler,
    QueryProcessingHandler,
    SessionHandler
)
from app.services.dify.service import DifyService


class KnowledgeBaseHandlerFactory:
    """知识库处理器工厂"""

    @staticmethod
    async def create_handler(db: Session, dify_service: Any, agent_config: Dict[str, Any]) -> KnowledgeBaseHandler:
        """
        创建知识库处理器
        
        Args:
            db: 数据库会话
            dify_service: Dify服务
            agent_config: 智能体配置
            
        Returns:
            知识库处理器
        """
        dify_service.__init__(api_key=settings.DATASETS_API_KEY)
        # 文秘办公处理器特殊定义
        if agent_config.get('type', '') == AgentType.SECRETARIAL.name:
            return SecretarialKnowledgeBaseHandler(db, dify_service, agent_config)

        return DefaultKnowledgeBaseHandler(db, dify_service, agent_config)


class QueryProcessingHandlerFactory:
    """查询处理器工厂"""

    @staticmethod
    async def create_handler(dify_service: Any, agent_config: Dict[str, Any]) -> QueryProcessingHandler:
        """
        创建查询处理器
        
        Args:
            dify_service: Dify服务
            agent_config: 智能体配置
            
        Returns:
            查询处理器
        """
        dify_service.__init__(api_key=agent_config['api_key'])
        return DifyQueryProcessingHandler(dify_service, agent_config)


class SessionHandlerFactory:
    """会话管理器工厂"""

    @staticmethod
    async def create_handler(db: Session, dify_service: DifyService, agent_config) -> SessionHandler:
        """
        创建会话管理器
        
        Args:
            db: 数据库会话
            agent_id: 智能体ID
            
        Returns:
            会话管理器
        """
        dify_service.__init__(api_key=agent_config['api_key'])
        return DifySessionHandler(db, dify_service, agent_config['agent_id'])
