<template>
    <div class="status-view" :class="[statusOption.iconType !== 'speck' && 'sv-is-center']">
        <div class="status-view-icon">
            <i v-if="statusOption.iconType === 'loading'" class="el-icon-loading" :style="iconStyle"></i>
            <i v-else-if="statusOption.iconType === 'check'" class="el-icon-circle-check" :style="iconStyle"></i>
            <i v-else-if="statusOption.iconType === 'close'" class="el-icon-circle-close" :style="iconStyle"></i>
            <i v-else-if="statusOption.iconType === 'refresh'" class="el-icon-refresh" :style="iconStyle"></i>
            <i v-else-if="statusOption.iconType === 'clock'" class="el-icon-alarm-clock" :style="iconStyle"></i>
            <i v-else-if="statusOption.iconType === 'speck'" class="el-icon-speck"
                :style="{ backgroundColor: statusOption.iconColor || statusOption.bgColor }"></i>
        </div>
        <div class="status-view-text" :style="textStyle">
            {{ statusOption.text }}
        </div>
    </div>
</template>

<script>
export default {
    name: 'StatusView',
    props: {
        statusOption: {
            type: Object,
            default() {
                return {}
            }
        },
    },
    data() {
        return {

        }
    },
    computed: {
        textStyle() {
            return {
                color: this.statusOption.color ? `${this.statusOption.color}b3` : '#fffb3'
            }
        },
        iconStyle() {
            return {
                color: this.statusOption.iconColor ? `${this.statusOption.iconColor}b3` : 
                       this.statusOption.color ? `${this.statusOption.color}b3` : '#fffb3'
            }
        }
    },
    mounted() {

    }
}
</script>

<style lang="less" scoped>
.status-view {
    display: flex;

    .status-view-text {
        margin-left: 5px;
    }

    .el-icon-speck {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 100%;
    }
}

.sv-is-center {
    justify-content: center;
}
</style>