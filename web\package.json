{"name": "case-analysis-system", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode deployment", "build": "vue-cli-service build --mode pro", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "0.4.0", "@types/echarts": "^5.0.0", "axios": "^0.21.1", "core-js": "^3.41.0", "countup.js": "^2.8.0", "docx": "^9.3.0", "docx-preview": "^0.3.5", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-tree-line": "^0.2.1", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "github-markdown-css": "^5.8.1", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "jquery": "^3.6.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "markdown-it": "^14.1.0", "markdown-it-echarts": "^0.1.3", "markdown-it-mermaid": "^0.2.5", "marked": "^15.0.7", "nprogress": "0.2.0", "particles.js": "^2.0.0", "postcss-pxtorem": "^5.1.1", "splitpanes": "2.4.1", "vue": "^2.6.14", "vue-pdf": "^4.3.0", "vue-router": "^3.5.1", "vuex": "^3.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/preset-env": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "json-loader": "^0.5.7", "less": "^4.2.2", "less-loader": "6.0.0", "mockjs": "^1.1.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true, "es6": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false, "ecmaVersion": 2018, "sourceType": "module"}, "rules": {"no-console": "off", "no-debugger": "off", "vue/no-parsing-error": [2, {"x-invalid-end-tag": false}]}}}