import logging
from typing import Any, Dict, List
from typing import Optional  # 添加这行导入

from fastapi import APIRouter, Depends
from fastapi import Body
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.db.database import get_db
from app.models.scene_3_record import scene3_search_order, Scene_3_Record
from app.models.user import User
from app.services.db_service import DatabaseService
from app.services.scene_3_service import Scene_3_Service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/scene_3/legal/search")
async def search_records(
        jlbh: Optional[str] = None,
        ajbh: Optional[str] = None,
        doc_name: Optional[str] = None,
        cqsx: Optional[str] = None,
        pzsj_start_time: Optional[str] = None,
        pzsj_end_time: Optional[str] = None,
        tfsj_start_time: Optional[str] = None,
        tfsj_end_time: Optional[str] = None,
        analysis_status: Optional[str] = None,
        check_status: Optional[str] = None,
        doc_num: Optional[str] = None,
        tfrxm: Optional[str] = None,
        ajmc: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    query = DatabaseService.build_legal_search_query(
        db, jlbh, ajbh, doc_name, cqsx, pzsj_start_time, pzsj_end_time, analysis_status, check_status, doc_num, tfrxm,
        ajmc, tfsj_start_time, tfsj_end_time
    )

    # 计算总记录数
    total_count = query.count()

    # 添加分页
    skip = (page - 1) * page_size
    query = query.order_by(scene3_search_order(), Scene_3_Record.pzsj.desc()).offset(skip).limit(page_size)

    results = query.all()
    return create_response(data={
        "records": results,
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": (total_count + page_size - 1) // page_size
    })


@router.post("/scene_3/sync")
async def scene_2_data_sync(
        current_user: User = Depends(UserContext.get_current),
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
) -> Dict[str, Any]:
    res = await Scene_3_Service.scene_3_data_sync(current_user.id, start_date, end_date)
    if not res:
        return create_response(
            message="同步异常",
            code=500
        )
    return create_response(
        message="同步成功",
        data=res
    )


@router.post("/scene_3/analyze")
async def scene_2_summary_analyze(jlbhs: List[str] = Body(..., embed=True)) -> Dict[str, Any]:
    try:
        # 异步调用，指令发送完成即可
        res = await Scene_3_Service.scene_3_doc_data_analyze(
            jlbhs=jlbhs
        )
        return create_response(
            message="分析指令已发送",
            data=res
        )
    except Exception as e:
        return create_response(
            message=f"分析异常: {str(e)}",
            code=500
        )
