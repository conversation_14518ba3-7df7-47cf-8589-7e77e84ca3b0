from typing import Tuple, Literal
from app.log.log_utils import LogUtils

class DeviceManager:
    """设备管理器：自动检测和配置计算设备"""

    @staticmethod
    def detect_available_device() -> Tuple[Literal["cpu", "gpu", "npu"], int]:
        """
        检测可用的计算设备
        返回: (设备类型, 设备数量)
        """
        try:
            # 检测NPU
            import paddle
            if hasattr(paddle, 'device') and hasattr(paddle.device, 'npu'):
                try:
                    npu_count = paddle.device.npu.device_count()
                    if npu_count > 0:
                        LogUtils.info(f"检测到 {npu_count} 个NPU设备")
                        return "npu", npu_count
                except Exception as e:
                    LogUtils.warning(f"NPU检测异常: {str(e)}")

            # 检测GPU
            if hasattr(paddle, 'device') and hasattr(paddle.device, 'cuda'):
                try:
                    gpu_count = paddle.device.cuda.device_count()
                    if gpu_count > 0:
                        LogUtils.info(f"检测到 {gpu_count} 个GPU设备")
                        return "gpu", gpu_count
                except Exception as e:
                    LogUtils.warning(f"GPU检测异常: {str(e)}")

        except ImportError as e:
            LogUtils.warning(f"Paddle导入失败: {str(e)}")

        # 默认使用CPU
        LogUtils.info("使用CPU进行推理")
        return "cpu", 1

    @staticmethod
    def get_ocr_config(device_type: str, device_id: int = 0) -> dict:
        """
        根据设备类型生成OCR配置
        """
        base_config = {
            "use_angle_cls": True,
            "lang": "ch",
            "use_gpu": False,
            "use_npu": False,
            "device_id": device_id
        }

        if device_type == "gpu":
            base_config["use_gpu"] = True
        elif device_type == "npu":
            base_config["use_npu"] = True

        return base_config
