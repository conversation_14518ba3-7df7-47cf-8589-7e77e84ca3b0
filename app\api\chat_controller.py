"""
聊天控制器
"""
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, UploadFile, File, Form
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.models.user import User
from app.services.chat_agent.provider import get_agent_service
from app.services.chat_agent.service import AgentService

router = APIRouter(prefix="/agents", tags=["聊天"])


class ChatRequest(BaseModel):
    """聊天请求"""
    message: str
    streaming: bool = True  # 新增：是否使用流式响应
    # 附件
    file_ids: List[str] = None


class ConversationName(BaseModel):
    """聊天请求"""
    name: str


class DatasetFile(BaseModel):
    """聊天请求"""
    file_name: str
    # 附件
    file_id: str = None


class ChatResponse(BaseModel):
    """聊天响应"""
    success: bool
    content: str
    metadata: Dict[str, Any] = {}


class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    success: bool
    message: str
    data: Dict[str, Any] = {}


class DatasetItem(BaseModel):
    """知识库项目"""
    id: int
    name: str
    description: Optional[str] = None
    is_personal: bool
    document_count: int
    created_at: str
    updated_at: Optional[str] = None
    owner_info: Optional[Dict[str, Any]] = None


class DatasetListResponse(BaseModel):
    """知识库列表响应"""
    success: bool
    datasets: List[DatasetItem] = []
    message: Optional[str] = None


@router.post("/{agent_id}/sessions/chat", response_model=ChatResponse)
async def process_chat(
        agent_id: str,
        request: ChatRequest,
        conversation_id: str = "",
        current_user=Depends(UserContext.get_current),
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    处理聊天消息，支持流式和非流式响应

    Args:
        agent_id: 智能体ID
        conversation_id: 会话ID
        request: 聊天请求
        current_user: 当前用户
        agent_service: 智能体服务

    Returns:
        聊天响应或流式响应
    """
    # 如果请求流式响应
    if request.streaming:
        return StreamingResponse(
            agent_service.process_agent_chat_message_stream(
                agent_id=agent_id,
                user_id=current_user.id,
                conversation_id=conversation_id,
                message=request.dict(exclude_unset=True)
            ),
            media_type="text/event-stream"
        )

    # 非流式响应处理
    result = await agent_service.process_agent_chat_message(
        agent_id=agent_id,
        user_id=current_user.id,
        conversation_id=conversation_id,
        message=request.dict(exclude_unset=True)
    )

    if not result.get("success", False):
        raise HTTPException(status_code=400, detail=result.get("message", "处理失败"))

    return create_response(data=result)


@router.post("/{agent_id}/dataset/upload")
async def upload_doc_to_dataset(
        agent_id: str,
        upload_type: str = Form(...),  # file or file_id
        file_name: str = Form(None),
        file_id: str = Form(None),
        file: UploadFile = File(None),
        current_user=Depends(UserContext.get_current),
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    上传文档

    Args:
        agent_id: 智能体ID
        file: 上传的文件结构
        current_user: 当前用户
        agent_service: 智能体服务

    Returns:
        文档上传响应
            file_name: str
    # 附件
    file_id: str = None
    """
    # 准备文档数据

    if upload_type == "file":
        if not file:
            return create_response(code=400, message='上传文件不能为空')
        # file 不能为空
        # 直接传递UploadFile对象
        document_data = {
            "file_object": file,
            "file_name": file.filename,
            "content_type": file.content_type
        }
    elif upload_type == "file_id":
        if not file_id or not file_name:
            return create_response(code=400, message='file_id和file_name不能为空')
        # file_id 不能为空 file_name 不能为空
        # 通过dify上传文件后得到文件id和文件名后上传知识库
        document_data = {
            "file_id": file_id,
            "file_name": file_name
        }
    else:
        return create_response(code=400, message="未知知识库文档上传方式")

    result = await agent_service.handle_agent_document_upload(
        agent_id=agent_id,
        user_id=current_user.id,
        document_data=document_data
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "上传失败"))

    return create_response(data=result.get("data", {}))


@router.post("/file/upload")
async def upload_file(
        agent_id: str = Form(...),
        session_id: str = Form(...),
        file: UploadFile = File(...),

        agent_service: AgentService = Depends(get_agent_service)
):
    """
    上传文档

    Args:
        agent_id: 智能体ID
        file: 上传的文件
        current_user: 当前用户
        agent_service: 智能体服务

    Returns:
        文档上传响应
    """
    # 准备文档数据
    document_data = {
        "file_object": file,  # 直接传递UploadFile对象
        "file_name": file.filename,
        "content_type": file.content_type
    }

    result = await agent_service.handle_file_upload(
        agent_id=agent_id,
        session_id=session_id,
        document_data=document_data
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "文件上传失败"))

    return create_response(data=result)


@router.post("/{agent_id}/create_session")
async def create_session(
        agent_id: str,
        current_user: User = Depends(UserContext.get_current),
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    创建会话（agent与用户的关联）

    Args:
        agent_id: 智能体ID
        current_user: 当前用户
        agent_service: 智能体服务

    Returns:
        关联ID
    """
    result = await agent_service.create_agent_session(
        agent_id=agent_id,
        user_id=current_user.id
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "创建agent关联失败"))

    return create_response(data=result)


@router.get("/{agent_id}/get_session")
async def agent_user_session(
        agent_id: str,
        current_user=Depends(UserContext.get_current),
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    获取会话
    
    Args:
        agent_id: 智能体ID
        current_user: 当前用户
        agent_service: 智能体服务
        
    Returns:
        会话列表响应
    """
    result = await agent_service.agent_user_session(
        agent_id=agent_id,
        user_id=current_user.id,
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "获取会话列表失败"))

    return create_response(data=result)


@router.get("/{agent_id}/conversations/{session_id}")
async def list_conversations(
        agent_id: str,
        session_id: str,
        last_id: Optional[str] = None,
        limit: int = 20,
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    获取历史对话列表

    Args:
        agent_id: 智能体ID
        session_id: 会话ID
        limit: 限制数量
        last_id: 上一页最后一条 数据ID
        current_user: 当前用户
        agent_service: 智能体服务

    Returns:
        会话列表响应
    """
    result = await agent_service.list_agent_user_conversations(
        agent_id=agent_id,
        session_id=session_id,
        limit=limit,
        last_id=last_id
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "获取会话列表失败"))

    return create_response(data=result)


@router.post("/{agent_id}/conversations/{conversation_id}/name")
async def update_conversation_name(
        agent_id: str,
        conversation_id: str,
        request: ConversationName,
        agent_service: AgentService = Depends(get_agent_service),
        current_user=Depends(UserContext.get_current),
):
    """
    获取历史对话列表

    Args:
        agent_id: 智能体ID
        session_id: 会话ID
        limit: 限制数量
        last_id: 上一页最后一条 数据ID
        current_user: 当前用户
        agent_service: 智能体服务

    Returns:
        会话列表响应
    """
    result = await agent_service.update_conversation_name(
        agent_id=agent_id,
        conversation_id=conversation_id,
        name=request.name,
        user_id=current_user.id
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "修改会话名称失败"))

    return create_response(data=result)


@router.get("/{agent_id}/conversation/history")
async def get_history(
        agent_id: str,
        conversation_id: str = "",
        first_id: Optional[str] = None,
        limit: int = 50,
        current_user=Depends(UserContext.get_current),
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    获取会话历史
    
    Args:
        agent_id: 智能体ID
        conversation_id: 会话ID
        limit: 限制数量
        current_user: 当前用户
        agent_service: 智能体服务
        
    Returns:
        历史记录响应
    """
    result = await agent_service.get_agent_session_history(
        agent_id=agent_id,
        conversation_id=conversation_id,
        user_id=current_user.id,
        limit=limit,
        first_id=first_id
    )

    if not result.get("success", False):
        raise HTTPException(status_code=400, detail=result.get("message", "获取会话历史失败"))

    return create_response(data=result)


@router.get("/{agent_id}/datasets")
async def list_datasets(
        agent_id: str,
        page: int = 1,
        limit: int = 20,
        agent_service: AgentService = Depends(get_agent_service)
):
    """
    获取智能体关联的知识库列表
    
    Args:
        agent_id: 智能体ID
        limit: 限制数量
        current_user: 当前用户
        agent_service: 智能体服务
        
    Returns:
        知识库列表响应
    """
    result = await agent_service.list_agent_datasets(
        agent_id=agent_id,
        page=page,
        limit=limit
    )

    if not result.get("success", False):
        return create_response(code=400, message=result.get("message", "获取知识库列表失败"))

    return create_response(data=result)
