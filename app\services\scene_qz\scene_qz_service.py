import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List

import aiohttp
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, text, func, distinct
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.qz_case_feature import QzCaseFeature
from app.models.police_record import PoliceRecord
from collections import defaultdict

from app.config import settings
from app.core.task_queue import TaskQueueManager


class SceneQzService:

    def __init__(self):
        super().__init__()
        # 注册场景5分析队列
        self.queue_name = "scene_8_analysis"

        # 异步分析队列
        self.queue_manager = TaskQueueManager()

    async def async_init(self):
        # 异步初始化代码
        await self.queue_manager.register_queue(
            queue_name=self.queue_name,
            processor=self._sync,
            max_size=1000,  # 队列容量
            concurrency=5  # 最大并发数
        )
    @staticmethod
    @transactional
    async def search_cases_by_entity(id_card: str, name: str, page: int = 1, page_size: int = 10, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"根据实体查询案件，身份证: {id_card}, 姓名: {name}, 页码: {page}, 每页数量: {page_size}")
        try:
            # 使用单个优化的SQL查询，避免临时表问题
            LogUtils.info(f"执行优化的SQL查询：身份证={id_card}, 姓名={name}")

            # 计算分页参数
            offset = (page - 1) * page_size

            # 统一的查询逻辑，支持身份证+姓名或仅姓名查询
            main_query_sql = """
            WITH pivot_data AS (
                SELECT
                    case_id,
                    entity_id,
                    MAX(CASE WHEN feature_type = 'ID_CARD' THEN feature_value END) AS ID_CARD,
                    MAX(CASE WHEN feature_type = 'ENTITY_NAME' THEN feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN feature_type = 'ENTITY_TYPE' THEN feature_value END) AS ENTITY_TYPE,
                    MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END) AS CASE_TYPE,
                    MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END) AS LOCATION
                FROM qz_case_feature
                GROUP BY case_id, entity_id
            ),
            target_entities AS (
                -- 找到目标实体：姓名匹配的实体
                SELECT DISTINCT entity_id, case_id, ID_CARD, ENTITY_NAME
                FROM pivot_data
                WHERE ENTITY_NAME = :name
                  AND (:id_card IS NULL OR ID_CARD = :id_card OR ID_CARD IS NULL)
            ),
            -- 按身份证聚类：如果有身份证就按身份证聚类，否则按姓名聚类
            entity_clusters_raw AS (
                SELECT
                    te.entity_id,
                    te.case_id,
                    CASE
                        WHEN :id_card IS NOT NULL AND te.ID_CARD = :id_card THEN :id_card
                        WHEN :id_card IS NOT NULL AND te.ID_CARD IS NULL THEN
                            CASE
                                WHEN EXISTS (SELECT 1 FROM target_entities te2 WHERE te2.ID_CARD = :id_card)
                                THEN :id_card
                                ELSE CONCAT('NO_ID_CARD_', :name)
                            END
                        WHEN :id_card IS NULL THEN COALESCE(te.ID_CARD, CONCAT('NO_ID_CARD_', :name))
                        ELSE CONCAT('NO_ID_CARD_', :name)
                    END as cluster_key
                FROM target_entities te
            ),
            entities_with_multiple_cases AS (
                SELECT cluster_key, entity_id
                FROM entity_clusters_raw
                GROUP BY cluster_key, entity_id
                HAVING COUNT(DISTINCT case_id) >= 2
            ),
            entity_clusters AS (
                SELECT
                    e.cluster_key,
                    p.case_id,
                    p.entity_id,
                    p.ID_CARD,
                    p.ENTITY_NAME,
                    p.ENTITY_TYPE,
                    -- 获取案件级别的信息（entity_id = 0）
                    (SELECT MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END)
                     FROM qz_case_feature qcf
                     WHERE qcf.case_id = p.case_id AND qcf.entity_id = 0) AS CASE_TYPE,
                    (SELECT MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END)
                     FROM qz_case_feature qcf
                     WHERE qcf.case_id = p.case_id AND qcf.entity_id = 0) AS LOCATION,
                    pr.police_time
                FROM pivot_data p
                JOIN entities_with_multiple_cases e ON p.entity_id = e.entity_id
                LEFT JOIN police_records pr ON p.case_id = pr.police_number
                WHERE pr.source = 0
                  AND p.ENTITY_NAME = :name  -- 确保只返回目标姓名的数据
            ),
            cluster_summary AS (
                SELECT
                    cluster_key,
                    MAX(CASE WHEN ID_CARD IS NOT NULL THEN ID_CARD END) as representative_id_card,
                    :name as representative_name,  -- 直接使用查询的姓名
                    COUNT(DISTINCT case_id) as case_count,
                    GROUP_CONCAT(DISTINCT
                        CONCAT(case_id, '|', COALESCE(ENTITY_TYPE, 'null'), '|',
                               COALESCE(DATE_FORMAT(police_time, '%Y-%m-%d %H:%i:%s'), ''), '|',
                               COALESCE(CASE_TYPE, ''), '|',
                               COALESCE(LOCATION, ''))
                        ORDER BY police_time DESC
                        SEPARATOR ';;'
                    ) as cases_info
                FROM entity_clusters
                GROUP BY cluster_key
                HAVING case_count >= 2
            )
            SELECT * FROM cluster_summary
            ORDER BY case_count DESC
            LIMIT :limit OFFSET :offset
            """

            params = {
                'id_card': id_card,
                'name': name,
                'limit': page_size,
                'offset': offset
            }

            LogUtils.info("SQL的参数: " + str(params))

            LogUtils.info("执行主查询SQL")
            result = db.execute(text(main_query_sql), params)
            clusters = result.fetchall()

            # 调试：打印查询结果
            LogUtils.info(f"SQL查询返回的原始结果: {clusters}")

            # 如果没有结果，返回空
            if not clusters:
                LogUtils.info("未找到符合条件的实体聚类")
                return {
                    "total": 0,
                    "items": [],
                    "page": page,
                    "page_size": page_size
                }

            LogUtils.info(f"找到{len(clusters)}个实体聚类")

            # 解析查询结果并构建返回数据
            all_clusters = []
            for cluster in clusters:
                representative_id_card = cluster[1]
                representative_name = cluster[2]
                cases_info = cluster[4]

                # 解析案件信息字符串
                cases_list = []
                if cases_info:
                    case_items = cases_info.split(';;')
                    for case_item in case_items:
                        parts = case_item.split('|')
                        if len(parts) >= 5:
                            case_id = parts[0]
                            entity_type = parts[1] if parts[1] != 'null' else None
                            police_time = parts[2] if parts[2] else None
                            case_type = parts[3] if parts[3] else None
                            location = parts[4] if parts[4] else None

                            cases_list.append({
                                "role": [entity_type] if entity_type else [],
                                "case": case_id,
                                "police_time": police_time,
                                "case_type": case_type,
                                "location": location
                            })

                # 确保至少有两个案件
                if len(cases_list) >= 2:
                    all_clusters.append({
                        "name": representative_name,
                        "id_card": representative_id_card,
                        "cases": cases_list
                    })
                    LogUtils.info(f"添加聚类结果：{representative_name}({representative_id_card})，{len(cases_list)}个案件")

            LogUtils.info(f"构建聚类结果完成，共{len(all_clusters)}个符合条件的实体聚类")

            # 计算案件总数（直接从聚类结果中计算）
            total_cases = sum(len(cluster['cases']) for cluster in all_clusters)

            LogUtils.info(f"查询完成，返回第{page}页，共{len(all_clusters)}个聚类，总计{total_cases}个案件")
            return {
                "total": total_cases,
                "items": all_clusters,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"根据实体查询案件时出错: {str(e)}")
            raise Exception(f"根据实体查询案件时出错: {str(e)}")

    @staticmethod
    @transactional
    async def cluster_entities(page: int = 1, page_size: int = 10, start_time: Optional[str] = None, end_time: Optional[str] = None, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"开始进行人员实体聚类，页码: {page}, 每页数量: {page_size}, 开始时间: {start_time}, 结束时间: {end_time}")
        try:
            # 计算分页参数
            offset = (page - 1) * page_size

            # 构建时间过滤条件
            time_filter_sql = ""
            params = {
                'limit': page_size,
                'offset': offset
            }

            if start_time:
                try:
                    datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    time_filter_sql += " AND pr.police_time >= :start_time"
                    params['start_time'] = start_time
                except ValueError:
                    LogUtils.error(f"开始时间格式错误: {start_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"开始时间格式错误: {start_time}")

            if end_time:
                try:
                    datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    time_filter_sql += " AND pr.police_time <= :end_time"
                    params['end_time'] = end_time
                except ValueError:
                    LogUtils.error(f"结束时间格式错误: {end_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"结束时间格式错误: {end_time}")

            # 使用单个优化的SQL查询，参考search_cases_by_entity的风格
            LogUtils.info("执行优化的实体聚类SQL查询")

            main_query_sql = f"""
            WITH pivot_data AS (
                SELECT
                    qcf.case_id,
                    qcf.entity_id,
                    MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE
                FROM qz_case_feature qcf
                JOIN police_records pr ON qcf.case_id = pr.police_number
                WHERE pr.source = 0 {time_filter_sql}
                  AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE')
                  AND qcf.entity_id > 0  -- 排除案件级别信息
                GROUP BY qcf.case_id, qcf.entity_id
            ),
            -- 关键修复：智能聚类逻辑，同名人员应该合并
            person_cases AS (
                SELECT
                    ENTITY_NAME,
                    ID_CARD,
                    case_id,
                    entity_id,
                    ENTITY_TYPE
                FROM pivot_data
                WHERE ENTITY_NAME IS NOT NULL
            ),
            -- 为每个姓名找到代表性身份证（如果存在的话）
            name_id_mapping AS (
                SELECT
                    ENTITY_NAME,
                    MAX(ID_CARD) as representative_id_card  -- 取任意一个非空身份证作为代表
                FROM person_cases
                WHERE ID_CARD IS NOT NULL
                GROUP BY ENTITY_NAME
            ),
            -- 为所有人员分配统一的聚类键
            person_cases_with_cluster AS (
                SELECT
                    pc.ENTITY_NAME,
                    pc.ID_CARD,
                    pc.case_id,
                    pc.entity_id,
                    pc.ENTITY_TYPE,
                    -- 智能聚类键：同名人员使用相同的聚类键
                    CASE
                        WHEN nim.representative_id_card IS NOT NULL THEN nim.representative_id_card
                        ELSE CONCAT('NO_ID_CARD_', pc.ENTITY_NAME)
                    END as cluster_key
                FROM person_cases pc
                LEFT JOIN name_id_mapping nim ON pc.ENTITY_NAME = nim.ENTITY_NAME
            ),
            -- 找到至少出现在两个案件中的人员（按聚类键分组）
            persons_with_multiple_cases AS (
                SELECT
                    cluster_key,
                    ENTITY_NAME,
                    COUNT(DISTINCT case_id) as case_count
                FROM person_cases_with_cluster
                GROUP BY cluster_key, ENTITY_NAME
                HAVING COUNT(DISTINCT case_id) >= 2
            ),
            -- 获取这些人员的所有案件详情
            entity_clusters AS (
                SELECT
                    pc.cluster_key,
                    pc.case_id,
                    pc.entity_id,
                    pc.ID_CARD,
                    pc.ENTITY_NAME,
                    pc.ENTITY_TYPE,
                    -- 获取案件级别的信息（entity_id = 0）
                    (SELECT MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END)
                     FROM qz_case_feature qcf2
                     WHERE qcf2.case_id = pc.case_id AND qcf2.entity_id = 0) AS CASE_TYPE,
                    (SELECT MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END)
                     FROM qz_case_feature qcf3
                     WHERE qcf3.case_id = pc.case_id AND qcf3.entity_id = 0) AS LOCATION,
                    pr.police_time
                FROM person_cases_with_cluster pc
                JOIN persons_with_multiple_cases pwmc ON pc.cluster_key = pwmc.cluster_key
                JOIN police_records pr ON pc.case_id = pr.police_number
                WHERE pr.source = 0 {time_filter_sql}
            ),
            cluster_summary AS (
                SELECT
                    cluster_key,
                    MAX(CASE WHEN ID_CARD IS NOT NULL THEN ID_CARD END) as representative_id_card,
                    MAX(ENTITY_NAME) as representative_name,
                    COUNT(DISTINCT case_id) as case_count,
                    GROUP_CONCAT(
                        CONCAT(case_id, '|', COALESCE(ENTITY_TYPE, 'null'), '|',
                               COALESCE(DATE_FORMAT(police_time, '%Y-%m-%d %H:%i:%s'), ''), '|',
                               COALESCE(CASE_TYPE, ''), '|',
                               COALESCE(LOCATION, ''))
                        ORDER BY police_time DESC
                        SEPARATOR ';;'
                    ) as cases_info
                FROM entity_clusters
                GROUP BY cluster_key
                HAVING case_count >= 2
            )
            SELECT * FROM cluster_summary
            ORDER BY case_count DESC
            LIMIT :limit OFFSET :offset
            """

            LogUtils.info("SQL的参数: " + str(params))
            LogUtils.info("时间过滤条件: " + (time_filter_sql if time_filter_sql else "无时间过滤"))
            LogUtils.debug(f"完整SQL查询:\n{main_query_sql}")

            result = db.execute(text(main_query_sql), params)
            clusters = result.fetchall()

            # 调试：打印查询结果
            LogUtils.info(f"SQL查询返回的原始结果: {len(clusters)}个聚类")
            if clusters:
                LogUtils.debug("聚类详情:")
                for i, cluster in enumerate(clusters):
                    cluster_key = cluster[0]
                    representative_id_card = cluster[1]
                    representative_name = cluster[2]
                    case_count = cluster[3]
                    LogUtils.debug(f"  聚类{i+1}: {representative_name}({representative_id_card}) - {case_count}个案件 - 聚类键: {cluster_key}")
            else:
                LogUtils.info("未找到任何聚类结果")

            # 如果没有结果，返回空
            if not clusters:
                LogUtils.info("未找到符合条件的实体聚类")
                return {
                    "total": 0,
                    "items": [],
                    "page": page,
                    "page_size": page_size
                }

            LogUtils.info(f"找到{len(clusters)}个实体聚类")

            # 解析查询结果并构建返回数据
            LogUtils.info("开始解析聚类结果并构建返回数据")
            all_clusters = []
            for i, cluster in enumerate(clusters):
                cluster_key = cluster[0]
                representative_id_card = cluster[1]
                representative_name = cluster[2]
                case_count = cluster[3]
                cases_info = cluster[4]

                LogUtils.debug(f"处理聚类{i+1}: {representative_name}, 原始案件信息: {cases_info[:100] if cases_info else 'None'}...")

                # 解析案件信息字符串
                cases_list = []
                if cases_info:
                    case_items = cases_info.split(';;')
                    LogUtils.debug(f"  分割后得到{len(case_items)}个案件项")

                    for j, case_item in enumerate(case_items):
                        parts = case_item.split('|')
                        LogUtils.debug(f"    案件{j+1}: {len(parts)}个字段 - {case_item}")

                        if len(parts) >= 5:
                            case_id = parts[0]
                            entity_type = parts[1] if parts[1] != 'null' else None
                            police_time = parts[2] if parts[2] else None
                            case_type = parts[3] if parts[3] else None
                            location = parts[4] if parts[4] else None

                            case_data = {
                                "role": [entity_type] if entity_type else [],
                                "case": case_id,
                                "police_time": police_time,
                                "case_type": case_type,
                                "location": location
                            }
                            cases_list.append(case_data)
                            LogUtils.debug(f"      解析结果: {case_id} - {case_type} - {entity_type}")
                        else:
                            LogUtils.warning(f"    案件{j+1}字段不足: {case_item}")

                # 确保至少有两个案件
                if len(cases_list) >= 2:
                    cluster_data = {
                        "name": representative_name,
                        "id_card": representative_id_card,
                        "cases": cases_list
                    }
                    all_clusters.append(cluster_data)
                    LogUtils.info(f"添加聚类结果：{representative_name}({representative_id_card})，{len(cases_list)}个案件")
                    LogUtils.debug(f"  聚类键: {cluster_key}, 案件列表: {[c['case'] for c in cases_list]}")
                else:
                    LogUtils.warning(f"跳过聚类{representative_name}：只有{len(cases_list)}个案件，少于最小要求2个")

            LogUtils.info(f"构建聚类结果完成，共{len(all_clusters)}个符合条件的实体聚类")

            # 计算案件总数（直接从聚类结果中计算）
            total_cases = sum(len(cluster['cases']) for cluster in all_clusters)

            LogUtils.info(f"查询完成，返回第{page}页，共{len(all_clusters)}个聚类，总计{total_cases}个案件")
            return {
                "total": total_cases,
                "items": all_clusters,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"人员实体聚类时出错: {str(e)}")
            raise Exception(f"人员实体聚类时出错: {str(e)}")
    @staticmethod
    @transactional
    async def search_cases_with_shared_entities(entities: List[Dict[str, Optional[str]]], case_type: str, page: int = 1, page_size: int = 10, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"根据多个实体和案件类型查询案件，实体: {[(e.get('id_card'), e.get('name')) for e in entities]}, 案件类型: {case_type}, 页码: {page}, 每页数量: {page_size}")
        try:
            # 使用单个优化的SQL查询，参考search_cases_by_entity的风格
            LogUtils.info("执行优化的共享实体案件查询SQL")

            # 计算分页参数
            offset = (page - 1) * page_size

            # 构建实体查询条件
            entity_conditions = []
            entity_params = {}

            for i, entity in enumerate(entities):
                name = entity.get("name")
                id_card = entity.get("id_card")

                if not name:
                    continue

                entity_params[f'name_{i}'] = name
                if id_card:
                    entity_params[f'id_card_{i}'] = id_card
                    entity_conditions.append(f"""
                        (ENTITY_NAME = :name_{i} AND
                         (:id_card_{i} IS NULL OR ID_CARD = :id_card_{i} OR ID_CARD IS NULL))
                    """)
                else:
                    entity_conditions.append(f"ENTITY_NAME = :name_{i}")

            if not entity_conditions:
                LogUtils.info("未提供有效的实体查询条件")
                return {"total": 0, "items": [], "page": page, "page_size": page_size}

            entity_condition_sql = " OR ".join(entity_conditions)

            # 主查询SQL，使用CTE优化
            main_query_sql = f"""
            WITH pivot_data AS (
                SELECT
                    case_id,
                    entity_id,
                    MAX(CASE WHEN feature_type = 'ID_CARD' THEN feature_value END) AS ID_CARD,
                    MAX(CASE WHEN feature_type = 'ENTITY_NAME' THEN feature_value END) AS ENTITY_NAME,
                    MAX(CASE WHEN feature_type = 'ENTITY_TYPE' THEN feature_value END) AS ENTITY_TYPE
                FROM qz_case_feature
                WHERE entity_id > 0  -- 排除案件级别信息
                GROUP BY case_id, entity_id
            ),
            target_entities AS (
                -- 找到目标实体：匹配输入的实体
                SELECT DISTINCT entity_id, case_id, ID_CARD, ENTITY_NAME, ENTITY_TYPE
                FROM pivot_data
                WHERE ({entity_condition_sql})
            ),
            case_level_info AS (
                -- 获取案件级别信息
                SELECT
                    case_id,
                    MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END) AS CASE_TYPE
                FROM qz_case_feature
                WHERE entity_id = 0
                GROUP BY case_id
            ),
            target_cases AS (
                -- 筛选指定类型的案件
                SELECT DISTINCT te.case_id
                FROM target_entities te
                JOIN case_level_info cli ON te.case_id = cli.case_id
                WHERE cli.CASE_TYPE = :case_type
            ),
            case_entity_mapping AS (
                -- 为每个案件统计包含的目标实体
                SELECT
                    te.case_id,
                    GROUP_CONCAT(DISTINCT te.ENTITY_NAME ORDER BY te.ENTITY_NAME SEPARATOR '|') as entity_names,
                    COUNT(DISTINCT te.ENTITY_NAME) as entity_count
                FROM target_entities te
                JOIN target_cases tc ON te.case_id = tc.case_id
                GROUP BY te.case_id
                HAVING entity_count >= 2  -- 至少包含2个目标实体
            ),
            valid_cases_with_details AS (
                -- 获取有效案件的详细信息
                SELECT
                    cem.case_id,
                    cli.CASE_TYPE,
                    pr.police_time,
                    -- 获取目标实体信息
                    GROUP_CONCAT(DISTINCT
                        CONCAT(te.entity_id, ':', COALESCE(te.ENTITY_NAME, ''), ':',
                               COALESCE(te.ID_CARD, ''), ':', COALESCE(te.ENTITY_TYPE, ''))
                        ORDER BY te.entity_id
                        SEPARATOR ';;'
                    ) as target_entities_info,
                    -- 获取其他涉及实体信息
                    GROUP_CONCAT(DISTINCT
                        CASE WHEN pd.entity_id NOT IN (SELECT entity_id FROM target_entities WHERE case_id = cem.case_id) THEN
                            CONCAT(pd.entity_id, ':', COALESCE(pd.ENTITY_NAME, ''), ':',
                                   COALESCE(pd.ID_CARD, ''), ':', COALESCE(pd.ENTITY_TYPE, ''))
                        END
                        ORDER BY pd.entity_id
                        SEPARATOR ';;'
                    ) as other_entities_info
                FROM case_entity_mapping cem
                JOIN case_level_info cli ON cem.case_id = cli.case_id
                LEFT JOIN police_records pr ON cem.case_id = pr.police_number
                LEFT JOIN target_entities te ON cem.case_id = te.case_id
                LEFT JOIN pivot_data pd ON cem.case_id = pd.case_id
                WHERE pr.source = 0
                GROUP BY cem.case_id, cli.CASE_TYPE, pr.police_time
            )
            SELECT
                case_id,
                CASE_TYPE,
                police_time,
                target_entities_info,
                other_entities_info
            FROM valid_cases_with_details
            ORDER BY police_time DESC
            LIMIT :limit OFFSET :offset
            """

            # 合并参数
            params = {
                'case_type': case_type,
                'limit': page_size,
                'offset': offset,
                **entity_params
            }

            LogUtils.info("SQL的参数: " + str(params))
            LogUtils.info("SQL过滤条件" + str(entity_condition_sql))
            LogUtils.debug(f"完整SQL查询:\n{main_query_sql}")

            # 执行主查询
            result = db.execute(text(main_query_sql), params)
            cases = result.fetchall()

            LogUtils.info(f"SQL查询返回的原始结果: {len(cases)}个案件")

            if not cases:
                LogUtils.info("未找到符合条件的案件")
                return {"total": 0, "items": [], "page": page, "page_size": page_size}

            # 解析查询结果并构建返回数据
            output_items = []
            for case in cases:
                case_id = case[0]
                case_type_val = case[1]
                police_time = case[2]
                target_entities_info = case[3]
                other_entities_info = case[4]

                # 解析目标实体信息
                roles = []
                if target_entities_info:
                    for entity_info in target_entities_info.split(';;'):
                        if entity_info.strip():
                            parts = entity_info.split(':')
                            if len(parts) >= 4:
                                entity_name = parts[1] if parts[1] else None
                                id_card = parts[2] if parts[2] else None
                                role = parts[3] if parts[3] else None

                                if entity_name:
                                    roles.append({
                                        "name": entity_name,
                                        "id_card": id_card,
                                        "role": role
                                    })

                # 解析其他涉及实体信息
                other_involved = []
                if other_entities_info:
                    for entity_info in other_entities_info.split(';;'):
                        if entity_info.strip():
                            parts = entity_info.split(':')
                            if len(parts) >= 4:
                                entity_id = parts[0] if parts[0] else None
                                entity_name = parts[1] if parts[1] else None
                                id_card = parts[2] if parts[2] else None
                                role = parts[3] if parts[3] else None

                                if entity_name:
                                    other_involved.append({
                                        "id": int(entity_id) if entity_id else None,
                                        "name": entity_name,
                                        "id_card": id_card,
                                        "role": role
                                    })

                output_items.append({
                    "case": case_id,
                    "case_type": case_type_val,
                    "roles": roles,
                    "other_involved": other_involved,
                    "police_time": police_time
                })

            # 计算总数（为了性能，这里使用当前结果数量作为近似值）
            # 在实际生产环境中，可以考虑添加单独的计数查询
            total_cases = len(output_items)

            LogUtils.info(f"search_cases_with_shared_entities查询完成，返回第{page}页，共{len(output_items)}条记录")
            return {
                "total": total_cases,
                "items": output_items,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"根据多个实体和案件类型查询案件时出错: {str(e)}")
            raise Exception(f"根据多个实体和案件类型查询案件时出错: {str(e)}")

    @staticmethod
    @transactional
    async def cluster_cases_with_shared_entities(page: int = 1, page_size: int = 10, start_time: Optional[str] = None, end_time: Optional[str] = None, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"开始进行具有共享实体的案件聚类，页码: {page}, 每页数量: {page_size}, 开始时间: {start_time}, 结束时间: {end_time}")
        try:
            # 时间过滤逻辑
            query_filters = [PoliceRecord.source == 0]
            if start_time:
                try:
                    start_time_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    query_filters.append(PoliceRecord.police_time >= start_time_dt)
                except ValueError as e:
                    LogUtils.error(f"开始时间格式错误: {start_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"开始时间格式错误: {start_time}")
            if end_time:
                try:
                    end_time_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    query_filters.append(PoliceRecord.police_time <= end_time_dt)
                except ValueError as e:
                    LogUtils.error(f"结束时间格式错误: {end_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"结束时间格式错误: {end_time}")

            # 子查询：筛选出至少出现在两个案件中的实体
            entities_with_multiple_cases = (
                db.query(QzCaseFeature.entity_id, QzCaseFeature.feature_value.label('entity_name'))
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .filter(*query_filters)
                .filter(QzCaseFeature.feature_type == 'ENTITY_NAME')
                .group_by(QzCaseFeature.entity_id, QzCaseFeature.feature_value)
                .having(func.count(distinct(QzCaseFeature.case_id)) > 1)
                .subquery()
            )
            entities_list = db.query(entities_with_multiple_cases).all()
            LogUtils.info(f"找到{len(entities_list)}个出现在多个案件中的实体")

            # 获取相关案件
            entity_cases = (
                db.query(QzCaseFeature.case_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(entities_with_multiple_cases, QzCaseFeature.entity_id == entities_with_multiple_cases.c.entity_id)
                .filter(*query_filters)
                .filter(QzCaseFeature.feature_type == 'ENTITY_NAME')
                .distinct()
                .subquery()
            )

            # 筛选案件类型
            target_cases = (
                db.query(QzCaseFeature.case_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .filter(
                    and_(
                        QzCaseFeature.feature_type == 'CASE_TYPE',
                        QzCaseFeature.case_id.in_(db.query(entity_cases.c.case_id)),
                        *query_filters
                    )
                )
                .distinct()
                .subquery()
            )

            # 查询所有相关实体
            all_involved_entities = (
                db.query(
                    QzCaseFeature.case_id,
                    QzCaseFeature.entity_id,
                    QzCaseFeature.feature_type,
                    QzCaseFeature.feature_value
                )
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(target_cases, QzCaseFeature.case_id == target_cases.c.case_id)
                .filter(
                    and_(
                        QzCaseFeature.feature_type.in_(['ID_CARD', 'ENTITY_NAME']),
                        *query_filters
                    )
                )
                .distinct()
                .subquery()
            )

            shared_entities_data = db.query(
                all_involved_entities.c.case_id,
                all_involved_entities.c.entity_id,
                all_involved_entities.c.feature_type,
                all_involved_entities.c.feature_value
            ).all()

            # 构建实体映射：(name, id_card) -> [(case_id, entity_id)]
            entity_map = defaultdict(list)
            name_to_id_cards = defaultdict(set)  # name -> set(id_card)
            for case_id, entity_id, feature_type, feature_value in shared_entities_data:
                if feature_type == 'ENTITY_NAME':
                    id_card = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ID_CARD'
                        )
                    ).scalar()
                    entity_map[(feature_value, id_card)].append((case_id, entity_id))
                    if id_card:
                        name_to_id_cards[feature_value].add(id_card)

            # 合并无身份证的实体
            final_entity_map = defaultdict(set)  # (name, id_card) -> set(case_id)
            for (name, id_card), case_entities in entity_map.items():
                if id_card is None:
                    matched_id_card = next(iter(name_to_id_cards[name]), None) if name in name_to_id_cards else None
                    if matched_id_card:
                        for case_id, _ in case_entities:
                            final_entity_map[(name, matched_id_card)].add(case_id)
                    else:
                        for case_id, _ in case_entities:
                            final_entity_map[(name, None)].add(case_id)
                else:
                    for case_id, _ in case_entities:
                        final_entity_map[(name, id_card)].add(case_id)

            # 筛选具有共享实体的案件
            case_entity_count = defaultdict(set)
            for (name, id_card), case_ids in final_entity_map.items():
                for case_id in case_ids:
                    case_entity_count[case_id].add((name, id_card))

            valid_cases = {case_id for case_id, entities in case_entity_count.items() if len(entities) >= 2}
            shared_entity_keys = [
                (name, id_card) for (name, id_card), case_ids in final_entity_map.items()
                if len(case_ids & valid_cases) >= 2
            ]
            LogUtils.info(f"找到{len(valid_cases)}个包含多个共享实体的案件，{len(shared_entity_keys)}个共享实体")

            # 筛选具有共享实体的案件
            cases_with_shared_entities = (
                db.query(QzCaseFeature.case_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(target_cases, QzCaseFeature.case_id == target_cases.c.case_id)
                .filter(
                    and_(
                        QzCaseFeature.feature_type == 'ENTITY_NAME',
                        QzCaseFeature.feature_value.in_([name for name, _ in shared_entity_keys]),
                        QzCaseFeature.case_id.in_(valid_cases),
                        *query_filters
                    )
                )
                .distinct()
                .subquery()
            )

            # 查询聚类案件的详细信息，包含所有实体
            clustered_cases_data = (
                db.query(
                    QzCaseFeature.case_id,
                    QzCaseFeature.entity_id,
                    QzCaseFeature.feature_type,
                    QzCaseFeature.feature_value,
                    PoliceRecord.police_time
                )
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(cases_with_shared_entities, QzCaseFeature.case_id == cases_with_shared_entities.c.case_id)
                .filter(
                    and_(
                        QzCaseFeature.feature_type.in_(['CASE_TYPE', 'ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD']),
                        *query_filters
                    )
                )
                .order_by(QzCaseFeature.case_id, QzCaseFeature.entity_id)
                .all()
            )

            cluster_data = defaultdict(lambda: {
                "involved": defaultdict(dict),
                "cases": defaultdict(dict)
            })

            for item in clustered_cases_data:
                case_id = item.case_id
                entity_id = item.entity_id
                police_time = item.police_time

                name = None
                id_card = None
                if item.feature_type == 'ENTITY_NAME':
                    name = item.feature_value
                    id_card = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ID_CARD'
                        )
                    ).scalar()
                elif item.feature_type == 'ID_CARD':
                    name = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ENTITY_NAME'
                        )
                    ).scalar()
                    id_card = item.feature_value
                elif item.feature_type == 'ENTITY_TYPE':
                    name = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ENTITY_NAME'
                        )
                    ).scalar()
                    id_card = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ID_CARD'
                        )
                    ).scalar()

                if item.feature_type == 'CASE_TYPE':
                    cluster_data[case_id]["cases"][case_id]["case_type"] = item.feature_value
                    cluster_data[case_id]["cases"][case_id]["case"] = case_id
                    cluster_data[case_id]["cases"][case_id]["police_time"] = police_time
                elif item.feature_type == 'ENTITY_NAME' and name:
                    cluster_data[case_id]["involved"][entity_id]["name"] = item.feature_value
                elif item.feature_type == 'ENTITY_TYPE' and name:
                    cluster_data[case_id]["involved"][entity_id]["role"] = item.feature_value
                elif item.feature_type == 'ID_CARD' and name:
                    cluster_data[case_id]["involved"][entity_id]["id_card"] = item.feature_value

            # 构建 clusters_by_persons_and_type
            clusters_by_persons_and_type = defaultdict(list)
            for case_id, data in cluster_data.items():
                case_type = data["cases"][case_id]["case_type"] if case_id in data["cases"] else None
                if not case_type:
                    continue

                # 构建 person_key，仅包含共享实体
                person_key = set()
                entity_id_to_key = {}
                for entity_id, person_info in data["involved"].items():
                    name = person_info.get("name")
                    id_card = person_info.get("id_card")
                    if name:
                        if id_card is None:
                            matched_id_card = next((i for n, i in shared_entity_keys if n == name and i is not None), None)
                            key = (name, matched_id_card if matched_id_card else None)
                        else:
                            key = (name, id_card)
                        if key in shared_entity_keys:
                            person_key.add(key)
                            entity_id_to_key[entity_id] = key

                if len(person_key) < 2:
                    continue

                cluster_key = (frozenset(person_key), case_type)

                if case_id in data["cases"]:
                    roles = []
                    seen_entities = set()
                    for entity_id, person_info in data["involved"].items():
                        name = person_info.get("name")
                        id_card = person_info.get("id_card")
                        role = person_info.get("role") or "null"  # 如果没有 role，设置为 "null"
                        if name and entity_id_to_key.get(entity_id) in person_key:
                            original_id_card = db.query(QzCaseFeature.feature_value).filter(
                                and_(
                                    QzCaseFeature.case_id == case_id,
                                    QzCaseFeature.entity_id == entity_id,
                                    QzCaseFeature.feature_type == 'ID_CARD'
                                )
                            ).scalar()
                            key = (name, original_id_card)
                            if key not in seen_entities:
                                seen_entities.add(key)
                                roles.append({
                                    "name": name,
                                    "id_card": original_id_card,
                                    "role": role
                                })

                    clusters_by_persons_and_type[cluster_key].append({
                        "case": data["cases"][case_id]["case"],
                        "police_time": data["cases"][case_id]["police_time"],
                        "other_involved": [],
                        "roles": roles
                    })

            # 构建 final_clusters
            final_clusters = []
            for (person_key, case_type), cases in clusters_by_persons_and_type.items():
                if len(cases) < 2:
                    continue

                involved_persons = []
                seen_entities = set()
                for name, id_card in person_key:
                    if (name, id_card) not in seen_entities:
                        seen_entities.add((name, id_card))
                        involved_persons.append({
                            "name": name,
                            "id_card": id_card
                        })

                for case in cases:
                    case_id = case["case"]
                    case_persons = set()
                    for entity_id, person_info in cluster_data[case_id]["involved"].items():
                        name = person_info.get("name")
                        id_card = person_info.get("id_card")
                        if name:
                            if id_card is None:
                                matched_id_card = next((i for n, i in shared_entity_keys if n == name and i is not None), None)
                                id_card = matched_id_card if matched_id_card else None
                            case_persons.add((name, id_card))

                    involved_person_set = set((p["name"], p["id_card"]) for p in involved_persons)

                    case["other_involved"] = [
                        {
                            "name": person_info.get("name"),
                            "id_card": person_info.get("id_card") or db.query(QzCaseFeature.feature_value).filter(
                                and_(
                                    QzCaseFeature.case_id == case_id,
                                    QzCaseFeature.entity_id == entity_id,
                                    QzCaseFeature.feature_type == 'ID_CARD'
                                )
                            ).scalar(),
                            "role": person_info.get("role") or "null"  # 如果没有 role，设置为 "null"
                        }
                        for entity_id, person_info in cluster_data[case_id]["involved"].items()
                        if person_info.get("name") and
                        (
                            person_info.get("name"),
                            person_info.get("id_card") or
                            next((i for n, i in shared_entity_keys if n == person_info.get("name") and i is not None), None)
                        ) not in involved_person_set
                    ]

                final_clusters.append({
                    "involved": involved_persons,
                    "case_type": case_type,
                    "cases": cases
                })

            total = len(final_clusters)
            offset = (page - 1) * page_size
            paginated_clusters = final_clusters[offset:offset + page_size]

            LogUtils.info(f"cluster_cases_with_shared_entities查询完成，共{total}个聚类，返回第{page}页，共{len(paginated_clusters)}条记录")
            return {
                "total": total,
                "items": paginated_clusters,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"具有共享实体的案件聚类时出错: {str(e)}")
            raise Exception(f"具有共享实体的案件聚类时出错: {str(e)}")

    @staticmethod
    @transactional
    async def search_cases_by_location(longitude: float, latitude: float, radius: float = 1000, page: int = 1, page_size: int = 10, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"根据位置查询案件，经度: {longitude}, 纬度: {latitude}, 半径: {radius}米, 页码: {page}, 每页数量: {page_size}")
        try:
            LogUtils.debug(f"输入参数: longitude={longitude}, latitude={latitude}, radius={radius}")

            spatial_condition_text = text(
                "ST_Distance_Sphere("
                "ST_SRID(ST_GeomFromText(CONCAT('POINT(', feature_value, ')')), 4326), "
                "ST_SRID(POINT(:longitude, :latitude), 4326)"
                ") <= :radius"
            )

            location_query = db.query(QzCaseFeature.case_id).\
                filter(
                    and_(
                        QzCaseFeature.feature_type == 'LOCATION',
                        QzCaseFeature.feature_value != None,
                        spatial_condition_text.params(
                            longitude=longitude,
                            latitude=latitude,
                            radius=radius
                        )
                    )
                ).\
                distinct().subquery()

            LogUtils.debug(f"SQL模板: {str(location_query)}")
            LogUtils.debug(f"绑定参数: longitude={longitude}, latitude={latitude}, radius={radius}")

            total = db.query(location_query).count()
            LogUtils.info(f"在半径{radius}米范围内找到{total}个案件")

            if total == 0:
                LogUtils.info("未找到符合位置条件的案件")
                return {
                    "total": 0,
                    "items": [],
                    "page": page,
                    "page_size": page_size
                }

            offset = (page - 1) * page_size
            paginated_case_ids = db.query(location_query).\
                offset(offset).limit(page_size).all()
            case_ids = [case_id for (case_id,) in paginated_case_ids]
            LogUtils.debug(f"分页后的案件ID: {case_ids}")

            case_details = db.query(QzCaseFeature, PoliceRecord.police_time).\
                join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number).\
                filter(
                    and_(
                        QzCaseFeature.case_id.in_(case_ids),
                        QzCaseFeature.feature_type.in_(['CASE_TYPE', 'ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD', 'LOCATION']),
                        PoliceRecord.source == 0
                    )
                ).\
                order_by(QzCaseFeature.case_id, QzCaseFeature.entity_id).\
                all()

            results = []
            current_case = None
            current_result = {}
            for item, police_time in case_details:
                if item.case_id != current_case:
                    if current_result:
                        results.append(current_result)
                    current_case = item.case_id
                    current_result = {
                        "case": item.case_id,
                        "case_type": None,
                        "location": None,
                        "entities": [],
                        "police_time": police_time
                    }

                if item.feature_type == 'CASE_TYPE':
                    current_result["case_type"] = item.feature_value
                elif item.feature_type == 'LOCATION':
                    current_result["location"] = item.feature_value
                elif item.feature_type in ['ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD']:
                    entity = next((e for e in current_result["entities"] if e["entity_id"] == item.entity_id), None)
                    if not entity:
                        entity = {"entity_id": item.entity_id, "name": None, "id_card": None, "role": None}
                        current_result["entities"].append(entity)

                    if item.feature_type == 'ENTITY_NAME':
                        entity["name"] = item.feature_value
                    elif item.feature_type == 'ENTITY_TYPE':
                        entity["role"] = item.feature_value
                    elif item.feature_type == 'ID_CARD':
                        entity["id_card"] = item.feature_value

            if current_result:
                results.append(current_result)

            return {
                "total": total,
                "items": results,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"根据位置查询案件时出错: {str(e)}")
            raise Exception(f"根据位置查询案件时出错: {str(e)}")

    @staticmethod
    @transactional
    async def get_periodic_stats(start_time: Optional[str] = None, end_time: Optional[str] = None, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"获取周期性案件统计信息，开始时间: {start_time}, 结束时间: {end_time}")
        try:
            cluster_entities_result = await SceneQzService.cluster_entities(page=1, page_size=10000, start_time=start_time, end_time=end_time, db=db)
            cluster_entities_num = cluster_entities_result["total"]

            cluster_cases_result = await SceneQzService.cluster_cases_with_shared_entities(page=1, page_size=1, start_time=start_time, end_time=end_time, db=db)
            cluster_cases_num = cluster_cases_result["total"]

            #TODO: 一地多起的total返回
            cluster_locations_num = 0

            return {
                "cluster_entities_num": cluster_entities_num,
                "cluster_cases_num": cluster_cases_num,
                "cluster_locations_num": cluster_locations_num
            }
        except Exception as e:
            LogUtils.error(f"获取周期性案件统计信息时出错: {str(e)}")
            raise Exception(f"获取周期性案件统计信息时出错: {str(e)}")



    @transactional
    async def scene_qz_data_sync(self, user_id: int, start_date: str, end_date: str, db: Session = None) -> bool:
        """同步案件数据"""

        LogUtils.info(f"开始同步数据，用户ID: {user_id}, 查询时间范围: {start_date} 至 {end_date}")
        try:
            # 查询行政案件和刑事案件编号
            def convert_start_date(date_str):
                """转换为当天零点"""
                dt = datetime.strptime(date_str, "%Y%m%d")
                return dt.strftime("%Y%m%d%H%M%S")

            def convert_end_date(date_str):
                """转换为当天最后一秒"""
                dt = datetime.strptime(date_str, "%Y%m%d")
                end_time = dt.replace(hour=23, minute=59, second=59)
                return end_time.strftime("%Y%m%d%H%M%S")

            start_date = convert_start_date(start_date)
            end_date = convert_end_date(end_date)
            """查询符合条件的案件编号和id"""
            sql = text("""

                SELECT id, police_number
                FROM police_records
                WHERE alarm_time BETWEEN :start_date AND :end_date
                AND source=0
            """)
            params = {

                'start_date': start_date,
                'end_date': end_date
            }
            result = db.execute(sql, params)
            rows = result.fetchall()
            ids = [row.id for row in rows]
            police_numbers = [row.police_number for row in rows]

            # 将查询到的数据聚合到字典
            task_datas = []
            for id, police_number in zip(ids, police_numbers):
                task_data = {
                    "id": id,
                    "police_number": police_number,

                }
                task_datas.append(task_data)

            # 将所有任务添加到统一队列
            for task_data in task_datas:
                sync_result = await self.queue_manager.add_task(self.queue_name, task_data)
                if not sync_result:
                    return False

            return True

        except Exception as e:
            LogUtils.error(f"同步过程出错: {str(e)}")
            return False

    @transactional
    async def _sync(self, task_data: dict, db: Session = None):
        id = task_data['id']
        police_number = task_data["police_number"]

        try:
            # 执行处警信息提取流程
            await self._dify_process(police_number, db)

        except Exception as e:
            LogUtils.error(f"dify流程 scene_qz_record_id: {id}: {str(e)}")




    @transactional
    async def _dify_process(self, police_number: str, db: Session = None):
        # 使用aiohttp进行异步HTTP请求
        timeout = aiohttp.ClientTimeout(total=180)  # 设置30秒总超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            headers = {
                'Authorization': f'Bearer {settings.SCENE_QZ_API_KEY}',
            }

            json_data = {
                'inputs': {'ajbh': f'{police_number}'},
                'response_mode': 'blocking',
                'user': 'abc-123',
            }

            is_error = False
            try:
                async with session.post(
                        f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                        headers=headers,
                        json=json_data,
                        timeout=timeout  # 为单个请求设置超时
                ) as response:
                    if response.status == 200:
                        # 获取响应数据
                        response_data = await response.json()
                        if response_data['data']['status'] != 'succeeded':
                            is_error = True
                    else:
                        is_error = True
            except asyncio.TimeoutError:
                LogUtils.error(f"任务scene_qz_record_id: {id} API请求超时")
                is_error = True

            if is_error:
                LogUtils.info(f"任务scene_qz_record_id: {id} 同步失败")

        LogUtils.info(f"任务scene_qz_record_id: {id} 处理完成")




_instance = None


async def get_scene_qz_service() -> SceneQzService:
    global _instance
    if not _instance:
        _instance = SceneQzService()
        await _instance.async_init()
    return _instance
