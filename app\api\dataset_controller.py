
from typing import Annotated

from fastapi import  APIRouter, File,UploadFile
import os
from app.common.response_com import create_response
from app.config import upload_file_config
from app.services.dataset_service import DatasetService, get_dataset_service

router = APIRouter()


#工作流处理试题集转QA
@router.post("/dataset/exam/workflow/upload")
async def examToQA(file: Annotated[UploadFile, File(description="上传文件")]):
    try:
        validate_file(file)

        dataset_service = await get_dataset_service()
        await dataset_service.examToQA(file)

        return create_response(
            message="试题集转换QA请求成功",
            code=200,
            data=''
        )
    except Exception as e:
        return create_response(
            message=f"试题集转换成QA:{str(e)}",
            code=500
        )

#工作流处理法律文书转QA
@router.post("/dataset/law/workflow/upload")
async def lawToQA(file: Annotated[UploadFile, File(description="上传文件")]):
    try:
        validate_file(file)

        dataset_service = await get_dataset_service()
        await dataset_service.lawToQA(file)

        return create_response(
            message="法律文书转换QA请求成功",
            code=200,
            data=''
        )
    except Exception as e:
        return create_response(
            message=f"法律文书转换成QA:{str(e)}",
            code=500
        )

def validate_file(file: UploadFile):
    """文件验证逻辑"""
    # 检查文件类型
    ext = file.filename.split('.')[-1].lower()

    if ext=="doc":
        raise Exception(400, f"请将doc格式文档另存为docx格式")
    if ext not in upload_file_config["allowed_types"]:
        raise Exception(400, f"不支持的文件类型 {ext}")

    # 检查文件大小
    file.file.seek(0, os.SEEK_END)
    file_size = file.file.tell()
    file.file.seek(0)
    if file_size > upload_file_config["max_file_size"]:
        raise Exception(413, "文件过大")



#数据集QA获取模型思考过程
@router.post("/dataset/reasoning/workflow")
async def getReasoning():
    try:
        dataset_service = await get_dataset_service()
        await dataset_service.getReasoning()

        return create_response(
            message="数据集QA获取模型思考过程开始",
            code=200,
            data=''
        )
    except Exception as e:
        return create_response(
            message=f"数据集QA获取模型思考过程:{str(e)}",
            code=500
        )