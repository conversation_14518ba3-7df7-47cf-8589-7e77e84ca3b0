<template>
  <div class="login-container">
    <!-- 添加粒子效果容器 -->
    <div class="particle-container">
      <canvas class="rain"></canvas>
      <canvas class="dashed"></canvas>
    </div>

    <div class="title">
      <img src="@/assets/images/gongan.png" alt="公安Logo" class="logo" />
      <h1>法制监督智能助手</h1>
      <div class="subtitle">· DeepSeek 智能辅助平台 ·</div>
    </div>

    <div class="login-box">
      <div class="bg_bottom">
        <div class="login-box-header">
          <img
            class="login-box-header-left"
            src="@/assets/images/svgs/login_register_head_1.svg"
            alt=""
          />
          <div class="login-box-header-context">欢迎登录</div>
          <img
            class="login-box-header-right"
            src="@/assets/images/svgs/login_register_head_1.svg"
            alt=""
          />
        </div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          label-width="0"
          class="login-form"
        >
          <el-form-item prop="police_number">
            <el-input
              v-model="loginForm.police_number"
              placeholder="警号"
              clearable
              class="custom-input"
            >
              <img slot="prefix" src="@/assets/images/icons/icon-register_jh.svg" alt="" />
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <PassInput
              v-model="loginForm.password"
              placeholder="密码"
              clearable
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>
          <el-form-item>
            <el-button :loading="loading" type="primary" class="login-button" @click="handleLogin">
              登录
            </el-button>
          </el-form-item>
          <!-- <div class="mock-login">
          <el-button 
            type="text" 
            class="mock-button" 
            @click="handleMockLogin"
            :loading="mockLoading"
          >
            <i class="el-icon-cpu"></i> 模拟登录
          </el-button>
        </div> -->
          <div class="login-options">
            <router-link to="/register" class="register-link">注册账号</router-link>
            <a :href="tutorialUrl" class="tutorial-link" target="_blank">
              <span>使用教程&nbsp;&nbsp;</span>
              点击下载
            </a>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import PassInput from "@/components/PassInput.vue";
import { setToken } from "@/utils/auth";

export default {
  name: "Login",
  components: { PassInput },
  data() {
    return {
      loginForm: {
        police_number: "",
        password: "",
      },
      loginRules: {
        police_number: [{ required: true, message: "请输入警号", trigger: "blur" }],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "密码长度至少为6个字符", trigger: "blur" },
        ],
      },
      loading: false,
      mockLoading: false,
      rainCtx: null,
      dashedCtx: null,
      animationFrame: null,
    };
  },
  computed: {
    tutorialUrl() {
      return `${window.location.origin}/downloads/法制智能监督助手操作手册.doc`;
    },
  },
  mounted() {
    this.initParticleEffect();
  },
  beforeDestroy() {
    this.stopParticleEffect();
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          this.loading = true;
          try {
            const res = await this.$store.dispatch("login", this.loginForm);
            console.log("登录API返回数据:", res);
            if (res.code === 200) {
              setToken(res.data.access_token);
              this.$store.commit("SET_USER", res.data.user);
              this.$store.commit("SET_USER_ROLE", res.data.userRole ? res.data.userRole : []);
              this.$message({
                message: "登录成功",
                type: "success",
              });
              this.$router.push("/");
            } else {
              this.$message.error(res.message || "登录失败");
            }
          } catch (error) {
            this.$message.error("登录失败，请稍后重试");
            console.error(error);
          } finally {
            this.loading = false;
          }
        }
      });
    },
    // 模拟登录处理函数
    async handleMockLogin() {
      this.mockLoading = true;
      try {
        // 模拟用户数据 - 修改为与API格式一致
        const mockUserData = {
          access_token: "mock_token_" + Date.now(),
          user: {
            id: 1,
            username: "admin",
            email: "<EMAIL>",
            role: "admin",
          },
        };

        // 模拟登录成功响应
        const mockResponse = {
          code: 200,
          message: "登录成功",
          data: mockUserData,
        };

        console.log("模拟登录数据:", mockResponse);

        // 正确处理模拟登录：通过覆盖store中的login方法
        // 手动提交mutation来设置认证状态
        setToken(mockUserData.access_token);
        this.$store.commit("SET_USER", mockUserData.user);

        // 显示登录成功提示
        this.$message({
          message: "模拟登录成功",
          type: "success",
        });

        // 登录成功后跳转到首页
        this.$router.push("/");
      } catch (error) {
        this.$message.error("模拟登录失败");
        console.error(error);
      } finally {
        this.mockLoading = false;
      }
    },
    initParticleEffect() {
      const rainCanvas = document.querySelector(".rain");
      const dashedCanvas = document.querySelector(".dashed");

      if (!rainCanvas || !dashedCanvas) return;

      const rainCtx = rainCanvas.getContext("2d");
      const dashedCtx = dashedCanvas.getContext("2d");

      // 设置画布大小
      const resizeCanvas = () => {
        const container = document.querySelector(".particle-container");
        if (!container) return;

        const rect = container.getBoundingClientRect();
        rainCanvas.width = rect.width;
        rainCanvas.height = rect.height;
        dashedCanvas.width = rect.width;
        dashedCanvas.height = rect.height;
      };

      resizeCanvas();
      window.addEventListener("resize", resizeCanvas);

      // 粒子效果
      class Particle {
        constructor() {
          this.init();
        }

        init() {
          this.x = Math.random() * rainCanvas.width;
          this.y = rainCanvas.height; // 从底部开始
          this.vy = -(Math.random() * 1 + 1); // 向上移动的速度
          this.size = Math.random() * 2 + 1; // 随机大小
          this.alpha = Math.random() * 0.5 + 0.5; // 随机透明度
          this.color = this.getRandomColor();
        }

        getRandomColor() {
          const colors = [
            { r: 0, g: 255, b: 255 }, // 青色
            { r: 0, g: 195, b: 255 }, // 浅蓝色
            { r: 20, g: 120, b: 156 }, // 深蓝色
          ];
          const color = colors[Math.floor(Math.random() * colors.length)];
          return `rgba(${color.r}, ${color.g}, ${color.b}, ${this.alpha})`;
        }

        draw() {
          rainCtx.beginPath();
          const gradient = rainCtx.createLinearGradient(
            this.x,
            this.y,
            this.x,
            this.y - this.size * 10
          );
          gradient.addColorStop(0, this.color);
          gradient.addColorStop(1, "rgba(0, 255, 255, 0)");
          rainCtx.fillStyle = gradient;
          rainCtx.fillRect(this.x, this.y, this.size, -this.size * 10); // 向上延伸的光效
          rainCtx.closePath();

          this.update();
        }

        update() {
          this.y += this.vy;

          // 如果粒子超出顶部，重新初始化
          if (this.y < 0) {
            this.init();
          }
        }
      }

      // 初始化粒子
      const particles = [];
      const particleCount = 50; // 粒子数量

      for (let i = 0; i < particleCount; i++) {
        setTimeout(() => {
          const particle = new Particle();
          particles.push(particle);
        }, i * 100); // 错开创建时间
      }

      // 虚线效果
      const drawDashedLines = () => {
        const w = dashedCanvas.width;
        const h = dashedCanvas.height;

        dashedCtx.lineWidth = 2;
        dashedCtx.setLineDash([4, 4]);
        dashedCtx.shadowOffsetX = 0;
        dashedCtx.shadowOffsetY = 0;
        dashedCtx.shadowColor = "#00ffff";
        dashedCtx.shadowBlur = 10;

        // 第一条曲线
        dashedCtx.beginPath();
        let grd = dashedCtx.createLinearGradient((w / 11) * 2, h / 3, (w / 5) * 2, h);
        grd.addColorStop(0, "#00ffff");
        grd.addColorStop(1, "#065261");
        dashedCtx.strokeStyle = grd;
        dashedCtx.moveTo((w / 5) * 2, h);
        dashedCtx.quadraticCurveTo(w / 5, (h / 6) * 5, (w / 11) * 2, h / 3);
        dashedCtx.stroke();

        // 第二条曲线
        dashedCtx.beginPath();
        grd = dashedCtx.createLinearGradient((w / 11) * 3.3, h / 2, (w / 3) * 1.1, (h / 6) * 5);
        grd.addColorStop(0, "#00c3ff");
        grd.addColorStop(1, "#0a4f6c");
        dashedCtx.strokeStyle = grd;
        dashedCtx.moveTo((w / 3) * 1.1, (h / 6) * 5);
        dashedCtx.quadraticCurveTo((w / 5) * 1.5, (h / 6) * 4.2, (w / 11) * 3.3, h / 2);
        dashedCtx.stroke();

        // 第三条曲线
        dashedCtx.beginPath();
        grd = dashedCtx.createLinearGradient((w / 3) * 1.4, h / 5, (w / 5) * 2, h / 2);
        grd.addColorStop(0, "#00c3ff");
        grd.addColorStop(1, "#0a4f6c");
        dashedCtx.strokeStyle = grd;
        dashedCtx.moveTo((w / 5) * 2, h / 2);
        dashedCtx.quadraticCurveTo((w / 3) * 1.2, (h / 4) * 1.4, (w / 3) * 1.4, h / 5);
        dashedCtx.stroke();
      };

      // 动画循环
      const animate = () => {
        rainCtx.clearRect(0, 0, rainCanvas.width, rainCanvas.height);
        dashedCtx.clearRect(0, 0, dashedCanvas.width, dashedCanvas.height);

        // 绘制粒子
        particles.forEach(particle => particle.draw());

        // 绘制虚线
        // drawDashedLines()

        this.animationFrame = requestAnimationFrame(animate);
      };

      animate();
    },
    stopParticleEffect() {
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame);
        this.animationFrame = null;
      }
      window.removeEventListener("resize", this.resizeCanvas);
    },
  },
};
</script>

<style lang="less" scoped>
.login-container {
  width: 100%;
  height: 100vh;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  flex-direction: column;
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  pointer-events: none;
}

.dashed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  pointer-events: none;
}

.login-box {
  width: 500px;
  background: #07182799;
  border: 1px solid #04d8be9e;
  border-radius: 2px;
  /* box-shadow: 0 0 30px rgba(0, 255, 255, 0.2),
              inset 0 0 40px rgba(0, 150, 255, 0.08); */
  backdrop-filter: blur(10px);
  z-index: 2;
  position: relative;

  .bg_bottom {
    width: 100%;
    padding: 40px;
    background-image: url("@/assets/images/svgs/box_bg_bottom.svg");
    background-size: 99%;
    background-position: 5px 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;

    &::before {
      content: "";
      position: absolute;
      right: -1px;
      bottom: 0;
      width: 1px;
      height: 22px;
      background-color: #071827d2;
    }

    &::after {
      content: "";
      position: absolute;
      right: 0;
      bottom: -1px;
      width: 22px;
      height: 1px;
      background-color: #071827d2;
    }
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -2px;
    right: -2px;
    bottom: 0;
    border-radius: 3px;
    padding: 3px;
    background: linear-gradient(
      180deg,
      rgba(0, 255, 255, 0) 11%,
      #00ffff 42%,
      rgba(0, 255, 255, 0) 79%
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  &::after {
    content: "";
    position: absolute;
    top: -1.5px;
    left: 0;
    right: 0;
    /* bottom: 0; */
    border-radius: 3px;
    padding: 1.5px;
    background: linear-gradient(
      90deg,
      rgba(0, 255, 255, 0) 0%,
      #00ffff 50%,
      rgba(0, 255, 255, 0) 100%
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }
}

.login-box-header {
  width: 100%;
  height: 40px;
  z-index: 5;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-box-header > :nth-child(1) {
  margin-left: -28px;
}

.login-box-header > :nth-child(2) {
  height: 100%;
  /* flex: 1; */
  background: url("@/assets/images/svgs/login_register_head_2.svg") repeat center;
  text-align: center;
  font-size: 16px;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  padding: 0 28px;
  line-height: 230%;
}

.login-box-header img {
  height: 100%;
}

.login-box-header > :nth-child(3) {
  /* 水平镜像翻转 */
  transform: scaleX(-1);
  margin-right: -28px;
}

.title {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
  /* filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5)); */
}

.title h1 {
  font-weight: 600;
  font-size: 28px;
  letter-spacing: 3px;
  margin-bottom: 3px;
  background: #00ffff;
  background-image: linear-gradient(180deg, #10e7ef 0%, #1299ef 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.subtitle {
  color: rgba(0, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 5px;
  letter-spacing: 1px;
}

.login-form {
  margin-top: 40px;
  &::after {
    content: "";
    position: absolute;
    bottom: 10px;
    right: -5px;
    width: 33px;
    height: 1px;
    background-color: #04d8be9e;
    transform: rotate(-45deg);
  }
}

:deep(.custom-input .el-input__inner) {
  background: #09354d !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  color: #fff !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 16px !important;
  border-radius: 4px !important;
  padding-left: 48px !important;
  transition: all 0.3s !important;
}

:deep(.custom-input .el-input__inner):focus {
  border-color: rgba(0, 255, 255, 0.6) !important;
}

:deep(.custom-input .el-input__icon) {
  color: rgba(0, 255, 255, 0.8) !important;
  font-size: 18px !important;
}

:deep(.custom-input .el-input__inner):-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  color: #fff !important;
  background-color: #09354d !important;
  font-size: 12px !important;
  transition: all 600s ease-in-out 0s !important;
}

:deep(.custom-input .el-input__prefix) {
  left: 12px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

:deep(.custom-input .el-input__prefix img) {
  height: 50%;
}

.login-button {
  width: 100%;
  margin-top: 15px;
  background: #00ffff !important;
  color: #071827;
  border: none !important;
  height: 48px !important;
  font-size: 16px !important;
  letter-spacing: 2px !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border-radius: 4px !important;
}

.login-button:hover {
  color: white;
  background: linear-gradient(90deg, #00a2f8, #00d4ff) !important;
  transform: translateY(-2px);
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.mock-login {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.mock-button {
  color: rgba(0, 255, 255, 0.8) !important;
  font-size: 14px !important;
  padding: 8px 15px !important;
  background: rgba(0, 36, 89, 0.6) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 4px !important;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.1) !important;
  transition: all 0.3s !important;
}

.mock-button:hover {
  color: #00ffff !important;
  background: rgba(0, 36, 89, 0.8) !important;
  border-color: rgba(0, 255, 255, 0.4) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
  transform: translateY(-1px);
}

.mock-button i {
  margin-right: 5px;
  font-size: 16px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  text-align: center;
  margin-top: -10px;
  font-size: 14px;
}

.register-link,
.reset-link,
.tutorial-link {
  color: #06c0c6;
  text-decoration: none;
  transition: all 0.3s;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  padding: 5px 0px;
  border-radius: 4px;
  border: 1px solid transparent;
}

.tutorial-link span {
  margin-right: 3px;
  color: #ffffffb3;
}

/* 自定义表单验证错误信息样式 */
.el-form-item__error {
  color: #f56c6c !important;
  text-shadow: 0 0 5px rgba(245, 108, 108, 0.3) !important;
}
</style>
