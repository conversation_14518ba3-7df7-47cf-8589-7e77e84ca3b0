/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  console.log("处理数据:",data)
  let config = {
    id: id || "id",
    parentId: parentId || "parent_id",
    children: children || "children",
  };

  var childrenListMap = {};
  var tree = [];
  for (let d of data) {
    d[config.children]=[]
    let id = d[config.id];
    childrenListMap[id] = d;
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    let parentObj = childrenListMap[parentId];
    
    if (!parentObj) {
      tree.push(d);
    } else {
      console.log("xxxx",parentObj)
      parentObj[config.children].push(d);
    }
  }
  return tree;
}
// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}


// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params;
  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (typeof (propName) === 'undefined') {
    search.params['beginTime'] = dateRange[0];
    search.params['endTime'] = dateRange[1];
  } else {
    search.params['begin' + propName] = dateRange[0];
    search.params['end' + propName] = dateRange[1];
  }
  return search;
}
