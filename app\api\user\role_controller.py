from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.permission_check import require_permissions
from app.db.database import get_db
from app.schemas.role_schema import (
    RoleCreate, RoleUpdate, RoleQueryDto, MenuAuthRequest
)
from app.services.user.role_service import getRoleList, getRoleById, RoleSave, RoleUp, deleteRole, menuAuthorization, \
    menuAuthorizationCodeCheck

router = APIRouter(prefix="/role", tags=["role"])


@router.post("/list")
@require_permissions(permission_key="role:list")
def get_roles(request: RoleQueryDto, db: Session = Depends(get_db)):
    """获取角色列表"""
    result = getRoleList(db, request)
    return create_response(code=200, data=result)


@router.get("/{role_id}")
@require_permissions(permission_key="role:byId")
def get_role(role_id: int, db: Session = Depends(get_db)):
    """获取角色详情"""
    return getRoleById(db, role_id)



@router.post("/save")
@require_permissions(permission_key="role:save")
def create_role(role: RoleCreate, db: Session = Depends(get_db)):
    """创建角色"""
    return RoleSave(db, role)



@router.post("/update")
@require_permissions(permission_key="role:update")
def update_role(role: RoleUpdate, db: Session = Depends(get_db)):
    """更新角色"""
    return RoleUp(db, role)



@router.delete("/delete/{role_id}")
@require_permissions(permission_key="role:delete")
def delete_role(role_id: int, db: Session = Depends(get_db)):
    """删除角色"""
    return  deleteRole(db, role_id)



@router.post("/menu_authorization")
@require_permissions(permission_key="role:menu_auth")
def menu_authorization( request:MenuAuthRequest, db: Session = Depends(get_db)):
    """角色与菜单"""
    return menuAuthorization(db,request)


@router.get("/menu_code_check/{role_id}")
@require_permissions(permission_key="role:menu_code_check")
def menu_authorization_code_check( role_id:int, db: Session = Depends(get_db)):
    """反显角色与菜单"""
    return menuAuthorizationCodeCheck(db,role_id)

