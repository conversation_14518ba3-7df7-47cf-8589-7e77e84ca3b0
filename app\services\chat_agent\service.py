"""
智能体服务实现
"""
import json
from typing import Dict, Any, Optional

from sqlalchemy.orm import Session

from app.core.context_handle import UserContext
from app.log.log_utils import LogUtils
from app.models.chat import ChatAgent, DocFeature, ChatAgentsMsg
from app.services.chat_agent.agents.base_agent import BaseAgentTemplate
from app.services.chat_agent.base import AgentType, UserRoleType
from app.services.chat_agent.decorators.capability_decorators import RoleBasedCapabilityDecorator
from app.services.chat_agent.handlers.interfaces import (
    KnowledgeBaseHandler,
    QueryProcessingHandler,
    SessionHandler
)
from app.services.chat_agent.registry import AgentRegistry


class AgentService:
    """智能体服务，实现门面模式"""

    def __init__(self,
                 db: Session,
                 registry: AgentRegistry,
                 kb_service: Any,  # KnowledgeBaseService
                 query_service: Any,  # QueryProcessingService
                 session_service: Any):  # SessionService
        self.db = db
        self.registry = registry
        self.kb_service = kb_service
        self.query_service = query_service
        self.session_service = session_service
        self._agent_cache = {}  # agent_id -> agent_instance

    async def get_agent_instance(self, agent_id: str, user_context=None) -> Optional[BaseAgentTemplate]:
        """
        获取智能体实例

        Args:
            agent_id: 智能体ID
            user_context: 用户信息

        Returns:
            智能体实例或None
        """
        # 检查缓存
        if user_context is None:
            user_context = {}
        if agent_id in self._agent_cache:
            return self._agent_cache[agent_id]

        # 从数据库获取智能体配置
        agent_config = await self._get_agent_config(agent_id)
        if agent_config is None:
            return None

        # 创建核心组件
        kb_handler = await self._create_kb_handler(agent_config)
        query_processor = await self._create_query_processor(agent_config)
        session_handler = await self._create_session_handler(agent_config)

        # 创建智能体实例
        agent = self.registry.create_agent(
            agent_config, kb_handler, query_processor, session_handler, self.db
        )

        # 缓存智能体实例
        if agent is not None:
            # 增加装饰器功能
            self._agent_cache[agent_id] = self._get_decorated_agent(agent, user_context)

        return agent

    def _get_decorated_agent(self, agent_instance, user_context):
        """
        装饰器

        Args:
            agent_instance: 智能体ID
            user_context: 角色权限

        Returns:
            智能体实例或None
        """
        # 从内到外组合装饰器（执行顺序与装饰顺序相反）

        # 1. 首先应用基础Agent实例
        decorated_agent = agent_instance

        # 2. 添加性能监控装饰器（最内层）
        # 性能监控应该在最内层，可以准确测量核心业务逻辑的性能
        decorated_agent = RoleBasedCapabilityDecorator(decorated_agent, user_context)

        return decorated_agent

    async def _get_agent_config(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """从数据库获取智能体配置"""
        agent = self.db.query(ChatAgent).filter(ChatAgent.agent_id == agent_id).first()
        if agent is None:
            LogUtils.warning(f"智能体不存在: {agent_id}")
            return None

        # 使用to_dict方法并添加agent_type字段
        config = agent.to_dict()
        return config

    async def _create_kb_handler(self, agent_config: Dict[str, Any]) -> KnowledgeBaseHandler:
        """创建知识库处理器"""
        return await self.kb_service.create_handler_for_agent(agent_config)

    async def _create_query_processor(self, agent_config: Dict[str, Any]) -> QueryProcessingHandler:
        """创建查询处理器"""
        return await self.query_service.create_processor_for_agent(agent_config)

    async def _create_session_handler(self, agent_config: Dict[str, Any]) -> SessionHandler:
        """创建会话管理器"""
        return await self.session_service.create_handler_for_agent(agent_config)

    # 以下是对外提供的服务方法

    async def process_agent_chat_message(self,
                                         agent_id: str,
                                         user_id: int,
                                         conversation_id: str,
                                         message: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理智能体聊天消息

        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            conversation_id: 会话ID
            message: 消息内容

        Returns:
            处理结果
        """
        # 获取用户上下文
        user_context = await self._get_user_context()
        # 获取智能体实例
        agent = await self.get_agent_instance(agent_id, user_context)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 处理聊天请求
        try:
            result = await agent.process_chat_request(message, user_context, conversation_id)
            return result
        except Exception as e:
            LogUtils.error(f"处理聊天消息失败: {str(e)}")
            return {"success": False, "message": f"处理失败: {str(e)}"}

    async def handle_file_upload(self,
                                 agent_id: str,
                                 session_id: str,
                                 document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理智能体文档上传

        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            document_data: 文档数据

        Returns:
            处理结果
        """
        # 获取用户上下文
        agent = await self.get_agent_instance(agent_id)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 处理文档上传
        try:
            document_id = await agent.handle_file_upload(document_data, session_id)
            return {
                "success": True,
                "document_id": document_id,
                "file_name": document_data.get("file_name")
            }
        except Exception as e:
            LogUtils.error(f"处理文档上传失败: {str(e)}")
            return {"success": False, "message": f"处理失败: {str(e)}"}

    async def handle_agent_document_upload(self,
                                           agent_id: str,
                                           user_id: int,
                                           document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理智能体文档上传

        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            document_data: 文档数据

        Returns:
            处理结果
        """
        # 获取用户上下文
        user_context = await self._get_user_context()
        # 获取智能体实例
        agent = await self.get_agent_instance(agent_id, user_context)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 处理文档上传
        try:

            result = await agent.handle_document_upload(document_data, user_context)
            # 新增dataset和doc_feature记录
            if result.get("success"):

                dataset = result.get("local_dataset")
                existing_doc = result.get("existing_doc")
                try:
                    if result.get("agent_tpye") == AgentType.SECRETARIAL.name and not result.get("segment_id"):
                        return {
                            "success": False,
                            "message": "特征提取失败",
                            "data": {}
                        }
                    structure_feature = result.get("features", {}).get("structure_feature", "")
                    writing_style_feature = result.get("features", {}).get("writing_style_feature", "")

                    if existing_doc is None:
                        # 创建doc_feature记录
                        doc_feature = DocFeature(
                            user_id=str(user_id),
                            doc_name=document_data.get("file_name", "未命名文档"),
                            local_dataset_id=dataset.id,
                            structure_feature=structure_feature,
                            writing_style_feature=writing_style_feature,
                            segment_id=result.get("segment_id", "")
                        )
                        self.db.add(doc_feature)
                        self.db.commit()
                    else:
                        existing_doc.structure_feature = structure_feature,
                        existing_doc.writing_style_feature = writing_style_feature,
                        self.db.merge(existing_doc)
                        self.db.commit()
                    LogUtils.info(f"成功记录文档元数据，dataset ID: {dataset.id}")

                    # 更新返回结果
                    result["data"] = {
                        "dataset_id": dataset.dataset_id,
                        "document_id": dataset.document_id
                    }
                except Exception as db_error:
                    LogUtils.error(f"数据库记录失败: {str(db_error)}")
                    return {
                        "success": False,
                        "message": "文档上传成功但元数据记录失败",
                        "data": result.get("data", {})
                    }

            return result
        except Exception as e:
            LogUtils.error(f"处理文档上传失败: {str(e)}")
            return {"success": False, "message": f"处理失败: {str(e)}"}

    async def create_agent_session(self, agent_id: str, user_id: int) -> Dict[str, Any]:
        """
        创建智能体关联记录

        Args:
            agent_id: 智能体ID
            user_id: 用户ID

        Returns:
            agent关联ID
        """
        try:
            # 检查是否已存在agent关联
            existing_session = self.db.query(ChatAgentsMsg).filter(
                ChatAgentsMsg.agent_id == agent_id,
                ChatAgentsMsg.user_id == user_id,  # 统一类型为字符串
            ).first()

            if existing_session:
                raise ValueError(f"用户{user_id}已存在智能体{agent_id}的会话记录，禁止重复创建")

            # 获取智能体实例
            agent = await self.get_agent_instance(agent_id)
            if agent is None:
                return {"success": False, "message": f"智能体不存在: {agent_id}"}

            # 创建新会话
            session_id = await agent.start_new_session(user_id)

            # 记录会话创建日志
            LogUtils.info(f"新会话创建成功 | agent_id={agent_id} session_id={session_id}")

            return {
                "success": True,
                "session_id": session_id
            }
        except ValueError as ve:
            LogUtils.warning(f"重复创建agent关联被阻止: {str(ve)}")
            return {"success": False, "message": str(ve)}
        except Exception as e:
            LogUtils.error(f"创建agent关联失败: {str(e)}")
            return {"success": False, "message": f"创建agent关联失败: {str(e)}"}

    async def agent_user_session(self,
                                 agent_id: str,
                                 user_id: int) -> Dict[str, Any]:
        """
        获取用户在智能体下的会话列表

        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            limit: 限制数量

        Returns:
            会话列表
        """
        # 获取智能体实例
        agent = await self.get_agent_instance(agent_id)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 获取会话列表
        try:
            msg_session_id = await agent.user_chat_session(user_id)
            return {
                "success": True,
                "msg_session_id": msg_session_id
            }
        except Exception as e:
            LogUtils.error(f"获取会话列表失败: {str(e)}")
            return {"success": False, "message": f"获取会话列表失败: {str(e)}"}

    async def get_agent_session_history(self,
                                        agent_id: str,
                                        conversation_id: str,
                                        user_id: int,
                                        first_id: str = None,
                                        limit: int = 50) -> Dict[str, Any]:
        """
        获取智能体会话历史

        Args:
            agent_id: 智能体ID
            conversation_id: 会话ID
            user_id: 用户id
            limit: 限制数量

        Returns:
            会话历史
        """
        # 获取智能体实例
        agent = await self.get_agent_instance(agent_id)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 获取会话历史
        try:
            has_more, history = await agent.get_chat_history(conversation_id, user_id, first_id, limit)
            return {
                "success": True,
                "history": history,
                "has_more": has_more
            }
        except Exception as e:
            LogUtils.error(f"获取会话历史失败: {str(e)}")
            return {"success": False, "message": f"获取会话历史失败: {str(e)}"}

    async def _get_user_context(self) -> Dict[str, Any]:
        user = UserContext.get_current_user_role()

        # 检查用户角色中是否包含 DATASET_MANAGER
        is_dataset_manager = False
        role_code = None

        if user.get('user_roles',[]):
            for role in user.get('user_roles',[]):
                if role.get('role_code') == UserRoleType.DATASET_MANAGER.name:
                    is_dataset_manager = True
                    role_code = role.get('role_code')
                    break

        return {
            "id": user.get("id"),
            "username": user.get("username"),
            "role": role_code,  # 如果是数据集管理员返回DATASET_MANAGER，否则返回None
            "is_dataset_manager": is_dataset_manager  # 添加标志位
        }

    async def list_agent_datasets(self,
                                  agent_id: str,
                                  page: int = 1,
                                  limit: int = 20) -> Dict[str, Any]:
        """
        获取智能体关联的知识库列表
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            is_personal: 是否只返回个人知识库
            limit: 返回数量限制
            
        Returns:
            知识库列表
        """
        # 获取用户上下文
        user_context = await self._get_user_context()

        # 获取智能体实例(已应用装饰器)
        agent = await self.get_agent_instance(agent_id, user_context)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}", "datasets": [], "has_more": False}

        # 调用agent实例的list_datasets方法
        try:
            result = await agent.list_datasets(user_context, page, limit)
            return result
        except Exception as e:
            LogUtils.error(f"获取知识库列表失败: {str(e)}")
            return {"success": False, "message": f"获取知识库列表失败: {str(e)}", "datasets": [], "has_more": False}

    async def list_agent_user_conversations(self, agent_id, session_id, limit, last_id):
        """
        获取用户在智能体下的历史对话列表

        Args:
            agent_id: 智能体ID
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        # 获取智能体实例
        agent = await self.get_agent_instance(agent_id)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 获取会话列表
        try:
            has_more, sessions = await agent.list_user_chat_conversations(session_id, limit, last_id)
            return {
                "success": True,
                "sessions": sessions,
                "has_more": has_more
            }
        except Exception as e:
            LogUtils.error(f"获取会话列表失败: {str(e)}")
            return {"success": False, "message": f"获取会话列表失败: {str(e)}"}

    async def update_conversation_name(self, agent_id, conversation_id, name, user_id):
        """
        修改会话名称
        """
        # 获取智能体实例
        agent = await self.get_agent_instance(agent_id)
        if agent is None:
            return {"success": False, "message": f"智能体不存在: {agent_id}"}

        # 获取会话列表
        try:
            name = await agent.update_conversation_name(conversation_id, name, user_id)
            return {
                "success": True,
                "name": name
            }
        except Exception as e:
            LogUtils.error(f"修改会话名称失败: {str(e)}")
            return {"success": False, "message": f"修改会话名称失败: {str(e)}"}

    async def process_agent_chat_message_stream(self,
                                                agent_id: str,
                                                user_id: int,
                                                conversation_id: str,
                                                message: Dict[str, Any]):
        """
        流式处理智能体聊天消息

        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            conversation_id: 会话ID
            message: 消息内容

        Yields:
            流式处理过程中的响应数据
        """
        try:
            # 获取用户上下文
            user_context = await self._get_user_context()

            # 获取智能体实例
            agent = await self.get_agent_instance(agent_id, user_context)
            if agent is None:
                yield f"data: {json.dumps({'error': f'智能体不存在: {agent_id}'})}\n\n"
                return

            # 调用流式处理方法
            async for chunk in agent.process_chat_request_stream(message, user_context, conversation_id):
                yield f"data: {chunk}\n\n"

            # 发送完成信号
            yield "data: [DONE]\n\n"

        except Exception as e:
            LogUtils.error(f"流式处理聊天消息失败: {str(e)}")
            yield f"data: {json.dumps({'error': f'处理失败: {str(e)}'})}\n\n"
            yield "data: [DONE]\n\n"
