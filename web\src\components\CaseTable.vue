<template>
  <div class="case-table">
    <el-table
      :data="tableData"
      stripe
      style="width: 100%"
      :header-cell-style="headerStyle"
      :cell-style="cellStyle">
      <el-table-column
        prop="caseNumber"
        label="处警编号"
        width="200">
      </el-table-column>
      <el-table-column
        prop="policeStation"
        label="处警单位"
        width="200">
      </el-table-column>
      <el-table-column
        prop="reportType"
        label="报警形式"
        width="100">
      </el-table-column>
      <el-table-column
        prop="caseType"
        label="警情类别"
        width="100">
      </el-table-column>
      <el-table-column
        prop="handleType"
        label="处警类别"
        width="100">
      </el-table-column>
      <el-table-column
        prop="handleResult"
        label="处警结果"
        width="100">
      </el-table-column>
      <el-table-column
        prop="reportContent"
        label="报警内容">
      </el-table-column>
      <el-table-column
        prop="handleDescription"
        label="处警反馈情况">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'CaseTable',
  data() {
    return {
      tableData: [
        {
          caseNumber: '420322202503100003521100001',
          policeStation: '郧西县公安局城关派出所',
          reportType: '110报警',
          caseType: '违反电动车',
          handleType: '其它求助',
          handleResult: '救助服务',
          reportContent: '报警人称：我的一辆白色电动车在这掉了，价值2000多元，请处理。',
          handleDescription: '民警巡逻时发现一辆白色电动车在路边，经查询车辆信息，确认人车对应，已通知车主来失，民警留在现场等候门店的店主到时，到某地点来车子停在那里都是有原因的，民警提醒车主要管好自己的车辆。'
        },
        {
          caseNumber: '420300202503097004018586801',
          policeStation: '丹江县公安局水库派出所',
          reportType: '110报警',
          caseType: '其他纠纷',
          handleType: '其他纠纷',
          handleResult: '现场调解处理',
          reportContent: '报警人称：死亡网上被人以骗套委托书诈骗会员的名义骗了7000多元，请处理。',
          handleDescription: '民警到场了解情况后得知，该事项人以曾在村民家里做事且由会员为由，诈骗2585元，已经到账并已经报案，正在等待处理。'
        }
        // ... 可以继续添加更多数据
      ]
    }
  },
  methods: {
    headerStyle() {
      return {
        backgroundColor: 'rgba(0,48,102,0.8)',
        color: '#00ffff',
        fontSize: '14px',
        fontWeight: 'bold'
      }
    },
    cellStyle() {
      return {
        backgroundColor: 'rgba(0,48,102,0.3)',
        color: '#ffffff',
        fontSize: '12px',
        borderColor: 'rgba(0,255,255,0.1)'
      }
    }
  }
}
</script>

<style scoped>
.case-table {
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}

.el-table {
  background-color: transparent !important;
}

/* 覆盖 element-ui 的默认样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(0,255,255,0.4);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0,48,102,0.3);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(0,48,102,0.5);
}

:deep(.el-table td, .el-table th.is-leaf) {
  border-bottom: 1px solid rgba(0,255,255,0.1);
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: rgba(0,255,255,0.1) !important;
}
</style> 