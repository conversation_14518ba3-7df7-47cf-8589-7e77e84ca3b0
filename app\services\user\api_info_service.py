from datetime import datetime

from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.models.api_info import ApiInfo
from app.models.role_org_api_permission import RoleOrgApiPermission
from app.schemas.api_info_schema import ApiInfoUpdate, ApiInfoCreateRequest, ApiInfoUpdateRequest, ApiInfoQueryRequest


class ApiInfoService:
    def __init__(self, db: Session):
        self.db = db

    def get_api_infos(self, request: ApiInfoQueryRequest):
        # 获取分页参数
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10
        offset = (page - 1) * page_size

        # 构建查询条件
        query = self.db.query(ApiInfo)

        # 添加name条件
        if request.name and request.name.strip():
            query = query.filter(ApiInfo.name.like(f"%{request.name.strip()}%"))

        # 添加api条件
        if request.api and request.api.strip():
            query = query.filter(ApiInfo.api.like(f"%{request.api.strip()}%"))

        # 获取总数
        total = query.count()

        # 获取分页数据
        items = query.offset(offset).limit(page_size).all()

        return {
            "total": total,
            "items": items,
            "page": page,
            "page_size": page_size
        }

    def get_api_info(self, api_id: int):
        try:
            # 查询 API 基本信息
            api_info = self.db.query(ApiInfo).filter(ApiInfo.id == api_id).first()
            if not api_info:
                raise Exception(detail="API信息不存在")

            # 查询关联的角色组织权限信息
            permissions = self.db.query(RoleOrgApiPermission).filter(
                RoleOrgApiPermission.api_id == api_id
            ).all()

            # 提取角色和组织代码
            role_ids = list(set([p.role_id for p in permissions]))
            org_codes = list(set([p.org_code for p in permissions]))

            # 将权限信息添加到返回结果中
            result = api_info.__dict__
            result['roleIds'] = role_ids
            result['org_codes'] = org_codes
            return create_response(data=result, code=200)


        except Exception as e:
            return create_response(message=f"获取API信息失败:{str(e)}", code=500)


    def create_api_info(self, request: ApiInfoCreateRequest):
        try:
            result = self.db.query(ApiInfo).filter(ApiInfo.value == request.value).first()
            if  result:
                raise Exception(f"API信息存在")
            apiInfo = ApiInfo(
                name=request.name,
                value=request.value,
                api=request.api,
                method=request.method,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            self.db.add(apiInfo)
            self.db.flush()  # 获取新插入记录的ID

            # 处理角色和组织代码的逻辑
            if not request.roleIds and request.org_codes:
                raise Exception("请选择角色")

            # 只有当roleIds不为空时才处理映射关系
            if request.roleIds:
                # 如果org_codes为空，设置默认值[-1]
                org_codes = request.org_codes if request.org_codes else ["-1"]

                # 组装角色和组织代码的映射关系
                role_org_mappings = [
                    {
                        "role_id": role_id,
                        "org_code": org_code,
                        "api_id": apiInfo.id
                    }
                    for role_id in request.roleIds
                    for org_code in org_codes
                ]

                # 批量插入映射关系
                if role_org_mappings:
                    self.batch_insert_role_org_permissions(role_org_mappings)

            self.db.commit()
            self.db.refresh(apiInfo)
            return create_response(data=apiInfo, code=200)
        except Exception as e:
            self.db.rollback()
            return  create_response(message=f"新增失败:{str(e)}",code=500 )


    def batch_insert_role_org_permissions(self,role_org_mappings: list):
        role_org_permissions = [
            RoleOrgApiPermission(**mapping)
            for mapping in role_org_mappings
        ]
        self.db.bulk_save_objects(role_org_permissions)

    def update_api_info(self, request: ApiInfoUpdateRequest):
        try:
            # 检查API是否存在
            db_api_info = self.db.query(ApiInfo).filter(ApiInfo.id == request.id).first()
            if not db_api_info:
                raise Exception("API信息不存在" )

            # 检查角色是否为空
            if not request.roleIds:
                raise Exception("角色不能为空" )

            # 1. 删除关联表数据
            self.db.query(RoleOrgApiPermission).filter(
                RoleOrgApiPermission.api_id == request.id
            ).delete()

            # 2. 更新API信息表
            db_api_info.name = request.name
            db_api_info.value = request.value
            db_api_info.api = request.api
            db_api_info.method = request.method
            db_api_info.update_time = datetime.now()
            self.db.add(db_api_info)

            # 3. 组装并新增关联表数据
            org_codes = request.org_codes if request.org_codes else ["-1"]
            role_org_mappings = [
                {
                    "role_id": role_id,
                    "org_code": org_code,
                    "api_id": request.id
                }
                for role_id in request.roleIds
                for org_code in org_codes
            ]

            if role_org_mappings:
                self.batch_insert_role_org_permissions(role_org_mappings)

            self.db.commit()

            return create_response(data=db_api_info, code=200)

        except Exception as e:
            self.db.rollback()
            return  create_response(message=f"{str(e)}", code=500)


    def delete_api_info(self, api_id: int):
        try:
            # 查询API信息
            db_api_info = self.db.query(ApiInfo).filter(ApiInfo.id == api_id).first()
            if not db_api_info:
                raise Exception("API信息不存在")

            # 删除关联的权限记录
            self.db.query(RoleOrgApiPermission).filter(
                RoleOrgApiPermission.api_id == api_id
            ).delete()

            # 删除API信息
            self.db.delete(db_api_info)
            self.db.commit()
            return create_response(data="success",code=200 )

        except Exception as e:
            self.db.rollback()
            return  create_response(message=f"删除失败:{str(e)}",code=500 )