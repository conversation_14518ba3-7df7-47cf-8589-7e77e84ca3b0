<template>
  <div class="chat-input">
    <div class="new-chat-btn" @click="addConversation" v-if="currentConversationId">
      <!-- <img src="@/assets/images/chat-add.png" alt="新建对话" class="chat-add-icon" /> -->
      <!-- <i class="el-icon-chat-dot-square"></i> -->
      <img src="@/assets/images/ai/newMessage.svg" alt="新建对话" class="chat-add-icon" />
      <span>开启新对话</span>
    </div>
    <div class="input-wrapper">
      <div class="upload-box" v-if="uploadFilesList.length > 0">
        <div class="scroll-btn left" v-if="canScrollLeft" @click="scrollLeft">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="upload-file-box" ref="fileBox" @scroll="handleScroll">
          <div class="upload-file-item" v-for="file in uploadFilesList" :key="file.uid">
            <i class="el-icon-document"></i>
            <el-tooltip :content="file.name" placement="top" effect="dark">
              <span class="file-name">{{ file.name }}</span>
            </el-tooltip>
            <i class="el-icon-close delete-icon" @click="handleRemoveFile(file)"></i>
          </div>
        </div>
        <div class="scroll-btn right" v-if="canScrollRight" @click="scrollRight">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
      <el-input
        v-model="input"
        type="textarea"
        :rows="3"
        placeholder="请输入消息，按 Ctrl + Enter 发送"
        @keydown.ctrl.enter.native="send"
        :disabled="loading"
      />
      <div class="send-btn" :class="{ disabled: !input.trim() || loading }" @click="send">
        <!-- <i class="el-icon-position"></i> -->
        <div class="knowledge-btn">
          <img src="@/assets/images/ai/send.svg" alt="上传" v-if="input.trim()" />
          <img src="@/assets/images/ai/send-disable.svg" alt="上传" v-else />
        </div>
      </div>
      <div class="ex-btn-box">
        <el-upload
          action="1233122312"
          :http-request="handleUpload"
          :show-file-list="false"
          :before-upload="beforeUploadPDF"
          :accept="`${fileType.join(',')},.md`"
        >
          <el-tooltip content="上传附件" placement="top">
            <div class="knowledge-btn">
              <img src="@/assets/images/ai/link.svg" alt="上传" />
            </div>
          </el-tooltip>
        </el-upload>
        <div v-if="isShowKnowledge && namespace === 'office'">
          <el-tooltip content="知识库" placement="top">
            <div class="knowledge-btn" @click="handleKnowledgeOpen">
              <img src="@/assets/images/ai/upload.svg" alt="知识库" />
            </div>
          </el-tooltip>
        </div>
        <div
          v-if="
            isShowKnowledge && (namespace === 'law' || namespace === 'legal') && showKnowledgeByRole
          "
        >
          <el-tooltip content="知识库" placement="top">
            <div class="knowledge-btn" @click="handleKnowledgeOpen">
              <img src="@/assets/images/ai/upload.svg" alt="知识库" />
            </div>
          </el-tooltip>
        </div>
      </div>

      <!-- <img
        class="send-btn"
        :class="{disabled: !input.trim() || loading}"
        src="@/assets/images/icons/top-circle.png"
        alt="发送"
        @click="send"
      /> -->
    </div>
    <Knowledge
      :visible="knowledgeVisible"
      v-if="knowledgeVisible"
      :namespace="namespace"
      @close="handleKnowledgeClose"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import {
  chatWithAI,
  renameConversation,
  send_chat_message,
  upload_file,
  update_conversation_name,
} from "@/api/chat";
import Knowledge from "./knowledge.vue";
import { fileType } from "@/constants";

export default {
  props: {
    namespace: {
      type: String,
      required: true,
    },
    isShowKnowledge: {
      type: Boolean,
      default: false,
    },
  },
  name: "ChatInput",
  components: {
    Knowledge,
  },
  data() {
    return {
      input: "",
      loading: false,
      knowledgeVisible: false,
      uploadFiles: null,
      uploadFilesList: [],
      file_ids: [],
      fileType,
      canScrollLeft: false,
      canScrollRight: false,
      isUploading: false,
      globalLoading: null,
    };
  },
  computed: {
    currentConversation() {
      return this.$store.getters[`${this.namespace}/currentConversation`];
    },
    currentConversationId() {
      return this.$store.getters[`${this.namespace}/currentConversationId`];
    },
    agent_id() {
      return this.$store.getters[`${this.namespace}/agent_id`];
    },
    session_id() {
      return this.$store.getters[`${this.namespace}/session_id`];
    },
    userRole() {
      return this.$store.getters["userRole"];
    },
    showKnowledgeByRole() {
      return this.userRole.some(item => item.role_code === "DATASET_MANAGER");
    },
  },
  watch: {
    currentConversationId(newVal, oldVal) {
      console.log("currentConversationId：", newVal, oldVal);
    },
  },
  methods: {
    setCurrentConversation(conversationId) {
      return this.$store.dispatch(`${this.namespace}/setCurrentConversation`, conversationId);
    },
    setConversations(payload) {
      return this.$store.dispatch(`${this.namespace}/setConversations`, payload);
    },
    addMessage(payload) {
      return this.$store.dispatch(`${this.namespace}/addMessage`, payload);
    },
    async send() {
      if (!this.input.trim() || this.loading) return;

      const message = this.input;
      this.input = "";
      this.loading = true;

      try {
        // 添加用户消息
        const userMessage = {
          id: Date.now().toString(),
          content: message,
          role: "user",
          created_at: Date.now(),
          message_files: [],
        };

        // 获取当前会话ID
        let conversationId = this.currentConversationId;
        let isNewConversation = !conversationId;
        console.log("isNewConversation", isNewConversation);

        // 如果是新会话，先创建一个临时会话ID
        if (isNewConversation) {
          conversationId = "temp_conv_" + Date.now();
          // 设置当前会话ID
          await this.setCurrentConversation(conversationId);
          // 获取现有的所有会话
          const existingConversations = this.$store.getters[`${this.namespace}/conversations`];
          // 创建新会话，放在最前面
          let newConversations = [
            {
              id: conversationId,
              name: "新对话",
              messages: [],
              created_at: Date.now() / 1000,
              updated_at: Date.now() / 1000,
            },
            ...existingConversations,
          ];
          await this.setConversations({
            conversations: newConversations,
            hasMore: true,
            append: false,
          });
        }

        // 添加用户消息到 store
        console.log("Adding user message:", userMessage);
        if (this.file_ids.length) {
          this.uploadFilesList.forEach(item => {
            userMessage.message_files.push({
              id: item.uid,
              filename: item.name,
              type: "document",
              url: "",
              mime_type: item.type,
              transfer_method: "local_file",
              belongs_to: "user",
            });
          });
        }
        await this.addMessage({
          conversationId,
          message: userMessage,
        });

        // 调用 AI API，新会话时不传递会话ID
        // const response = await chatWithAI(message, isNewConversation ? null : conversationId);
        const response = await send_chat_message(
          this.agent_id,
          isNewConversation ? null : conversationId,
          {
            message,
            streaming: true,
            file_ids: this.file_ids,
          }
        );

        this.file_ids = [];
        this.uploadFilesList = [];
        this.uploadFiles = null;

        const reader = response.body.getReader();
        let accumulatedContent = "";
        let messageId = null;

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const text = new TextDecoder().decode(value);
          const lines = text.split("\n").filter(line => line.trim());

          for (const line of lines) {
            if (line.startsWith("data: ") && line !== "data: [DONE]") {
              try {
                const data = JSON.parse(line.slice(6));
                // console.log("Received data:", data);

                if (data.event === "message") {
                  // 如果是新会话，获取服务器返回的会话ID并更新
                  if (isNewConversation && data.conversation_id) {
                    const newConversationId = data.conversation_id;
                    await update_conversation_name(this.agent_id, newConversationId, {
                      name: message,
                    });

                    // 获取临时会话的所有消息
                    const tempConv = this.currentConversation;
                    // 更新当前会话ID
                    await this.setCurrentConversation(newConversationId);
                    // 重新获取会话列表
                    await this.$store.dispatch(`${this.namespace}/fetchConversations`, {});
                    // 获取更新后的会话列表
                    const updatedConversations =
                      this.$store.getters[`${this.namespace}/conversations`];
                    // 找到新会话
                    const newConv = updatedConversations.find(c => c.id === newConversationId);
                    if (newConv) {
                      // 将临时会话的消息合并到新会话中
                      newConv.messages = [...tempConv.messages];
                      // 更新会话列表
                      await this.setConversations({
                        conversations: updatedConversations,
                        hasMore: true,
                        append: false,
                      });
                    }
                    conversationId = newConversationId;
                    isNewConversation = false;
                  }

                  accumulatedContent += data.answer;
                  messageId = data.message_id;

                  // 更新或添加 AI 消息
                  const aiMessage = {
                    id: messageId,
                    content: accumulatedContent,
                    role: "assistant",
                    created_at: data.created_at * 1000,
                  };

                  // console.log('Adding AI message:', aiMessage);
                  await this.addMessage({
                    conversationId,
                    message: aiMessage,
                  });
                  // console.log("AI消息添加成功:", aiMessage);

                  // 强制更新视图
                  this.$forceUpdate();
                } else if (data.event === "message_end") {
                  console.log("Message completed:", data.metadata);
                } else if (data.type === "error") {
                  this.$message.error(data.content);
                }
              } catch (e) {
                console.error("Error parsing message:", e);
              }
            }
          }
        }
      } catch (error) {
        console.error("Error in send:", error);
      } finally {
        this.loading = false;
      }
    },
    handleKnowledgeOpen(e) {
      e.stopPropagation();
      this.knowledgeVisible = true;
    },
    handleKnowledgeClose() {
      this.knowledgeVisible = false;
    },
    async addConversation() {
      // 只切换到欢迎页，不弹窗
      this.$store.commit(`${this.namespace}/SET_CURRENT_CONVERSATION`, null);
    },
    beforeUploadPDF(file) {
      const isTypeOk = this.fileType.includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 50;

      if (!isTypeOk) {
        this.$message.error(`只能上传 ${this.fileType.join(",")} 文件!`);
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 50MB!");
        return false;
      }
      return true;
    },
    async handleUpload(options) {
      try {
        this.uploadFiles = options.file;
        this.isUploading = true;
        this.globalLoading = this.$loading({
          lock: true,
          text: "文件上传中...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        const formData = new FormData();
        formData.append("file", this.uploadFiles);
        formData.append("session_id", this.session_id);
        formData.append("agent_id", this.agent_id);
        const res = await upload_file(formData);
        if (res.code === 200 && res.data && res.data.success) {
          this.$message.success("文件上传成功");
          this.file_ids.push(res.data.document_id);
          this.uploadFilesList.push(this.uploadFiles);
        } else {
          throw new Error(res.message || "文件上传失败");
        }
      } catch (error) {
        // 只有在实际上传过程中出现错误时才提示
        if (error.toString() !== "cancel") {
          console.error("文件上传失败:", error);
        }
      } finally {
        this.isUploading = false;
        if (this.globalLoading) {
          this.globalLoading.close();
          this.globalLoading = null;
        }
        this.uploadFiles = [];
      }
    },
    handleRemoveFile(file, index) {
      this.uploadFilesList = this.uploadFilesList.filter(f => f.uid !== file.uid);
      this.file_ids.splice(index, 1);
    },
    scrollLeft() {
      const fileBox = this.$refs.fileBox;
      if (fileBox) {
        fileBox.scrollLeft -= 200;
      }
    },
    scrollRight() {
      const fileBox = this.$refs.fileBox;
      if (fileBox) {
        fileBox.scrollLeft += 200;
      }
    },
    handleScroll() {
      const fileBox = this.$refs.fileBox;
      if (fileBox) {
        this.canScrollLeft = fileBox.scrollLeft > 0;
        this.canScrollRight = fileBox.scrollLeft < fileBox.scrollWidth - fileBox.clientWidth;
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.handleScroll();
    });
  },
  updated() {
    this.$nextTick(() => {
      this.handleScroll();
    });
  },
};
</script>

<style scoped lang="less">
.chat-input {
  padding: 24px 32px;
  border-radius: 16px;
  max-width: 60%;
  margin: 0 auto;
  transition: transform 0.2s, background 0.2s;
  position: relative;
  .new-chat-btn {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    /* width: 100%; */
    height: 36px;
    border-radius: 8px;
    padding: 0px 10px;
    transition: transform 0.2s, background 0.2s;
    background: rgba(0, 255, 255, 0.2);
    color: #00ffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    border: none;
    font-weight: 500;
    box-shadow: none;
    img {
      width: 20px;
      height: 20px;
    }
  }

  .new-chat-btn:hover {
    // transform: scale(1.05);
  }

  .new-chat-btn span {
    margin-left: 6px;
  }
}
.chat-input:hover,
.chat-input:focus {
  transform: scale(1.02);
}

.input-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  max-width: 790px;
  .upload-box {
    height: 100px;
    width: 100%;
    background-color: #0f2336;
    margin-bottom: -2px;
    position: relative;
    display: flex;
    align-items: center;
  }
  .knowledge-btn {
    font-size: 18px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 40px;
      height: 40px;
    }
    // &:hover {
    //   background-color: rgba(0, 255, 255, 0.1);
    // }
    i {
      font-size: 18px;
    }
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
  .ex-btn-box {
    position: absolute;
    left: 18px;
    bottom: 18px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }
}
.send-btn {
  position: absolute;
  right: 18px;
  bottom: 18px;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 255, 0.2);
  cursor: pointer;
  transition: filter 0.2s;
}
.send-btn.disabled {
  filter: grayscale(1) opacity(0.5);
  pointer-events: none;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  resize: none;
  font-size: 16px;
  line-height: 1.7;
  padding: 16px 60px 60px 16px;
  border-radius: 0;
  // border-radius: 12px;

  border: none;
  background-color: #0f2336;
  color: rgba(255, 255, 255, 0.7);
}
:deep(.el-textarea__inner) {
  resize: none;
  font-size: 16px;
  line-height: 1.7;
  padding: 16px 60px 60px 16px;
  // border-radius: 12px;
  border-radius: 0;
  border: none;
  background-color: #0f2336;
  color: rgba(255, 255, 255, 0.7);
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.4);
    border-radius: 4px;
    box-shadow: inset 0 0 4px rgba(0, 192, 255, 0.3);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.4);
    box-shadow: inset 0 0 6px rgba(0, 192, 255, 0.5);
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 25, 60, 0.3);
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  }
}
:deep(.el-textarea__inner:focus) {
  border: none;
}

.upload-file-box {
  margin: 10px 0;
  padding: 10px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  overflow-x: auto;
  position: relative;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;

  .upload-file-item {
    display: inline-flex;
    align-items: center;
    padding: 8px;
    background: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 4px;
    transition: all 0.3s ease;
    max-width: 200px;
    height: 50px;

    &:hover {
      background: rgba(0, 48, 102, 0.4);
      border-color: rgba(0, 255, 255, 0.3);
    }

    .el-icon-document {
      font-size: 16px;
      color: #00ffff;
      margin-right: 8px;
    }

    .file-name {
      flex: 1;
      color: #ffffff;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 120px;
    }

    .delete-icon {
      font-size: 16px;
      color: #ff4d4f;
      cursor: pointer;
      padding: 4px;
      margin-left: 8px;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 77, 79, 0.1);
        transform: scale(1.1);
      }
    }
  }
}

.scroll-btn {
  position: absolute;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 255, 0.3);
  }

  &.left {
    left: 10px;
  }

  &.right {
    right: 10px;
  }

  i {
    color: #fff;
    font-size: 16px;
  }
}
</style>
