<template>
  <el-input
    v-model="password"
    :type="showPassword ? 'text' : 'password'"
    :placeholder="placeholder"
    class="custom-input"
  >
    <img slot="prefix" :src="prefixIcon" alt="" />
    <span v-if="password" slot="suffix" class="pass-input__suffix" @click="togglePasswordVisibility">
      <!-- <i :class="['el-icon-view', {'not_view': !showPassword}]"></i> -->
      <img v-if="showPassword" src="@/assets/images/open-eye.png" alt="" />
      <img v-else src="@/assets/images/close-eye.png" alt="" />
    </span>
  </el-input>
</template>

<script>
export default {
  name: 'PassInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入密码'
    },
    prefixIcon: {
      type: Object,
      default: () => require('@/assets/images/icons/icon-register_pass.svg')
    }
  },
  data() {
    return {
      showPassword: false,
      password: this.value
    }
  },
  watch: {
    value(val) {
      this.password = val
    },
    password(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    }
  }
}
</script>

<style lang="scss" scoped>
.pass-input__suffix {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  margin-left: 5px;
  
  img {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }
}

:deep(.el-input__suffix) {
  display: flex;
  align-items: center;
}
</style>
