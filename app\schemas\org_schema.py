from pydantic import BaseModel
from typing import List, Optional




class OrgBase(BaseModel):
    name: str
    sort: int
    status: Optional[bool] = None
    parent_id: int = -1
    can_sub_query: int

class OrgCreate(OrgBase):
    role_ids:List[int]
    parent_org_code:Optional[str] = None


class OrgUpdate(OrgBase):
    id: int
    role_ids: List[int]

class OrgInDB(OrgBase):
    id: int

    class Config:
        orm_mode = True

class UserOrgCreate(BaseModel):
    user_id: int
    org_code: str
    primary_flag: bool

class OrgRoleCreate(BaseModel):
    org_code: str
    role_id: int
    org_id:int


class OrgAddRequest(BaseModel):
    access_token: str
    token_type: str

