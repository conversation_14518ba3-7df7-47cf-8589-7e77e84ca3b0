<template>
  <div class="file-review bigscreen">
    <PageHeader title="涉案财物预警" subtitle="Amount Warning" />

    <div class="content-wrapper">
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="common-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="未及时入库预警" name="0">
          </el-tab-pane>
          <el-tab-pane label="未及时处置预警" name="1">
          </el-tab-pane>
        </el-tabs>
      </div>
      <YetWarehoused v-if="activeTab === '0'" />
      <YetDisposed v-else />
    </div>
    
  </div>
</template>

<script>
import YetWarehoused from "@/components/YetWarehoused.vue";
import YetDisposed from "@/components/YetDisposed.vue";
import PageHeader from "@/components/PageHeader.vue";
export default {
  name: "AmountWarning",
  components: {
    PageHeader,
    YetWarehoused,
    YetDisposed
  },
  data() {
    return {
      activeTab: "0",
    };
  },
  created() {
  
  },
  beforeDestroy() {
   
  },
  methods: {
    handleTabClick() {}
  },
  computed: {
   
  },
};
</script>

<style lang="less" scoped>
@import url("./index.less");
</style>

<style lang="less">
.tabs-section .el-tabs__active-bar {
  width: 18% !important;
  margin-left: 28px;
}

@import url("./global.less");
</style>
