from sqlalchemy import Column, Integer, String, DateTime, Index, case, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Scene6RecordBase = declarative_base()


class scene_6_record(Scene6RecordBase):
    """
        场景6记录数据模型：涉案财务记录表
    """
    __tablename__ = 'scene_6_record'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    user_id = Column(Integer, index=True)

    # 案件基本数据
    case_name = Column(String(128), comment="案件名称")
    case_code = Column(String(64), index=True, comment="案件编号")
    handle_org_code = Column(String(128), comment="主办单位")
    hoster_name = Column(String(64), comment="主办人人名")
    involved_person = Column(String(64), comment="涉案人姓名")

    # 扣押/缴纳财务数据
    measure_code = Column(String(50), comment='处理措施代码')
    measure_type = Column(String(50), comment='处理措施类型(扣押-刑事/扣押-行政/缴纳)')
    measure_object_name = Column(String(255), comment='措施对象名称(物品名称/人民币等)')
    seized_quantity = Column(Integer, comment='扣押财物数量')
    mod_time = Column(String(50), comment="修改时间")   # 对应库里的修改时间，工作指引中的措施批准时间

    # 涉案财务表数据，包含登记入库、出库、保管延期
    registration_time = Column(String(50), comment='登记入库时间')
    registration_quantity = Column(String(50), comment='登记入库数量')
    storage_period = Column(String(20), comment='入库保管期限(天)')
    storage_expire_time = Column(String(50), comment='保管到期时间')
    adjust_time = Column(String(50), comment='调整时间')

    operation_time = Column(String(50), comment='操作时间')
    extension_registration_date = Column(String(50), comment='调用延期登记日期')
    extension_period = Column(String(20), comment='延期期限(天)')

    # 分析状态 0:未分析 1:分析中 2:分析完成 3:分析失败
    analysis_status = Column(Integer, default=0, nullable=False, index=True)
    # 分析结论详情:未超期、入库超期、出库超期
    analysis_result = Column(String(64), index=True, comment='分析结论')
    overdue_time = Column(String(20), comment='超期时间(天)')
    current_status = Column(String(64), comment='当前数据状态(扣押/入库/延期/出库)')
    org_code = Column(String(255), comment='机构orgCode')
    case_details = Column(JSON, default=[], comment="案件详情JSON数据")

    def __repr__(self):
        return f"<Scene5Record(case_code={self.case_code}, name={self.case_name})>"


def finance_search_order(field: any):
    # 定义分析状态的排序优先级
    analysis_status_order = {
        1: 1,  # 分析中
        2: 2,  # 已分析
        0: 3,  # 未分析
        3: 4  # 分析失败
    }

    # 使用case语句进行自定义排序
    return case(
        {status: order for status, order in analysis_status_order.items()},
        value=field
    )
