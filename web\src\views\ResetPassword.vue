<template>
  <div class="reset-password-container">
    <!-- 添加粒子效果容器 -->
    <div class="particle-container">
      <canvas class="rain"></canvas>
      <canvas class="dashed"></canvas>
    </div>
    
    <div class="reset-password-box">
      <div class="title">
        <h1>重置密码</h1>
        <div class="subtitle">DeepSeek智能辅助平台</div>
      </div>
      <el-form ref="resetForm" :model="resetForm" :rules="resetRules" label-width="0" class="reset-form">
        <el-form-item prop="username">
          <el-input
            v-model="resetForm.username"
            placeholder="用户名"
            prefix-icon="el-icon-user"
            clearable
            class="custom-input"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="resetForm.password"
            placeholder="新密码"
            prefix-icon="el-icon-lock"
            show-password
            clearable
            class="custom-input"
          />
        </el-form-item>
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="resetForm.confirmPassword"
            placeholder="确认新密码"
            prefix-icon="el-icon-lock"
            show-password
            clearable
            class="custom-input"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="reset-button"
            @click="handleReset"
          >
            重置密码
          </el-button>
        </el-form-item>
        <div class="reset-options">
          <router-link to="/login" class="login-link">返回登录</router-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { resetPassword } from '@/api/auth'

export default {
  name: 'ResetPassword',
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.resetForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      resetForm: {
        username: '',
        password: '',
        confirmPassword: ''
      },
      resetRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, message: '用户名长度至少为2个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' },
          { max: 12, message: '密码长度至长为12个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      loading: false,
      rainCtx: null,
      dashedCtx: null,
      animationFrame: null
    }
  },
  mounted() {
    this.initParticleEffect()
  },
  beforeDestroy() {
    this.stopParticleEffect()
  },
  methods: {
    handleReset() {
      this.$refs.resetForm.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // 提交重置密码
            const { username, password } = this.resetForm
            const res = await resetPassword({ username, password })
            if (res.code === 200) {
              this.$message({
                message: '密码重置成功，请使用新密码登录',
                type: 'success'
              })
              // 重置成功后跳转到登录页
              this.$router.push('/login')
            } else {
              this.$message.error(res.message || '密码重置失败')
            }
          } catch (error) {
            console.error(error)
          } finally {
            this.loading = false
          }
        }
      })
    },
    initParticleEffect() {
      const rainCanvas = document.querySelector('.rain')
      const dashedCanvas = document.querySelector('.dashed')
      
      if (!rainCanvas || !dashedCanvas) return
      
      const rainCtx = rainCanvas.getContext('2d')
      const dashedCtx = dashedCanvas.getContext('2d')
      
      // 设置画布大小
      const resizeCanvas = () => {
        const container = document.querySelector('.particle-container')
        if (!container) return
        
        const rect = container.getBoundingClientRect()
        rainCanvas.width = rect.width
        rainCanvas.height = rect.height
        dashedCanvas.width = rect.width
        dashedCanvas.height = rect.height
      }
      
      resizeCanvas()
      window.addEventListener('resize', resizeCanvas)
      
      // 粒子效果
      class Particle {
        constructor() {
          this.init()
        }
        
        init() {
          this.x = Math.random() * rainCanvas.width
          this.y = rainCanvas.height // 从底部开始
          this.vy = -(Math.random() * 1 + 1) // 向上移动的速度
          this.size = Math.random() * 2 + 1 // 随机大小
          this.alpha = Math.random() * 0.5 + 0.5 // 随机透明度
          this.color = this.getRandomColor()
        }
        
        getRandomColor() {
          const colors = [
            { r: 0, g: 255, b: 255 },  // 青色
            { r: 0, g: 195, b: 255 },  // 浅蓝色
            { r: 20, g: 120, b: 156 }  // 深蓝色
          ]
          const color = colors[Math.floor(Math.random() * colors.length)]
          return `rgba(${color.r}, ${color.g}, ${color.b}, ${this.alpha})`
        }
        
        draw() {
          rainCtx.beginPath()
          const gradient = rainCtx.createLinearGradient(this.x, this.y, this.x, this.y - this.size * 10)
          gradient.addColorStop(0, this.color)
          gradient.addColorStop(1, 'rgba(0, 255, 255, 0)')
          rainCtx.fillStyle = gradient
          rainCtx.fillRect(this.x, this.y, this.size, -this.size * 10) // 向上延伸的光效
          rainCtx.closePath()
          
          this.update()
        }
        
        update() {
          this.y += this.vy
          
          // 如果粒子超出顶部，重新初始化
          if (this.y < 0) {
            this.init()
          }
        }
      }
      
      // 初始化粒子
      const particles = []
      const particleCount = 50 // 粒子数量
      
      for (let i = 0; i < particleCount; i++) {
        setTimeout(() => {
          const particle = new Particle()
          particles.push(particle)
        }, i * 100) // 错开创建时间
      }
      
      // 虚线效果
      const drawDashedLines = () => {
        const w = dashedCanvas.width
        const h = dashedCanvas.height
        
        dashedCtx.lineWidth = 2
        dashedCtx.setLineDash([4, 4])
        dashedCtx.shadowOffsetX = 0
        dashedCtx.shadowOffsetY = 0
        dashedCtx.shadowColor = '#00ffff'
        dashedCtx.shadowBlur = 10
        
        // 第一条曲线
        dashedCtx.beginPath()
        let grd = dashedCtx.createLinearGradient(w / 11 * 2, h / 3, w / 5 * 2, h)
        grd.addColorStop(0, "#00ffff")
        grd.addColorStop(1, "#065261")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2, h)
        dashedCtx.quadraticCurveTo(w / 5, h / 6 * 5, w / 11 * 2, h / 3)
        dashedCtx.stroke()
        
        // 第二条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 11 * 3.3, h / 2, w / 3 * 1.1, h / 6 * 5)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 3 * 1.1, h / 6 * 5)
        dashedCtx.quadraticCurveTo(w / 5 * 1.5, h / 6 * 4.2, w / 11 * 3.3, h / 2)
        dashedCtx.stroke()
        
        // 第三条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 3 * 1.4, h / 5, w / 5 * 2, h / 2)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2, h / 2)
        dashedCtx.quadraticCurveTo(w / 3 * 1.2, h / 4 * 1.4, w / 3 * 1.4, h / 5)
        dashedCtx.stroke()
      }
      
      // 动画循环
      const animate = () => {
        rainCtx.clearRect(0, 0, rainCanvas.width, rainCanvas.height)
        dashedCtx.clearRect(0, 0, dashedCanvas.width, dashedCanvas.height)
        
        // 绘制粒子
        particles.forEach(particle => particle.draw())
        
        // 绘制虚线
        drawDashedLines()
        
        this.animationFrame = requestAnimationFrame(animate)
      }
      
      animate()
    },
    stopParticleEffect() {
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
      window.removeEventListener('resize', this.resizeCanvas)
    }
  }
}
</script>

<style lang="less" scoped>
.reset-password-container {
  width: 100%;
  height: 100vh;
  background: url('@/assets/images/bg.png') no-repeat center center;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  pointer-events: none;
}

.dashed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  pointer-events: none;
}

.reset-password-box {
  width: 460px;
  padding: 40px;
  background: rgba(0, 24, 51, 0.7);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.2),
              inset 0 0 40px rgba(0, 150, 255, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.reset-password-box::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid rgba(0, 255, 255, 0.1);
  border-radius: 10px;
  pointer-events: none;
}

.title {
  text-align: center;
  margin-bottom: 30px;
}

.title h1 {
  color: #00ffff;
  font-weight: 600;
  font-size: 32px;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.7),
              0 0 40px rgba(0, 255, 255, 0.5);
  letter-spacing: 2px;
  margin-bottom: 10px;
}

.subtitle {
  color: rgba(0, 255, 255, 0.8);
  font-size: 16px;
  margin-top: 5px;
  letter-spacing: 1px;
}

.reset-form {
  margin-top: 20px;
}

:deep(.custom-input  .el-input__inner) {
  background: rgba(0, 36, 89, 0.7) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  color: #fff !important;
  height: 50px !important;
  line-height: 50px !important;
  font-size: 16px !important;
  border-radius: 4px !important;
  padding-left: 45px !important;
  box-shadow: inset 0 0 20px rgba(0, 150, 255, 0.05) !important;
  transition: all 0.3s !important;
}

:deep(.custom-input  .el-input__inner):focus {
  border-color: rgba(0, 255, 255, 0.6) !important;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3) !important;
}

:deep(.custom-input  .el-input__prefix) {
  left: 15px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

:deep(.custom-input .el-input__icon) {
  color: rgba(0, 255, 255, 0.8) !important;
  font-size: 18px !important;
}

.reset-button {
  width: 100%;
  margin-top: 10px;
  background: linear-gradient(90deg, #0082c8, #00b4db) !important;
  border: none !important;
  height: 50px !important;
  font-size: 16px !important;
  letter-spacing: 2px !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border-radius: 4px !important;
}

.reset-button:hover {
  background: linear-gradient(90deg, #00a2f8, #00d4ff) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4) !important;
  transform: translateY(-2px);
}

.reset-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.reset-button:hover::before {
  left: 100%;
}

.reset-options {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  font-size: 14px;
}

.login-link {
  color: #00ffff;
  text-decoration: none;
  transition: all 0.3s;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  padding: 0 5px;
}

.login-link:hover {
  text-decoration: none;
  color: #00ffff;
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
  border-bottom: 1px solid rgba(0, 255, 255, 0.5);
}

/* 自定义表单验证错误信息样式 */
.el-form-item__error {
  color: #f56c6c !important;
  text-shadow: 0 0 5px rgba(245, 108, 108, 0.3) !important;
}
</style> 