import request from '@/utils/request'

// 获取接口列表
export function getApiList(data) {
  return request({
    url: '/api/api-info/list',
    method: 'post',
    data
  })
}

// 获取接口详情
export function getApiDetail(id) {
  return request({
    url: `/api/api-info/byid/${id}`,
    method: 'get'
  })
}

// 新增接口
export function createApi(data) {
  return request({
    url: '/api/api-info/save',
    method: 'post',
    data
  })
}

// 更新接口
export function updateApi(data) {
  return request({
    url: '/api/api-info/update',
    method: 'put',
    data
  })
}

// 删除接口
export function deleteApi(id) {
  return request({
    url: `/api/api-info/delete/${id}`,
    method: 'delete'
  })
} 