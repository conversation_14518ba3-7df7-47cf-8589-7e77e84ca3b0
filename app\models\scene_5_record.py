from sqlalchemy import Column, String, Text, Integer, JSON, case

from .base.normal_po_base import NormalPoBase
from .base.scene_analysis_base import SceneAnalysis


class Scene5Record(SceneAnalysis):
    """场景5记录数据模型
    
    继承自场景分析基表，扩展案件相关字段
    """
    __tablename__ = 'scene_5_record'

    case_name = Column(String(128), comment="案件名称")
    org_code = Column(String(255), comment="机构code")
    case_code = Column(String(64), index=True, comment="案件编号")
    handle_org_code = Column(String(128), comment="办案单位")
    hoster_id = Column(String(64), comment="主办人ID")
    hoster_name = Column(String(64), comment="主办人人名")
    mod_time = Column(String(30), comment="更新时间")
    src = Column(JSON, default=[], comment="卷宗来源地址链接数组")
    fh_rksj = Column(String(30), comment="入库时间")

    aj_type = Column(String(30), comment="案件类别: 21-一般程序;10-刑事")
    analysis_page = Column(JSON, default=[], comment="页码标题信息")
    analysis_error = Column(JSON, default=[], comment="分析错误异常信息")
    ocr_status = Column(Integer, default=0,
                        comment="分析状态: 0-未识别,2-识别完成,3-识别失败")
    source = Column(Integer, default=0,
                    comment="数据来源: 0-数据同步,1-文件上传")

    def __repr__(self):
        return f"<Scene5Record(case_code={self.case_code}, name={self.case_name})>"


class Scene5OcrPO(NormalPoBase):
    """场景5记录数据模型

    继承自场景分析基表，扩展案件相关字段
    """
    __tablename__ = 'scene_5_ocr'

    #
    scene_5_record_id = Column(Integer, index=True, comment="案件编号")
    src_source = Column(String(128), comment="卷宗来源地址")
    ocr_content = Column(Text, comment="卷宗ocr识别内容")
    sn = Column(Integer, comment="卷宗内容顺序")

    def __repr__(self):
        return f"<Scene5Record(scene_5_record_id={self.scene_5_record_id})>"


def scene5_search_order():
    # 定义分析状态的排序优先级
    analysis_result_order = {
        1: 1,  # 分析中
        2: 2,  # 已分析
        0: 3,  # 未分析
        3: 4  # 分析失败
    }

    # 使用case语句进行自定义排序
    return case(
        {status: order for status, order in analysis_result_order.items()},
        value=Scene5Record.analysis_result
    )
