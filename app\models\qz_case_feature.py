from sqlalchemy import Column, Integer, String, Enum
from sqlalchemy.dialects.mysql import VARCHAR
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class QzCaseFeature(Base):
    __tablename__ = 'qz_case_feature'

    case_id = Column(VARCHAR(50), primary_key=True, nullable=False)
    entity_id = Column(Integer, primary_key=True, nullable=False)
    feature_type = Column(Enum('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE','LOCATION'), nullable=False, index=True)
    feature_value = Column(VARCHAR(255), primary_key=True, nullable=False)
