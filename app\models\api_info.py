from datetime import datetime

from sqlalchemy import Column, Integer, String, DateTime
from app.db.database import Base

class ApiInfo(Base):
    __tablename__ = "api_info"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(64), nullable=False)
    value = Column(String(64), nullable=False)
    api = Column(String(200), nullable=False)
    method = Column(String(10))
    create_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='创建时间')
    update_time= Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='修改时间')