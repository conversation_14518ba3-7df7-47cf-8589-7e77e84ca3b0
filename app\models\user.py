from sqlalchemy import Column, Integer, String, Boolean, DateTime, event
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

UserBase = declarative_base()

class User(UserBase):
    __tablename__ = 'user_info'

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50),  index=True, nullable=False,comment='名称')
    hashed_password = Column(String(128), nullable=False,comment='密码')
    is_active = Column(Boolean, default=True)
    id_card_number = Column(String(18), unique=True, index=True, nullable=False,comment='身份证号（必填且唯一，长度18）')
    police_number = Column(String(50), unique=True,index=True, nullable=False,comment='警号（必填）')
    police_unit = Column(String(128), index=False, nullable=False,comment='所在单位部门（具体到部门）')
    phone_number = Column(String(11), index=True, nullable=False,comment=' 联系电话（必填，长度11）')
    role = Column(String(50), index=True, nullable=False,default="POLICE",comment='角色（必填） ADMIN POLICE')
    is_online = Column(Integer, default=1, nullable=False, comment='用户在线状态：0 离线，1 在线')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='用户最后活动时间')
    is_delete = Column(Integer, default=0, nullable=False, comment='是否删除 0 未删除，1 已删除')

@event.listens_for(User, 'before_update')
def update_timestamp(mapper, connection, target):
    """在更新记录前自动更新 update_time 字段"""
    target.update_time = datetime.now()

