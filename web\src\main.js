import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import "./assets/css/reset.css";
import "./assets/css/index.css";
import "./assets/css/global.scss";
import "particles.js";
import $ from "jquery";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import "core-js/features/array/at"; // 让低版本浏览器支持 at()
import "@/utils/rem";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 分页组件
import Pagination from "@/components/Pagination";
import { handleTree, resetForm, addDateRange } from "@/utils/common";
import "./assets/images/icons";
import "./permission"
// 引入 mock
import Mock from "mockjs";
// import "./mock/menu";

Vue.use(ElementUI);
Vue.config.productionTip = false;
Vue.prototype.resetForm = resetForm;

window.jQuery = window.$ = $;
Vue.component("RightToolbar", RightToolbar);
Vue.component("Pagination", Pagination);
Vue.prototype.handleTree = handleTree;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange

new Vue({
  router,
  store,
  render: h => h(App),
}).$mount("#app");
