from pydantic import BaseModel
from typing import Optional, List


class ApiInfoBase(BaseModel):
    name: str
    value: str
    api: str
    method: Optional[str] = None

class ApiInfoCreate(ApiInfoBase):
    pass

class ApiInfoUpdate(BaseModel):
    name: Optional[str] = None
    value: Optional[str] = None
    api: Optional[str] = None
    method: Optional[str] = None

class ApiInfoInDB(ApiInfoBase):
    id: int

    class Config:
        orm_mode = True

class ApiInfoCreateRequest(BaseModel):
    name: str
    value: str
    api: str
    method: str
    roleIds:Optional[List[int]] = None
    org_codes: Optional[List[str]] = None

class ApiInfoUpdateRequest(BaseModel):
    name: str
    value: str
    api: str
    method: str
    roleIds:Optional[List[int]] = None
    org_codes: Optional[List[str]] = None
    id:int

class ApiInfoQueryRequest(BaseModel):
    page: Optional[int] = 1
    page_size: Optional[int] = 10
    name: Optional[str] = None
    api: Optional[str] = None