"""
具体智能体实现
"""
from typing import Dict, Any

from app.services.chat_agent.base import AgentOperation
from app.services.chat_agent.actions.base import ActionHandler
from app.services.chat_agent.actions.core_actions import (
    SecretarialFeaturesAction,
    TextGenerationAction,
    DefaultDatasetUploadAction, SecretarialDatasetUploadAction
)
from app.services.chat_agent.agents.base_agent import BaseAgentTemplate

class GeneralChatAgent(BaseAgentTemplate):
    """通用对话智能体"""
    
    def _build_action_chains(self) -> Dict[AgentOperation, ActionHandler]:
        # 聊天处理链
        generation_action = TextGenerationAction(self.query_processor)
        
        # 文档上传链
        upload_action = DefaultDatasetUploadAction(self.kb_handler)
        
        return {
            AgentOperation.PROCESS_CHAT: ActionHandler(generation_action),
            AgentOperation.HANDLE_DOCUMENT_UPLOAD: ActionHandler(upload_action)
        }


class LegalCodeAgent(BaseAgentTemplate):
    """法律法规智能体"""

    def _build_action_chains(self) -> Dict[AgentOperation, ActionHandler]:
        # 聊天处理链
        generation_action = TextGenerationAction(self.query_processor)

        # 文档上传链
        upload_action = DefaultDatasetUploadAction(self.kb_handler)

        return {
            AgentOperation.PROCESS_CHAT: ActionHandler(generation_action),
            AgentOperation.HANDLE_DOCUMENT_UPLOAD: ActionHandler(upload_action)
        }

class DetentionCenterCodeAgent(BaseAgentTemplate):
    """法律法规智能体"""

    def _build_action_chains(self) -> Dict[AgentOperation, ActionHandler]:
        # 聊天处理链
        generation_action = TextGenerationAction(self.query_processor)

        # 文档上传链
        upload_action = DefaultDatasetUploadAction(self.kb_handler)

        return {
            AgentOperation.PROCESS_CHAT: ActionHandler(generation_action),
            AgentOperation.HANDLE_DOCUMENT_UPLOAD: ActionHandler(upload_action)
        }

class SecretarialAgent(BaseAgentTemplate):
    """文秘办公智能体"""
    
    def _build_action_chains(self) -> Dict[AgentOperation, ActionHandler]:
        # 聊天处理链
        retrieval_action = SecretarialFeaturesAction(self.kb_handler, self.db)
        generation_action = TextGenerationAction(self.query_processor)
        
        chat_chain_head = ActionHandler(retrieval_action)
        chat_chain_head.set_next(ActionHandler(generation_action))
        
        # 文档上传链
        upload_action = SecretarialDatasetUploadAction(self.kb_handler,self.db)
        
        return {
            AgentOperation.PROCESS_CHAT: chat_chain_head,
            AgentOperation.HANDLE_DOCUMENT_UPLOAD: ActionHandler(upload_action)
        }
    
    def _format_chat_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """重写格式化聊天响应方法，添加文秘特定的格式"""
        base_response = super()._format_chat_response(result)
        
        # 添加文秘特定的格式或信息
        if base_response["success"]:
            # 这里可以添加文秘特定的格式化逻辑
            pass
        
        return base_response
    
    def _format_upload_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """重写格式化上传响应方法，添加文秘特定的格式"""
        base_response = super()._format_upload_response(result)
        
        # 添加文秘特定的格式或信息
        if base_response["success"]:
            # 这里可以添加文秘特定的格式化逻辑
            if "features" in base_response["data"]:
                # 添加文档结构分析结果
                base_response["data"]["document_structure"] = {
                    "title": result.get("document_features", {}).get("title", ""),
                    "sections": result.get("document_features", {}).get("sections", []),
                    "style_analysis": result.get("document_features", {}).get("style", {})
                }
        
        return base_response
