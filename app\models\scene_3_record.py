from sqlalchemy import Column, String, Integer, DateTime, Text, case
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Scene_3_RecordBase = declarative_base()


class Scene_3_Record(Scene_3_RecordBase):
    __tablename__ = 'scene_3_record'

    # User ID
    user_id = Column(Integer, index=True)

    id = Column(Integer, primary_key=True, autoincrement=True)

    ajbh = Column(String(128), index=True, nullable=True, comment='案件编号')
    ajmc = Column(String(200), index=True, comment='案件名称')
    jlbh = Column(String(128), index=True, nullable=True, comment='唯一标识,法律文书编号')

    doc_name = Column(String(200), index=True, comment='文书名称')
    doc_num = Column(String(200), index=True, comment='文书字号')
    tfrxm = Column(String(200), index=True, comment='填发人姓名')

    cqsx = Column(String(200), index=True, comment='法律文书简要')

    # doc_name="行政处罚决定书" csrg示例19561106
    # 第一段文字：违法行为人姓名{xm}，性别{xbhidden}，年龄{nl}，出生日期{csrg}年{csrg}月{csrg}日，身份证件种类{zjlxhidden}，证件号{zjhm},户籍所在地{hjxz}，现住址{xzxz}，工作单位{}，违法经历{wfjl}。
    # 第二段文字：jyaq
    # 第三段文字：以上事实有{zjcl}等证据证实
    # 第四段文字：{zsss}根据{flyj}之规定，现决定{cfcs}
    # 第五段：{zxfs}
    # 第六段：如不服本决定，可以在收到本决定书之日起六十日内向{ssdw}人民政府申请行政复议或者在六个月内依法向{ssdw}人民法院提起行政诉讼。

    # doc_name="起诉意见书"
    # 犯罪嫌疑人信息：wfjl
    # 其他内容为text_1,2...（判空后加入数组，最后一段为‘综上所述开头’）

    # doc_name="提请批准逮捕书"
    # 第一段；xyr
    # 第二段:ajly
    # 第三段:fzss(根据\\n分割成不同的段落)
    # 第四段:xgzj
    # 第五段:rzrf
    # 第六段:flyj

    doc_data = Column(Text, comment='文书内容')

    pzsj = Column(DateTime, comment='批准时间', nullable=True)
    tfsj = Column(DateTime, comment='填发时间', nullable=True)
    fh_rksj = Column(DateTime, index=True, comment='入库时间')

    detection_result = Column(Text, comment='笔录检测结果')
    # 0未分析，1分析中，2分析完成，3分析失败
    analysis_status = Column(Integer, default=0, nullable=False, index=True)
    # 分析状态 0:无问题 1:有问题
    check_status = Column(Integer, default=0, nullable=False, index=True)

    created_at = Column(DateTime, nullable=False, server_default=func.now(), index=True, comment="创建时间")
    # 最后修改时间
    updated_at = Column(
        DateTime,
        nullable=True,
        server_default=func.now(),
        onupdate=func.now(),
        index=True,
        comment="最后修改时间"
    )

def scene3_search_order():
    # 定义分析状态的排序优先级
    analysis_status_order = {
        1: 1,  # 分析中
        2: 2,  # 已分析
        0: 3,      # 未分析
        3: 4     # 分析失败
    }

    # 使用case语句进行自定义排序
    return case(
        {status: order for status, order in analysis_status_order.items()},
        value=Scene_3_Record.analysis_status
    )