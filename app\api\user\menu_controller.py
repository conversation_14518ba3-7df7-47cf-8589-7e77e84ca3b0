from typing import Set

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.permission_check import require_permissions
from app.db.database import get_db
from app.schemas.menu_schema import MenuCreate, MenuUpdate
from app.services.user.menu_service import MenuService

router = APIRouter(prefix="/menu", tags=["menu"])

@router.get("/get_userMenu")
@require_permissions(permission_key="menu:userMenu")
def get_userMenu(db: Session = Depends(get_db)):
    """获取用户菜单列表"""
    menu_service = MenuService(db);
    result = menu_service.get_menus();
    return create_response(code=200, data=result)

@router.get("/get_TotalMenu")
@require_permissions(permission_key="menu:totalMenu")
def get_TotalMenu(db: Session = Depends(get_db)):
    """获取总的菜单列表"""
    menu_service = MenuService(db);
    return menu_service.get_TotalMenu();

@router.get("/byId/{menu_id}")
@require_permissions(permission_key="menu:byId")
def get_menu(menu_id: int, db: Session = Depends(get_db)):
    """获取菜单详情"""
    menu_service = MenuService(db)
    return menu_service.get_menu(menu_id)


@router.post("/addmenu")
@require_permissions(permission_key="menu:save")
def create_menu(menu: MenuCreate, db: Session = Depends(get_db)):
    """创建菜单"""
    menu_service = MenuService(db)
    return menu_service.create_menu(menu)


@router.post("/update")
@require_permissions(permission_key="menu:update")
def update_menu(menu: MenuUpdate, db: Session = Depends(get_db)):
    """更新菜单"""
    menu_service = MenuService(db)
    return  menu_service.update_menu( menu)


@router.delete("/delete/{menu_id}")
@require_permissions(permission_key="menu:delete")
def delete_menu(menu_id: int, db: Session = Depends(get_db)):
    """删除菜单"""
    menu_service = MenuService(db)
    return menu_service.delete_menu(menu_id)

