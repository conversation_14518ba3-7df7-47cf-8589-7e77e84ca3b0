from typing import Dict

from numpy.lib.utils import source
from sqlalchemy.orm import Session

from app.core.context_handle import UserContext
from app.models.police_record import PoliceRecord


class Scene1Service:
    @staticmethod
    def upload_create_record(db: Session, english_data: Dict[str, str], case_type: int, user_id: int):
        # 创建记录
        police_number = UserContext.get_current().police_number
        police_number = f"-{police_number}"
        db_record = PoliceRecord(**english_data, user_id=user_id,case_type=case_type,source=1,org_code=police_number)
        db.add(db_record)
        db.commit()
        db.refresh(db_record)
        return db_record

    @staticmethod
    def unload_update_record(db: Session, english_data: Dict[str, str], case_type: int, user_id: int,
                             existing_record: PoliceRecord):
        for key, value in english_data.items():
            setattr(existing_record, key, value)
        existing_record.case_type = case_type
        existing_record.source = 1
        existing_record.user_id = user_id
        existing_record.check_results = None
        existing_record.analysis_status = 0
        existing_record.analysis_level = 'white'
        existing_record.analysis_details = None
        db.merge(existing_record)
        db.commit()
        return existing_record
