.q-header {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 106px;
  margin-bottom: 18px;
  position: relative;
}

.q-bg-left {
  height: 106px;
  flex: 1;
}

.q-bg-right {
  height: 106px;
  flex: 1;
  transform: scaleX(-1);
  /* 水平翻转，创建对称效果 */
}

.q-bg-container {
  background-image: url('@/assets/images/header-center-bg.png');
  background-size: contain;
  background-repeat: repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 106px;
  margin-left: -3px;
  margin-right: -3px;
  padding: 0px 26px;

  img {
    width: 310px;
  }
}

.q-bg-container img {
  max-height: 36px;
  margin-bottom: 5px;
}

.q-sub-title {
  font-size: 18px;
  color: rgba(0, 255, 255);
  letter-spacing: 1px;
  margin: 3px 0 0;
  text-align: center;
  white-space: nowrap;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.home-container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  color: #fff;
  background-image: linear-gradient(135deg, #0b1428 0%, #081020 100%);
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.particle-container canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.content-area {
  position: relative;
  z-index: 10;
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 474px;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    "header header"
    "cards stats";
  /* gap: 20px; */
  padding: 0px 22px;
  height: calc(100vh - 96px - 40px - 80px);
}

/* 页面顶部标题区域 */
.header {
  grid-area: header;
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}

.header::before,
.header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 150px;
  height: 2px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0), rgba(0, 255, 255, 0.7), rgba(0, 255, 255, 0));
}

.header::before {
  left: calc(50% - 250px);
}

.header::after {
  right: calc(50% - 250px);
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.title-container::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 500px;
  height: 120px;
  background: url('@/assets/images/title_bg.png') no-repeat center;
  background-size: contain;
  z-index: -1;
}

.main-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
  letter-spacing: 2px;
}

.sub-title {
  font-size: 14px;
  color: rgba(0, 255, 255, 0.8);
  margin: 5px 0 0;
  letter-spacing: 1px;
}

/* 功能卡片网格 */
.card-grid {
  grid-area: cards;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  /* gap: 20px; */
  margin-bottom: 20px;
  column-gap: 10px;
  /* 水平间距保持不变 */
  row-gap: 10px;
  margin-right: 5px;
}

.card {
  position: relative;
  border-radius: 1px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  /* max-height: 456px; */
  height: auto;
  background-image: url('@/assets/images/center-bg-item.png');
  background-repeat: no-repeat;
  background-size: cover;
}

/* @media (min-width: 1500px) {
  .card {
    background-image: url('@/assets/images/home_page/列表-背景-大.png');
  }
}

@media (max-width: 1499px) {
  .card {
    background-image: url('@/assets/images/home_page/列表-背景-小.png');
  }
} */

/* 添加中心渐变背景 */
.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(261deg, rgba(0, 255, 255, 0) 0%, #008989 50%, rgba(0, 255, 255, 0) 100%);
  opacity: 0.15;
  z-index: 0;
  pointer-events: none;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 255, 255, 0.2);
  border-color: rgba(0, 255, 255, 0.4);
  border-bottom: 4px #00FFFF solid;
}

.card:hover .card-icon {
  transform: scale(1.1);
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  background: transparent;
  border-radius: 5px;
}

/* 添加内部渐变边框 */
.card:hover .card-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -1px;
  right: -1px;
  bottom: 0;
  border-radius: 3px;
  padding: 2px;
  background: linear-gradient(180deg, rgba(0, 255, 255, 0) 0%, #00FFFF 50%, rgba(0, 255, 255, 0) 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.card:hover .card-content::after {
  content: '';
  position: absolute;
  top: 19px;
  left: 0;
  right: 0;
  bottom: -20px;
  border-radius: 3px;
  padding: 2px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0) 0%, #00FFFF 50%, rgba(0, 255, 255, 0) 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}


.card-icon {
  width: 160px;
  height: 142.69px;
  margin-bottom: 30px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s ease;
}

.criminal-icon {
  background-image: url('@/assets/images/home_page/刑事.webp')
}

.record-icon {
  background-image: url('@/assets/images/home_page/笔录.webp')
}

.analysis-icon {
  background-image: url('@/assets/images/home_page/分析.webp')
}

.investigation-icon {
  background-image: url('@/assets/images/home_page/刑事案件.webp')
}

.legal-icon {
  background-image: url('@/assets/images/home_page/法律文书.webp')
}

.file-icon {
  background-image: url('@/assets/images/home_page/卷宗.webp')
}

.admin-icon {
  background-image: url('@/assets/images/home_page/财物.webp')
}

.more-icon {
  background-image: url('@/assets/images/home_page/更多.webp')
}

.card-label {
  display: flex;
  flex-direction: column;
  text-align: center;
  width: 100%;
}

.label-text {
  /* font-size: 15px;
  font-weight: 600;
  color: #00ffff;
  margin-bottom: 5px;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5); */
  height: 28px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
}

.label-subtext {
  /* font-size: 10px;
  color: rgba(0, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px; */
  /* width: 120px; */
  height: 17px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #51878F;
  letter-spacing: 0;
  text-align: center;
  margin-top: 8px;
}

/* 右侧统计区域 */
.stats-sidebar {
  grid-area: stats;
  display: flex;
  flex-direction: column;
  /* gap: 20px; */
  margin-left: 5px;
  margin-bottom: 20px;
}

.stats-box {
  position: relative;
  /* background: rgba(13, 33, 51, 0.7); */
  border: 1px solid rgba(0, 255, 255, 0.479);
  border-radius: 2px;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
  overflow: hidden;
  height: 205px;
}

.stats-sidebar> :nth-child(1) {
  background: url('@/assets/images/svgs/box_bg_bottom.svg');
  background-repeat: no-repeat;
  background-position: right bottom;
}

.stats-sidebar> :nth-child(2) {
  background: url('@/assets/images/svgs/box_bg_bottom.svg') 0% 100.3%/100% no-repeat;
}

.stats-box::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0), rgba(0, 255, 255), rgba(0, 255, 255, 0));
}

.stats-box-title {
  padding: 12px 10px 25px 10px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 18px;
  color: #FFFFFF;
  letter-spacing: 0;
  height: 62px;
  display: flex;
  align-items: center;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
  background-image: url('@/assets/images/编组15.png');
  background-size: 100% 100%;
}

.stats-box-content {
  padding: 0 15px;
}

/* 用户统计样式 */
.user-stats {
  margin-bottom: 10px;
}

.user-stats .stats-box-content {
  display: flex;
  justify-content: space-around;
}

.user-count-item {
  text-align: center;
  padding: 5px 10px;
}

.user-count-number {
  font-size: 42px;
  font-weight: 700;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.user-count-label {
  font-size: 14px;
  /* color: rgba(0, 255, 255, 0.7); */
  color: white;
  margin-top: 5px;
}

/* DeepSeek统计样式 */
.deepseek-stats {
  flex: 1;
}

.stats-progress-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  margin-top: 10px;
  position: relative;
  cursor: pointer;
  height: auto;
  transition: all 0.3s ease;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.stats-progress-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.stats-progress-item:last-child {
  margin-bottom: 0;
}

.progress-circle {
  position: relative;
  width: 70px;
  height: 70px;
  margin-right: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.progress-circle svg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transform: rotate(-90deg);
  background: transparent;
  z-index: 2;
}

/* 确保SVG内部元素也是透明的 */
.progress-circle svg circle {
  fill: none;
}

/* 确保渐变定义不会产生背景 */
.progress-circle svg defs {
  background: transparent;
}

.progress-circle svg linearGradient {
  background: transparent;
}



.circle-bg-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.9;
  z-index: 1;
}

.circle-fill {
  z-index: 2;
}

.red {
  background-image: url('@/assets/images/red-back.png');
  border: none;
  /* box-shadow: 0 3px 10px rgba(255, 0, 0, 0.2); */
}

.yellow {
  background-image: url('@/assets/images/yellow-back.png');
  border: none;
  box-shadow: 0 3px 10px rgba(255, 170, 0, 0.2);
}

.blue {
  background-image: url('@/assets/images/blue-back.png');
  border: none;
  box-shadow: 0 3px 10px rgba(0, 170, 255, 0.2);
}

/* 为渐变添加动画效果 */
@keyframes rotateGradient {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.red-fill,
.yellow-fill,
.blue-fill {
  /* 可以添加动画效果 */
  /* animation: rotateGradient 3s linear infinite; */
}

.red .progress-percentage {
  color: white;
  text-shadow: 0 0 4px rgba(255, 50, 50, 0.8);
  font-size: 18px;
  font-weight: bold;
}

.yellow .progress-percentage {
  color: white;
  text-shadow: 0 0 4px rgba(255, 200, 0, 0.8);
  font-size: 18px;
  font-weight: bold;
}

.blue .progress-percentage {
  color: white;
  text-shadow: 0 0 4px rgba(0, 200, 255, 0.8);
  font-size: 18px;
  font-weight: bold;
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  font-weight: bold;
  z-index: 2;
}

.progress-info {
  flex: 1;
  display: flex;
  padding-right: 24px;
  justify-content: space-between;
  align-items: center;
  padding-left: 10px;
}

.progress-left {
  display: flex;
  flex-direction: column;
}

.progress-label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  margin-bottom: 2px;
}

.progress-sublabel {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.progress-value {
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
  color: white;
  margin-right: 24px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.progress-arrow {
  font-size: 20px;
  font-weight: 700;
  margin-left: 5px;
  opacity: 0.8;
  transition: all 0.3s ease;
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.stats-progress-item:hover .progress-arrow {
  transform: translateX(3px);
  opacity: 1;
}

/* AI助手图标 */
.ai-assistant {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 80px;
  z-index: 10;
}

.ai-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.ai-image:hover {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-area {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    grid-template-areas:
      "header"
      "cards"
      "stats";
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .stats-sidebar {
    display: grid;
    grid-template-columns: 1fr 2fr;
    /* gap: 20px; */
  }

  .deepseek-stats {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .content-area {
    padding: 15px;
  }

  .header::before,
  .header::after {
    width: 80px;
  }

  .header::before {
    left: calc(50% - 150px);
  }

  .header::after {
    right: calc(50% - 150px);
  }

  .title-container::before {
    width: 300px;
  }

  .main-title {
    font-size: 24px;
  }

  .card-grid {
    grid-template-columns: 1fr;
  }

  .stats-sidebar {
    grid-template-columns: 1fr;
  }

  .deepseek-stats {
    grid-column: 1;
  }
}