<template>
  <div class="header-big">
    <div class="page-header">
      <div class="left-part">
        <!-- <div class="title" @click="goToHome">{{ title }}</div>
      <div class="subtitle">{{ subtitle }}</div> -->
      </div>
      <div class="center-logo">
        <!-- <img src="@/assets/images/gongan.png" alt="公安标志" class="gongan-logo" /> -->
        <div class="title" >{{ title }}</div>
      </div>
      <div class="right-part">
        <!-- <el-tooltip content="返回首页" placement="bottom" effect="light">
        <el-button class="home-btn" type="text" @click="goToHome">
          <i class="el-icon-s-home"></i>
        </el-button>
      </el-tooltip> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["title", "subtitle"],
  methods: {
    goToHome() {
      this.$router.push("/");
    },
  },
};
</script>

<style scoped>
.header-big {
  height: 35px;
}
.page-header {
  /* display: flex; */
  /* justify-content: space-between; */
  align-items: center;
  /* margin-bottom: 20px; */
  padding: 0 20px;
  height: 45px;
  line-height: 45px;
  /* background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2); */
  /* border-radius: 4px; */
  /* position: relative; */
  background-color: #0f2336;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
}

.left-part {
  display: flex;
  flex-direction: column;
}

.center-logo {
  /* position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10; */
}

.gongan-logo {
  width: 60px;
  height: 60px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s;
}

/* .title:hover {
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
  transform: translateY(-1px);
} */

.subtitle {
  font-size: 12px;
  color: rgba(0, 255, 255, 0.6);
  margin-top: 4px;
}

.right-part {
  display: flex;
  align-items: center;
}

.home-btn {
  color: #00ffff;
  font-size: 24px;
  padding: 0;
  margin: 0;
  transition: all 0.3s;
}

.home-btn:hover {
  color: #ffffff;
  transform: scale(1.1);
  background: transparent;
}

.home-btn:focus {
  background: transparent;
}
</style>
