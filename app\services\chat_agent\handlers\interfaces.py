"""
聊天智能体核心处理器接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Tuple


class KnowledgeBaseHandler(ABC):
    """知识库处理器接口"""

    @abstractmethod
    async def search_documents(self, dataset_id: str, query: str) -> List[Dict[str, Any]]:
        """
        根据查询和过滤条件搜索文档
        
        Args:
            query: 搜索查询
            filters: 过滤条件
            
        Returns:
            匹配的文档列表
        """
        pass

    @abstractmethod
    async def extract_document_features(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取文档特征
        
        Args:
            doc_data: 文档数据
            
        Returns:
            文档特征
        """
        pass

    @abstractmethod
    async def extract_feature(self, document_data: Dict[str, Any], existing_features):
        """
        通过dify流程进行文档特征提取

        """
        pass

    @abstractmethod
    async def get_or_create_dataset(self, user: Dict[str, any], agent_id: str):
        """
        获取或创建知识库

        """
        pass

    @abstractmethod
    async def push_document_to_dataset(self, dataset_id: str, document_id: str, context: Dict[str, Any], user: Dict[str, any]):
        """
        获取或创建知识库

        """
        pass

    @abstractmethod
    async def push_segment_to_document(self, dataset_id: str, document_id: str, document_data: Dict[str, Any],
                                       segment_id: str = None):
        """
        获取或创建知识库

        """
        pass

    @abstractmethod
    async def list_datasets(self, agent_id: str, user_context: Dict[str, Any],
                            page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取知识库列表
        
        Args:
            agent_id: 智能体ID
            user_context: 用户上下文信息
            is_personal: 是否只返回个人知识库
            limit: 返回结果数量限制
            
        Returns:
            知识库列表
        """
        pass

    @abstractmethod
    async def check_duplicate_upload(self, document_data:Dict[str, Any], user_context:Dict[str, Any],agent_id:str):
        """
        校验文档是否重复上传知识库

        Args:
            agent_id: 智能体ID
            user_context: 用户上下文信息
            document_data: 知识库文档信息

        Returns:
            重复上传的文档信息
        """
        pass


class QueryProcessingHandler(ABC):
    """查询处理器接口"""

    @abstractmethod
    async def generate_answer(self,
                              query: str,
                              context: List[Dict[str, Any]],
                              user_profile: Dict[str, Any],
                              agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成回答
        
        Args:
            query: 用户查询
            context: 上下文信息
            user_profile: 用户信息
            agent_config: 智能体配置
            
        Returns:
            生成的回答
        """
        pass

    @abstractmethod
    async def generate_answer_stream(self,
                                     query: Dict[str, Any],
                                     global_context: Dict[str, Any],
                                     dataset_context: str,
                                     user_profile: Dict[str, Any],
                                     agent_config: Dict[str, Any]):
        """
        流式生成回答
        
        Args:
            query: 用户查询字典，包含message和可能的file_ids等
            dataset_context: 知识库上下文信息
            global_context: 全局上下文信息
            user_profile: 用户信息
            agent_config: 智能体配置
            
        Yields:
            生成的回答块
        """
        pass

    @abstractmethod
    async def plan_actions(self,
                           query: str,
                           user_profile: Dict[str, Any],
                           agent_config: Dict[str, Any]) -> List[str]:
        """
        规划执行的动作序列
        
        Args:
            query: 用户查询
            user_profile: 用户信息
            agent_config: 智能体配置
            
        Returns:
            动作名称列表
        """
        pass

    @abstractmethod
    async def execute_action(self,
                             action_name: str,
                             params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行指定动作
        
        Args:
            action_name: 动作名称
            params: 动作参数
            
        Returns:
            动作执行结果
        """
        pass


class SessionHandler(ABC):
    """会话管理器接口"""

    @abstractmethod
    async def start_new_session(self, user_id: int, agent_id: str) -> str:
        """
        开始新会话
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            
        Returns:
            会话ID
        """
        pass

    @abstractmethod
    async def clear_session_context(self, session_id: str) -> bool:
        """
        清除会话上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功
        """
        pass

    @abstractmethod
    async def get_recent_history(self,
                                 conversation_id: str,
                                 user_id: int,
                                 first_id: str = None,
                                 limit: int = 10) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        获取最近的会话历史
        
        Args:
            conversation_id: 会话ID
            limit: 限制数量
            
        Returns:
            会话历史列表
        """
        pass

    @abstractmethod
    async def add_message_to_history(self,
                                     session_id: str,
                                     message_data: Dict[str, Any]) -> bool:
        """
        添加消息到历史
        
        Args:
            session_id: 会话ID
            message_data: 消息数据
            
        Returns:
            是否成功
        """
        pass

    @abstractmethod
    async def get_conversations(self,
                                session_id: str,
                                limit: int = 20,
                                last_id: str = None) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        获取用户的会话列表
        
        Args:
            session_id: 会话ID
            limit: 限制数量
            
        Returns:
            会话列表
        """
        pass

    @abstractmethod
    async def update_conversation_name(self,
                                      conversation_id, name, user_id):
        """
        修改会话名称

        Args:
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        pass
    @abstractmethod
    async def file_upload(self,
                          document_data: Dict[str, Any],
                          session_id: str):
        """
        获取用户的会话列表

        Args:
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        pass
