from typing import Any, Dict, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, Body
from starlette.responses import Response

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.models.user import User
from app.services.scene_2_service import Scene2Service
from pydantic import BaseModel
import urllib.parse

router = APIRouter()

class Scene2SearchParams(BaseModel):
    """立而不侦场景查询参数模型"""
    ajbh: Optional[str] = None
    ajmc: Optional[str] = None  # 案件名称
    analysis_result: Optional[str] = None  # 分析结论
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    analysis_status: Optional[str] = None
    ajzt: Optional[str] = None
    zbdwmc: Optional[str] = None
    page: Optional[int] = 1
    page_size: Optional[int] = 10

@router.post("/scene_2/sync")
async def scene_2_data_sync(
        current_user: User = Depends(UserContext.get_current),
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    立而不侦场景数据同步接口

    Returns:
        Dict[str, Any]: 同步结果
    """
    res = await Scene2Service.scene_2_data_sync(current_user.id,start_date,end_date)
    if not res:
        return create_response(
            message="同步异常",
            code=500
        )
    return create_response(
        message="同步成功",
        data=res
    )

@router.post("/scene_2/analyze")
async def scene_2_summary_analyze(ajbhes: List[str] = Body(..., embed=True)) -> Dict[str, Any]:
    """
    立而不侦场景分析接口

    Args:
        start_date: 开始日期，可选
        end_date: 结束日期，可选
        user_id: 用户ID，默认为system

    Returns:
        Dict[str, Any]: 分析结果
    """
    try:
        # 异步调用，指令发送完成即可
        res = await Scene2Service.scene_2_summary_analyze(
            ajbhes=ajbhes
        )
        return create_response(
            message="分析指令已发送",
            data=res
        )
    except Exception as e:
        return create_response(
            message=f"分析异常: {str(e)}",
            code=500
        )

@router.post("/scene_2/search")
async def scene_2_search(
    search_params: Scene2SearchParams = Body(...),
) -> Dict[str, Any]:
    """
    立而不侦场景查询接口

    Args:
        search_params: 查询参数，包含以下可选字段：
            - ajbh: 案件编号
            - start_date: 开始日期
            - end_date: 结束日期
            - analysis_status: 分析状态
            - ajzt: 案件状态
            - zbdwmc: 主办单位名称
            - page: 页码（默认1）
            - page_size: 每页数量（默认10）
        current_user: 当前用户信息

    Returns:
        Dict[str, Any]: 查询结果，包含：
            - total: 总记录数
            - items: 当前页记录列表
    """
    try:
        result = await Scene2Service.scene_2_search(search_params.__dict__)
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        return create_response(
            message=f"查询异常: {str(e)}",
            code=500
        )

@router.post("/scene_2/export")
async def scene_2_export(
    search_params: Scene2SearchParams = Body(...),
) -> Response:
    """
    导出立而不侦场景数据为Excel
    
    Args:
        search_params: 查询参数，包含以下可选字段：
            - ajbh: 案件编号
            - ajmc: 案件名称（模糊搜索）
            - analysis_result: 分析结论
            - start_date: 开始日期
            - end_date: 结束日期
            - analysis_status: 分析状态
            - ajzt: 案件状态
            - zbdwmc: 主办单位名称
            
    Returns:
        Response: Excel文件响应
    """
    try:
        search_params.analysis_status= 'completed'
        # 移除分页参数，获取所有数据
        search_params_dict = search_params.__dict__
        search_params_dict.pop('page', None)
        search_params_dict.pop('page_size', None)
        
        # 查询数据
        result = await Scene2Service.scene_2_search(search_params_dict)
        
        # 导出Excel
        excel_data = Scene2Service.export_excel(result['items'])
        
        # 生成文件名（使用当前时间）
        filename = f"立而不侦分析数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # URL编码文件名
        encoded_filename = urllib.parse.quote(filename)
        
        # 返回Excel文件
        return Response(
            content=excel_data,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f'attachment; filename="{encoded_filename}"',
                "Access-Control-Expose-Headers": "Content-Disposition"
            }
        )
        
    except Exception as e:
        return create_response(
            message=f"导出异常: {str(e)}",
            code=500
        )