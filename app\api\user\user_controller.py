

from fastapi import APIRouter, Depends,File, Form, UploadFile
from sqlalchemy.orm import Session

from app.api.user.excel_utils import process_org_excel, process_org_tree, process_org_user_excel
from app.common.response_com import create_response
from app.db.database import get_db
from app.schemas.user_schema import UserCreateRequest, UserLoginRequest, UserChangeRequest
from app.services.user_service import create_user, authenticate_user, create_access_token, logout_user, get_user, \
    change_pwd, get_police_number, get_user_stats, get_id_card_number, get_user_to_role, get_user_to_org, \
    batchList, getOrgByPoliceCode, upBatchOrgToRole, upBatchUserToOrg, create_user2  # 添加导入

router = APIRouter()


@router.post("/register", description="用户注册")
async def register_user(
        request: UserCreateRequest,
        db: Session = Depends(get_db)
):
    user = get_police_number(db, request.police_number)
    if user is not None:
        return create_response(code=1002, message="警号已存在")
    user_id_card_number = get_id_card_number(db, request.id_card_number)
    if user_id_card_number is not None:
        return create_response(code=1006, message="身份证号已存在")
    # user =
    # access_token = create_access_token(data={"sub": user.police_number})
    return create_user2(db, request)




@router.post("/userlogin", description="用户登录")
async def login_user(
        request: UserLoginRequest,
        db: Session = Depends(get_db)
):
    user = authenticate_user(db, request.police_number, request.password)
    if not user:
        return create_response(code=1003, message="警号或密码错误")
    # 检查用户状态
    if user.is_delete == 1:
        return create_response(code=500, message="账号已被删除")

    if user.is_active != 1:
        return create_response(code=500, message="账号未激活")
    user.is_online = 1
    db.add(user)
    db.commit()
    db.refresh(user)
    userRole = get_user_to_role(db, user.id)  # 用户的主角色  有可能为空
    userOrg=get_user_to_org(db,user.id)  # 用户的主部门
    # 创建用户拓展信息，包含用户角色，用户主部门
    user_expand = {
        "userRole":userRole,  # 用户的主角色
        "userOrg": userOrg,  # 用户的主部门  {org_code:xx,origin_org_code:xx}  我们定义code   映射code
    }
    access_token = create_access_token(data={"sub": user.police_number, "user_expand": user_expand})
    return create_response(code=200, data={"access_token": access_token, "user": user,"userRole":userRole})


@router.post("/logout", description="用户退出登录")
async def logout_user_endpoint(
        token: str,
        db: Session = Depends(get_db),
):
    logout_user(db, token)
    return create_response(code=200, message="退出登录成功")


@router.post("/changepassword", description="修改密码")
async def change_password(
        request: UserChangeRequest,
        db: Session = Depends(get_db)
):
    if get_user(db, request.police_number) is None:
        return create_response(code=1004, message="该用户不存在")
    user = change_pwd(db, request)
    if user is None:
        return create_response(code=1005, message="修改失败")
    return create_response(code=200)


# 新增用户统计接口
@router.get("/userstats", description="用户统计")
async def get_user_statistics(
        db: Session = Depends(get_db)
):
    stats = get_user_stats(db)
    return create_response(code=200, data=stats)

@router.get("/userstats", description="用户统计")
async def get_user_statistics(
        db: Session = Depends(get_db)
):
    stats = get_user_stats(db)
    return create_response(code=200, data=stats)


@router.get("/getOrgByPoliceCode/{police_code}", description="通过警号查询部门")
async def get_org_by_police_code(
    police_code: str,
    db: Session = Depends(get_db)
):
    return getOrgByPoliceCode(db, police_code)



@router.post("/precut/org/upload")
async def upload(
        file: UploadFile = File(...),
        db: Session = Depends(get_db)
):
    org_data = await process_org_excel(file)
    tree = org_data['tree']
    result = process_org_tree(tree)
    batchList(db,result)
    ## 同步机构与角色逻辑
    upBatchOrgToRole(db);
    print(result)

@router.post("/precut/user/upload")
async def uploadUser(
        file: UploadFile = File(...),
        db: Session = Depends(get_db)
):
    org_data = await process_org_user_excel(file,db) ## 处理用户关系excel文件上传
    upBatchUserToOrg(db); ##处理历史用户 机构与用户绑定关系







