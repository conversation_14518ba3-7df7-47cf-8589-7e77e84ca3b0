from typing import Optional
from sqlalchemy.orm import Session
from app.models.situation_analysis_record import SituationAnalysisRecord


def get_existing_report(db: Session, start_time: str, end_time: str) -> Optional[SituationAnalysisRecord]:
    # 实现唯一约束查询逻辑
    # 例如：
    return db.query(SituationAnalysisRecord) \
        .filter(SituationAnalysisRecord.start_time == start_time,
                SituationAnalysisRecord.end_time == end_time) \
        .first()


def save_report(
    db: Session,
    record: SituationAnalysisRecord  # 参数改为接收对象
) -> SituationAnalysisRecord:
    """
    保存态势分析报告记录（支持新增/更新）

    Parameters:
        db: 数据库会话
        record: 报告记录对象（需包含有效数据）

    Returns:
        数据库中保存后的报告记录对象
    """
    db.add(record)
    db.commit()
    db.refresh(record)
    return record
