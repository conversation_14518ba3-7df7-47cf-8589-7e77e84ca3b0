import request from '@/utils/request'

// 获取一人多事数据
export function getClusterEntities(data) {
  return request({
    url: '/api/scene_qz/search_by_entity',
    method: 'post',
    data:data
  })
}

// 一地多起查询
export function searchByLocation(params) {
  return request({
    url: '/api/scene_qz/search_by_location',
    method: 'post',
    data: params
  });
}

// 一事多发查询
export function searchCasesWithSharedEntities(data) {
  return request({
    url: '/api/scene_qz/search_cases_with_shared_entities',
    method: 'post',
    data
  });
}

// 统计数据查询
export function getPeriodicStats(params) {
  return request({
    url: '/api/scene_qz/periodic_stats',
    method: 'get',
    params
  });
}

// 一人多事详情查询
export function getPersonClusterEntities(params) {
  return request({
    url: '/api/scene_qz/cluster_entities',
    method: 'get',
    params
  });
}

// 获取一事多警聚类数据
export function getClusterCasesWithSharedEntities(params) {
  return request({
    url: '/api/scene_qz/cluster_cases_with_shared_entities',
    method: 'get',
    params
  });
}

// 同步数据
export function syncSceneData(params) {
  return request({
    url: '/api/scene_qz/sync',
    method: 'post',
    data: params
  })
}
