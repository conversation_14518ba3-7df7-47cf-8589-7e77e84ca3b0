const path = require('path');
const resolve = dir => path.resolve(__dirname, dir);

// 引入等比适配插件
const pxToRem = require("postcss-pxtorem");
// 配置基本大小
const postcss = pxToRem({
  rootValue: 16, // 表示根元素字体大小或根据input参数返回根元素字体大小
  propList: ["*"], // 可以从px更改为rem的属性, 通配符*表示启用所有属性
  selectorBlackList: [".norem"], // 过滤掉.norem开头的class，不进行rem转换
});

module.exports = {
  outputDir: "caseanalysis-web",
  publicPath: "./",
  productionSourceMap: false,
  filenameHashing: true,
  devServer: {
    proxy: {
      [process.env.VUE_APP_API_BASE_URL]: {
        // target: 'http://192.168.201.221:9904',
        target: "http://192.168.201.221:9904",
        changeOrigin: true,
        pathRewrite: {
          [`^${process.env.VUE_APP_API_BASE_URL}`]: "",
        },
      },
      [process.env.VUE_APP_CHAT_PREFIX]: {
        target: "http://192.168.201.221:9901",
        changeOrigin: true,
        pathRewrite: {
          [`^${process.env.VUE_APP_CHAT_PREFIX}`]: "",
        },
      },
    },
  },
  configureWebpack: {
    performance: {
      hints: false,
    },
    resolve: {
      alias: {
        "@": require("path").resolve(__dirname, "src"),
      },
    },
    output: {
      filename: "js/[name].[contenthash:8].js",
      chunkFilename: "js/[name].[contenthash:8].js",
    },
  },
  chainWebpack: config => {
    config.plugin("html").tap(args => {
      args[0].title = process.env.VUE_APP_TITLE; // 这里可以修改网页标题
      return args;
    });
    config.module
      .rule("json")
      .test(/\.json$/)
      .type("javascript/auto")
      .use("json-loader")
      .loader("json-loader")
      .end();
    config.module
      .rule("docx")
      .test(/\.(docx)$/)
      .use("file-loader")
      .loader("file-loader")
      .options({
        name: "files/[name].[hash:8].[ext]",
      })
      .end();
      // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/images/icons/svg'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/images/icons/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  },
  lintOnSave: false,
  css: {
    // 使用css分离插件 ExtractTextPlugin
    extract: true,
    // 开启 CSS source maps
    sourceMap: false,
    // css预设器配置项
    loaderOptions: {
      postcss: {
        postcssOptions: {
          plugins: [postcss],
        },
      },
    },
  },
};
