.file-review {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;

  .tabs-section {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .action-buttons {
      display: flex;
      gap: 10px;
    }

    .sync-button,
    .batch-button {
      display: flex;
      align-items: center;
      gap: 5px;

      i {
        font-size: 14px;
      }
    }

    .batch-button {
      background-color: rgba(25, 195, 125, 0.2);
      border-color: #19c37d;
      color: #19c37d;

      &:hover {
        background-color: rgba(25, 195, 125, 0.3);
        border-color: #19c37d;
        color: #19c37d;
      }

      &[disabled] {
        background-color: rgba(25, 195, 125, 0.1);
        border-color: rgba(25, 195, 125, 0.3);
        color: rgba(25, 195, 125, 0.5);
      }
    }

    :deep(.el-tabs__header) {
      margin-bottom: 0;
      border-bottom: none;
    }

    :deep(.el-tabs__nav-wrap) {
      margin-bottom: 0;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
      background-color: rgba(0, 48, 102, 0.3);
      border: 1px solid rgba(0, 255, 255, 0.2);
      color: #ffffff;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      transition: all 0.3s;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
      background-color: rgba(0, 255, 255, 0.1);
      border: 1px solid rgba(0, 255, 255, 0.4);
      color: #00ffff;
      font-weight: bold;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
      color: #00ffff;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #00ffff;
      height: 3px;
    }

    :deep(.el-tabs__nav-next),
    :deep(.el-tabs__nav-prev) {
      color: rgba(255, 255, 255, 0.7);
    }

    :deep(.el-tabs__nav-next:hover),
    :deep(.el-tabs__nav-prev:hover) {
      color: #00ffff;
    }

    .tab-indicator {
      background-color: rgba(0, 48, 102, 0.1);
      padding: 8px 16px;
      border-radius: 0 0 4px 4px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      display: flex;
      align-items: center;
      margin-top: 6px;
    }

    .tab-type {
      color: #00ffff;
      font-weight: bold;
      margin-left: 5px;
    }
  }

  :deep(.el-tooltip__popper.is-light) {
    background: rgba(0, 48, 102, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
  }

  :deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow) {
    border-bottom-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow::after) {
    border-bottom-color: rgba(0, 48, 102, 0.95) !important;
  }

  .search-section {
    .search-left {
      height: 110px;
    }
  }

  .search-right {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 10px;
  }

  .content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 0;
  }

  :deep(.el-table) {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  :deep(.el-table__header-wrapper) {
    flex-shrink: 0;
  }

  :deep(.el-table__body-wrapper) {
    flex: 1;
    overflow-y: auto !important;
    height: 0 !important;
  }

  .pagination-container {
    margin-top: 20px;
    margin-right: 60px;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
  }

  /* Element UI 样式覆盖 */
  :deep(.el-input__inner) {
    background-color: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    color: #ffffff;
  }

  :deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 1);
  }

  :deep(.el-input__inner:hover),
  :deep(.el-input__inner:focus) {
    border-color: #00ffff;
  }

  :deep(.el-button) {
    background: rgba(0, 255, 255, 0.2);
    border-color: #00ffff;
    color: #00ffff;
    padding: 0 20px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    min-width: 80px;
  }

  :deep(.el-button:hover) {
    background: rgba(0, 255, 255, 0.3);
    border-color: #00ffff;
    color: #00ffff;
  }

  :deep(.el-table) {
    background-color: transparent !important;
    flex: 1;
    min-height: 0;
    // .el-table__header .el-checkbox {
    //   padding-left: 5px;
    // }
  }

  // :deep(.el-table__header) {
  //   background-color: rgba(0, 48, 102, 0.5) !important;
  // }

  // :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  //   width: 6px;
  //   height: 6px;
  // }

  // :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  //   background: rgba(0, 255, 255, 0.4);
  //   border-radius: 3px;
  // }

  // :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  //   background: rgba(0, 48, 102, 0.3);
  // }

  // :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  //   background-color: rgba(0, 48, 102, 0.5) !important;
  // }

  // :deep(.el-table td, .el-table th.is-leaf) {
  //   border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  //   background-color: rgba(0, 48, 102, 0.3) !important;
  // }

  // :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  //   background-color: rgba(0, 255, 255, 0.2) !important;
  //   color: #00ffff !important;
  // }

  // :deep(.el-table__row) {
  //   transition: all 0.3s;
  // }

  // :deep(.el-table__body tr:hover > td) {
  //   background-color: rgba(0, 255, 255, 0.2) !important;
  //   color: #00ffff !important;
  // }

  // :deep(.el-table__header-wrapper) {
  //   background-color: transparent !important;
  // }

  // :deep(.el-table__body tr) {
  //   background-color: transparent !important;
  // }

  // :deep(.el-table__body tr td) {
  //   background-color: rgba(0, 48, 102, 0.3) !important;
  // }

  // :deep(.el-table__body tr:hover td) {
  //   background-color: rgba(0, 255, 255, 0.1) !important;
  // }

  // .detail-container {
  //   padding: 20px;
  //   background: rgba(0, 48, 102, 0.2);
  //   border-radius: 4px;
  //   margin: 10px 20px;
  // }

  // .detail-header {
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   margin-bottom: 20px;
  //   padding-bottom: 10px;
  //   border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  // }

  // .detail-title {
  //   font-size: 18px;
  //   color: #00ffff;
  //   font-weight: bold;
  // }

  // .detail-number {
  //   color: rgba(0, 255, 255, 0.7);
  //   font-size: 16px;
  // }

  // .detail-content {
  //   display: flex;
  //   flex-direction: column;
  //   gap: 20px;
  // }

  // .detail-section {
  //   background: rgba(0, 48, 102, 0.3);
  //   border: 1px solid rgba(0, 255, 255, 0.1);
  //   border-radius: 4px;
  //   padding: 15px;
  // }

  // .section-title {
  //   color: #00ffff !important;
  //   font-size: 14px;
  //   font-weight: bold;
  //   margin-bottom: 15px;
  //   position: relative;
  //   padding-left: 12px;
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  // }

  // .section-title::before {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   top: 50%;
  //   transform: translateY(-50%);
  //   width: 4px;
  //   height: 16px;
  //   background: #00ffff;
  //   border-radius: 2px;
  // }

  // .detail-grid {
  //   display: grid;
  //   grid-template-columns: repeat(3, 1fr);
  //   gap: 15px;
  // }

  // .detail-item {
  //   display: flex;
  //   align-items: center;
  // }

  // .item-label {
  //   color: rgba(255, 255, 255, 0.7);
  //   margin-right: 8px;
  //   font-size: 14px;
  //   white-space: nowrap;
  // }

  // .item-value {
  //   color: #ffffff;
  //   font-size: 14px;

  //   a {
  //     color: #fff;
  //   }
  // }

  // .detail-text {
  //   color: #ffffff;
  //   font-size: 14px;
  //   line-height: 1.6;
  //   text-align: justify;
  // }

  :deep(.el-table__expanded-cell) {
    background-color: transparent !important;
  }

  :deep(.el-table__expand-icon) {
    color: #00ffff;
  }

  :deep(.el-table__expand-icon .el-icon) {
    font-size: 16px;
    position: static;
    margin-left: 0px;
    margin-right: 0px;
  }

  :deep(.el-table__expand-icon--expanded) {
    transform: rotate(90deg);
  }

  :deep(.el-table__expand-icon:hover) {
    color: #ffffff;
  }

  .pagination-container {
    margin-top: 20px;
  }

  :deep(.el-pagination) {
    padding: 0;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
    background-color: rgba(0, 255, 255, 0.2);
    color: #00ffff;
    border: 1px solid #00ffff;
  }

  :deep(.el-pagination.is-background .el-pager li) {
    background-color: rgba(0, 48, 102, 0.3);
    color: #ffffff;
    border: 1px solid rgba(0, 255, 255, 0.1);
    margin: 0 3px;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
    color: #00ffff;
    border-color: #00ffff;
  }

  :deep(.el-pagination.is-background .btn-next),
  :deep(.el-pagination.is-background .btn-prev) {
    background-color: rgba(0, 48, 102, 0.3);
    color: #ffffff;
    border: 1px solid rgba(0, 255, 255, 0.1);
  }

  :deep(.el-pagination.is-background .btn-next:hover),
  :deep(.el-pagination.is-background .btn-prev:hover) {
    color: #00ffff;
    border-color: #00ffff;
  }

  :deep(.el-pagination__total),
  :deep(.el-pagination__jump),
  :deep(.el-pagination__sizes) {
    color: #ffffff;
  }

  :deep(.el-select:hover .el-input__inner) {
    border-color: #00ffff;
  }

  :deep(.el-select .el-input__inner) {
    background-color: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.1);
    color: #ffffff;
  }

  :deep(.el-select-dropdown) {
    background-color: rgba(0, 48, 102, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  :deep(.el-select-dropdown__item) {
    color: #ffffff;
  }

  :deep(.el-select-dropdown__item.hover),
  :deep(.el-select-dropdown__item:hover) {
    background-color: rgba(0, 255, 255, 0.1);
    color: #00ffff;
  }

  .button-group {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .upload-excel {
    display: inline-block;
  }

  :deep(.el-upload) {
    display: inline-block;
  }

  :deep(.el-button) {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  :deep(.el-icon-upload2),
  :deep(.el-icon-refresh),
  :deep(.el-icon-download) {
    font-size: 16px;
  }

  /* 添加tooltip样式 */
  :deep(.el-tooltip__popper) {
    background-color: rgba(0, 48, 102, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
    max-width: 400px !important;
    line-height: 1.5 !important;
  }

  :deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow) {
    border-top-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow::after) {
    border-top-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow) {
    border-bottom-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow::after) {
    border-bottom-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-form-item__label) {
    color: #ffffff !important;
  }

  :deep(.el-date-editor) {
    // background-color: rgba(0, 48, 102, 0.3) !important;
    border: 1px solid rgba(0, 255, 255, 0.2);
    align-items: center !important;

    .el-range__icon {
      margin-top: 3px;
    }
  }

  :deep(.el-date-editor:hover),
  :deep(.el-date-editor:focus) {
    border-color: #00ffff;
  }

  :deep(.el-form-item.el-form-item--default) {
    margin-bottom: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 10px;
  }

  /* Element UI 样式覆盖 */
  :deep(.el-loading-mask) {
    background-color: rgba(0, 24, 51, 0.9) !important;
  }

  :deep(.el-loading-spinner .el-loading-text) {
    color: #00ffff !important;
  }

  :deep(.el-loading-spinner .path) {
    stroke: #00ffff !important;
  }

  :deep(.el-date-editor .el-range-input) {
    color: #ffffff;
    background: transparent;
  }

  :deep(.el-date-editor .el-range-separator) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel) {
    background: rgba(0, 48, 102, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  :deep(.el-picker-panel__content) {
    color: #ffffff;
  }

  :deep(.el-date-table th) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-date-table td.available:hover) {
    color: #00ffff;
  }

  :deep(.el-date-table td.current:not(.disabled)) {
    background-color: rgba(0, 255, 255, 0.2);
    color: #00ffff;
  }

  :deep(.el-picker-panel__shortcut) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel__shortcut:hover) {
    color: #00ffff;
  }

  :deep(.el-date-picker__header-label) {
    color: #ffffff;
  }

  :deep(.el-date-picker__header-label:hover) {
    color: #00ffff;
  }

  :deep(.el-picker-panel__icon-btn) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel__icon-btn:hover) {
    color: #00ffff;
  }

  :deep(.el-form--inline .el-form-item__label) {
    padding-right: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    float: inline-start;
    flex-shrink: 0;
  }

  :deep(.el-button) {
    height: 32px;
    padding: 0 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.el-button [class*="el-icon-"] + span) {
    margin-left: 4px;
  }

  :deep(.el-select .el-input__inner) {
    padding-right: 25px;
  }

  :deep(.el-date-editor .el-range__icon) {
    line-height: 24px;
  }

  :deep(.el-date-editor .el-range__close-icon) {
    display: flex;
    align-items: center;
  }

  .abnormal-btn {
    color: #ff9900;
    background: transparent;
    border: none;
    padding: 0;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-left: 16px;
  }

  .abnormal-btn:hover {
    color: #ffb84d;
    background: transparent;
  }

  .abnormal-btn i {
    font-size: 16px;
  }

  :deep(.handImportDialog) {
    background-color: #02162c;
  }
  :deep(.handImportDialog .el-dialog__header .el-dialog__title) {
    color: white;
  }
  /* 异常弹框样式 */
  :deep(.abnormal-dialog) {
    background: rgba(0, 24, 51, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    border-radius: 8px !important;
    height: 60% !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
  }

  :deep(.abnormal-dialog .el-dialog__header) {
    padding: 15px 20px !important;
    background: linear-gradient(
      180deg,
      rgba(0, 48, 102, 0.8) 0%,
      rgba(0, 48, 102, 0.6) 100%
    ) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 8px 8px 0 0 !important;
  }

  :deep(.abnormal-dialog .el-dialog__title) {
    color: #00ffff !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
  }

  :deep(.abnormal-dialog .el-dialog__body) {
    padding: 20px !important;
    background: rgba(0, 24, 51, 0.4) !important;
    height: 90% !important;
    overflow-y: auto !important;
  }

  :deep(.input-dialog) {
    height: 400px !important;

    .btn-box {
      display: flex;
      justify-content: center;
    }
  }

  :deep(.input-dialog .el-dialog__body) {
    height: 400px !important;
  }

  :deep(.upload-dialog) {
    height: 515px !important;

    .upload-container {
      border-radius: 6px;
      background-color: transparent;

      .el-upload-list__item-name {
        color: #ffffff;
      }

      .el-upload-list {
        overflow-y: auto;
        height: 200px;
        padding-bottom: 5px;

        /* 滚动条样式 */
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-thumb {
          background: linear-gradient(180deg, rgba(0, 85, 255, 0.6), rgba(0, 192, 255, 0.6));
          border-radius: 4px;
          box-shadow: inset 0 0 4px rgba(0, 192, 255, 0.3);
        }

        &::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(180deg, rgba(0, 85, 255, 0.8), rgba(0, 192, 255, 0.8));
          box-shadow: inset 0 0 6px rgba(0, 192, 255, 0.5);
        }

        &::-webkit-scrollbar-track {
          background: rgba(0, 25, 60, 0.3);
          border-radius: 4px;
          box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        }
      }

      .upload-box {
        .el-upload__tip {
          margin-top: 10px;
          font-size: 14px;
          color: #fff !important;
        }

        .el-upload {
          width: 100%;
          color: #fff !important;
        }

        .el-upload-dragger {
          width: 100%;
          padding: 40px 20px;
          background-color: transparent !important;
          border: 1px dashed #409eff;

          .el-icon-upload {
            font-size: 60px;
            color: #409eff;
            margin-bottom: 20px;
          }

          .el-upload__text {
            font-size: 16px;
            color: #fff;

            em {
              color: #409eff;
              font-style: normal;
            }
          }

          .el-upload__tip {
            margin-top: 10px;
            font-size: 14px;
            color: #fff !important;
          }
        }
      }
    }
  }

  :deep(.upload-dialog .el-dialog__body) {
    height: 400px !important;
    overflow-y: hidden !important;
  }

  .abnormal-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .phase-section {
    background: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 4px;
    padding: 15px;
  }

  .phase-row {
    display: flex;
    gap: 15px;
  }

  .phase-item {
    flex: 1;
    min-width: 0;
  }

  .phase-title {
    color: #00ffff;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  }

  .phase-content {
    color: #ffffff;
    font-size: 14px;
  }

  .abnormal-item {
    color: #ff4d4f;
    margin-bottom: 5px;
    line-height: 1.5;
  }

  .no-abnormal {
    color: #52c41a;
  }

  .qa-abnormal-section,
  .qa-compare-section {
    background: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 4px;
    padding: 15px;
  }

  .section-title {
    color: #00ffff;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .abnormal-table {
    width: 100%;
  }

  .compare-content {
    background: rgba(0, 24, 51, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 4px;
    padding: 15px;
  }

  .compare-text {
    color: #ffffff;
    font-size: 14px;
    line-height: 1.6;
  }

  /* 异常文本高亮 */
  .abnormal-text {
    color: #ff4d4f;
    cursor: pointer;
    position: relative;
  }

  .abnormal-text:hover::after {
    content: attr(data-reason);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 24, 51, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    color: #ffffff;
    z-index: 1;
  }

  :deep(.warning-row) {
    background-color: rgba(255, 0, 0, 0.2) !important;
  }

  :deep(.warning-row td) {
    background-color: rgba(255, 0, 0, 0.2) !important;
  }

  :deep(.el-table--striped .el-table__body tr.warning-row td) {
    background-color: rgba(255, 0, 0, 0.2) !important;
  }

  :deep(.el-table--enable-row-hover .el-table__body tr.warning-row:hover > td) {
    background-color: rgba(255, 0, 0, 0.3) !important;
  }

  /* 添加PDF分析对话框相关样式 */
  :deep(.pdf-analysis-dialog) {
    background: rgba(0, 24, 51, 0.98) !important;
    border: 1px solid rgba(0, 180, 255, 0.3) !important;
    border-radius: 6px !important;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.7) !important;
  }

  :deep(.pdf-analysis-dialog .el-dialog__header) {
    padding: 15px 20px !important;
    background: linear-gradient(180deg, rgba(0, 32, 80, 0.9), rgba(0, 24, 65, 0.85)) !important;
    border-bottom: 1px solid rgba(0, 180, 255, 0.3) !important;
    border-radius: 6px 6px 0 0 !important;
  }

  :deep(.pdf-analysis-dialog .el-dialog__title) {
    color: #00c0ff !important;
    font-size: 20px !important;
    font-weight: bold !important;
    text-shadow: 0 0 10px rgba(0, 192, 255, 0.4) !important;
    letter-spacing: 1px !important;
  }

  :deep(.pdf-analysis-dialog .el-dialog__body) {
    background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 25, 60, 0.98)) !important;
    padding: 25px !important;
    max-height: 75vh !important;
    overflow-y: auto !important;
  }

  :deep(.pdf-analysis-dialog .el-dialog__footer) {
    background: linear-gradient(0deg, rgba(0, 32, 80, 0.9), rgba(0, 24, 65, 0.85)) !important;
    border-top: 1px solid rgba(0, 180, 255, 0.2) !important;
    padding: 12px 20px !important;
    border-radius: 0 0 6px 6px !important;
  }

  .analysis-wrapper {
    min-height: 300px;
    color: #e6f7ff;
  }

  .progress-container {
    margin-bottom: 25px;
    background: rgba(0, 30, 70, 0.5);
    border: 1px solid rgba(0, 180, 255, 0.2);
    border-radius: 8px;
    padding: 20px;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.3);
  }

  .analysis-progress {
    margin-bottom: 15px;
  }

  .analysis-progress :deep(.el-progress-bar__outer) {
    background-color: rgba(0, 40, 85, 0.6) !important;
    border-radius: 10px !important;
    height: 24px !important;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.4) !important;
  }

  .analysis-progress :deep(.el-progress-bar__inner) {
    background: linear-gradient(90deg, #0055ff, #00c0ff) !important;
    border-radius: 10px !important;
    box-shadow: 0 0 10px rgba(0, 192, 255, 0.5) !important;
    transition: width 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) !important;
  }

  .analysis-progress :deep(.el-progress-bar__innerText) {
    color: #ffffff !important;
    font-size: 16px !important;
    font-weight: bold !important;
    text-shadow: 0 0 6px rgba(0, 0, 0, 0.6) !important;
  }

  .analysis-progress :deep(.el-progress__text) {
    color: #00c0ff !important;
    font-weight: bold !important;
    font-size: 16px !important;
    text-shadow: 0 0 8px rgba(0, 192, 255, 0.5) !important;
  }

  .progress-text {
    color: #00c0ff;
    font-size: 18px;
    text-align: center;
    margin: 8px 0;
    font-weight: bold;
    text-shadow: 0 0 6px rgba(0, 192, 255, 0.4);
  }

  .list-header {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: linear-gradient(90deg, rgba(0, 40, 85, 0.8), rgba(0, 50, 100, 0.7));
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #00c0ff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  }

  .list-header i {
    color: #00c0ff;
    margin-right: 10px;
    font-size: 20px;
    text-shadow: 0 0 8px rgba(0, 192, 255, 0.4);
  }

  .list-header span {
    color: #00c0ff;
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 1px;
    text-shadow: 0 0 8px rgba(0, 192, 255, 0.4);
  }

  .process-list {
    background: rgba(0, 30, 70, 0.4);
    border: 1px solid rgba(0, 180, 255, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
  }

  .process-item {
    margin-bottom: 12px;
  }

  .node-header {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background: rgba(0, 40, 85, 0.5);
    border-radius: 6px;
    border-left: 3px solid rgba(0, 180, 255, 0.5);
    transition: all 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .node-header:hover {
    background: rgba(0, 50, 100, 0.6);
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .node-tag {
    background-color: rgba(0, 180, 255, 0.25) !important;
    border-color: rgba(0, 180, 255, 0.2) !important;
    color: #00c0ff !important;
    margin-right: 12px;
    font-weight: bold !important;
  }

  .node-text {
    color: #e6f7ff;
    font-size: 15px;
    line-height: 1.5;
  }

  .final-result {
    background: rgba(0, 30, 70, 0.4);
    border: 1px solid rgba(0, 180, 255, 0.2);
    border-radius: 8px;
    padding: 20px;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
  }

  .result-content {
    background: rgba(0, 20, 45, 0.7);
    border: 1px solid rgba(0, 180, 255, 0.15);
    border-radius: 6px;
    padding: 20px;
    color: #e6f7ff;
    font-family: "Courier New", monospace;
    font-size: 15px;
    line-height: 1.6;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    max-height: 350px;
    overflow-y: auto;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  }

  /* 滚动条样式 */
  .analysis-wrapper::-webkit-scrollbar,
  .result-content::-webkit-scrollbar,
  .process-list::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .analysis-wrapper::-webkit-scrollbar-thumb,
  .result-content::-webkit-scrollbar-thumb,
  .process-list::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(0, 85, 255, 0.6), rgba(0, 192, 255, 0.6));
    border-radius: 4px;
    box-shadow: inset 0 0 4px rgba(0, 192, 255, 0.3);
  }

  .analysis-wrapper::-webkit-scrollbar-thumb:hover,
  .result-content::-webkit-scrollbar-thumb:hover,
  .process-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(0, 85, 255, 0.8), rgba(0, 192, 255, 0.8));
    box-shadow: inset 0 0 6px rgba(0, 192, 255, 0.5);
  }

  .analysis-wrapper::-webkit-scrollbar-track,
  .result-content::-webkit-scrollbar-track,
  .process-list::-webkit-scrollbar-track {
    background: rgba(0, 25, 60, 0.3);
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  }

  /* 优化按钮样式 */
  .pdf-analysis-dialog :deep(.el-button) {
    background: linear-gradient(90deg, rgba(0, 85, 255, 0.2), rgba(0, 192, 255, 0.2));
    border: 1px solid rgba(0, 192, 255, 0.6);
    color: #00c0ff;
    padding: 8px 20px;
    font-size: 15px;
    font-weight: bold;
    border-radius: 6px;
    text-shadow: 0 0 5px rgba(0, 192, 255, 0.3);
    transition: all 0.3s;
    letter-spacing: 1px;
  }

  .pdf-analysis-dialog :deep(.el-button:hover) {
    background: linear-gradient(90deg, rgba(0, 85, 255, 0.3), rgba(0, 192, 255, 0.3));
    border-color: rgba(0, 192, 255, 0.8);
    color: #ffffff;
    box-shadow: 0 0 15px rgba(0, 192, 255, 0.4);
    transform: translateY(-2px);
  }

  /* PDF分析对话框进度条样式 */
  .pdf-analysis-dialog .el-progress-bar__innerText,
  .pdf-analysis-dialog .el-progress__text {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 0 6px rgba(0, 0, 0, 0.5) !important;
  }

  .collapse-btn {
    margin-left: 10px;
  }

  .node-tag {
    margin-left: 10px;
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .item-title {
    font-weight: bold;
  }

  .item-content {
    margin-top: 5px;
  }

  .analysis-result-section {
    margin-top: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }

  .section-title {
    font-weight: bold;
  }

  .is-expanded {
    transform: rotate(90deg);
  }

  .is-expanded i {
    transition: transform 0.3s ease;
  }

  /* 简化PDF分析对话框样式 */
  .pdf-analysis-dialog {
    /deep/ .el-dialog__body {
      padding: 15px 20px;
    }
  }

  .analysis-wrapper {
    min-height: 300px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .section-title {
    margin-top: 10px;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
  }

  .process-list {
    margin-bottom: 20px;
    max-height: 50vh;
    overflow-y: auto;
    padding-right: 10px;

    .process-item {
      margin-bottom: 10px;
    }

    .node-header {
      font-weight: 600;
      padding: 10px 15px;
      color: #f8f9fa;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      transition: all 0.3s;

      &:hover {
        /* background-color: #e9f5fe; */
      }

      .node-index {
        margin-right: 10px;
      }
    }
  }

  .final-result {
    border-top: 1px solid #eee;
    padding-top: 15px;
    max-height: 30vh;
    overflow-y: auto;

    .result-content {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      font-family: "Courier New", monospace;
      white-space: pre-wrap;
    }
  }

  /* 自定义滚动条样式 */
  :deep(.pdf-analysis-dialog .el-dialog__body::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :deep(.pdf-analysis-dialog .el-dialog__body::-webkit-scrollbar-thumb) {
    background: linear-gradient(180deg, rgba(0, 180, 255, 0.7), rgba(0, 255, 255, 0.7));
    border-radius: 4px;
  }

  :deep(.pdf-analysis-dialog .el-dialog__body::-webkit-scrollbar-track) {
    background: rgb(2, 35, 90, 0.9) !important;
    border-radius: 4px;
  }

  /* 更好的滚动效果 */
  .analysis-wrapper,
  .process-list,
  .final-result,
  .result-content {
    scroll-behavior: smooth;
  }

  /* PDF分析对话框进度条样式 */
  .pdf-analysis-dialog .el-progress-bar__innerText,
  .pdf-analysis-dialog .el-progress__text {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 0 4px rgba(0, 255, 255, 0.4) !important;
  }

  /* 为进度条提供更好的视觉效果 */
  .pdf-analysis-dialog .el-progress-bar__outer {
    background-color: rgba(0, 48, 102, 0.5) !important;
    border-radius: 8px !important;
    height: 20px !important;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
  }

  .pdf-analysis-dialog .el-progress-bar__inner {
    background: linear-gradient(90deg, #00b4ff, #00ffff) !important;
    border-radius: 8px !important;
    box-shadow: 0 0 8px rgba(0, 255, 255, 0.4) !important;
    transition: width 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) !important;
  }

  /* 确保所有弹窗内容可滚动 */
  .el-dialog__body {
    max-height: 80vh;
    overflow-y: auto;
  }

  :deep(.error-text-def) {
    background-color: red !important;
  }

  .sync-button {
    position: absolute;
    right: 20px;
    margin-top: -5rem;
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.4);
    color: #00ffff;
    transition: all 0.3s;
  }

  .sync-button:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .sync-button i {
    font-size: 16px;
  }

  .sync-button span {
    font-size: 14px;
  }
}

.left-operations {
  display: flex;
  align-items: center;
}
