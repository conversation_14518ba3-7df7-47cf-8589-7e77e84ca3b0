import os

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    PROJECT_NAME: str = "警情数据处理服务"
    # MySQL数据库URL配置
    SQLALCHEMY_DATABASE_URL: str = "mysql+pymysql://root:<EMAIL>:9905/caseanalysis?charset=utf8mb4"
    LLM_API_URL: str = "http://192.168.201.221:9901/v1/chat-messages"
    # dify登录接口
    DIFY_LOGIN_API_URL: str = "http://192.168.201.221:9901/console/api/login"
    LLM_CRIMINAL_API_TOKEN: str = "app-UpzZ6fUJOtADCHoMHdeqcCk4"
    LLM_ADMINISTRATIVE_API_TOKEN: str = "app-AAp2sHlzI5iKqa2PYoyfANO0"
    # 默认dify用户名
    DIFY_USER: str = "<EMAIL>"
    # 默认dify密码
    DIFY_USER_PASSWD: str = "1qw2!QW@"

    DIFY_API_PREIX: str = 'http://wh.eseasky.cn:9901'
    DIFY_WORKFLOW_ENDPOINT: str = '/v1/workflows/run'

    # 场景2(立案不侦)专用配置
    SCENE2_API_KEY: str = "app-w3dV5FGxnlFx7mvAY8cVwKat"
    # 场景3(法律文书)专用配置
    SCENE3_API_KEY: str = "app-EcENrGiQGFpTLp5DojsnVoO6"
    # 场景4(笔录)专用配置
    SCENE4_API_KEY: str = "app-tAYAhuglmlg65fl7q8dhIE16"
    # 场景5(卷宗审核)专用配置
    SCENE5_API_KEY: str = "app-LTqtQIDK0NDC7ap9PDHTyzOT"

    SCENE_QZ_API_KEY: str = "app-gKTqctOldF7MiL23D3s2X4S4"
    # 卷宗
    DZJZ_API_KEY: str = "app-8x3aCajvRla0IIJwWENpdBDS"

    # 试题集生成数据集工作流
    STJ_API_KEY: str = "app-7WtCdiYWsFykeYOT3NrXzIsP"
    # 法律文书生成数据集工作流
    FLWS_API_KEY: str = "app-QRJNzkneAxSasdNUcpCVfm2j"
    # 数据集生成reasoning工作流
    REASON_API_KEY: str = "app-3F2S9hqEQikcfV31XvOwJSsC"

    # 知识库APIKEY
    DATASETS_API_KEY: str = "dataset-6tUlaX5DWeBsWVxdRUlzU8ta"
    # 特征提取apikey
    EXTRACT_FEATURES: str = "app-uyOJrf3GjulZ1IuCozgHeqsq"
    # 法条运用知识库ID
    LEGAL_CODE_DATASET_ID: str = "acf12a7d-6729-4c45-a608-dfaad23adaed"
    DETENTION_CENTRE_DATASET_ID: str = "2f50fa7b-03b3-4bd5-86b1-3d94c2dc5e0c"

    # reranking_model,embedding_model 配置
    RERANKING_PROVIDER_NAME: str = "langgenius/siliconflow/siliconflow"
    RERANKING_MODEL_NAME: str = "BAAI/bge-reranker-v2-m3"
    EMBEDDING_MODEL: str = "BAAI/bge-large-zh-v1.5"
    EMBEDDING_MODEL_PROVIDER: str = "langgenius/siliconflow/siliconflow"


settings = Settings()

src = "http://192.168.201.221:9900"
# 获取项目根目录

# 定义上传目录 服务器地址的版本
UPLOAD_DIR = os.path.join('/userdata/nginx', 'static', "uploads")

# # 定义上传目录 本地地址的版本
# BASE_DIR = Path(__file__).resolve().parent.parent
# UPLOAD_DIR = os.path.join(BASE_DIR, "static", "uploads")


# 配置参数
upload_file_config = {
    "max_chunk_size": 3000,  # 最大文本块长度
    "overlap_size": 150,  # 块重叠长度
    "allowed_types": ["docx", "pdf", "xls", "xlsx"],
    "max_file_size": 50 * 1024 * 1024  # 50MB
}
