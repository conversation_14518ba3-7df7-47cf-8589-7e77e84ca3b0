from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declarative_base

"""
场景分析表构建基类
"""

Base = declarative_base()


class SceneAnalysis(Base):
    """抽象的基类模型 (不会创建实际表)"""
    __abstract__ = True
    __allow_unmapped__ = True

    id = Column(Integer, primary_key=True, index=True)
    analysis_result = Column(Integer, default=0,
                             comment="分析状态: 0-未分析,1-分析中,2-分析完成,3-分析失败")
    update_time = Column(
        DateTime,
        nullable=True,
        server_default=func.now(),
        onupdate=func.now(),
        index=True,
        comment="最后修改时间"
    )

    def __repr__(self):
        return f"<SceneAnalysis(id={self.id}, result={self.analysis_result})>"
