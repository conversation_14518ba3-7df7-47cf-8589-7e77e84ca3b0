<template>
  <el-card v-if="visible" class="event-cluster-detail">
    <div
      class="panel-header"
      style="display: flex; align-items: center; justify-content: space-between"
    >
      <span style="font-weight: bold">一事多发详情</span>
      <el-button type="text" icon="el-icon-close" @click="$emit('close')" />
    </div>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else class="cluster-list common-scroll">
      <div v-for="(cluster, index) in clusterData" :key="index" class="cluster-item">
        <div class="cluster-header">
          <div class="cluster-title">
            <span class="case-type">{{ cluster.case_type }}</span>
            <span class="involved-count">涉及人员：{{ cluster.involved.length }}人</span>
          </div>
        </div>

        <div class="cluster-body">
          <div class="involved-section">
            <div class="section-title">涉案人员</div>
            <el-table :data="cluster.involved" style="width: 100%" size="small">
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column prop="id_card" label="身份证号" width="200" />
            </el-table>
          </div>

          <div class="cases-section">
            <div class="section-title">关联案件</div>
            <div
              v-for="(case_item, case_index) in cluster.cases"
              :key="case_index"
              class="case-item"
            >
              <div class="case-header">
                <span class="case-id">处警编号：{{ case_item.case }}</span>
                <span class="case-time">报警时间：{{ formatDateTime(case_item.police_time) }}</span>
              </div>
              <div class="case-body">
                <el-table :data="case_item.roles" style="width: 100%" size="small">
                  <el-table-column prop="name" label="姓名" width="120" />
                  <el-table-column prop="id_card" label="身份证号" width="200" />
                  <el-table-column prop="role" label="角色类型" />
                </el-table>
                <div
                  v-if="case_item.other_involved && case_item.other_involved.length > 0"
                  class="other-involved"
                >
                  <div class="section-title">其他涉案人员</div>
                  <el-table :data="case_item.other_involved" style="width: 100%" size="small">
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="id_card" label="身份证号" width="200" />
                    <el-table-column prop="role" label="角色类型" />
                  </el-table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="margin-top: 8px; text-align: right"
    />
  </el-card>
</template>

<script>
import { getClusterCasesWithSharedEntities } from "@/api/caseAnalyze";

export default {
  name: "EventClusterDetail",
  props: {
    visible: Boolean,
    startTime: String,
    endTime: String,
  },
  data() {
    return {
      clusterData: [],
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.page = 1;
        this.fetchClusterData();
      }
    },
    startTime() {
      if (this.visible) {
        this.page = 1;
        this.fetchClusterData();
      }
    },
    endTime() {
      if (this.visible) {
        this.page = 1;
        this.fetchClusterData();
      }
    },
  },
  methods: {
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "";
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    async fetchClusterData() {
      if (this.loading) return;

      try {
        this.loading = true;
        console.log("开始获取一事多警数据:", {
          start_time: this.startTime,
          end_time: this.endTime,
          page: this.page,
          page_size: this.pageSize,
        });

        if (!this.startTime || !this.endTime) {
          this.$message.warning("请选择时间范围");
          return;
        }

        const response = await getClusterCasesWithSharedEntities({
          start_time: this.startTime,
          end_time: this.endTime,
          page: this.page,
          page_size: this.pageSize,
        });

        console.log("获取一事多警数据响应:", response);

        if (response.code === 200) {
          this.clusterData = response.data.items || [];
          this.total = response.data.total || 0;
          console.log("设置clusterData:", this.clusterData);
        } else {
          this.$message.error(response.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取一事多警数据失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.page = 1;
      this.fetchClusterData();
    },

    handleCurrentChange(val) {
      this.page = val;
      this.fetchClusterData();
    },
  },
};
</script>

<style lang="less" scoped>
.event-cluster-detail {
  margin-top: 24px;
  border: none;
  color: rgb(255, 255, 255, 0.8);
  background-color: transparent;
  :deep(tr th) {
    background-color: transparent !important;
  }

  .cluster-list {
    // max-height: 420px;
    overflow-y: auto;
    padding-right: 8px;

    .cluster-item {
      background: #22304a;
      border-radius: 8px;
      margin-bottom: 24px;
      padding: 18px 24px;

      .cluster-header {
        margin-bottom: 16px;

        .cluster-title {
          display: flex;
          align-items: center;
          gap: 16px;

          .case-type {
            font-size: 18px;
            font-weight: 600;
            color: #fff;

            &::before {
              content: "";
              display: inline-block;
              width: 4px;
              height: 16px;
              background: #409eff;
              border-radius: 2px;
              margin-right: 8px;
            }
          }

          .involved-count {
            color: #bfc8e2;
            font-size: 14px;
            padding: 4px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
          }
        }
      }

      .cluster-body {
        .section-title {
          color: #bfc8e2;
          font-size: 14px;
          margin-bottom: 12px;
          padding-left: 8px;
          border-left: 2px solid #409eff;
        }

        .involved-section {
          margin-bottom: 24px;
        }

        .cases-section {
          .case-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;

            .case-header {
              display: flex;
              justify-content: space-between;
              margin-bottom: 16px;

              .case-id {
                color: #fff;
                font-weight: 500;
              }

              .case-time {
                color: #bfc8e2;
              }
            }

            .case-body {
              .other-involved {
                margin-top: 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
