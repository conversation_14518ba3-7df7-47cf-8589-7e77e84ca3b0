from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>wordBearer
from jose import JW<PERSON><PERSON>r, jwt
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.context_handle import UserContext
from app.db.database import get_db
from app.services.user_service import get_police_number, ACCESS_TOKEN_EXPIRE_MINUTES

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

SECRET_KEY = "your_secret_key"
ALGORITHM = "HS256"


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        police_number: str = payload.get("sub")
        user_expand=payload.get("user_expand") ## 获取jwt中的  用户拓展信息
        UserContext.current_user_info_expand.set(user_expand)
        if police_number is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = get_police_number(db, police_number)


    if user is None:
        raise credentials_exception
    
    # 检查用户是否在线
    if user.is_online != 1:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User is offline",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户最后更新时间是否在token失效时间内
    current_time = datetime.now()
    timeout = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    if user.update_time and user.update_time < current_time - timeout:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User session expired",
            headers={"WWW-Authenticate": "Bearer"},
        )

    UserContext.current_user.set(user)  # 修改为使用UserContext类的变量
    return user
