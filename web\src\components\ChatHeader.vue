<template>
  <div class="chat-header-wrapper">
    <div class="header-content">AI智能助手 · {{ title }}</div>
    <!-- <div class="welcome-title">{{ welcomeTitle }}</div> -->
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: "AI智能助手",
    },
    welcomeTitle: {
      type: String,
      default: "欢迎使用AI智能助手，我是你的助手，我可以为您提供帮助。",
    },
  },
};
</script>
<style scoped lang="less">
.chat-header-wrapper {
  width: 100%;
  padding: 15px 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  height: 56px;
  background: #0f2336;
  box-shadow: inset 0 -1px 0 0 #06445d;
  .header-content {
    // font-weight: bold;
    color: #ffffff;
    font-weight: 600;
    font-size: 18px;
    letter-spacing: 0;
  }
  .welcome-title {
    position: absolute;
    bottom: -100%;
    // border: 1px solid #fff;
    border-radius: 20px;
    width: 400px;
    margin: 0 auto;
    text-align: center;
    // margin-top: 350px;
    font-size: 18px;
    padding: 5px;
    color: #fff;
  }
}
</style>
