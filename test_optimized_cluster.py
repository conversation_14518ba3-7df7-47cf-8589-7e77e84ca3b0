#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的共享实体案件聚类函数
验证性能和正确性
"""

import asyncio
import time
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from app.services.scene_qz.scene_qz_service import SceneQzService

async def test_optimized_cluster():
    """测试优化后的聚类函数"""
    print("=" * 60)
    print("测试优化后的共享实体案件聚类函数")
    print("=" * 60)

    # 获取数据库连接
    db = next(get_db())

    try:
        # 测试参数
        test_cases = [
            {
                "name": "完整数据测试",
                "page": 1,
                "page_size": 10,
                "start_time": "2020-03-16 22:17:23",
                "end_time": "2026-03-16 23:56:41"
            },
            {
                "name": "单页测试（用于统计）",
                "page": 1,
                "page_size": 1,
                "start_time": "2020-03-16 22:17:23",
                "end_time": "2026-03-16 23:56:41"
            },
            {
                "name": "第二页测试",
                "page": 2,
                "page_size": 2,
                "start_time": "2020-03-16 22:17:23",
                "end_time": "2026-03-16 23:56:41"
            }
        ]

        for test_case in test_cases:
            print(f"\n🧪 {test_case['name']}")
            print("-" * 40)

            start_time = time.time()

            # 调用优化后的函数
            result = await SceneQzService.cluster_cases_with_shared_entities(
                page=test_case["page"],
                page_size=test_case["page_size"],
                start_time=test_case["start_time"],
                end_time=test_case["end_time"],
                db=db
            )

            end_time = time.time()
            duration = end_time - start_time

            print(f"⏱️  执行时间: {duration:.3f} 秒")
            print(f"📊 总数: {result['total']}")
            print(f"📄 当前页: {result['page']}")
            print(f"📋 页大小: {result['page_size']}")
            print(f"📝 返回条目数: {len(result['items'])}")

            # 验证结果
            if result['items']:
                print(f"\n📋 聚类详情:")
                for i, cluster in enumerate(result['items']):
                    case_type = cluster.get('case_type', 'UNKNOWN')
                    involved_count = len(cluster.get('involved', []))
                    cases_count = len(cluster.get('cases', []))

                    print(f"  聚类 {i+1}: {case_type}")
                    print(f"    共享实体数: {involved_count}")
                    print(f"    案件数: {cases_count}")

                    # 显示共享实体
                    if cluster.get('involved'):
                        print(f"    共享实体:")
                        for person in cluster['involved']:
                            name = person.get('name', 'Unknown')
                            id_card = person.get('id_card', 'None')
                            print(f"      - {name} (身份证: {id_card})")

                    # 显示案件
                    if cluster.get('cases'):
                        print(f"    相关案件:")
                        for case in cluster['cases'][:3]:  # 只显示前3个案件
                            case_id = case.get('case', 'Unknown')
                            roles_count = len(case.get('roles', []))
                            other_count = len(case.get('other_involved', []))
                            print(f"      - {case_id} (共享实体: {roles_count}, 其他: {other_count})")

                        if len(cluster['cases']) > 3:
                            print(f"      ... 还有 {len(cluster['cases']) - 3} 个案件")

                    print()
            else:
                print("❌ 未找到聚类结果")

            print("-" * 40)

        # 性能对比测试
        print(f"\n🚀 性能对比测试")
        print("-" * 40)

        # 多次执行测试性能稳定性
        times = []
        for i in range(5):
            start_time = time.time()
            result = await SceneQzService.cluster_cases_with_shared_entities(
                page=1,
                page_size=1,
                start_time="2020-03-16 22:17:23",
                end_time="2026-03-16 23:56:41",
                db=db
            )
            end_time = time.time()
            duration = end_time - start_time
            times.append(duration)
            print(f"  第 {i+1} 次: {duration:.3f} 秒 (total: {result['total']})")

        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        print(f"\n📈 性能统计:")
        print(f"  平均时间: {avg_time:.3f} 秒")
        print(f"  最快时间: {min_time:.3f} 秒")
        print(f"  最慢时间: {max_time:.3f} 秒")
        print(f"  时间稳定性: {'良好' if (max_time - min_time) < 0.1 else '一般'}")

        print(f"\n✅ 测试完成！")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_optimized_cluster())
