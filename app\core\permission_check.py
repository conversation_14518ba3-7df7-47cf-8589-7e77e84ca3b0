from functools import wraps

from fastapi import HTTPException

from app.core.context_handle import UserContext

from app.services.user_service import check_permission

def require_permissions(permission_key: str = None):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取数据库会话
            db = kwargs.get('db')
            if not db:
                raise HTTPException(status_code=500, detail="数据库连接异常")
            # 获取当前用户信息
            userId = UserContext.get_current_id()
            # 检查权限
            if permission_key:
                check_permission(db, permission_key, userId)
            return func(*args, **kwargs)
        return wrapper

    return decorator