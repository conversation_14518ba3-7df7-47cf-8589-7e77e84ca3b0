import axios from "axios";
import request from "@/utils/request";
import { getToken } from "@/utils/auth";

const BASE_URL = "https://api.deepseek.com/v1"; // 请替换为实际的 DeepSeek API 地址

// 案件类流失巡查
export function chatWithAI(message, conversationId = "") {
  const user = JSON.parse(localStorage.getItem("user"))?.police_number || "";
  let url = "http://192.168.201.221:9901/v1/chat-messages";
  //let url = process.env.VUE_APP_CHAT_PREFIX+"/v1/chat-messages";
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_AI_CHAT_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: {},
      query: message,
      response_mode: "streaming",
      conversation_id: conversationId,
      auto_generate_name: true,
      user: user,
      files: [],
    }),
  });
}

// 智能对话
export function chatOnly(message, conversationId = "") {
  // let url = process.env.VUE_APP_CHAT_PREFIX+"/v1/chat-messages";
  let url = "http://192.168.201.221:9901/v1/chat-messages";
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_AI_CHAT_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: {},
      query: `请使用Markdown格式回答以下问题：${message}`,
      response_mode: "streaming",
      conversation_id: conversationId,
      user: "abc-123",
      files: [],
    }),
  });
}

export function chatNl2sqlFlow(message, conversationId = "") {
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/chat-messages";
  // let url = "http://120.46.15.192:9901/v1/chat-messages";
  return fetch(url, {
    method: "POST",
    headers: {
      // 'Authorization': process.env.VUE_APP_DIFY_MESSAGE_TOKEN,
      Authorization: "Bearer app-hHZEBP3rspuehFYxcf4mTBVx",
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: {},
      query: `${message}`,
      response_mode: "streaming",
      conversation_id: conversationId,
      user: "abc-123",
      files: [],
    }),
  });
}

export function chatNl2sql(message) {
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/workflows/run";
  // let url = "http://120.46.15.192:9901/v1/workflows/run";
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_DIFY_WORKFLOW_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: { input: message },
      // response_mode: "blocking",
      response_mode: "streaming",
      user: "abc-123",
      files: [],
    }),
  });
}

export function chatSituationAnalysis(message) {
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/workflows/run";
  // let url = "http://120.46.15.192:9901/v1/workflows/run";
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_SITUATION_WORKFLOW_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: { input: message },
      response_mode: "blocking",
      user: "abc-123",
      files: [],
    }),
  });
}

export function runPoliceSituationWorkflow(
  startMonth,
  endMonth,
  reportId,
  unitIds = [],
  police_station
) {
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/workflows/run";
  // let url = "http://120.46.15.192:9901/v1/workflows/run"
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_SITUATION_WORKFLOW_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: {
        type: "police_situation",
        date_range: JSON.stringify([
          String(startMonth.replace("-", "")),
          String(endMonth.replace("-", "")),
        ]),
        report_id: reportId,
        unit_ids: unitIds,
        police_station: police_station,
      },
      response_mode: "streaming",
      user: "abc-123",
    }),
  });
}

// 处理PDF文件分析的工作流
export function runPdfAnalysisWorkflow(mdId) {
  const token = localStorage.getItem("token");
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/workflows/run";
  // let url = "http://120.46.15.192:9901/v1/workflows/run";
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_CHECK_RECORD_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      inputs: {
        md_id: mdId,
        jwt: "Bearer " + token,
      },
      response_mode: "streaming",
      user: "abc-123",
    }),
  });
}

// 导出警情数据
// export function chatNl2sql(message) {
//   return request({
//     url: '/api/nl2sql',
//     method: 'post',
//     params:message,
//   })
// }

export function fetchConversations({ last_id = "", limit = 19 }) {
  // let url = 'http://192.168.201.221:9901/v1/conversations'
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/conversations";
  const user = JSON.parse(localStorage.getItem("user"))?.police_number || "";
  return axios.get(url, {
    params: { user, last_id, limit },
    headers: {
      Authorization: process.env.VUE_APP_AI_CHAT_TOKEN,
    },
  });
}

export function fetchConversationHistory({ conversation_id, api_key, limit = 10, first_id = "" }) {
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/messages";
  const user = JSON.parse(localStorage.getItem("user"))?.police_number || "";
  return axios.get(url, {
    params: { user, conversation_id, limit, first_id },
    headers: {
      Authorization: process.env.VUE_APP_AI_CHAT_TOKEN,
    },
  });
}

export function deleteConversation(conversationId) {
  // let url = `http://192.168.201.221:9901/v1/conversations/${conversationId}`
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/conversations/" + conversationId;
  const user = JSON.parse(localStorage.getItem("user"))?.police_number || "";
  return fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: process.env.VUE_APP_AI_CHAT_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ user }),
  });
}

export function renameConversation(conversationId, name) {
  // let url = `http://192.168.201.221:9901/v1/conversations/${conversationId}/name`
  let url = process.env.VUE_APP_CHAT_PREFIX + "/v1/conversations/" + conversationId + "/name";
  const user = JSON.parse(localStorage.getItem("user"))?.police_number || "";
  return fetch(url, {
    method: "POST",
    headers: {
      Authorization: process.env.VUE_APP_AI_CHAT_TOKEN,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name,
      auto_generate: false,
      user,
    }),
  });
}

/* 创建用户与agent的会话关联
  POST /api/agents/{agent_id}/create_session
  接口ID：298522815
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298522815 */
export function create_session(agent_id) {
  return request({
    url: `/api/agents/${agent_id}/create_session`,
    method: "POST",
  });
}

/* 获取用户与智能体的会话关联
  GET /api/agents/{agent_id}/get_session
  接口ID：298861648
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298861648 */
export function get_session(agent_id) {
  return request({
    url: `/api/agents/${agent_id}/get_session`,
    method: "GET",
  });
}

/*   发送聊天消息
  POST /api/agents/{agent_id}/sessions/chat
  接口ID：298556770
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298556770 */
export function send_chat_message(agent_id, conversation_id = undefined, data) {
  const token = getToken();
  const str = `${process.env.VUE_APP_API_BASE_URL}/api/agents/${agent_id}/sessions/chat`;
  // const str = `http://192.168.201.25:9904/api/agents/${agent_id}/sessions/chat`;
  // const str = `http://192.168.201.221:9904/api/agents/${agent_id}/sessions/chat`;
  var params = { conversation_id };
  const queryString = new URLSearchParams(params).toString();
  // 完整的请求 URL（包含查询参数）
  const requestUrl = conversation_id ? `${str}?${queryString}` : `${str}`;
  return fetch(requestUrl, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
}

/* 获取历史对话列表
  GET /api/agents/{agent_id}/conversations/{session_id}
  接口ID：298559107
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298559107 */
export function get_conversation_list(agent_id, session_id, data) {
  return request({
    url: `/api/agents/${agent_id}/conversations/${session_id}`,
    method: "GET",
    params: {
      limit: 19,
      ...data,
    },
  });
}

/* 获取历史对话
  GET /api/agents/{agent_id}/conversation/history
  接口ID：298559670
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298559670 */
export function get_conversation_history(agent_id, data) {
  return request({
    url: `/api/agents/${agent_id}/conversation/history`,
    method: "GET",
    params: data,
  });
}

/* 文件上传
  POST /api/agents/file/upload
  接口ID：299041231
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-299041231 */
export function upload_file(data) {
  return request({
    url: `/api/agents/file/upload`,
    method: "POST",
    data: data,
    timeout: 60 * 1000 * 2,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/* 获取知识库列表
  GET /api/agents/{agent_id}/datasets
  接口ID：298560507
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298560507 */
export function get_datasets(agent_id, data) {
  return request({
    url: `/api/agents/${agent_id}/datasets`,
    method: "GET",
    params: data,
  });
}

/* 上传文档到知识库
  POST /api/agents/{agent_id}/dataset/upload
  接口ID：298557731
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-298557731 */
export function upload_dataset(agent_id, data) {
  return request({
    url: `/api/agents/${agent_id}/dataset/upload`,
    method: "POST",
    data: data,
    timeout: 60 * 1000 * 2,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/* 修改会话名称
  POST /api/agents/{agent_id}/conversations/{conversation_id}/name
  接口ID：300030618
  接口地址：https://app.apifox.com/link/project/6008240/apis/api-300030618 */
export function update_conversation_name(agent_id, conversation_id, data) {
  console.log(data);
  return request({
    url: `/api/agents/${agent_id}/conversations/${conversation_id}/name`,
    method: "POST",
    data,
  });
}
