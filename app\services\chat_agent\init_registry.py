"""
初始化智能体注册表
"""
from app.services.chat_agent.base import AgentType
from app.services.chat_agent.registry import AgentRegistry
from app.services.chat_agent.agents.specific_agents import (
    GeneralChatAgent,
    LegalCodeAgent,
    SecretarialAgent, DetentionCenterCodeAgent
)


def init_agent_registry() -> AgentRegistry:
    """
    初始化智能体注册表
    
    Returns:
        初始化后的智能体注册表
    """
    registry = AgentRegistry()
    
    # 注册智能体类型
    registry.register_agent_type(AgentType.GENERAL.name, GeneralChatAgent)
    registry.register_agent_type(AgentType.LEGAL_CODE.name, LegalCodeAgent)
    registry.register_agent_type(AgentType.SECRETARIAL.name, SecretarialAgent)
    registry.register_agent_type(AgentType.DETENTION_CENTRE.name, DetentionCenterCodeAgent)

    return registry
