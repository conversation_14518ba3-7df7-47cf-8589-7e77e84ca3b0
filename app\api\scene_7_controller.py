
from datetime import datetime

from fastapi import Depends, Body, APIRouter
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.db.database import get_db
from app.models.situation_analysis_record import SituationAnalysisRecord
from app.schemas.situation_analysis_schema import ReportGenerateRequest, UpdateReportRequest, ListReportRequest, \
    DetailReportRequest, DeleteReportRequest
from app.services.situation_analysis_service import get_existing_report, save_report  # 新增导入服务层

router = APIRouter()


@router.post("/generate", description="生成执法分析报告")
async def start_report_generation(
    request: ReportGenerateRequest = Body(...),
    db: Session = Depends(get_db),
):
    try:
        start_dt = datetime.strptime(request.start_time, "%Y-%m-%d")
        end_dt = datetime.strptime(request.end_time, "%Y-%m-%d")
    except ValueError:
        raise create_response(code=500, message="时间格式错误，应为YYYY-MM-DD")

    # 2. 时间范围校验
    if start_dt >= end_dt:
        raise create_response(code=500, message="结束月份必须晚于开始月份")

    # 查询已存在报告
    existing_report = get_existing_report(db, request.start_time, request.end_time)  # 调用服务层方法
    if existing_report and existing_report.status != 1:
        return create_response(code= 1001, message="正在进行的执法分析报告")

    # 3. 创建新报告
    report_name = (
        f"{start_dt.strftime('%Y年%m月')}-"  # 格式化为"2024年01月"
        f"{end_dt.strftime('%m月')}"  # 格式化为"04月"
        "执法分析报告"
    )
    user = UserContext.get_current()
    new_report = SituationAnalysisRecord(
        user_id = user.id,
        report_name=report_name,
        start_time=start_dt,
        end_time=end_dt,
        status=0
    )
    # 调用服务层保存
    saved_report = save_report(db, new_report)
    return create_response(data=saved_report,message="报告生成任务已创建")





@router.post("/reports", description="报告列表模糊查询接口")
async def list_reports(
        request: ListReportRequest = Body(...),
        db: Session = Depends(get_db),
):
    query = db.query(SituationAnalysisRecord)

    # 模糊查询条件
    if request.report_name and request.report_name.strip():
        query = query.filter(
            SituationAnalysisRecord.report_name.ilike(f"%{request.report_name.strip()}%")
        )

    # 计算总数
    total = query.count()

    # 分页查询
    records = query.order_by(SituationAnalysisRecord.created_at.desc()).offset(
        (request.page - 1) * request.page_size
    ).limit(request.page_size).with_entities(
        SituationAnalysisRecord.id,
        SituationAnalysisRecord.report_name,
        SituationAnalysisRecord.start_time,
        SituationAnalysisRecord.end_time,
        SituationAnalysisRecord.status,
        SituationAnalysisRecord.created_at,
        SituationAnalysisRecord.updated_at
    ).all()

    # 转换为字典列表
    results = [
        {
            "id": r.id,
            "report_name": r.report_name,
            "start_time": r.start_time.strftime("%Y-%m-%d"),
            "end_time": r.end_time.strftime("%Y-%m-%d"),
            "status": r.status,
            "created_at": r.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": r.updated_at.strftime("%Y-%m-%d %H:%M:%S") if r.updated_at else None
        } for r in records
    ]

    return create_response(data={
        "records": results,
        "total": total,
        "page": request.page,
        "page_size": request.page_size,
        "total_pages": (total + request.page - 1) // request.page_size
    })
@router.post("/get_report_detail", description="报告详情查询接口")
async def get_report_detail(
        request: DetailReportRequest = Body(...),
        db: Session = Depends(get_db),
):
    """根据ID获取报告详情（包含MD内容）"""
    record = db.query(SituationAnalysisRecord).filter_by(id=request.report_id).first()
    if not record:
        raise create_response(code=500, message="报告不存在")

    # 返回完整记录（包括MD内容）
    detail = {
        "id": record.id,
        "report_name": record.report_name,
        "start_time": record.start_time.strftime("%Y-%m-%d"),
        "end_time": record.end_time.strftime("%Y-%m-%d"),
        "status": record.status,
        "status_label": record.STATUS_MAPPING.get(record.status, "未知状态"),
        "created_at": record.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        "updated_at": record.updated_at.strftime("%Y-%m-%d %H:%M:%S") if record.updated_at else None,
        "md_content": record.md_content
    }
    return create_response(data=detail)

# 2. 新增删除接口
@router.post("/delete", description="删除执法分析报告")
async def delete_report(
    request: DeleteReportRequest = Body(...),
    db: Session = Depends(get_db),
):
    # 查询记录是否存在
    record = db.query(SituationAnalysisRecord).filter_by(id=request.report_id).first()
    if not record:
        raise create_response(code=500, message="报告记录不存在")

    try:
        # 执行删除操作
        db.delete(record)
        db.commit()
        return create_response(code = 200, message="报告删除成功")
    except Exception as e:
        db.rollback()
        raise create_response(code=500, message=f"删除失败: {str(e)}")
