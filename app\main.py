import asyncio
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, APIRouter, Depends
from fastapi.middleware.cors import CORSMiddleware

from app.api import scene_1_controller, scene_controller, scene_7_controller, scene_6_controller, \
    scene_5_controller, dataset_controller, scene_qz_controller
from app.api import scene_4_controller, scene_2_controller, scene_3_controller, chat_controller
from app.api.scene_6_controller import init_scheduler, scheduler
from app.api.user import role_controller, org_controller, menu_controller, user_backend_controller, api_info_controller, \
    user_controller
from app.config import settings
from app.core.auth import get_current_user
from app.db.database import create_tables
from app.log.log_utils import LogUtils
from app.services.scene_2_service import Scene2Service
from app.services.scene_3_service import Scene_3_Service
from app.services.scene_4_service import Scene4Service


# 在应用启动时创建表
@asynccontextmanager
async def lifespan(app: FastAPI):
    LogUtils.info(f"正在初始化数据库...")
    create_tables()
    LogUtils.info(f"数据库初始化完成！...")

    # 启动后台任务处理器
    # 创建信号量，限制最大并发任务数为10
    max_concurrent_tasks = 10

    task_semaphore1 = asyncio.Semaphore(max_concurrent_tasks)
    asyncio.create_task(Scene2Service.start_background_processor(task_semaphore1))

    task_semaphore2 = asyncio.Semaphore(max_concurrent_tasks)
    asyncio.create_task(Scene_3_Service.start_background_processor(task_semaphore2))

    task_semaphore3 = asyncio.Semaphore(max_concurrent_tasks)
    asyncio.create_task(Scene4Service.start_background_processor(task_semaphore3))

    # 初始化定时任务
    init_scheduler(app)

    yield

    # 关闭调度器
    if scheduler.running:
        scheduler.shutdown(wait=False)
        LogUtils.info("场景6定时任务已关闭")


app = FastAPI(title=settings.PROJECT_NAME, lifespan=lifespan)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 公共路由（无需认证）
public_router = APIRouter()
public_router.include_router(user_controller.router)  # 挂载 auth 模块中的公共路由
public_router.include_router(scene_controller.router)

# 受保护路由（需要认证）
protected_router = APIRouter(dependencies=[Depends(get_current_user)])
# 挂载模块中的受保护路由
protected_router.include_router(scene_1_controller.router)
protected_router.include_router(scene_2_controller.router)
protected_router.include_router(scene_3_controller.router)
protected_router.include_router(scene_4_controller.router)
protected_router.include_router(scene_5_controller.router)
protected_router.include_router(scene_6_controller.router)
protected_router.include_router(scene_7_controller.router)
protected_router.include_router(dataset_controller.router)
public_router.include_router(scene_qz_controller.router)
protected_router.include_router(chat_controller.router)

protected_router.include_router(role_controller.router)
protected_router.include_router(org_controller.router)
protected_router.include_router(menu_controller.router)
protected_router.include_router(user_backend_controller.router)
protected_router.include_router(api_info_controller.router)
# 注册路由
app.include_router(public_router, prefix="/api")
app.include_router(protected_router, prefix="/api")

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=9904, limit_concurrency=1000, timeout_keep_alive=15, backlog=2048,
                limit_max_requests=3000)
