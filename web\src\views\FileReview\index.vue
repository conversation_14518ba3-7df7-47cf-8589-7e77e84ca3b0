<template>
  <div class="file-review bigscreen">
    <PageHeader title="卷宗审核" subtitle="File audit" />

    <div class="content-wrapper">
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="common-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="已起诉刑事案件" name="10">
          </el-tab-pane>
          <el-tab-pane label="已结案行政案件" name="21">
          </el-tab-pane>
        </el-tabs>
        <div class="action-buttons">
        </div>
      </div>
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="common-search search-form">
          <div class="search-left" :class="[showMoreSearch && 'has-height']">
            <el-form-item label="案件编号">
              <el-input v-model="searchForm.ajbh" placeholder="请输入案件编号"></el-input>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item label="案件名称">
              <el-input v-model="searchForm.ajmc" placeholder="请输入案件名称"></el-input>
            </el-form-item>
            <!-- <el-form-item label="主办人">
              <el-input v-model="searchForm.hoster_name" placeholder="请输入主办人"></el-input>
            </el-form-item> -->
            <el-form-item label="分析状态">
              <el-select v-model="searchForm.analysis_result" placeholder="请选择分析状态" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="未分析" value="0"></el-option>
                <el-option label="分析中" value="1"></el-option>
                <el-option label="分析完成" value="2"></el-option>
                <el-option label="分析失败" value="3"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <div class="el-button_text" @click="toggleMoreSearch">
                {{ !showMoreSearch ? '收起' : '展开' }}
                <i v-show="showMoreSearch" class="el-icon-arrow-down"></i>
                <i v-show="!showMoreSearch" class="el-icon-arrow-up"></i>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button type="primary" @click="handleSync" class="tool-btn">
            <i class="el-icon-refresh"></i>
            <span>同步数据</span>
          </el-button>
          <el-button type="primary" @click="handleBatchAnalyze" :disabled="selectedRows.length === 0" class="tool-btn">
            <i class="el-icon-data-analysis"></i>
            批量分析
          </el-button>
          <el-button type="primary" @click="handleImport" class="tool-btn">
            <i class="el-icon-upload2"></i>
            导入
          </el-button>
          <!-- <el-button type="primary" @click="handleBattchFileUpSC">
            <i class="el-icon-data-analysis"></i>
            {{ activeTab == 10 ? '刑事' : '行政' }}附件审查
          </el-button> -->
        </div>
      </div>

      <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          :row-class-name="rowClassName" v-loading="loading" :row-key="row => row.md_id"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="60" align="center" :selectable="checkSelectable"></el-table-column>
          <el-table-column type="index" label="序号" width="70"></el-table-column>
          <el-table-column prop="case_name" label="案件名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="case_code" label="案件编号" show-overflow-tooltip min-width="120"></el-table-column>
          <el-table-column prop="handle_org_code" label="办案单位" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="hoster_name" label="主办人" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="mod_time" label="更新时间" width="180" align="center" show-overflow-tooltip
            :formatter="formatDateTime"></el-table-column>
          <!-- <el-table-column
            prop="fh_rksj"
            label="入库时间"
            width="180"
            align="center"
            show-overflow-tooltip
            :formatter="formatDateTime"
          ></el-table-column> -->
          <el-table-column prop="ocr_status" label="识别状态" width="120" align="center">
            <template slot-scope="scope">
              <!-- <el-tag :type="getOcrStatusType(scope.row.ocr_status)">
                {{ getOcrStatusText(scope.row.ocr_status) }}
              </el-tag> -->
              <StatusView :status-option="formatOcrStatus(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column prop="analysis_result" label="分析状态" width="120" align="center">
            <template slot-scope="scope">
              <!-- <el-tag :type="getAnalysisStatusType(scope.row.analysis_result)">
                {{ getAnalysisStatusText(scope.row.analysis_result) }}
              </el-tag> -->
              <StatusView :status-option="formatAnalysisStatus(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" align="center">
            <!-- 未识别状态才能看到上传按钮-->
            <template slot-scope="scope">
              <!-- <el-button v-if="scope.row.ocr_status === 0" type="primary" size="mini" @click="handleUpload(scope.row)">
                上传
              </el-button> -->
              <el-button v-if="scope.row.analysis_result === 2" type="success" size="mini" class="row-operation-btn row-operation-btn-color"
                @click="handleReview(scope.row)">
                在线审阅
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>
    </div>

    <!-- 添加录入弹框 -->
    <el-dialog title="录入案件" :visible.sync="inputDialogVisible" width="500px" height="500px"
      :before-close="handleInputCancel" custom-class="abnormal-dialog input-dialog" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="true">
      <div class="input-content">
        <el-form :model="inputForm" :rules="inputFormRules" ref="inputForm" label-width="80px">
          <el-form-item label="案件名称" prop="case_name">
            <el-input v-model="inputForm.case_name" placeholder="请输入案件名称"></el-input>
          </el-form-item>
          <el-form-item label="案件编号" prop="case_code">
            <el-input v-model="inputForm.case_code" placeholder="请输入案件编号"></el-input>
          </el-form-item>
          <el-form-item label="办案单位" prop="handle_org_code">
            <el-input v-model="inputForm.handle_org_code" placeholder="请输入办案单位"></el-input>
          </el-form-item>
          <el-form-item label="主办人" prop="hoster_name">
            <el-input v-model="inputForm.hoster_name" placeholder="请输入主办人"></el-input>
          </el-form-item>
        </el-form>
        <div class="btn-box">
          <el-button type="primary" @click="handleInputSubmit">提交</el-button>
          <el-button @click="handleInputCancel">取消</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="上传文件" :visible.sync="uploadDialogVisible" width="500px" :before-close="handleUploadCancel"
      :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true"
      custom-class="abnormal-dialog upload-dialog">
      <div class="upload-container">
        <el-upload class="upload-box" multiple :before-upload="beforeUploadPDF" :file-list="uploadFiles" action="123"
          drag :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip" slot="tip">支持上传PDF，PNG文件，单个文件不超过100MB</div>
        </el-upload>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleUploadCancel">取 消</el-button>
        <el-button type="primary" @click="handleUploadPDF">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 日期范围选择对话框 -->
    <DateRangeDialog :visible.sync="dateRangeDialogVisible" @confirm="handleDateRangeConfirm"
      @cancel="handleDateRangeCancel" @close="handleDateRangeClose"></DateRangeDialog>

    <el-dialog
      title="选择案件类型"
      :visible.sync="importDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      custom-class="handImportDialog"
      @close="handleImportDialogClose"
    >
      <el-form label-width="100px">
        <el-form-item label="案件类型" required>
          <el-select v-model="selectedCaseType" placeholder="请选择案件类型" style="width: 100%">
            <el-option
              v-for="item in caseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleImportDialogClose">取 消</el-button>
        <el-button type="primary" @click="handleImportConfirm" :disabled="!selectedCaseType">确 定</el-button>
      </span>
    </el-dialog>

    <input
      ref="uploadInput"
      type="file"
      accept=".pdf"
      style="display: none"
      @change="handleFileChange"
    >
  </div>
</template>

<script>
import { getRecordList, getRecordDetail, syncList, create_aj, uploadFile, batchAnalyzeCases, importPDF } from "@/api/file";
import PageHeader from "@/components/PageHeader.vue";
import DateRangeDialog from "@/components/DateRangeDialog.vue";
import StatusView from "@/components/StatusView.vue";

export default {
  name: "RecordCheck",
  components: {
    PageHeader,
    DateRangeDialog,
    StatusView
  },
  data() {
    return {
      searchForm: {
        timeRange: [],
        ajmc: "",
        hoster_name: "",
        analysis_result: "",
        ajbh: '',
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
      total: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      autoRefreshTimer: null,
      activeTab: "10",
      fileList: [],
      inputDialogVisible: false,
      inputForm: {
        case_name: "",
        case_code: "",
        handle_org_code: "",
        hoster_name: "",
      },
      inputFormRules: {
        case_name: [{ required: true, message: "请输入案件名称", trigger: "blur" }],
        case_code: [{ required: true, message: "请输入案件编号", trigger: "blur" }],
        handle_org_code: [{ required: true, message: "请输入办案单位", trigger: "blur" }],
        hoster_name: [{ required: true, message: "请输入主办人", trigger: "blur" }],
      },
      uploadFiles: [],
      uploadForm: {
        md_id: "",
      },
      uploadDialogVisible: false,
      dateRangeDialogVisible: false,
      selectedRows: [],
      showMoreSearch: false,
      importDialogVisible: false,
      selectedCaseType: '',
      caseTypeOptions: [
        { label: '刑事案件', value: '10' },
        { label: '行政案件', value: '21' }
      ],
      analysisFormatMap: {
        0: { text: "未分析", color: "#909399", iconType: '', bgColor: "rgba(144,147,153,0.1)" },
        1: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        2: { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
      },
      ocrFormatMap: {
        0: { text: "未识别", color: "#909399", iconType: '', bgColor: "rgba(144,147,153,0.1)" },
        2: { text: "识别完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "识别失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
      },
    };
  },
  created() {
    this.loadTableData();
    // 每分钟刷新一次
    // this.autoRefreshTimer = setInterval(() => {
    //   this.loadTableData();
    // }, 1000 * 60);
    // 使用实例属性保存定时器ID，便于后续清除
  },
  beforeDestroy() {
    // 清除定时器
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
    }
  },
  methods: {
    formatOcrStatus(row) {
      const status = this.ocrFormatMap[row.ocr_status];
      return status || {};
    },
    // 分析状态格式化
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.analysis_result];
      return status || {};
    },
    calcException(detection_result) {
      if (detection_result) {
        let result = JSON.parse(detection_result);
        if (result.check_status === 1) {
          return true;
        }
      }
      return false;
    },
    formattedText(text) {
      return text.replace(/\n/g, "<br>");
    },
    handleUpload(row) {
      this.uploadForm.md_id = row.md_id;
      this.uploadDialogVisible = true;
    },
    handleUploadCancel() {
      this.uploadDialogVisible = false;
      this.uploadFiles = [];
    },
    handleChange(file, fileList) {
      // 调用 beforeUploadPDF 进行验证
      const isValid = this.beforeUploadPDF(file.raw);
      console.log(isValid, "isValid");
      if (!isValid) {
        // 如果验证不通过，从文件列表中移除该文件
        this.uploadFiles = fileList.filter(f => f.uid !== file.uid);
        return false;
      }
      this.uploadFiles = fileList.filter(f => {
        return ["application/pdf", "image/png"].includes(f.raw.type);
      });
      return true;
    },
    handleRemove(file, fileList) {
      this.uploadFiles = fileList;
    },
    beforeUploadPDF(file) {
      console.log(file, "file");
      const isTypeOk = ["application/pdf", "image/png"].includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 100;

      if (!isTypeOk) {
        this.$message.error("只能上传 PDF 或 PNG 文件!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    async handleUploadPDF(options) {
      // 检查文件状态
      if (this.uploadFiles.length === 0) {
        this.$message.error("请选择要上传的文件!");
        return;
      }
      // 检查文件是否已准备好，如果文件未准备好，则提示用户;
      if (this.uploadFiles.some(file => file.status !== "ready")) {
        this.$message.error("文件未添加完成，请等待!");
        return;
      }
      try {
        const file = options.file;
        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
             是否确认文件无缺失？是否上传？
            </div>
          </div>
        `,
          "上传确认",
          {
            confirmButtonText: "确定上传",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const formData = new FormData();
          this.uploadFiles.forEach(file => {
            formData.append("files", file.raw);
          });
          formData.append("md_id", this.uploadForm.md_id);
          const config = {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          };

          const res = await uploadFile(formData, config);
          if (res.code === 200) {
            this.$message.success(res.message || "文件上传成功");
            this.uploadDialogVisible = false;
            this.uploadFiles = [];

            // 重新加载表格数据
            await this.loadTableData();
          } else {
            // throw new Error(res.message || "PDF上传失败");
          }
        } else {
          this.$message.info("已取消上传");
        }
      } catch (error) {
        if (error.toString() !== "cancel") {
          console.error("PDF上传失败:", error);

        }
      } finally {
        this.loading = false;
      }
    },
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
          aj_type: this.activeTab,
        };

        // 添加搜索条件
        if (this.searchForm.timeRange && this.searchForm.timeRange.length === 2) {
          params.start_time = this.searchForm.timeRange[0];
          params.end_time = this.searchForm.timeRange[1];
        }
        if (this.searchForm.ajmc) {
          params.ajmc = this.searchForm.ajmc;
        }
        if (this.searchForm.hoster_name) {
          params.hoster_name = this.searchForm.hoster_name;
        }
        if (this.searchForm.ajbh) {
          params.ajbh = this.searchForm.ajbh;
        }
        if (this.searchForm.analysis_result) {
          params.analysis_result = this.searchForm.analysis_result;
        }

        const res = await getRecordList(params);
        if (res.code === 200) {
          this.tableData = res.data.items || [];
          this.tableData.forEach(item => {
            // 设置md_id为id，用于后续操作
            item.md_id = item.id;
            // 处理分析结果，从analysis_page中提取页码标题信息
            if (item.analysis_page && item.analysis_page.length > 0) {
            }
            // 处理原始数据中的src字段，如果需要
            if (item.src && Array.isArray(item.src)) {
              // 可以在这里处理源文件链接
            }
            // 处理分析错误信息
            if (item.analysis_error && item.analysis_error.length > 0) {
              // 可以在这里处理错误信息
            }
          });
          this.total = res.data.total || 0;
        } else {
          throw new Error(res.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;
      }
    },
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#FFFFFFcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    rowClassName({ row }) {
      // if (!row.detection_result) {
      //   return "";
      // }
      // try {
      //   let result = JSON.parse(row.detection_result);
      //   if (result.check_status === 1) {
      //     return "warning-row";
      //   }
      // } catch (e) {
      //   console.error("解析detection_result失败:", e);
      // }
      return "";
    },
    async handleSearch() {
      this.currentPage = 1;
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        timeRange: [],
        ajmc: "",
        hoster_name: "",
        analysis_result: "",
        ajbh: ''
      };
      this.handleSearch();
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      const date = new Date(cellValue);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    getStatusType(status) {
      // 根据分析状态返回相应的类型
      switch (status) {
        case -2:
          return "info";
        case -1:
          return "danger";
        case 0:
          return "warning";
        case 1:
          return "success";
        default:
          return "";
      }
    },
    getStatusText(status) {
      // 根据分析状态返回相应的文本
      switch (status) {
        case -2:
          return "待导入";
        case -1:
          return "未处理";
        case 0:
          return "处理中";
        case 1:
          return "已处理";
        default:
          return "未知状态";
      }
    },
    getOcrStatusType(status) {
      // 根据OCR状态返回相应的类型
      switch (status) {
        case 0:
          return "info";
        case 2:
          return "success";
        case 3:
          return "danger";
        default:
          return "info";
      }
    },
    getOcrStatusText(status) {
      // 根据OCR状态返回相应的文本
      switch (status) {
        case 0:
          return "未识别";
        case 2:
          return "识别完成";
        case 3:
          return "识别失败";
        default:
          return "未知状态";
      }
    },
    getAnalysisStatusType(status) {
      // 根据分析状态返回相应的类型
      switch (status) {
        case 0:
          return "info";
        case 1:
          return "warning";
        case 2:
          return "success";
        case 3:
          return "danger";
        default:
          return "info";
      }
    },
    getAnalysisStatusText(status) {
      // 根据分析状态返回相应的文本
      switch (status) {
        case 0:
          return "未分析";
        case 1:
          return "分析中";
        case 2:
          return "分析完成";
        case 3:
          return "分析失败";
        default:
          return "未知状态";
      }
    },
    getTimeCheckText(status) {
      switch (status) {
        case "TIME_FORMAT_ERROR":
          return "时间解析异常";
        case "START_END_NULL":
          return "开始、结束时间均为空";
        case "START_NULL":
          return "开始时间为空，结束时间不为空";
        case "END_NULL":
          return "开始时间不为空，结束时间为空";
        case "TIME_LIMIT":
          return "时间不够15分钟";
        default:
          return "时间校验异常";
      }
    },
    getLocationCheckText(status) {
      switch (status) {
        case "NULL":
          return "地点为空";
        case "CONTENT_MISS":
          return "地点缺失";
        default:
          return "地点校验异常";
      }
    },

    scrollToTarget(event) {
      if (event.target.dataset.action) {
        const element = document.getElementById(event.target.dataset.action);
        if (!element) return;
        element.scrollIntoView({
          behavior: "smooth",
        });
      }
    },

    handleTabClick() {
      this.currentPage = 1;
      this.tableData = [];
      this.loadTableData();
    },
    // 同步功能
    async handleSync() {
      this.dateRangeDialogVisible = true;
    },
    async handleDateRangeConfirm(dateRange) {
      try {
        this.loading = true;
        const params = {
          start_date: dateRange[0],
          end_date: dateRange[1]
        };
        const res = await syncList(params);
        if (res.code === 200) {
          this.$message.success("数据同步成功");
          await this.loadTableData();
        } else {
          throw new Error(res.message || "数据同步失败");
        }
      } catch (error) {
        console.error("数据同步失败:", error);

      } finally {
        this.loading = false;
        this.dateRangeDialogVisible = false;
      }
    },
    handleDateRangeCancel() {
      this.dateRangeDialogVisible = false;
    },
    handleDateRangeClose() {
      this.dateRangeDialogVisible = false;
    },
    handleInput() {
      this.inputDialogVisible = true;
    },
    handleInputSubmit() {
      this.$refs.inputForm.validate(valid => {
        if (valid) {
          // 这里可以调用API提交数据
          create_aj({
            ...this.inputForm,
            type: this.activeTab,
          })
            .then(res => {
              this.$message.success(res.message || "录入成功");
              this.loadTableData(); // 刷新表格数据
              this.inputDialogVisible = false;
              this.$refs.inputForm.resetFields(); // 重置表单
            })
            .catch(err => {
              this.$message.error(err.message || "录入失败");
            });
        } else {
          return false;
        }
      });
    },
    handleInputCancel() {
      this.inputDialogVisible = false;
      this.$refs.inputForm.resetFields(); // 重置表单
    },
    handleReview(row) {
      const routeData = this.$router.resolve({
        path: '/file-review/preview',
        query: {
          md_id: row.id,
          case_name: row.case_name
        }
      });
      window.open(routeData.href, '_blank');
    },
    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 批量分析
    async handleBatchAnalyze() {
      try {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请选择要分析的案件');
          return;
        }

        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              确认对选中的 ${this.selectedRows.length} 个案件进行批量分析吗？
            </div>
          </div>
        `,
          "分析确认",
          {
            confirmButtonText: "确定分析",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const ajbhes = this.selectedRows.map(row => row.id);
          const res = await batchAnalyzeCases({ ids: ajbhes });
          if (res.code === 200) {
            this.$message.success("批量分析任务已启动");
            await this.loadTableData();
            this.selectedRows = [];
          } else {
            throw new Error(res.message || "批量分析失败");
          }
        } else {
          this.$message.info("已取消分析");
        }
      } catch (error) {
        if (error.toString() !== "cancel" && error.toString() !== "close") {
          console.error("批量分析失败:", error);

        }
      } finally {
        this.loading = false;
      }
    },
    checkSelectable(row) {
      // 根据分析状态判断是否可选
      // 状态为0(未分析)或3(分析失败)时可选
      return row.analysis_result == 0 || row.analysis_result == 3;
    },
    toggleMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
    },
    // 附件审查
    handleBattchFileUpSC() {

    },
    handleImport() {
      this.importDialogVisible = true;
    },
    handleImportDialogClose() {
      this.importDialogVisible = false;
    },
    handleImportConfirm() {
      if (!this.selectedCaseType) {
        this.$message.warning('请选择案件类型');
        return;
      }
      this.importDialogVisible = false;
      this.$refs.uploadInput.click();
    },
    handleFileChange(event) {
      const file = event.target.files[0];
      if (file) {
        if (this.beforeImportPDF(file)) {
          this.handleImportPDF(file);
        }
      }
      // 清空文件输入框，以便可以重复选择同一个文件
      event.target.value = '';
    },
    handleImportPDF(file) {
      const formData = new FormData();
      formData.append('aj_type', this.selectedCaseType);
      formData.append('pdf_file', file);
      
      this.loading = true;
      importPDF(formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.code === 200) {
          this.$message.success('导入成功');
          this.loadTableData();
          this.selectedCaseType = ''; // 上传成功后清空选择
        } else {
          throw new Error(res.message || '导入失败');
        }
      }).catch(error => {
        console.error('导入失败:', error);
      }).finally(() => {
        this.loading = false;
      });
    },
    
    beforeImportPDF(file) {
      const isPDF = file.type === 'application/pdf';
      if (!isPDF) {
        this.$message.error('只能上传PDF文件!');
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="less" scoped>
@import url("./index.less");
</style>

<style lang="less">
.tabs-section .el-tabs__active-bar {
  width: 18% !important;
  margin-left: 28px;
}

@import url("./global.less");
</style>
