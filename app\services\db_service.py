from datetime import datetime
from typing import List, Dict

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.core.context_handle import UserContext
from app.log.log_utils import LogUtils
from app.models.SacwRecords import SacwDjrkjl, SacwClck, SacwBgyq
from app.models.org import Org
from app.models.police_record import PoliceRecord
from app.models.scene_3_record import Scene_3_Record
from app.models.scene_4_po import Scene4PO
from app.models.scene_6_po import scene_6_record


class DatabaseService:
    # 字段映射表
    FIELD_MAPPING = {
        'police_number': '处警编号',
        'police_unit': '处警单位',
        'police_time': '处警日期时间',
        'alarm_time': '接警时间',
        'call_name': '报警人姓名',
        'alarm_form': '报警形式',
        'alarm_call': '报警电话',
        'alarm_category': '接警类别',
        'police_category': '处警类别',
        'police_name': '处警人',
        'police_results': '处警结果',
        'alarm_content': '报警内容',
        'alarm_details': '处警详细情况',
    }

    MEASURE_FIELD_MAPPING = {
        'case_number': '案件编号',
        'case_name': '案件名称',
        'case_operator_org': '主办单位',
        'case_operator': '主办人',
        'measure_type': '处理措施',
        'measure_object_name': '措施对象名称',
        'measure_approve_date': '措施批准时间',
    }

    CASW_FIELD_MAPPING = {
        'ajbh': '案件编号',
        'ajmc': '案件名称',
        'cwbh': '物品编号',
        'cwmc': '物品名称',
        'cwsl': '入库数量',
        'fh_rksj': '入库时间',
        'czsj': '操作时间',
        'status': '状态',
    }

    @staticmethod
    async def save_records(db: Session, records: List[Dict]):
        try:
            for record in records:
                db_record = PoliceRecord(**record)
                db.add(db_record)
            db.commit()
            LogUtils.info(f"成功保存 {len(records)} 条记录")
        except Exception as e:
            db.rollback()
            LogUtils.error(f"数据库保存失败: {str(e)}")
            raise

    @staticmethod
    async def save_record(db: Session, db_record: PoliceRecord):
        try:
            db.merge(db_record)
            db.commit()
            LogUtils.info(f"成功保存")
        except Exception as e:
            db.rollback()
            LogUtils.error(f"数据库保存失败: {str(e)}")
            raise

    @staticmethod
    def map_fields_to_english(record_data: dict, field_mapping: dict) -> dict:
        """
        将中文字段名映射为英文字段名
        
        Args:
            record_data (dict): 包含中文字段名的原始数据
            field_mapping (dict): 中英文映射关系字典
        Returns:
            dict: 包含英文字段名的映射后数据
        """
        english_data = {
            k: record_data[v]  # k 是英文名，v 是中文名
            for k, v in field_mapping.items()  # 遍历 FIELD_MAPPING
            if v in record_data  # 如果中文名在 record_data 中
        }
        LogUtils.info(f"字段映射结果: {english_data}")
        return english_data

    @staticmethod
    def create_casemeasure_record(db: Session, english_data: Dict[str, str]):
        # 创建记录
        db_record = scene_6_record(**english_data)
        db.add(db_record)
        db.commit()
        db.refresh(db_record)
        return db_record

    @staticmethod
    def create_sacw_record(db: Session, english_data: Dict[str, str]):
        try:
            # 检查状态字段是否存在
            if 'status' not in english_data:
                raise ValueError("导入数据缺少'状态'字段")

            status = english_data['status']
            english_data.pop('status')

            # 检查状态值是否合法
            if status not in ['入库', '出库', '保管延期']:
                raise ValueError(f"无效的状态值: {status}，必须是'入库'、'出库'或'保管延期'之一")

            if status == '入库':
                # 安全移除字段
                english_data.pop('czsj', None)  # 如果字段不存在，不会报错
                db_record = SacwDjrkjl(**english_data)
            elif status == '出库':
                english_data.pop('cwsl', None)
                db_record = SacwClck(**english_data)
            elif status == '保管延期':
                english_data.pop('cwsl', None)
                db_record = SacwBgyq(**english_data)

            db.add(db_record)
            db.commit()
            db.refresh(db_record)
            return db_record

        except KeyError as e:
            raise ValueError(f"导入数据缺少必要字段: {str(e)}")
        except Exception as e:
            db.rollback()
            raise ValueError(f"创建记录失败: {str(e)}")

    @staticmethod
    def check_tmp_transcript_exists(db: Session, md5_id: str) -> bool:
        existing_record = db.query(Scene4PO).filter(
            Scene4PO.md_id == md5_id
        ).first()
        return existing_record

    @staticmethod
    def check_tmp_transcript_exists_by_id(db: Session, id: int) -> bool:
        existing_record = db.query(Scene4PO).filter(
            Scene4PO.id == id
        ).first()
        return existing_record

    @staticmethod
    def check_legal_record_exists(db: Session, jlbh: str) -> bool:
        existing_record = db.query(Scene_3_Record).filter(
            Scene_3_Record.jlbh == jlbh
        ).first()
        return existing_record

    @staticmethod
    def build_search_query(db: Session, start_time: str = None, end_time: str = None,
                           police_number: str = None, police_unit: str = None,
                           alarm_form: str = None, alarm_category: str = None,
                           police_category: str = None, police_results: str = None,
                           analysis_level: str = None, check_results: str = None, alarm_content: str = None,
                           case_type: int = 0, analysis_status: str = None):
        """
        构建动态查询条件

        Args:
            db (Session): 数据库会话
            start_time (str, optional): 开始时间
            end_time (str, optional): 结束时间
            police_number (str, optional): 处警编号
            police_unit (str, optional): 处警单位
            alarm_form (str, optional): 报警形式
            alarm_category (str, optional): 接警类别
            police_category (str, optional): 处警类别
            police_results (str, optional): 处警结果
            analysis_level (str, optional): AI分析等级
            check_results (str, optional): 核查结果
            alarm_content (str, optional): 报警内容（支持模糊查询）
            case_type (int, required): 案件类型
            analysis_status (int, required): 分析状态
        Returns:
            Query: 构建好的查询对象
        """
        from datetime import datetime

        query = db.query(PoliceRecord)

        # 构建动态查询条件
        query = query.filter(PoliceRecord.case_type == case_type)
        if start_time and end_time:
            # 转换为datetime对象并设置时间
            start_datetime = datetime.strptime(start_time, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
            end_datetime = datetime.strptime(end_time, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
            query = query.filter(PoliceRecord.police_time >= start_datetime).filter(
                PoliceRecord.police_time <= end_datetime)
        elif start_time:
            start_datetime = datetime.strptime(start_time, '%Y-%m-%d').replace(hour=0, minute=0, second=0)
            query = query.filter(PoliceRecord.police_time >= start_datetime)
        elif end_time:
            end_datetime = datetime.strptime(end_time, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
            query = query.filter(PoliceRecord.police_time <= end_datetime)

        if police_number:
            query = query.filter(PoliceRecord.police_number == police_number)
        if police_unit:
            query = query.filter(PoliceRecord.police_unit.ilike(f'%{police_unit}%'))
        if alarm_form:
            query = query.filter(PoliceRecord.alarm_form == alarm_form)
        if alarm_category:
            query = query.filter(PoliceRecord.alarm_category == alarm_category)
        if police_category:
            query = query.filter(PoliceRecord.police_category == police_category)
        if police_results:
            query = query.filter(PoliceRecord.police_results == police_results)
        if analysis_level:
            query = query.filter(PoliceRecord.analysis_level == analysis_level)
        if check_results:
            query = query.filter(PoliceRecord.check_results == check_results)
        if alarm_content:
            query = query.filter(PoliceRecord.alarm_content.ilike(f'%{alarm_content}%'))  # 模糊查询
        if analysis_status:
            query = query.filter(PoliceRecord.analysis_status == analysis_status)
        # 获取当前用户信息
        current_org_code = UserContext.get_current_user_org().get("org_code")
        police_number = UserContext.current_user.get().police_number
        # 查询当前用户机构权限
        org_info = db.query(Org).filter(Org.org_code == current_org_code).first()
        # 构建机构和警号的查询条件
        org_condition = (
            PoliceRecord.org_code.like(f"{current_org_code}%")
            if org_info and org_info.can_sub_query == 1
            else PoliceRecord.org_code == current_org_code
        )
        # 组合查询条件
        query = query.filter(
            or_(
                org_condition,
                PoliceRecord.org_code == f"-{police_number}"
            )
        )
        return query

    @staticmethod
    def build_transcript_search_query(db: Session, start_time: str = None, end_time: str = None,
                                      asker: str = None, bldd: str = None, analysis_status: str = None,
                                      ajbh: str = None, md_id: str = None, check_status: str = None, ajmc: str = None):

        query = db.query(Scene4PO)
        if ajbh:
            query = query.filter(Scene4PO.ajbh == ajbh)
        if md_id:
            query = query.filter(Scene4PO.md_id == md_id)
        if analysis_status:
            query = query.filter(Scene4PO.analysis_status == analysis_status)
        if check_status:
            query = query.filter(Scene4PO.check_status == check_status)
        if ajmc:
            query = query.filter(Scene4PO.ajmc.ilike(f'%{ajmc}%'))  # 模糊查询
        # 构建动态查询条件
        if start_time and end_time:
            # 转换为datetime对象并设置时间
            start_datetime = datetime.strptime(start_time, '%Y%m%d').replace(hour=0, minute=0, second=0)
            end_datetime = datetime.strptime(end_time, '%Y%m%d').replace(hour=23, minute=59, second=59)
            query = query.filter(Scene4PO.kssj >= start_datetime).filter(
                Scene4PO.jssj <= end_datetime)
        elif start_time:
            start_datetime = datetime.strptime(start_time, '%Y%m%d').replace(hour=0, minute=0, second=0)
            query = query.filter(Scene4PO.kssj >= start_datetime)
        elif end_time:
            end_datetime = datetime.strptime(end_time, '%Y%m%d').replace(hour=23, minute=59, second=59)
            query = query.filter(Scene4PO.jssj <= end_datetime)

        if bldd:
            query = query.filter(Scene4PO.bldd.ilike(f'%{bldd}%'))  # 模糊查询
        # 根据asker模糊查询查询 TempTranscriptRecord.mjyxm 和 TempTranscriptRecord.mjexm 这两个字段,只要之中一个满足条件即可
        if asker:
            query = query.filter(
                or_(
                    Scene4PO.mjyxm.ilike(f'%{asker}%'),
                    Scene4PO.mjexm.ilike(f'%{asker}%')
                )
            )
        # query = query.order_by(Scene4PO.upload_time.desc())
        return query

    @staticmethod
    def build_legal_search_query(db: Session, jlbh: str = None, ajbh: str = None,
                                 doc_name: str = None, cqsx: str = None, pzsj_start_time: str = None,
                                 pzsj_end_time: str = None,
                                 analysis_status: str = None, check_status: str = None, doc_num: str = None,
                                 tfrxm: str = None, ajmc: str = None, tfsj_start_time: str = None,
                                 tfsj_end_time: str = None):

        query = db.query(Scene_3_Record)
        if check_status:
            query = query.filter(Scene_3_Record.check_status == check_status)
        if analysis_status:
            query = query.filter(Scene_3_Record.analysis_status == analysis_status)
        # 构建动态查询条件
        if jlbh:
            query = query.filter(Scene_3_Record.jlbh == jlbh)
        if ajbh:
            query = query.filter(Scene_3_Record.ajbh == ajbh)
        if doc_name:
            query = query.filter(Scene_3_Record.doc_name.ilike(f'%{doc_name}%'))
        if doc_num:
            query = query.filter(Scene_3_Record.doc_num.ilike(f'%{doc_num}%'))
        if tfrxm:
            query = query.filter(Scene_3_Record.tfrxm.ilike(f'%{tfrxm}%'))
        if ajmc:
            query = query.filter(Scene_3_Record.ajmc.ilike(f'%{ajmc}%'))
        if cqsx:
            query = query.filter(Scene_3_Record.cqsx.ilike(f'%{cqsx}%'))
        if (pzsj_start_time and pzsj_end_time):
            # 转换为datetime对象并设置时间
            start_datetime = datetime.strptime(pzsj_start_time, '%Y%m%d').replace(hour=0, minute=0, second=0)
            end_datetime = datetime.strptime(pzsj_end_time, '%Y%m%d').replace(hour=23, minute=59, second=59)
            query = query.filter(Scene_3_Record.pzsj >= start_datetime).filter(
                Scene_3_Record.pzsj <= end_datetime)
        if (tfsj_start_time and tfsj_end_time):
            # 转换为datetime对象并设置时间
            start_datetime = datetime.strptime(tfsj_start_time, '%Y%m%d').replace(hour=0, minute=0, second=0)
            end_datetime = datetime.strptime(tfsj_end_time, '%Y%m%d').replace(hour=23, minute=59, second=59)
            query = query.filter(Scene_3_Record.tfsj >= start_datetime).filter(
                Scene_3_Record.tfsj <= end_datetime)

        # query = query.order_by(Scene_3_Record.updated_at.desc())
        return query

    @staticmethod
    def calculate_skip(page: int, page_size: int) -> int:
        """
        计算分页的偏移量
        :param page: 当前页码
        :param page_size: 每页大小
        :return: 偏移量
        """
        if page < 1 or page_size < 1:
            raise ValueError("页码和每页大小必须为正整数")
        return (page - 1) * page_size

    @staticmethod
    async def save_records_batch(db: Session, records: List[PoliceRecord]):
        try:
            # 批量添加记录
            db.bulk_save_objects(records)
            # 提交事务
            db.commit()
        except Exception as e:
            db.rollback()
            LogUtils.error(f"数据库保存失败: {str(e)}")

    @staticmethod
    def build_finance_search_query(db: Session, measure_type: str = None, measure_object_name: str = None):
        query = db.query(scene_6_record)

        if measure_type:
            query = query.filter(scene_6_record.measure_type == measure_type)
        if measure_object_name:
            query = query.filter(scene_6_record.measure_object_name.ilike(f'%{measure_object_name}%'))

        # query = query.order_by(scene_6_record.update_time.desc())
        return query
