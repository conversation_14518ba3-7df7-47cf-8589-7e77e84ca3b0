<template>
  <div class="right-panel">
    <div class="panel-item">
      <div class="panel-title">业务办理排名</div>
      <div class="ranking-list">
        <div class="ranking-item" v-for="(item, index) in rankingData" :key="index">
          <span class="rank-num" :class="{'top-three': index < 3}">{{ index + 1 }}</span>
          <span class="rank-name">{{ item.name }}</span>
          <span class="rank-value">{{ item.value }}</span>
          <div class="progress-bar">
            <div class="progress" :style="{width: item.percentage + '%'}"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="panel-item">
      <div class="panel-title">业务处理趋势</div>
      <div class="trend-chart" ref="trendChart"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'RightPanel',
  data() {
    return {
      trendChart: null,
      rankingData: [
        { name: '户籍业务', value: '2,345', percentage: 90 },
        { name: '车辆业务', value: '2,145', percentage: 85 },
        { name: '出入境业务', value: '1,986', percentage: 80 },
        { name: '证件业务', value: '1,876', percentage: 75 },
        { name: '其他业务', value: '1,654', percentage: 70 }
      ]
    }
  },
  mounted() {
    this.initTrendChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.trendChart && this.trendChart.resize()
    },
    initTrendChart() {
      this.trendChart = echarts.init(this.$refs.trendChart)
      const option = {
        color: ['#00ffff', '#ffff00'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['受理量', '办结量'],
          textStyle: {
            color: '#ffffff'
          },
          top: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLine: {
            lineStyle: {
              color: '#ffffff'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ffffff'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            name: '受理量',
            type: 'bar',
            data: [320, 332, 301, 334, 390, 330],
            barWidth: '30%'
          },
          {
            name: '办结量',
            type: 'bar',
            data: [220, 282, 291, 334, 290, 320],
            barWidth: '30%'
          }
        ]
      }
      this.trendChart.setOption(option)
    }
  }
}
</script>

<style scoped>
.right-panel {
  width: 400px;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.panel-item {
  width: 100%;
  height: 45%;
  background: rgba(0,48,102,0.3);
  border: 1px solid rgba(0,255,255,0.2);
  margin-bottom: 20px;
  padding: 15px;
  box-sizing: border-box;
}

.panel-title {
  color: #00ffff;
  font-size: 18px;
  margin-bottom: 15px;
}

.ranking-list {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: #ffffff;
}

.rank-num {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  background: rgba(255,255,255,0.1);
  margin-right: 10px;
}

.rank-num.top-three {
  background: #00ffff;
  color: #000;
}

.rank-name {
  flex: 1;
  font-size: 14px;
}

.rank-value {
  margin: 0 10px;
  color: #00ffff;
}

.progress-bar {
  width: 100px;
  height: 4px;
  background: rgba(255,255,255,0.1);
}

.progress {
  height: 100%;
  background: #00ffff;
  transition: width 0.3s ease;
}

.trend-chart {
  height: calc(100% - 40px);
}

::-webkit-scrollbar {
  width: 4px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0,255,255,0.2);
  border-radius: 2px;
}
</style> 