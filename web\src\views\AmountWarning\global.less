/* 导入确认对话框全局样式 */
.el-message-box.import-confirm-dialog {
  background: rgba(0, 24, 51, 0.85) !important;
  border: 1px solid rgba(0, 255, 255, 0.4) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
  min-width: 420px !important;
}

.import-confirm-content {
  padding: 15px 0;
}

.confirm-item {
  margin: 8px 0;
  color: #ffffff;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1.5;
}

.confirm-item .el-icon-warning {
  color: #e6a23c;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(230, 162, 60, 0.4);
}

.el-message-box.import-confirm-dialog .el-message-box__header {
  padding: 12px 16px 10px !important;
  background: linear-gradient(
    180deg,
    rgba(0, 48, 102, 0.8) 0%,
    rgba(0, 48, 102, 0.6) 100%
  ) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

.el-message-box.import-confirm-dialog .el-message-box__title {
  color: #00ffff !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

.el-message-box.import-confirm-dialog .el-message-box__content {
  padding: 20px 16px !important;
  background: rgba(0, 24, 51, 0.4) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.15) !important;
}

.el-message-box.import-confirm-dialog .el-message-box__btns {
  padding: 12px 16px !important;
  background: linear-gradient(
    0deg,
    rgba(0, 48, 102, 0.8) 0%,
    rgba(0, 48, 102, 0.6) 100%
  ) !important;
  border-radius: 0 0 8px 8px !important;
}

.el-message-box.import-confirm-dialog .el-button {
  height: 32px !important;
  padding: 0 20px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  transition: all 0.3s !important;
}

.el-message-box.import-confirm-dialog .el-button--primary {
  background: rgba(0, 255, 255, 0.2) !important;
  border: 1px solid #00ffff !important;
  color: #00ffff !important;
}

.el-message-box.import-confirm-dialog .el-button--primary:hover {
  background: rgba(0, 255, 255, 0.3) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

.el-message-box.import-confirm-dialog .el-button--default {
  background: rgba(144, 147, 153, 0.1) !important;
  border: 1px solid rgba(144, 147, 153, 0.3) !important;
  color: #ffffff !important;
}

.el-message-box.import-confirm-dialog .el-button--default:hover {
  background: rgba(144, 147, 153, 0.2) !important;
  border-color: rgba(144, 147, 153, 0.4) !important;
  transform: translateY(-1px) !important;
}

.el-message-box.import-confirm-dialog .el-message-box__close {
  color: rgba(0, 255, 255, 0.7) !important;
  font-size: 16px !important;
  transition: all 0.3s !important;
}

.el-message-box.import-confirm-dialog .el-message-box__close:hover {
  color: #00ffff !important;
  transform: rotate(90deg) !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.4) !important;
}

/* 全局对话框滚动条美化 */
.el-dialog__body::-webkit-scrollbar,
.el-message-box__message::-webkit-scrollbar,
.el-drawer__body::-webkit-scrollbar,
.el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.el-dialog__body::-webkit-scrollbar-thumb,
.el-message-box__message::-webkit-scrollbar-thumb,
.el-drawer__body::-webkit-scrollbar-thumb,
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(0, 180, 255, 0.5), rgba(0, 255, 255, 0.5));
  border-radius: 3px;
  box-shadow: inset 0 0 3px rgba(0, 255, 255, 0.2);
  transition: background 0.3s ease-in-out;
}

.el-dialog__body::-webkit-scrollbar-thumb:hover,
.el-message-box__message::-webkit-scrollbar-thumb:hover,
.el-drawer__body::-webkit-scrollbar-thumb:hover,
.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(0, 180, 255, 0.8), rgba(0, 255, 255, 0.8));
  box-shadow: inset 0 0 5px rgba(0, 255, 255, 0.4);
}

.el-dialog__body::-webkit-scrollbar-track,
.el-message-box__message::-webkit-scrollbar-track,
.el-drawer__body::-webkit-scrollbar-track,
.el-table__body-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 48, 102, 0.2);
  border-radius: 3px;
}

.el-dialog__body::-webkit-scrollbar-corner,
.el-message-box__message::-webkit-scrollbar-corner,
.el-drawer__body::-webkit-scrollbar-corner,
.el-table__body-wrapper::-webkit-scrollbar-corner {
  background: transparent;
}

/* 下拉选择滚动条美化 */
.el-select-dropdown__wrap::-webkit-scrollbar,
.el-cascader-menu__wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb,
.el-cascader-menu__wrap::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(0, 180, 255, 0.5), rgba(0, 255, 255, 0.5));
  border-radius: 3px;
  box-shadow: inset 0 0 3px rgba(0, 255, 255, 0.2);
  transition: background 0.3s ease-in-out;
}

.el-select-dropdown__wrap::-webkit-scrollbar-thumb:hover,
.el-cascader-menu__wrap::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(0, 180, 255, 0.8), rgba(0, 255, 255, 0.8));
  box-shadow: inset 0 0 5px rgba(0, 255, 255, 0.4);
}

.el-select-dropdown__wrap::-webkit-scrollbar-track,
.el-cascader-menu__wrap::-webkit-scrollbar-track {
  background: rgba(0, 48, 102, 0.2);
  border-radius: 3px;
}

.el-select-dropdown__wrap::-webkit-scrollbar-corner,
.el-cascader-menu__wrap::-webkit-scrollbar-corner {
  background: transparent;
}

/* 进度条文本样式 */
.progress-container .el-progress__text {
  color: #00ffff !important;
}

/* PDF分析对话框进度条样式 */
.pdf-analysis-dialog .el-progress-bar__innerText,
.pdf-analysis-dialog .el-progress__text {
  color: #ffffff !important;
  font-weight: bold !important;
  text-shadow: 0 0 4px rgba(0, 255, 255, 0.4) !important;
}
