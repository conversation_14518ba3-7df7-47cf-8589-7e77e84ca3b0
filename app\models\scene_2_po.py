from sqlalchemy import Column, String, Text, Integer, DateTime, Date, case
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Scene2POBase = declarative_base()

# 字段映射
FIELD_MAPPING = {
    'ajbh': '案件编号',
    'ajmc': '案件名称',
    'ajlb': '案件类别',
    'ajzt': '案件状态',
    'zbrxm': '主办人姓名',
    'lasj': '立案时间',
    'analysis_result': '分析结论'
}

# 颜色映射
COLOR_MAPPING = {
    'red': 'FF9999',    # 浅红色
    'yellow': 'FFF9B3', # 浅黄色
    'None': 'FFFFFF'    # 白色
}

def scene2_search_order():
    # 定义分析状态的排序优先级
    analysis_status_order = {
        'analyzing': 1,  # 分析中
        'completed': 2,  # 已分析
        'init': 3,      # 未分析
        'failed': 4     # 分析失败
    }

    # 使用case语句进行自定义排序
    return case(
        {status: order for status, order in analysis_status_order.items()},
        value=Scene2PO.analysis_status
    )

class Scene2PO(Scene2POBase):
    """案件分析记录数据模型"""
    __tablename__ = 'scene_2_analysis'
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 案件唯一编号（主键）
    ajbh = Column(String(128), index=True,nullable=True, comment='案件编号')
    # 内容概要
    summary = Column(Text, comment='概要内容')
    # 分析状态（init-同步/processing-分析中/completed-分析完成/failed-分析失败）
    analysis_status = Column(String(16), index=True,  comment='分析状态')
    # 分析结论详情
    analysis_result = Column(String(16), index=True, comment='分析结论')
    # red、yellow、None
    analysis_tag = Column(String(16), comment='分析标记（颜色）')
    # 关联用户ID
    user_id = Column(Integer, index=True,comment='用户ID')
    lasj = Column(DateTime)  # 立案时间
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')  # 新增更新时间字段

    # ---
    # 程序类字段
    bacx = Column(String(16), comment='办案程序')
    ajlb = Column(String(16), comment='案件类别')
    ajfab = Column(String(16), comment='案件副类别')
    ajmc = Column(String(128), index=True,  comment='案件名称')
    ajzt = Column(String(16), index=True,  comment='案件状态')
    ajly = Column(String(16), comment='案件来源')

    # 单位信息
    sldw = Column(String(128), comment='受理单位')
    zbdwmc = Column(String(128), index=True, comment='主办单位名称')

    # 人员信息
    zbrxm = Column(String(16), comment='主办人姓名')

    # 时间信息
    slsj = Column(Date, index=True, comment='受理时间')
    org_code = Column(String(255), comment='机构code')