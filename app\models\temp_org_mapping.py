
from sqlalchemy import Column, Integer, String
from app.db.database import Base

class temp_org_to_user(Base):
    __tablename__ = "temp_org_to_user"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    origin_org_code = Column(String(255), nullable=False, comment="原始组织编码")
    origin_parent_org_code = Column(String(255), nullable=False, comment="原始父级组织编码")
    person_code = Column(String(255), nullable=False, comment="人员编号")
    level_code = Column(String(255), nullable=False, comment="层级")
