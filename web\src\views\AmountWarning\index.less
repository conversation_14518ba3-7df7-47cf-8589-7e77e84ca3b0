.file-review {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
  .tabs-section {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
      border-bottom: none;
    }

    :deep(.el-tabs__nav-wrap) {
      margin-bottom: 0;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
      background-color: rgba(0, 48, 102, 0.3);
      border: 1px solid rgba(0, 255, 255, 0.2);
      color: #ffffff;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      transition: all 0.3s;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
      background-color: rgba(0, 255, 255, 0.1);
      border: 1px solid rgba(0, 255, 255, 0.4);
      color: #00ffff;
      font-weight: bold;
    }

    :deep(.el-tabs--card > .el-tabs__header .el-tabs__item:hover) {
      color: #00ffff;
    }

    :deep(.el-tabs__active-bar) {
      background-color: #00ffff;
      height: 3px;
    }

    :deep(.el-tabs__nav-next),
    :deep(.el-tabs__nav-prev) {
      color: rgba(255, 255, 255, 0.7);
    }

    :deep(.el-tabs__nav-next:hover),
    :deep(.el-tabs__nav-prev:hover) {
      color: #00ffff;
    }

    .tab-indicator {
      background-color: rgba(0, 48, 102, 0.1);
      padding: 8px 16px;
      border-radius: 0 0 4px 4px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      display: flex;
      align-items: center;
      margin-top: 6px;
    }

    .tab-type {
      color: #00ffff;
      font-weight: bold;
      margin-left: 5px;
    }
  }

  :deep(.el-tooltip__popper.is-light) {
    background: rgba(0, 48, 102, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
  }

  :deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow) {
    border-bottom-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow::after) {
    border-bottom-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-button) {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  :deep(.el-icon-upload2),
  :deep(.el-icon-refresh),
  :deep(.el-icon-download) {
    font-size: 16px;
  }

  /* 添加tooltip样式 */
  :deep(.el-tooltip__popper) {
    background-color: rgba(0, 48, 102, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
    max-width: 400px !important;
    line-height: 1.5 !important;
  }

  :deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow) {
    border-top-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow::after) {
    border-top-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow) {
    border-bottom-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow::after) {
    border-bottom-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-form-item__label) {
    color: #ffffff !important;
  }

  :deep(.el-form--inline .el-form-item) {
    margin-right: 0;
    margin-bottom: 0px;
  }

  :deep(.el-date-editor) {
    // background-color: rgba(0, 48, 102, 0.3) !important;
    // border: 1px solid rgba(0, 255, 255, 0.2);
  }

  :deep(.el-date-editor:hover),
  :deep(.el-date-editor:focus) {
    border-color: #00ffff;
  }

  :deep(.el-form-item.el-form-item--default) {
    margin-bottom: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 10px;
  }

  /* Element UI 样式覆盖 */
  :deep(.el-loading-mask) {
    background-color: rgba(0, 24, 51, 0.9) !important;
  }

  :deep(.el-loading-spinner .el-loading-text) {
    color: #00ffff !important;
  }

  :deep(.el-loading-spinner .path) {
    stroke: #00ffff !important;
  }

  :deep(.el-date-editor.el-input__inner) {
    width: 240px;
  }

  :deep(.el-date-editor--datetime.el-input__inner) {
    width: 240px;
  }

  :deep(.el-date-editor .el-range-input) {
    color: #ffffff;
    background: transparent;
  }

  :deep(.el-date-editor .el-range-separator) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel) {
    background: rgba(0, 48, 102, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  :deep(.el-picker-panel__content) {
    color: #ffffff;
  }

  :deep(.el-date-table th) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-date-table td.available:hover) {
    color: #00ffff;
  }

  :deep(.el-date-table td.current:not(.disabled)) {
    background-color: rgba(0, 255, 255, 0.2);
    color: #00ffff;
  }

  :deep(.el-picker-panel__shortcut) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel__shortcut:hover) {
    color: #00ffff;
  }

  :deep(.el-date-picker__header-label) {
    color: #ffffff;
  }

  :deep(.el-date-picker__header-label:hover) {
    color: #00ffff;
  }

  :deep(.el-picker-panel__icon-btn) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel__icon-btn:hover) {
    color: #00ffff;
  }

  :deep(.el-form--inline .el-form-item__label) {
    padding-right: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    float: inline-start;
  }

  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }

  :deep(.el-button) {
    height: 32px;
    padding: 0 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.el-button [class*="el-icon-"] + span) {
    margin-left: 4px;
  }

  :deep(.el-select .el-input__inner) {
    padding-right: 25px;
  }

  :deep(.el-date-editor .el-range__icon) {
    line-height: 24px;
  }

}
