**0409-merge：基于v0402分支，预合并v0402与mysql-v320代码**

# CaseAnalysis

## 安装依赖 (进入docker目录)
pip install -r requirements.txt

## 运行项目（根目录） uvicorn启动 项目 只会从命令中获取 服务参数如 host 默认8000,port,代码中参数无效 ,
uvicorn app.main:app --port 9999 --host 0.0.0.0 --workers 4

## 守护线程启动
nohup uvicorn app.main:app --port 9904 --host 0.0.0.0 --workers 4 --reload > uvicorn.log 2>&1 &

## 服务器 停止项目
sudo lsof -t -i tcp:9904
kill pid1 pid2
## 访问项目
http://127.0.0.1:8000/api

## docker 构建环境镜像(创建一个image目录存放 dockerfile,requirements.txt 文件 ,进入 image文件)

docker build -t caseanalysis .

## docker 启动镜像(创建/data/docker/case/code,/data/docker/case/code/data 路径,这个路径可以根据实际情况更改为自己的)

docker run -d -p 9904:9904 --name caseanalysis_app -v /data/docker/case/code:/app caseanalysis 

## docker 下载镜像到指定目录

docker save -o /data/docker/case/docker/caseanalysis.tar caseanalysis

## docker 将本地镜像进行加载

docker load -i caseanalysis.tar  