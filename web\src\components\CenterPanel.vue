<template>
  <div class="center-panel">
    <div class="map-container" ref="mapContainer">
      <!-- 地图将在这里渲染 -->
    </div>
    <div class="data-overview">
      <div class="overview-item" v-for="(item, index) in overviewData" :key="index">
        <div class="item-title">{{ item.title }}</div>
        <div class="item-value">{{ item.value }}</div>
        <div class="item-chart" :ref="'miniChart' + index"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import AMapLoader from '@amap/amap-jsapi-loader'

export default {
  name: 'CenterPanel',
  components: {
  },
  data() {
    return {
      map: null,
      miniCharts: [],
      overviewData: [
        { title: '总办件量', value: '89,634', trend: [30, 40, 20, 50, 40, 60, 70] },
        { title: '正在办理', value: '2,634', trend: [20, 30, 40, 20, 30, 40, 50] },
        { title: '已办结', value: '86,999', trend: [50, 60, 70, 60, 50, 60, 70] },
        { title: '办结率', value: '97.8%', trend: [90, 95, 93, 96, 97, 98, 97] }
      ]
    }
  },
  mounted() {
    this.initMap()
    this.initMiniCharts()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    this.map && this.map.destroy()
  },
  methods: {
    handleResize() {
      this.map && this.map.resize()
      this.miniCharts.forEach(chart => chart && chart.resize())
    },
    async initMap() {
      try {
        await AMapLoader.load({
          key: 'e7b1e4d0a2f4c9e3b5d8a6f9c2e5b8a1',  // 这里使用一个示例key，请替换为您申请的key
          version: '2.0',
          plugins: ['AMap.ToolBar', 'AMap.Scale', 'AMap.DistrictSearch'],  // 添加行政区划查询插件
          securityJsCode: 'e0c1b5a2d4f7c9e3b6a8d2f5c7e9b4a0'  // 这里使用一个示例安全密钥，请替换为您的安全密钥
        })
        this.map = new AMap.Map(this.$refs.mapContainer, {
          zoom: 10,
          center: [110.787916, 32.646907],
          mapStyle: 'amap://styles/dark',
          viewMode: '3D'
        })
        
        // 添加行政区划边界
        const districtSearch = new AMap.DistrictSearch({
          level: 'city',
          subdistrict: 1
        })
        
        districtSearch.search('十堰市', (status, result) => {
          if (status === 'complete') {
            const bounds = result.districtList[0].boundaries
            const polygon = new AMap.Polygon({
              path: bounds,
              strokeColor: '#00ffff',
              strokeWeight: 2,
              strokeOpacity: 1,
              fillColor: '#0066ff',
              fillOpacity: 0.2
            })
            this.map.add(polygon)
            this.map.setFitView()
          }
        })

        this.map.addControl(new AMap.ToolBar())
        this.map.addControl(new AMap.Scale())
      } catch (e) {
        console.error('地图加载失败：', e)
      }
    },
    initMiniCharts() {
      this.overviewData.forEach((item, index) => {
        const chart = echarts.init(this.$refs['miniChart' + index][0])
        const option = {
          grid: {
            top: 0,
            left: 0,
            right: 0,
            bottom: 0
          },
          xAxis: {
            type: 'category',
            show: false
          },
          yAxis: {
            type: 'value',
            show: false
          },
          series: [{
            data: item.trend,
            type: 'line',
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#00ffff'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(0,255,255,0.3)'
              }, {
                offset: 1,
                color: 'rgba(0,255,255,0)'
              }])
            }
          }]
        }
        chart.setOption(option)
        this.miniCharts.push(chart)
      })
    }
  }
}
</script>

<style scoped>
.center-panel {
  flex: 1;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.map-container {
  flex: 1;
  background: rgba(0,48,102,0.3);
  border: 1px solid rgba(0,255,255,0.2);
  margin-bottom: 20px;
}

.data-overview {
  height: 120px;
  display: flex;
  justify-content: space-between;
}

.overview-item {
  flex: 1;
  margin: 0 10px;
  background: rgba(0,48,102,0.3);
  border: 1px solid rgba(0,255,255,0.2);
  padding: 15px;
  box-sizing: border-box;
}

.item-title {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 10px;
}

.item-value {
  color: #00ffff;
  font-size: 24px;
  margin-bottom: 10px;
}

.item-chart {
  height: 30px;
}
</style> 