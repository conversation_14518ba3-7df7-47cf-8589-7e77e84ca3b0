from pydantic import BaseModel
from typing import List, Optional

class RoleBase(BaseModel):
    name: str
    sort: int
    status: bool
    code:str

class RoleCreate(RoleBase):
    pass

class RoleUpdate(RoleBase):
    id: int

class RoleInDB(RoleBase):
    id: int

    class Config:
        orm_mode = True

class UserRoleCreate(BaseModel):
    user_id: int
    role_id: int

class RoleMenuCreate(BaseModel):
    role_id: int
    menu_id: int

class RoleMenuList(BaseModel):
    role_id: int
    menu_ids: List[int]


class RoleQueryDto(BaseModel):
    name:Optional[str] = None
    status:Optional[str] = None
    page: Optional[int] = 1
    page_size: Optional[int] = 10

class MenuAuthRequest(BaseModel):
    role_id:int ## 角色id
    menu_ids:Optional[List[int] ]=  None ##菜单集合