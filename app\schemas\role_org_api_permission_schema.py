from pydantic import BaseModel
from typing import Optional

class RoleOrgApiPermissionBase(BaseModel):
    role_id: int
    org_code: str
    api_id: int

class RoleOrgApiPermissionCreate(RoleOrgApiPermissionBase):
    pass

class RoleOrgApiPermissionUpdate(BaseModel):
    role_id: Optional[int] = None
    org_code: Optional[str] = None
    api_id: Optional[int] = None

class RoleOrgApiPermissionInDB(RoleOrgApiPermissionBase):
    id: int

    class Config:
        orm_mode = True