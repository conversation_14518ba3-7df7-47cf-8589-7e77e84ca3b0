<template>
  <div class="home-container">
    <!-- 添加AI聊天组件 -->
    <!-- <AIChatBox /> -->
    <!-- 添加粒子效果容器 -->
    <div class="particle-container">
      <canvas class="rain"></canvas>
      <canvas class="dashed"></canvas>
    </div>
    <!-- 主体内容 -->
    <div class="content">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="panel-card tech-border">
          <div class="card-header">
            <!-- <span class="header-text">服务内容</span> -->
            <span class="header-text">功能展示</span>
            <!-- <span class="header-number">\01</span> -->
          </div>
          <div class="service-grid">
            <div class="service-item" v-for="(item, index) in serviceItems" :key="index" @click="handleServiceItemClick(item)">
              <div class="service-icon">
                <i :class="item.icon"></i>
              </div>
              <span class="service-name">{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间面板 -->
      <div class="center-panel">
        <!-- 添加gong图片 -->
        <div class="gong-container">
          <img src="@/assets/images/gongan.png" alt="gong" class="gong-image">
        </div>
        <div class="police-motto">
          <p>对党忠诚 服务人民</p>
          <p>执法公正 纪律严明</p>
        </div>
        <!-- 数字展示 -->
        <div class="number-display">
          <div class="title-box">
            <span class="main-title">法制监督智能助手</span>
            <span class="sub-title">DeepSeek智能辅助平台</span>
          </div>
        </div>
        <template v-if="userRole === 'ADMIN'">
          <div class="user-stats-container">
            <div class="user-stats">
              <div class="number-item">
                <div class="number">{{ online_users }}</div>
                <span class="label">在线人数</span>
              </div>
              <div class="number-item">
                <div class="number">{{ registered_users }}</div>
                <span class="label">注册人数</span>
              </div>
            </div>
          </div>
        </template>
        <!-- 地图展示 -->
        <div class="map-container">
          <!-- <img src="@/assets/images/map.png" alt="地图" class="map-image"> -->
          <img src="@/assets/images/jt.png" alt="光影效果1" class="light-effect jt-effect">
          <img src="@/assets/images/lbx.png" alt="光影效果2" class="light-effect lbx-effect">
          <div class="map-overlay"></div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="panel-card">
          <div class="card-title">
            <span class="title-text">DeepSeek警情信息分析</span>
          </div>
          <div class="stats-grid">
            <div class="stats-row">
              <div class="stats-item" @click="handleStatsClick('red')">
                <div class="stats-circle">
                  <span class="stats-number" ref="redCountEl">{{ red_count }}</span>
                </div>
                <span class="stats-label">标红数量</span>
              </div>
              <div class="stats-item" @click="handleStatsClick('yellow')">
                <div class="stats-circle">
                  <span class="stats-number" ref="yellowCountEl">{{ yellow_count }}</span>
                </div>
                <span class="stats-label">标黄数量</span>
              </div>
              <div class="stats-item" @click="handleStatsClick('blue')">
                <div class="stats-circle">
                  <span class="stats-number" ref="blueCountEl">{{ blue_count }}</span>
                </div>
                <span class="stats-label">标蓝数量</span>
              </div>
            </div>
            <div class="stats-row">
              <div class="stats-item" @click="handleStatsClick('red')">
                <div class="gauge-wrapper">
                  <svg viewBox="0 0 100 100">
                    <circle class="gauge-bg" cx="50" cy="50" r="45"/>
                    <circle class="gauge-fill red" 
                            cx="50" 
                            cy="50" 
                            r="45"
                            :style="{ strokeDasharray: `${parseFloat(red_ratio) * 2.83} 283` }"/>
                  </svg>
                  <div class="gauge-center">{{ red_ratio }}%</div>
                </div>
                <span class="stats-label">标红率</span>
              </div>
              <div class="stats-item" @click="handleStatsClick('yellow')">
                <div class="gauge-wrapper">
                  <svg viewBox="0 0 100 100">
                    <circle class="gauge-bg" cx="50" cy="50" r="45"/>
                    <circle class="gauge-fill yellow" 
                            cx="50" 
                            cy="50" 
                            r="45"
                            :style="{ strokeDasharray: `${parseFloat(yellow_ratio) * 2.83} 283` }"/>
                  </svg>
                  <div class="gauge-center">{{ yellow_ratio }}%</div>
                </div>
                <span class="stats-label">标黄率</span>
              </div>
              <div class="stats-item" @click="handleStatsClick('blue')">
                <div class="gauge-wrapper">
                  <svg viewBox="0 0 100 100">
                    <circle class="gauge-bg" cx="50" cy="50" r="45"/>
                    <circle class="gauge-fill blue" 
                            cx="50" 
                            cy="50" 
                            r="45"
                            :style="{ strokeDasharray: `${parseFloat(blue_ratio) * 2.83} 283` }"/>
                  </svg>
                  <div class="gauge-center">{{ blue_ratio }}%</div>
                </div>
                <span class="stats-label">标蓝率</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserStats, getStatisticsSum, getAnalysisLevelStats } from '@/api/statistics'
// import { getUserStats } from '@/api/user'
import { CountUp } from 'countup.js'
// 移除静态导入
// import AIChatBox from '@/components/AIChatBox.vue'
import { loginDify } from '@/api/case'

export default {
  name: 'Home',
  components: {
    // 不再直接注册AIChatBox组件
  },
  data() {
    return {
      loading: false,
      total_count: 0,
      analyzed_count: 0,
      red_count: 0,
      yellow_count: 0,
      blue_count: 0,
      red_ratio: 0,
      yellow_ratio: 0,
      blue_ratio: 0,
      countInstances: {},
      refreshTimer: null,
      rainCtx: null,
      dashedCtx: null,
      animationFrame: null,
      online_users: 0,
      registered_users: 0,
      userRole: '',
      serviceItems: [
        { name: '案件类警情流失巡查', icon: 'criminal-icon' },
        { name: '笔录巡查', icon: 'record-icon' },
        { name: '态势分析', icon: 'analysis-icon', description: '警情数据态势分析' },
        { name: '刑事案件立而不侦巡查', icon: 'investigation-icon' },
        { name: '法律文书巡查', icon: 'legal-icon' },
        { name: '卷宗审核', icon: 'file-icon' },
        { name: '涉案财物、保证金预警', icon: 'admin-icon' },
        { name: '更多扩展模块', icon: 'more-icon' },
      ]
    }
  },
  created() {
    // 获取用户角色
    const userInfo = this.$store.state.user
    this.userRole = userInfo?.role || ''

    // 如果是管理员，获取用户统计信息
    if (this.userRole === 'ADMIN') {
      this.fetchUserStats()
    }

    this.fetchAnalysisLevelStats()
    this.startRefreshTimer()
  },
  mounted() {
    this.initParticleEffect()
  },
  beforeDestroy() {
    this.stopRefreshTimer()
    this.stopParticleEffect()
  },
  methods: {
    startRefreshTimer() {
      this.refreshTimer = setInterval(() => {
        this.fetchAnalysisLevelStats()
      }, 60000)
    },
    stopRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    async fetchStatisticsSum() {
      try {
        const res = await getStatisticsSum()
        if (res.code === 200 && res.data) {
          const totalCount = res.data.total_count || 0
          const analyzedCount = res.data.analyzed_count || 0
          
          this.total_count = 0
          this.analyzed_count = 0
          
          this.$nextTick(() => {
            this.animateElement(document.querySelector('.number-item:first-child .number'), totalCount, 0)
            this.animateElement(document.querySelector('.number-item:last-child .number'), analyzedCount, 0)
          })
          
          setTimeout(() => {
            this.total_count = totalCount
            this.analyzed_count = analyzedCount
          }, 2000)
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },
    async fetchAnalysisLevelStats() {
      try {
        const res = await getAnalysisLevelStats()
        if (res.code === 200 && res.data) {
          const redCount = res.data.red_count || 0
          const yellowCount = res.data.yellow_count || 0
          const blueCount = res.data.blue_count || 0
          
          this.red_ratio = parseFloat(res.data.red_ratio) || 0
          this.yellow_ratio = parseFloat(res.data.yellow_ratio) || 0
          this.blue_ratio = parseFloat(res.data.blue_ratio) || 0
          
          this.red_count = 0
          this.yellow_count = 0
          this.blue_count = 0
          
          this.$nextTick(() => {
            this.animateElement(this.$refs.redCountEl, redCount, 0)
            this.animateElement(this.$refs.yellowCountEl, yellowCount, 0)
            this.animateElement(this.$refs.blueCountEl, blueCount, 0)
          })
          
          setTimeout(() => {
            this.red_count = redCount
            this.yellow_count = yellowCount
            this.blue_count = blueCount
          }, 2000)
        }
      } catch (error) {
        console.error('获取分析等级统计数据失败:', error)
      }
    },
    async fetchUserStats() {
      try {
        const res = await getUserStats()
        if (res.code === 200 && res.data) {
          this.online_users = res.data.online_users || 0
          this.registered_users = res.data.total_users || 0
        }
      } catch (error) {
        console.error('获取用户统计信息失败:', error)
      }
    },
    handleServiceItemClick(item) {
      if(item.name === '知识库'){
        // 1、调用接口
        loginDify().then(res => {
          if (res.code === 200 && res.data) {
            localStorage.setItem('setup_status','finished')
            localStorage.setItem('console_token',res.data.data.access_token)
            localStorage.setItem('refresh_token',res.data.data.refresh_token)
            window.open('/datasets', '_blank')
          }
        })
      } else if(item.name==='案件类警情流失巡查'){
        this.handleAnalysisClick()
      } else if(item.name==='笔录巡查'){
        this.$router.push('/record-check')
      } else if(item.name === '刑事案件立而不侦巡查'){
        this.$router.push('/stand-firm-without-spying')
      } else if(item.name === '态势分析'){
        this.$router.push('/situation-analysis')
      } else if(item.name === '法律文书巡查'){
        this.$router.push('/legal-record-check')
      } else if (item.name === "卷宗审核") {
        this.$router.push("/file-review");
      } else if (item.name === "涉案财物、保证金预警") {
        this.$router.push("/amount-warning");
      } else {
        this.$notify({
          title: '功能开发中',
          message: '该功能正在开发中，敬请期待',
          type: 'warning',
          position: 'top-right',
          duration: 3000,
          offset: 50
        });
      }
    },
    handleAnalysisClick() {
      this.$router.push('/case-management')
    },
    handleStatsClick(level) {
      this.$router.push({
        path: '/case-management',
        query: { analysis_level: level }
      })
    },
    animateElement(element, endValue, decimals = 0) {
      if (!element) return
      
      const id = element.id || Math.random().toString(36).substr(2, 9)
      const options = {
        startVal: 0,
        duration: 2,
        decimals: decimals,
        useEasing: true,
        useGrouping: true
      }
      
      const countUp = new CountUp(element, endValue, options)
      if (!countUp.error) {
        countUp.start()
        this.countInstances[id] = countUp
      } else {
        console.error(countUp.error)
      }
    },
    initParticleEffect() {
      const rainCanvas = document.querySelector('.rain')
      const dashedCanvas = document.querySelector('.dashed')
      
      if (!rainCanvas || !dashedCanvas) return
      
      const rainCtx = rainCanvas.getContext('2d')
      const dashedCtx = dashedCanvas.getContext('2d')
      
      // 设置画布大小
      const resizeCanvas = () => {
        const container = document.querySelector('.particle-container')
        if (!container) return
        
        const rect = container.getBoundingClientRect()
        rainCanvas.width = rect.width
        rainCanvas.height = rect.height
        dashedCanvas.width = rect.width
        dashedCanvas.height = rect.height
      }
      
      resizeCanvas()
      window.addEventListener('resize', resizeCanvas)
      
      // 粒子效果
      class Particle {
        constructor() {
          this.init()
        }
        
        init() {
          this.x = Math.random() * rainCanvas.width
          this.y = rainCanvas.height // 从底部开始
          this.vy = -(Math.random() * 1 + 1) // 向上移动的速度
          this.size = Math.random() * 2 + 1 // 随机大小
          this.alpha = Math.random() * 0.5 + 0.5 // 随机透明度
          this.color = this.getRandomColor()
        }
        
        getRandomColor() {
          const colors = [
            { r: 0, g: 255, b: 255 },  // 青色
            { r: 0, g: 195, b: 255 },  // 浅蓝色
            { r: 20, g: 120, b: 156 }  // 深蓝色
          ]
          const color = colors[Math.floor(Math.random() * colors.length)]
          return `rgba(${color.r}, ${color.g}, ${color.b}, ${this.alpha})`
        }
        
        draw() {
          rainCtx.beginPath()
          const gradient = rainCtx.createLinearGradient(this.x, this.y, this.x, this.y - this.size * 10)
          gradient.addColorStop(0, this.color)
          gradient.addColorStop(1, 'rgba(0, 255, 255, 0)')
          rainCtx.fillStyle = gradient
          rainCtx.fillRect(this.x, this.y, this.size, -this.size * 10) // 向上延伸的光效
          rainCtx.closePath()
          
          this.update()
        }
        
        update() {
          this.y += this.vy
          
          // 如果粒子超出顶部，重新初始化
          if (this.y < 0) {
            this.init()
          }
        }
      }
      
      // 初始化粒子
      const particles = []
      const particleCount = 50 // 增加粒子数量
      
      for (let i = 0; i < particleCount; i++) {
        setTimeout(() => {
          const particle = new Particle()
          particles.push(particle)
        }, i * 100) // 错开创建时间
      }
      
      // 虚线效果
      const drawDashedLines = () => {
        const w = dashedCanvas.width
        const h = dashedCanvas.height
        
        dashedCtx.lineWidth = 2
        dashedCtx.setLineDash([4, 4])
        dashedCtx.shadowOffsetX = 0
        dashedCtx.shadowOffsetY = 0
        dashedCtx.shadowColor = '#00ffff'
        dashedCtx.shadowBlur = 10
        
        // 第一条曲线
        dashedCtx.beginPath()
        let grd = dashedCtx.createLinearGradient(w / 11 * 2, h / 3, w / 5 * 2, h)
        grd.addColorStop(0, "#00ffff")
        grd.addColorStop(1, "#065261")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2, h)
        dashedCtx.quadraticCurveTo(w / 5, h / 6 * 5, w / 11 * 2, h / 3)
        dashedCtx.stroke()
        
        // 第二条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 11 * 3.3, h / 2, w / 3 * 1.1, h / 6 * 5)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 3 * 1.1, h / 6 * 5)
        dashedCtx.quadraticCurveTo(w / 5 * 1.5, h / 6 * 4.2, w / 11 * 3.3, h / 2)
        dashedCtx.stroke()
        
        // 第三条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 3 * 1.4, h / 5, w / 5 * 2, h / 2)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2, h / 2)
        dashedCtx.quadraticCurveTo(w / 3 * 1.2, h / 4 * 1.4, w / 3 * 1.4, h / 5)
        dashedCtx.stroke()
        
        // 第四条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 5 * 3.1, h / 3 * 1.2, w / 5 * 3.2, h / 2 * 1.5)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 3.2, h / 2 * 1.5)
        dashedCtx.quadraticCurveTo(w / 5 * 3.35, h / 2 * 1.2, w / 5 * 3.1, h / 3 * 1.2)
        dashedCtx.stroke()
        
        // 第五条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 5 * 3.3, h / 4, w / 5 * 3.2, h / 2 * 1.9)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 3.03, h / 2 * 1.9)
        dashedCtx.quadraticCurveTo(w / 5 * 3.8, h / 2 * 1.2, w / 5 * 3.3, h / 4)
        dashedCtx.stroke()
        
        // 第六条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 5 * 3.8, h / 2 * 1.2, w / 5 * 2.9, h)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2.9, h)
        dashedCtx.quadraticCurveTo(w / 5 * 3.7, h / 2 * 1.6, w / 5 * 3.8, h / 2 * 1.2)
        dashedCtx.stroke()
      }
      
      // 动画循环
      const animate = () => {
        rainCtx.clearRect(0, 0, rainCanvas.width, rainCanvas.height)
        dashedCtx.clearRect(0, 0, dashedCanvas.width, dashedCanvas.height)
        
        // 绘制粒子
        particles.forEach(particle => particle.draw())
        
        // 绘制虚线
        drawDashedLines()
        
        this.animationFrame = requestAnimationFrame(animate)
      }
      
      animate()
    },
    stopParticleEffect() {
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
      window.removeEventListener('resize', this.resizeCanvas)
    }
  }
}
</script>

<style scoped>
.home-container {
  width: 100%;
  height: 100vh;
  background: url('@/assets/images/bg.png') no-repeat center center;
  background-size: cover;
  position: relative;
  overflow: hidden;
  padding: 20px 40px;
  box-sizing: border-box;
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  pointer-events: none;
}

.dashed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  pointer-events: none;
}

.gong-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.gong-image {
  height: 120px;
  object-fit: contain;
  filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.4));
}

.police-motto {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 5px;
  margin-bottom: 15px;
  position: relative;
  font-family: 'FZxiaobiao-S01', '方正小标宋简体', 'SimSun', serif;
}

.police-motto::before {
  content: '';
  position: absolute;
  width: 30%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.5), transparent);
  top: -2px;
}

.police-motto p {
  color: #00ffff;
  font-size: 30px;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  margin: 5px 0;
  letter-spacing: 4px;
  font-weight: 500;
  animation: textGlow 3s infinite alternate;
}

.police-motto p:nth-child(2) {
  animation-delay: 1.5s;
}

@keyframes textGlow {
  from {
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  }
  to {
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
  }
}

.header-decoration {
  position: relative;
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  z-index: 2;
}

.title-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.main-title {
  color: #00ffff;
  font-weight: 600;
  font-size: 36px;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.7),
              0 0 40px rgba(0, 255, 255, 0.5);
  letter-spacing: 2px;
  display: block;
  margin-bottom: 8px;
}

.sub-title {
  color: rgba(0, 255, 255, 0.8);
  font-size: 18px;
  letter-spacing: 1px;
  display: block;
}

.content {
  display: flex;
  gap: 40px;
  height: calc(100%);
  margin-top: 30px;
}

.left-panel,
.right-panel {
  width: 25%;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
}

.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  padding-top: 30px;
  /* position: relative;
  justify-content: space-between; */
}

.panel-card {
  /* background: rgba(0, 24, 51, 0.3); */
  background: url("../assets/images/box-bg.png") no-repeat center top;
  background-size: 100% 100%;
  /* border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px; */
  padding: 12px;
  height: 100%;
}

.card-title {
  color: #00ffff;
  font-size: 18px;
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  text-align: center;
}

.case-list {
  color: #fff;
}

.list-header {
  display: grid;
  grid-template-columns: 1fr 0.8fr 1.2fr;
  padding: 12px;
  background: rgba(0, 255, 255, 0.15);
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 18px;
  text-align: center;
}

.list-content {
  position: relative;
  height: calc(100vh - 400px);
  overflow: hidden;
}

.list-wrapper {
  transition: transform 0.05s linear;
}

.list-item {
  display: grid;
  grid-template-columns: 1fr 0.8fr 1.2fr;
  padding: 10px;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.15);
}

.list-item:hover {
  background: rgba(0, 255, 255, 0.1);
}

.case-detail {
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.analysis-level {
  text-align: center;
  padding: 2px 8px;
  border-radius: 2px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analysis-level::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  border-radius: 2px;
}

.level-high::before {
  background-color: #FF9999;
}

.level-medium::before {
  background-color: #FFF9B3;
}

.level-low::before {
  background-color: #99CCFF;
}

.level-default::before {
  background-color: #FFFFFF;
}

.level-high {
  color: #ff4d4f;
  /* background: rgba(255, 77, 79, 0.1); */
}

.level-medium {
  color: #faad14;
  /* background: rgba(250, 173, 20, 0.1); */
}

.level-low {
  color: #52c41a;
  /* background: rgba(82, 196, 26, 0.1); */
}

.analysis-reason {
  color: rgba(255, 255, 255, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.number-display {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  background: rgba(0, 24, 51, 0.3);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 20px 40px;
  min-width: 700px;
  height: 100px;
}

.number-item {
  text-align: center;
  position: relative;
  flex: 1;
}

.number-item:first-child::after {
  content: '';
  position: absolute;
  right: -60px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 70%;
  background: rgba(0, 255, 255, 0.15);
}

.number {
  font-size: 48px;
  color: #ffd700;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  margin-bottom: 12px;
  font-family: 'Arial';
  font-weight: 600;
}

.label {
  color: #00ffff;
  font-size: 20px;
  text-shadow: 0 0 12px rgba(0, 255, 255, 0.4);
}

.map-container {
  position: relative;
  width: 95%;
  height: 550px;
  margin: 30px auto;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.map-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
  position: relative;
  z-index: 1;
  padding: 10px;
}

.light-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.6;
  mix-blend-mode: screen;
  padding: 10px;
}

.jt-effect {
  z-index: 2;
  width: 100%;
  height: 100%;
  animation: rotateJt 25s linear infinite reverse;
}

.lbx-effect {
  z-index: 3;
  width: 100%;
  height: 100%;
  animation: rotateLbx 20s linear infinite;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: none;
  pointer-events: none;
  z-index: 4;
}

@keyframes rotateLbx {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes rotateJt {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.analysis-button {
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
  border: 2px solid rgba(0, 255, 255, 0.5);
  padding: 15px 60px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 30px;
  position: absolute;
  overflow: hidden;
  width: 40%;
  text-align: center;
  z-index: 4;
}

.analysis-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.analysis-button:hover {
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.4), transparent);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.4);
  transform: scale(1.05);
}

.analysis-button:hover::before {
  left: 100%;
}

.button-text {
  color: #00ffff;
  font-size: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  font-weight: 500;
  letter-spacing: 2px;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 60px);
  padding: 20px;
}

.stats-row {
  display: flex;
  justify-content: space-around;
  gap: 20px;
  align-items: center;
  height: calc(50% - 10px);
}

.stats-row:first-child {
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  padding-bottom: 20px;
}

.stats-row:last-child {
  padding-top: 20px;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  width: calc(33% - 10px);
  padding: 20px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-item:hover {
  transform: scale(1.05);
  filter: brightness(1.2);
}

.gauge-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.gauge-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 12;
}

.gauge-fill {
  fill: none;
  stroke-width: 12;
  stroke-linecap: round;
  transform: rotate(-90deg);
  transform-origin: center;
  transition: stroke-dasharray 0.8s ease-in-out;
}

.gauge-fill.red {
  stroke: #ff4d4f;
  filter: drop-shadow(0 0 5px rgba(255, 77, 79, 0.5));
}

.gauge-fill.yellow {
  stroke: #faad14;
  filter: drop-shadow(0 0 5px rgba(250, 173, 20, 0.5));
}

.gauge-fill.blue {
  stroke: #1890ff;
  filter: drop-shadow(0 0 5px rgba(24, 144, 255, 0.5));
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  color: #00ffff;
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
  font-weight: 600;
  animation: numberGlow 3s infinite;
}

.gauge-wrapper:nth-child(1) .gauge-center {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation-delay: 0.5s;
}

.gauge-wrapper:nth-child(2) .gauge-center {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation-delay: 1s;
}

.gauge-wrapper:nth-child(3) .gauge-center {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation-delay: 1.5s;
}

@keyframes numberGlow {
  0% {
    text-shadow: 0 0 5px currentColor;
  }
  50% {
    text-shadow: 0 0 15px currentColor;
  }
  100% {
    text-shadow: 0 0 5px currentColor;
  }
}

.stats-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 3px solid rgba(0, 255, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
}

.stats-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('@/assets/images/img1.png') no-repeat center center;
  background-size: cover;
  z-index: 1;
}

.stats-circle::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('@/assets/images/img2.png') no-repeat center center;
  background-size: cover;
  z-index: 2;
  animation: rotateBg 10s linear infinite;
}

@keyframes rotateBg {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.stats-number {
  color: #00ffff;
  font-size: 32px;
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
  position: relative;
  z-index: 3;
  font-weight: 600;
  letter-spacing: 2px;
}

.stats-item:nth-child(1) .stats-circle {
  box-shadow: 0 0 20px rgba(255, 77, 79, 0.3);
}

.stats-item:nth-child(2) .stats-circle {
  box-shadow: 0 0 20px rgba(250, 173, 20, 0.3);
}

.stats-item:nth-child(3) .stats-circle {
  box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
}

.stats-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-top: 0;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* 大屏风格 Tooltip 样式 */
:deep(.ds-tooltip) {
  background: linear-gradient(135deg, rgba(0, 36, 89, 0.92), rgba(0, 12, 36, 0.95)) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 195, 255, 0.4) !important;
  border-radius: 4px !important;
  box-shadow: 0 0 25px rgba(0, 180, 255, 0.35),
              inset 0 0 40px rgba(0, 150, 255, 0.08) !important;
  padding: 0 !important;
}

:deep(.ds-tooltip[x-placement^="top"] .popper__arrow) {
  border-top-color: rgba(0, 195, 255, 0.4) !important;
  filter: drop-shadow(0 2px 10px rgba(0, 180, 255, 0.35)) !important;
}

:deep(.ds-tooltip[x-placement^="top"] .popper__arrow::after) {
  border-top-color: rgba(0, 36, 89, 0.92) !important;
}

.custom-tooltip-content {
  min-width: 200px;
}

.tooltip-header {
  background: linear-gradient(90deg, rgba(0, 195, 255, 0.18), rgba(0, 195, 255, 0.05));
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid rgba(0, 195, 255, 0.25);
}

.tooltip-icon {
  width: 4px;
  height: 14px;
  background: #00ffff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
  border-radius: 2px;
}

.tooltip-header span {
  color: #00ffff;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 1px;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
}

.tooltip-body {
  padding: 12px 15px;
  color: rgba(255, 255, 255, 0.95);
  font-size: 14px;
  line-height: 1.6;
  letter-spacing: 0.5px;
  max-width: 300px;
  word-break: break-all;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.tooltip-body::-webkit-scrollbar {
  width: 4px;
}

.tooltip-body::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
}

.tooltip-body::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.05);
  border-radius: 2px;
}

/* 自定义右侧面板滚动条样式 */
.stats-grid::-webkit-scrollbar {
  width: 4px;
}

.stats-grid::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
}

.stats-grid::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.05);
  border-radius: 2px;
}

.stats-progress {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0 10px;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid rgba(0, 255, 255, 0.2);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

.progress-fill {
  height: 100%;
  transition: width 0.8s ease-in-out;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  animation: shine 2s linear infinite;
}

.red-fill {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
}

.yellow-fill {
  background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
}

.blue-fill {
  background: linear-gradient(90deg, #1890ff 0%, #69c0ff 100%);
}

.progress-number {
  min-width: 45px;
  color: #00ffff;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
}

@keyframes shine {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

.tech-border {
  /* background: rgba(0, 24, 51, 0.2); */
  background: url("../assets/images/box-bg.png") no-repeat center top;
  background-size: 100% 100%;
  padding: 12px;
  position: relative;
  overflow: hidden;
}

.tech-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  margin: 0;
  pointer-events: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 20px;
  position: relative;
}

.header-text {
  color: #00ffff;
  font-size: 18px;
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  text-align: center;
  width: 100%;
}

.header-subtitle {
  font-size: 14px;
  color: rgba(0, 255, 255, 0.7);
  margin-left: 10px;
}

.header-number {
  font-size: 24px;
  color: rgba(0, 255, 255, 0.3);
  font-family: 'Arial';
  font-weight: bold;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: 25px;
  padding: 15px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 25px 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.service-item:hover {
  transform: translateY(-3px);
}

.service-item:hover .service-icon {
  border-color: rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
}

.service-icon {
  width: 70px;
  height: 70px;
  border: 2px solid rgba(0, 255, 255, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(0, 24, 51, 0.3);
  position: relative;
}

.service-icon::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 50%;
}

.service-icon i {
  font-size: 38px;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.service-icon i[class*="el-icon-"] {
  font-size: 38px;
}

.service-icon i:not([class*="el-icon-"])::before {
  font-size: 38px;
  display: inline-block;
}

/* 自定义图标样式 */
.criminal-icon::before { 
  content: '⚡'; 
  font-size: 38px;
}

.knowledge-icon::before { 
  content: '📚';
  font-size: 38px;
}

.admin-icon::before { 
  content: '📋';
  font-size: 38px;
}

.legal-icon::before { 
  content: '📜';
  font-size: 38px;
}

.record-icon::before { 
  content: '✍️';
  font-size: 38px;
}

.file-icon::before { 
  content: '📁';
  font-size: 38px;
}

.analysis-icon::before {
  content: '📊';
  font-size: 38px;
}

/* 新增刑事案件立而不侦巡查图标 */
.investigation-icon::before {
  content: '🔍';
  font-size: 38px;
}

/* 新增更多扩展模块图标 */
.more-icon::before {
  content: '➕';
  font-size: 38px;
}

/* 自定义左侧面板滚动条样式 */
.service-grid::-webkit-scrollbar {
  width: 4px;
}

.service-grid::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
}

.service-grid::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.05);
  border-radius: 2px;
}
.service-name {
  font-size: 16px;
  color: white;
}

.user-stats-container {
  width: 95%;
  margin: 20px auto;
  background: rgba(0, 24, 51, 0.3);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 20px;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.user-stats .number-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.user-stats .number-item:first-child::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 70%;
  background: rgba(0, 255, 255, 0.15);
}

.user-stats .number {
  font-size: 36px;
  color: #ffd700;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  margin-bottom: 8px;
  font-family: 'Arial';
  font-weight: 600;
}

.user-stats .label {
  color: #00ffff;
  font-size: 16px;
  text-shadow: 0 0 12px rgba(0, 255, 255, 0.4);
}
</style>