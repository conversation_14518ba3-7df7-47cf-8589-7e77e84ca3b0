<template>
  <div class="location-multi-case">
    <div class="search-section">
      <el-form
        :inline="true"
        :model="searchForm"
        class="common-search search-form"
        label-width="120px"
      >
        <div class="search-left">
          <el-form-item label="经度">
            <el-input v-model="searchForm.longitude" placeholder="输入经度"></el-input>
          </el-form-item>
          <el-form-item label="纬度">
            <el-input v-model="searchForm.latitude" placeholder="输入纬度"></el-input>
          </el-form-item>
        </div>
        <div class="search-right">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="sub-total">共找到{{ total }}组一地多起</div>
    <div class="case-list">
      <div v-for="item in locationList" :key="item.case" class="case-card">
        <div class="card-header">
          <span class="card-title">{{ item.case_type }} - {{ item.case }}</span>
          <span class="card-id">位置: {{ item.location }}</span>
        </div>
        <div class="card-body">
          <div class="case-table table-container">
            <el-table :data="item.entities" style="width: 100%" size="small">
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column prop="id_card" label="身份证号" width="200" />
              <el-table-column prop="role" label="角色类型" />
            </el-table>
          </div>
          <div class="case-info">
            <div class="info-item">
              <span class="label">报警时间：</span>
              <span class="value">{{ formatDateTime(item.police_time) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { searchByLocation } from "@/api/caseAnalyze";

export default {
  name: "LocationMultiCase",
  data() {
    return {
      searchForm: {
        longitude: "",
        latitude: "",
      },
      locationList: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  methods: {
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "";
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    async fetchLocationList() {
      try {
        const params = {
          ...this.searchForm,
          page: this.page,
          page_size: this.pageSize,
        };

        const response = await searchByLocation(params);

        if (response.code === 200) {
          const { items, total } = response.data;
          this.total = total;
          this.locationList = items;
        } else {
          this.$message.error(response.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取一地多起数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchLocationList();
    },

    handleCurrentChange(val) {
      this.page = val;
      this.fetchLocationList();
    },

    handleSearch() {
      if (!this.searchForm.longitude || !this.searchForm.latitude) {
        this.$message.warning("请输入经纬度");
        return;
      }
      this.page = 1;
      this.fetchLocationList();
    },

    resetSearch() {
      this.searchForm = {
        longitude: "",
        latitude: "",
      };
      this.page = 1;
      this.locationList = [];
      this.total = 0;
    },
  },
};
</script>

<style lang="less" scoped>
.location-multi-case {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 500px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  .search-section {
    padding: 24px 32px 8px 32px;
    border-radius: 8px;
    margin-bottom: 18px;
    flex-shrink: 0;
    .el-form-item {
      margin-right: 24px;
      .el-input,
      .el-select {
        color: #fff;
      }
    }
  }

  .sub-total {
    color: #bfc8e2;
    font-size: 14px;
    margin-bottom: 20px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    display: inline-block;
    flex-shrink: 0;
  }

  .case-list {
    flex: 1;
    padding-right: 10px;
    margin-bottom: 20px;
    overflow: visible;
  }

  .case-card {
    background: #22304a;
    border-radius: 8px;
    margin-bottom: 24px;
    padding: 18px 24px 12px 24px;
    .card-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      padding: 16px 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
      }

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        flex: 1;
        min-width: 300px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 16px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      .card-id {
        color: #bfc8e2;
        font-size: 14px;
        padding: 4px 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
      }
    }

    .card-body {
      .case-info {
        margin-top: 16px;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .label {
            color: #bfc8e2;
            font-size: 14px;
          }

          .value {
            color: #fff;
            font-size: 14px;
          }
        }
      }
    }
  }

  .pagination-container {
    padding: 0 20px;
    text-align: right;
    background: #18233a;
    flex-shrink: 0;
  }
}
</style>
