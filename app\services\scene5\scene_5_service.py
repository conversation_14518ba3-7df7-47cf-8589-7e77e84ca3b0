import asyncio
import hashlib
import os
from datetime import datetime
from typing import Any, List, Dict
from urllib.parse import urlparse, parse_qs

import aiohttp
import fitz
from fastapi import UploadFile
from sqlalchemy import and_, or_
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.config import settings, src
from app.core.context_handle import UserContext
from app.core.task_queue import TaskQueueManager
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.org import Org
from app.models.scene_5_record import Scene5Record, Scene5OcrPO, scene5_search_order
from app.services.scene5.paddle_ocr import OCR_WORKER_COUNT, paddle_ocr_service
from app.services.user.org_service import OrgService


class Scene5Service:

    def __init__(self):
        super().__init__()
        # 注册场景5分析队列
        self.analysis_queue_name = "scene_5_analysis"
        # ocr识别队列
        self.ocr_queue_name = "scene_5_ocr"
        # 注册场景5同步队列
        self.queue_name = "scene_5_sync"
        # 异步分析队列
        self.queue_manager = TaskQueueManager()

    async def async_init(self):
        # 异步初始化代码
        await self.queue_manager.register_queue(
            queue_name=self.queue_name,
            processor=self._sync,
            max_size=1000,  # 队列容量
            concurrency=20  # 最大并发数
        )
        await self.queue_manager.register_queue(
            queue_name=self.analysis_queue_name,
            processor=self._do_analyze_with_ocr,
            max_size=1000,  # 队列容量
            concurrency=50  # 最大并发数
        )
        await self.queue_manager.register_queue(
            queue_name=self.ocr_queue_name,
            processor=self._ocr,
            max_size=5000,  # 队列容量
            concurrency=OCR_WORKER_COUNT  # 最大并发数 与 ocr_pool 数量一致
        )

    @staticmethod
    @transactional
    async def scene_5_search(search_params: Dict[str, Any], db: Session) -> Dict[str, Any]:
        LogUtils.info(f"开始查询数据，参数: {search_params}")
        try:
            # 构建查询条件
            query = db.query(Scene5Record)
            conditions = []

            # 添加查询条件
            if search_params.get('ajbh'):
                conditions.append(Scene5Record.case_code == search_params['ajbh'])
            if search_params.get('ajmc'):
                # 案件名称模糊搜索
                conditions.append(Scene5Record.case_name.like(f"%{search_params['ajmc']}%"))
            if search_params.get('aj_type'):
                # 分析结论模糊搜索
                conditions.append(Scene5Record.aj_type == search_params['aj_type'])
            if search_params.get('analysis_result'):
                # 分析结论模糊搜索
                conditions.append(Scene5Record.analysis_result == search_params['analysis_result'])
            if search_params.get('start_date'):
                conditions.append(Scene5Record.mod_time >= search_params['start_date'])
            if search_params.get('end_date'):
                conditions.append(Scene5Record.mod_time <= search_params['end_date'])
            # 应用查询条件
            if conditions:
                query = query.filter(and_(*conditions))
            # 获取当前用户信息
            org_code = UserContext.get_current_user_org().get("org_code")
            police_number = UserContext.current_user.get().police_number

            # 查询当前用户机构权限
            org_info = db.query(Org).filter(Org.org_code == org_code).first()

            # 构建机构权限查询条件
            org_condition = (
                Scene5Record.org_code.like(f"{org_code}%")
                if org_info and org_info.can_sub_query == 1
                else Scene5Record.org_code == org_code
            )

            # 添加机构或警号的查询条件
            query = query.filter(
                or_(
                    org_condition,
                    Scene5Record.org_code == f"-{police_number}"
                )
            )

            # 获取总记录数
            total = query.count()
            LogUtils.info(f"查询到总记录数: {total}")
            page = search_params.get('page')
            page_size = search_params.get('page_size')

            # 自定义排序
            status_order = scene5_search_order()

            if page is None and page_size is None:
                items = query.order_by(status_order, Scene5Record.update_time.desc()).all()
            else:
                page = 1 if page is None else page
                page_size = 10 if page_size is None else page_size
                # 分页参数
                offset = (page - 1) * page_size

                # 获取分页数据，
                items = query.order_by(status_order, Scene5Record.update_time.desc()).offset(offset).limit(
                    page_size).all()
                LogUtils.info(f"获取到第 {page} 页数据，每页 {page_size} 条")

            # 转换结果为字典列表
            result_items = []
            for item in items:
                item_dict = {k: v for k, v in vars(item).items() if not k.startswith('_')}
                result_items.append(item_dict)

            return {
                "total": total,
                "items": result_items,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"查询数据时出错: {str(e)}")
            raise Exception(f"查询数据时出错: {str(e)}")

    @staticmethod
    @transactional
    async def scene_5_get_detail(id: int, db: Session) -> Dict[str, Any]:
        LogUtils.info(f"开始查询数据，id: {id}")
        try:
            # 构建查询条件
            record = db.query(Scene5Record).filter(
                Scene5Record.id == id,
            ).first()

            if record:
                record_dict = {k: v for k, v in vars(record).items()
                               if not k.startswith('_')}
                return record_dict

        except Exception as e:
            LogUtils.error(f"查询数据时出错: {str(e)}")
            raise Exception(f"查询数据时出错: {str(e)}")

    def _query_administrative_cases(self, start_date: str, end_date: str, db: Session) -> List[str]:
        def convert_start_date(date_str):
            """转换为当天零点"""
            dt = datetime.strptime(date_str, "%Y%m%d")
            return dt.strftime("%Y%m%d%H%M%S")

        def convert_end_date(date_str):
            """转换为当天最后一秒"""
            dt = datetime.strptime(date_str, "%Y%m%d")
            end_time = dt.replace(hour=23, minute=59, second=59)
            return end_time.strftime("%Y%m%d%H%M%S")

        start_date = convert_start_date(start_date)
        end_date = convert_end_date(end_date)
        """查询符合条件的行政案件编号（按案件编号分组，取最新入库记录）"""
        sql = text("""
            WITH ranked_cases AS (
                SELECT 
                    ajbh,
                    ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY fh_rksj DESC) as rn
                FROM ods_aj_jbxx 
                WHERE ajzt = :ajzt 
                AND bacx = :bacx 
                AND jasj BETWEEN :start_date AND :end_date
            )
            SELECT ajbh
            FROM ranked_cases
            WHERE rn = 1
        """)
        params = {
            'ajzt': '205',
            'bacx': '21',
            'start_date': start_date,
            'end_date': end_date
        }
        result = db.execute(sql, params)
        return [row.ajbh for row in result.fetchall()]

    def _query_prosecution_cases(self, start_date: str, end_date: str, db: Session) -> List[str]:

        """查询符合条件的已起诉刑事案件编号（按案件编号分组，取最新入库记录）"""
        sql = text("""
            WITH ranked_cases AS (
                SELECT 
                    ajbh,
                    ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY fh_rksj DESC) as rn
                FROM ods_aj_flws_dxglb
                WHERE jdjg = :jdjg
                AND pzsj BETWEEN :start_date AND :end_date
            )
            SELECT ajbh
            FROM ranked_cases
            WHERE rn = 1
        """)
        params = {
            'jdjg': '143500',
            'start_date': start_date,
            'end_date': end_date
        }
        result = db.execute(sql, params)
        return [row.ajbh for row in result.fetchall()]

    # ocr异步识别
    @transactional
    async def _ocr(self, task_data: dict, db: Session = None):
        """处理OCR识别任务"""
        id = task_data["id"]
        url = task_data["url"]
        sn = task_data["sn"]
        total_urls = task_data["total_urls"]
        callback = task_data["callback"]
        callback_params = task_data["callback_params"]

        LogUtils.info("开始处理ocr识别")

        # 检查是否已存在OCR记录
        existing_record = db.query(Scene5OcrPO).filter(
            Scene5OcrPO.scene_5_record_id == id,
            Scene5OcrPO.src_source == url
        ).first()
        is_first_execution = existing_record is None

        if not is_first_execution:
            LogUtils.info(f"OCR记录已存在 scene_5_record_id: {id} URL {url}")
            ocr_po = existing_record
        else:
            ocr_po = Scene5OcrPO(
                scene_5_record_id=id,
                src_source=url,
                sn=sn
            )

        if not ocr_po.ocr_content:
            try:
                # 执行OCR识别
                ocr_result = await paddle_ocr_service.orc_image(url)

                if ocr_result:
                    ocr_po.ocr_content = ocr_result
                db.add(ocr_po)
                db.commit()
                LogUtils.info(f"OCR结果入库成功 scene_5_record_id: {id} URL {url}")

            except Exception as e:
                LogUtils.error(f"OCR处理失败 scene_5_record_id: {id}: {str(e)}")
                try:
                    record = db.query(Scene5Record).filter(
                        Scene5Record.id == id
                    ).first()
                    if record:
                        record.ocr_status = 3
                        db.commit()
                except Exception as db_error:
                    LogUtils.error(f"更新Scene5Record失败: {str(db_error)}")
                return

        # 检查是否为案件的最后一个URL
        # 首次执行，允许分析为空--所有src识别完后均会触发dify流程
        if is_first_execution:
            completed_count = db.query(Scene5OcrPO).filter(
                Scene5OcrPO.scene_5_record_id == id,
            ).count()
        else:
            # 非首次执行，不允许为空
            completed_count = db.query(Scene5OcrPO).filter(
                Scene5OcrPO.scene_5_record_id == id,
                Scene5OcrPO.ocr_content.isnot(None)
            ).count()
        LogUtils.info(
            f"OCR记录已存在 scene_5_record_id: {id} ocr总数量: {total_urls} ocr已完成数量: {completed_count} ")
        if completed_count == total_urls:
            # 所有URL都处理完成，执行回调
            try:
                await callback(callback_params)
            except Exception as e:
                LogUtils.error(f"执行回调失败 scene_5_record_id: {id}: {str(e)}")

    # 同步逻辑异步执行
    @transactional
    async def _sync(self, task_data: dict, db: Session = None):
        case_numbers = task_data["case_numbers"]
        detail_data = task_data["detail_data"]
        case_type_map = task_data["case_type_map"]
        # db = task_data["db"]

        batch_size = 100
        total = len(case_numbers)
        success = 0
        org_map = OrgService(db).get_org_map(db)
        for i, case_number in enumerate(case_numbers, 1):
            if case_number not in detail_data:
                continue
            # 查询 Scene5Record 是否已经同步
            # 若已存在记录，检查src是否有变更
            # 添加根据 source = 0 的筛选条件
            existing_record = db.query(Scene5Record).filter(
                Scene5Record.case_code == case_number,
                Scene5Record.source == 0  # 添加source筛选条件
            ).first()

            # 若已存在记录，检查src是否有变更
            if existing_record:
                new_src = detail_data[case_number]["src"]
                if existing_record.src != new_src:
                    # 更新为最新的同步数据
                    existing_record.src = new_src
                    existing_record.fh_rksj = detail_data[case_number]["fh_rksj"]
                    # 重置状态
                    existing_record.ocr_status = 0  # 未识别
                    existing_record.analysis_result = 0  # 未分析
                    db.add(existing_record)
                else:
                    LogUtils.info(f'已存在案件 {case_number}，未更改')
                continue  # 跳过后续的新增逻辑

            try:
                detail = detail_data[case_number]
                scene5_record = Scene5Record(
                    # org_code=org_code,
                    case_code=detail["case_code"],
                    case_name=detail["case_name"],
                    handle_org_code=detail["handle_org_code"],
                    hoster_id=detail["hoster_id"],
                    mod_time=detail["mod_time"],
                    src=detail["src"],
                    fh_rksj=detail["fh_rksj"],
                    aj_type=case_type_map[case_number] , # 新增案件类型字段
                    org_code=org_map.get(detail["handle_org_code"], "0000")
                )
                db.add(scene5_record)
                success += 1

                # 每100条或最后一条时提交事务
                if i % batch_size == 0 or i == len(case_numbers):
                    db.commit()
                    LogUtils.info(f"已提交第 {i // batch_size} 批次数据")

            except Exception as e:
                LogUtils.error(f"插入案件 {case_number} 出错: {str(e)}")
                continue

        LogUtils.info(f"数据同步完成，总数: {total}, 成功: {success}, 失败: {total - success}")

    @transactional
    async def scene_5_data_sync(self, user_id: int, start_date: str, end_date: str, db: Session = None) -> bool:
        """同步案件数据"""

        LogUtils.info(f"开始同步数据，用户ID: {user_id}")
        try:
            LogUtils.info(f"查询时间范围: {start_date} 至 {end_date}")

            # 查询行政案件和刑事案件编号
            admin_cases = self._query_administrative_cases(start_date, end_date, db)
            prosecution_cases = self._query_prosecution_cases(start_date, end_date, db)

            # 标记案件类型并合并去重
            case_type_map = {}
            for case in admin_cases:
                case_type_map[case] = "21"  # 行政案件
            for case in prosecution_cases:
                case_type_map[case] = "10"  # 刑事案件

            case_numbers = list(case_type_map.keys())
            if not case_numbers:
                return True
            # 批量查询ag_dzdawj表中的详细信息
            detail_sql = text("""
                SELECT case_code, case_name, handle_org_code, 
                       hoster_id, mod_time, src, fh_rksj
                FROM ods_ag_dzdawj
                WHERE case_code IN :case_numbers
                ORDER BY case_code, CAST(sn AS UNSIGNED)
            """)
            detail_result = db.execute(detail_sql, {'case_numbers': case_numbers})

            # 聚合相同case_code的src字段为JSON数组
            detail_data = self.src_aggregation(detail_result)
            LogUtils.info(f"合并后的案件总数: {len(list(detail_data.keys()))}")

            task_data = {
                "case_numbers": case_numbers,
                "detail_data": detail_data,
                "case_type_map": case_type_map,
            }
            sync_result = await self.queue_manager.add_task(self.queue_name, task_data)
            if not sync_result:
                return False

            return True

        except Exception as e:
            LogUtils.error(f"同步过程出错: {str(e)}")
            return False

    # src聚合入库预处理
    def src_aggregation(self, detail_result) -> dict[Any, dict[str, list[Any] | Any]]:
        detail_data = {}
        for row in detail_result:
            src = self.process_image_url(row.src)
            if row.case_code in detail_data:
                # 如果已存在则追加src到数组
                detail_data[row.case_code]["src"].append(src)
            else:
                # 初始化新条目并创建src数组
                detail_data[row.case_code] = {
                    "case_code": row.case_code,
                    "case_name": row.case_name,
                    "handle_org_code": row.handle_org_code,
                    "hoster_id": row.hoster_id,
                    "mod_time": row.mod_time,
                    "fh_rksj": row.fh_rksj,
                    "src": [src]
                }

        # 将src数组转换为JSON字符串
        # for case_code, data in detail_data.items():
        #     data["src"] = json.dumps(data["src"], ensure_ascii=False)

        return detail_data

    def process_image_url(self, url):
        """
        处理图片下载地址，根据不同格式返回正确的下载URL

        参数:
            url (str): 原始图片下载地址

        返回:
            str: 处理后的下载地址
        """
        # 解析URL
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        # 检查是否有path参数
        if 'path' in query_params:
            path = query_params['path'][0]

            # 判断是否为格式2（path以/http:开头）
            if path.startswith('/http:'):
                # 格式2: 提取path后的地址
                url = path[1:]  # 去掉开头的斜杠

        # 检查http:后面是单'/'还是双'//',如果是单斜杠则转为双斜杠
        if 'http:/' in url and not 'http://' in url:
            url = url.replace('http:/', 'http://')

        return url

    @transactional
    async def _dify_process(self, id: int, record: Scene5Record, db: Session = None):
        # 使用aiohttp进行异步HTTP请求
        timeout = aiohttp.ClientTimeout(total=180)  # 设置30秒总超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            headers = {
                'Authorization': f'Bearer {settings.SCENE5_API_KEY}',
            }

            json_data = {
                'inputs': {'id': f'{id}'},
                'response_mode': 'blocking',
                'user': 'abc-123',
            }

            is_error = False
            try:
                LogUtils.info(f"卷宗dify 流程开始: {json_data} ")
                async with session.post(
                        f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                        headers=headers,
                        json=json_data,
                        timeout=timeout  # 为单个请求设置超时
                ) as response:
                    if response.status == 200:
                        # 获取响应数据
                        response_data = await response.json()
                        if response_data['data']['status'] != 'succeeded':
                            is_error = True
                    else:
                        is_error = True
            except asyncio.TimeoutError:
                LogUtils.error(f"任务scene_5_record_id: {id} API请求超时")
                is_error = True

            if is_error:
                record.analysis_result = 3
                db.add(record)
                db.commit()
        LogUtils.info(f"任务scene_5_record_id: {id} 处理完成")

    """执行带OCR的完整分析流程"""

    @transactional
    async def _do_analyze_with_ocr(self, task_data: dict, db: Session = None):
        # 回调函数
        async def process_callback(callback_params: dict):
            LogUtils.info("处理回调")
            id = callback_params.get('id')
            record: Scene5Record = callback_params.get('record')

            # ocr识别完成
            record.ocr_status = 2

            # 分析进行中
            record.analysis_result = 1
            db.add(record)
            db.commit()

            # dify 流程调用
            await self._dify_process(id, record, db)

        id = task_data['id']

        # 查询案件记录
        record = db.query(Scene5Record).filter_by(id=id).first()
        if not record or not record.src:
            return

        src_list = record.src
        if not src_list:
            return

        try:
            callback_params = {
                'id': id,
                'record': record,
            }

            # 创建并提交所有OCR任务
            ocr_tasks = []
            for i, url in enumerate(src_list, 1):
                ocr_task = {
                    "id": id,
                    "url": url,
                    "sn": i,
                    "total_urls": len(src_list),  # 添加总数信息
                    "callback": process_callback,  # 添加回调函数
                    "callback_params": callback_params  # 添加回调参数
                }
                ocr_tasks.append(ocr_task)

            # 将所有任务添加到统一队列
            for task in ocr_tasks:
                await self.queue_manager.add_task(self.ocr_queue_name, task)

            LogUtils.info(f"scene_5_record_id: {id} 所有OCR任务已提交到统一队列")

        except Exception as e:
            LogUtils.error(f"分析流程异常 scene_5_record_id: {id}: {str(e)}")
            if record:
                record.ocr_status = 3
                db.commit()
                LogUtils.warning(f"已更新scene_5_record_id: {id} 失败状态")

    async def scene_5_analyze_with_ocr(self, ids: [int]):
        for id in ids:
            task_param = {
                'id': id,
            }
            await self.queue_manager.add_task(self.analysis_queue_name, task_param)

    @staticmethod
    @transactional
    async def upload(
            aj_type: str,
            pdf_file: UploadFile,
            db: Session
    ) -> Dict[str, Any]:
        """
        同时创建案件信息并上传PDF文件，将PDF转换为图片保存
        """
        try:

            # 检查文件类型
            if not pdf_file.filename.lower().endswith('.pdf'):
                raise Exception(f"只能上传PDF文件")

            # 确保上传目录存在
            SAVE_DIR = 'saved_images'
            if not os.path.exists(SAVE_DIR):
                os.makedirs(SAVE_DIR)

            # 读取PDF文件内容
            content = await pdf_file.read()
            md5_id = hashlib.md5(content).hexdigest()
            # 当前时间戳
            time = datetime.now().strftime("%Y%m%d%H%M%S")
            pdf_document = fitz.open(stream=content, filetype="pdf")

            # 保存所有页面为图片并收集URL
            image_urls = []
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                pix = page.get_pixmap()

                # 生成图片文件名
                image_filename = f"{md5_id}_{time}_page_{page_num + 1}.png"
                image_path = os.path.join(SAVE_DIR, image_filename)

                # 保存图片
                pix.save(image_path)

                # 创建文件访问URL
                image_url = f"{src}/aj_src_local/{image_filename}"
                image_urls.append(image_url)
            # 获取当前用户人 信息
            police_number=UserContext.get_current().police_number
            police_number=f"-{police_number}"
            # 创建案件记录
            scene5_record = Scene5Record(
                aj_type=aj_type,
                src=image_urls,  # 存储所有图片URL
                mod_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                ocr_status=0,  # 未识别状态
                analysis_result=0,  # 未分析状态
                source=1 , # 数据来源: 1-文件上传
                org_code= police_number
            )

            # 保存记录
            db.add(scene5_record)
            db.commit()

            return {
                "file_name": pdf_file.filename,
                "images": [{"page": i + 1, "url": url} for i, url in enumerate(image_urls)]
            }
        except Exception as e:
            print(e)
            db.rollback()
            raise Exception(f"卷宗PDF文件解析流程失败")
        finally:
            if 'pdf_document' in locals():
                pdf_document.close()


_instance = None


async def get_scene_5_service() -> Scene5Service:
    global _instance
    if not _instance:
        _instance = Scene5Service()
        await _instance.async_init()
    return _instance
