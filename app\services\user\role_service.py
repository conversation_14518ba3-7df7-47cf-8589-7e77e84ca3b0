from typing import List, Optional, Set, Dict

from fastapi import HTT<PERSON>Exception
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.models.org import OrgToRole
from app.models.role import Role, UserToRole, RoleMenuPermission
from app.schemas.role_schema import RoleCreate, RoleUpdate, UserRoleCreate, RoleMenuCreate, RoleMenuList, RoleQueryDto, \
    MenuAuthRequest


def getRoleList(self, role_id: int) -> Optional[Dict]:
    userId=UserContext.get_current_id()
    sql = 'SELECT u.id AS userid, r.name AS role_name FROM user_info  u JOIN user_to_role ur ON u.id = ur.user_id JOIN role_info  r ON ur.role_id = r.id WHERE u.id = :userId'
    result = self.db.execute(text(sql),{"userId":userId}).fetchall()
    if not result:
        return None

        # 处理结果，构建字典
    user_roles = {}
    for row in result:
        userid = row.userid
        role_name = row.role_name

        if userid not in user_roles:
            user_roles[userid] = {"userid": userid, "role": []}

        user_roles[userid]["role"].append({"name": role_name})

    return user_roles


def getRoleList(db: Session, request: RoleQueryDto) -> dict:
    try:
        # 设置默认分页参数
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10
        offset = (page - 1) * page_size

        # 构建基础查询
        query = db.query(Role)
        # 添加查询条件
        if request.name:
            query = query.filter(Role.name.like(f"%{request.name}%"))
        if request.status is not None and request.status != '' and request.status != 'null':
            query = query.filter(Role.status == request.status)
        # 获取总数
        total = query.count()
        # 获取分页数据
        roles = query.order_by(Role.id.desc()) \
            .offset(offset) \
            .limit(page_size) \
            .all()
        # 转换为字典列表
        result_items = []
        for role in roles:
            role_dict = {
                "id": role.id,
                "name": role.name,
                "status": 1 if role.status else 0,
                "sort": role.sort,
                "code":role.code
            }
            result_items.append(role_dict)

        return {
            "total": total,
            "items": result_items,
            "page": page,
            "page_size": page_size
        }

    except Exception as e:
        return create_response(code=500, data="str(e)")



def getRoleById(db: Session, id: int):
    try:
        # 查询角色
        role = db.query(Role).filter(Role.id == id).first()
        if not role:
            return None
        # 转换为字典
        role_dict = {
            "id": role.id,
            "name": role.name,
            "status": 1 if role.status else 0,
            "sort": role.sort,
            "code":role.code
        }

        return create_response(code=200, data=role_dict)
    except Exception as e:
        return create_response(code=500, message="str(e)")



def RoleSave(db: Session, role: RoleCreate) :
    try:
        # 检查角色编码是否存在
        result = db.query(Role).filter(Role.code == role.code).first()
        if result:
            raise Exception(detail=f"角色编码 {role.code} 已存在" )

        # 创建新角色
        db_role = Role(**role.dict())
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        return create_response(code=200,data=db_role)

    except Exception as e:
        db.rollback()
        if isinstance(e, HTTPException):
            raise e
        return create_response(
            code=500,
            data=str(e),
            message="保存角色失败"
        )


def RoleUp(db: Session, role: RoleUpdate):
    try:
        # 先查询数据库中的角色
        db_role = db.query(Role).filter(Role.id == role.id).first()

        if not db_role:
            raise Exception("角色不存在")

        # 更新角色属性
        for key, value in role.dict(exclude_unset=True).items():
            setattr(db_role, key, value)

        db.commit()
        db.refresh(db_role)
        result= {
            "id": db_role.id,
            "name": db_role.name,
            "status": db_role.status,
            "sort": db_role.sort
             }

        return create_response(code=200,data=result)

    except Exception as e:
        db.rollback()
        return create_response(
            code=500,
            data=str(e)
        )


def deleteRole(db: Session, role_id: int) -> bool:
    try:
        # 查询角色是否存在
        db_role = db.query(Role).filter(Role.id == role_id).first()
        if not db_role:
            raise Exception("角色不存在")

        # 检查角色关联关系
        userToRole = db.query(UserToRole).filter(UserToRole.role_id == role_id).first()
        orgToRole = db.query(OrgToRole).filter(OrgToRole.role_id == role_id).first()
        if userToRole or orgToRole:
            raise Exception("该角色存在用户或组织绑定关系，请先解绑后再删除")

        try:
            # 删除角色菜单权限
            db.query(RoleMenuPermission).filter(
                RoleMenuPermission.role_id == role_id
            ).delete()

            # 删除角色
            db.delete(db_role)
            db.commit()
            return create_response(code=200, data=True)

        except SQLAlchemyError as e:
            db.rollback()
            return create_response(
                code=500,
                message=f"数据库操作失败: {str(e)}"
            )
    except Exception as e:
        return create_response(
            code=500,
            message=f"删除角色失败: {str(e)}"
        )


def assign_role_to_user(self, user_role: UserRoleCreate) -> UserToRole:
    db_user_role = UserToRole(**user_role.dict())
    self.db.add(db_user_role)
    self.db.commit()
    self.db.refresh(db_user_role)
    return db_user_role


def remove_role_from_user(self, user_id: int, role_id: int) -> bool:
    db_user_role = self.db.query(UserToRole).filter(
        UserToRole.user_id == user_id,
        UserToRole.role_id == role_id
    ).first()
    if db_user_role:
        self.db.delete(db_user_role)
        self.db.commit()
        return True
    return False


def get_user_roles(self, user_id: int) -> List[Role]:
    return self.db.query(Role).join(UserToRole).filter(UserToRole.user_id == user_id).all()


def assign_menu_to_role(self, role_menu: RoleMenuCreate) -> RoleMenuPermission:
    db_role_menu = RoleMenuPermission(**role_menu.dict())
    self.db.add(db_role_menu)
    self.db.commit()
    self.db.refresh(db_role_menu)
    return db_role_menu


def assign_menus_to_role(self, role_menu_list: RoleMenuList) -> List[RoleMenuPermission]:
    # 先删除该角色的所有菜单权限
    self.db.query(RoleMenuPermission).filter(
        RoleMenuPermission.role_id == role_menu_list.role_id
    ).delete()

    # 添加新的菜单权限
    role_menus = []
    for menu_id in role_menu_list.menu_ids:
        role_menu = RoleMenuPermission(
            role_id=role_menu_list.role_id,
            menu_id=menu_id
        )
        role_menus.append(role_menu)

    self.db.add_all(role_menus)
    self.db.commit()
    for role_menu in role_menus:
        self.db.refresh(role_menu)

    return role_menus


def get_role_menus(self, role_id: int) -> List[int]:
    return [menu[0] for menu in self.db.query(RoleMenuPermission.menu_id)
    .filter(RoleMenuPermission.role_id == role_id).all()]

def menuAuthorization( db: Session, request: MenuAuthRequest):
    try:
        # 检查角色是否存在
        role = db.query(Role).filter(Role.id == request.role_id).first()
        if not role:
            raise HTTPException(
                status_code=400,
                detail="角色不存在"
            )
        # 删除原有的角色菜单权限
        db.query(RoleMenuPermission).filter(
            RoleMenuPermission.role_id == request.role_id
        ).delete()
       ## 优化逻辑
        # 如果 menu_ids 不为空才进行新增操作
        if request.menu_ids:
            # 组装新的权限数据
            role_menu_permissions = [
                RoleMenuPermission(
                    role_id=request.role_id,
                    menu_id=menu_id
                ) for menu_id in request.menu_ids
            ]
            # 批量插入新的权限数据
            db.bulk_save_objects(role_menu_permissions)

        db.commit()
        return create_response( code=200,data=True)
    except Exception as e:
        db.rollback()
        return create_response(
            code=500,
            data=str(e),
            message="fail"
        )


def menuAuthorizationCodeCheck(db: Session, roleId: int):
    try:
        # 检查角色是否存在
        role = db.query(Role).filter(Role.id == roleId).first()
        if not role:
            raise Exception("角色不存在" )

        # 查询该角色的菜单权限，只获取 menu_id
        menu_ids = db.query(RoleMenuPermission.menu_id).filter(
            RoleMenuPermission.role_id == roleId
        ).all()

        # 将查询结果转换为简单的 ID 列表
        result= {
            "check_code": [menu_id[0] for menu_id in menu_ids]
        }
        return create_response(
            code=200,
            data=result
        )

    except Exception as e:
        return create_response(
            code=500,
            data=str(e),
            message="fail"
        )
