#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 cluster_summary 步骤
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from sqlalchemy import text


async def debug_cluster_summary():
    """调试 cluster_summary 步骤"""
    print("开始调试 cluster_summary 步骤...")
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 测试 cluster_summary 步骤
        cluster_summary_sql = """
        WITH pivot_data AS (
            SELECT
                qcf.case_id,
                qcf.entity_id,
                MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
                MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
            FROM qz_case_feature qcf
            JOIN police_records pr ON qcf.case_id = pr.police_number
            WHERE pr.source = 0
              AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
            GROUP BY qcf.case_id, qcf.entity_id
        ),
        entities_with_multiple_cases AS (
            SELECT
                ENTITY_NAME,
                ID_CARD,
                COUNT(DISTINCT case_id) as case_count
            FROM pivot_data
            WHERE ENTITY_NAME IS NOT NULL AND entity_id > 0
            GROUP BY ENTITY_NAME, ID_CARD
            HAVING COUNT(DISTINCT case_id) >= 2
        ),
        name_id_mapping AS (
            SELECT
                ENTITY_NAME,
                MAX(ID_CARD) as representative_id_card
            FROM entities_with_multiple_cases
            WHERE ID_CARD IS NOT NULL
            GROUP BY ENTITY_NAME
        ),
        shared_entities_with_cluster AS (
            SELECT
                emc.ENTITY_NAME,
                emc.ID_CARD,
                CASE
                    WHEN nim.representative_id_card IS NOT NULL THEN nim.representative_id_card
                    ELSE CONCAT('NO_ID_CARD_', emc.ENTITY_NAME)
                END as cluster_key
            FROM entities_with_multiple_cases emc
            LEFT JOIN name_id_mapping nim ON emc.ENTITY_NAME = nim.ENTITY_NAME
        ),
        cases_with_shared_entities AS (
            SELECT DISTINCT
                pd.case_id,
                COALESCE(pd.CASE_TYPE, 'UNKNOWN') as CASE_TYPE,
                pr.police_time
            FROM pivot_data pd
            JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            JOIN police_records pr ON pd.case_id = pr.police_number
            WHERE pd.entity_id > 0 AND pr.source = 0
        ),
        case_shared_entity_count AS (
            SELECT
                cwse.case_id,
                cwse.CASE_TYPE,
                cwse.police_time,
                COUNT(DISTINCT sec.cluster_key) as shared_entity_count
            FROM cases_with_shared_entities cwse
            JOIN pivot_data pd ON cwse.case_id = pd.case_id
            JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            WHERE pd.entity_id > 0
            GROUP BY cwse.case_id, cwse.CASE_TYPE, cwse.police_time
            HAVING COUNT(DISTINCT sec.cluster_key) >= 2
        ),
        case_entities_detail AS (
            SELECT
                csec.case_id,
                csec.CASE_TYPE,
                csec.police_time,
                pd.entity_id,
                pd.ENTITY_NAME,
                pd.ID_CARD,
                pd.ENTITY_TYPE,
                sec.cluster_key,
                CASE WHEN sec.cluster_key IS NOT NULL THEN 1 ELSE 0 END as is_shared_entity
            FROM case_shared_entity_count csec
            JOIN pivot_data pd ON csec.case_id = pd.case_id
            LEFT JOIN shared_entities_with_cluster sec ON (
                pd.ENTITY_NAME = sec.ENTITY_NAME AND
                (pd.ID_CARD = sec.ID_CARD OR (pd.ID_CARD IS NULL AND sec.ID_CARD IS NULL))
            )
            WHERE pd.entity_id > 0 AND pd.ENTITY_NAME IS NOT NULL
        ),
        case_shared_entities AS (
            SELECT
                case_id,
                CASE_TYPE,
                police_time,
                GROUP_CONCAT(DISTINCT
                    CONCAT(ENTITY_NAME, '|', COALESCE(ID_CARD, ''))
                    ORDER BY ENTITY_NAME
                    SEPARATOR ';;'
                ) as shared_entities_key
            FROM case_entities_detail
            WHERE is_shared_entity = 1
            GROUP BY case_id, CASE_TYPE, police_time
        )
        SELECT
            CASE_TYPE,
            shared_entities_key,
            COUNT(DISTINCT case_id) as case_count,
            GROUP_CONCAT(DISTINCT
                CONCAT(case_id, '|', DATE_FORMAT(police_time, '%Y-%m-%d %H:%i:%s'))
                ORDER BY police_time DESC
                SEPARATOR ';;'
            ) as cases_info
        FROM case_shared_entities
        GROUP BY CASE_TYPE, shared_entities_key
        ORDER BY case_count DESC
        """
        
        print("执行 cluster_summary 查询...")
        result = db.execute(text(cluster_summary_sql))
        cluster_summary = result.fetchall()
        
        print(f"cluster_summary 结果数量: {len(cluster_summary)}")
        
        if cluster_summary:
            print("cluster_summary 结果:")
            for i, row in enumerate(cluster_summary):
                print(f"  {i+1}. 案件类型: {row[0]}")
                print(f"      共享实体: {row[1]}")
                print(f"      案件数: {row[2]}")
                print(f"      案件信息: {row[3]}")
                print()
                
            # 检查哪些聚类有2个或更多案件
            print("检查案件数 >= 2 的聚类:")
            valid_clusters = [row for row in cluster_summary if row[2] >= 2]
            print(f"符合条件的聚类数量: {len(valid_clusters)}")
            
            for i, row in enumerate(valid_clusters):
                print(f"  {i+1}. 案件类型: {row[0]}")
                print(f"      共享实体: {row[1]}")
                print(f"      案件数: {row[2]}")
                print(f"      案件信息: {row[3]}")
                print()
        else:
            print("cluster_summary 没有结果")
        
        print("调试完成！")
        
    except Exception as e:
        print(f"调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(debug_cluster_summary())
