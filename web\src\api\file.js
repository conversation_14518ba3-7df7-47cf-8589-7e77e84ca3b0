import request from "@/utils/request";
// 获取笔录列表
export function getRecordList(data) {
  return request({
    url: "/api/scene_5/search",
    method: "post",
    data,
  });
}

// 获取笔录详情
export function getRecordDetail(data) {
  return request({
    url: "/api/scene_5/get/detail",
    method: "get",
    params: data,
  });
}

// 同步列表
export function syncList(data) {
  return request({
    url: "/api/scene5/sync",
    method: "post",
    data,
  });
}

// 创建案件
export function create_aj(data) {
  return request({
    url: "/api/scene_5/create",
    method: "post",
    data,
  });
}

// 上传文件
export function uploadFile(data, config = {}) {
  return request({
    url: "/api/scene_5/upload",
    method: "post",
    data,
    ...config,
  });
}

// 批量分析案件
export function batchAnalyzeCases(data) {
  return request({
    url: "/api/scene_5/analyze",
    method: "post",
    data,
  });
}

// 导入PDF文件
export function importPDF(data, config = {}) {
  return request({
    url: "/api/scene_5/upload",
    method: "post",
    data,
    ...config,
  });
}
