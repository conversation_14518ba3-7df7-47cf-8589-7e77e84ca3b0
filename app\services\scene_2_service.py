import asyncio
import io
import json
from datetime import datetime
from typing import Dict, Optional, Any, List

import aiohttp
from fastapi import Depends
from openpyxl.styles import PatternFill
from openpyxl.workbook import Workbook
from sqlalchemy import text, and_, or_
from sqlalchemy.orm import Session

from app.config import settings
from app.core.context_handle import UserContext
from app.db.database import get_db
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.org import Org
from app.models.scene_2_po import Scene2PO, FIELD_MAPPING, COLOR_MAPPING, scene2_search_order
from app.services.user.org_service import OrgService

# 创建任务队列
analysis_queue = asyncio.Queue()


class Scene2Service:
    # 类级别的缓存字典
    _dict_cache = {}  # 格式: {(code, dic_id): item}
    # 定义枚举映射
    # 案件状态
    _AZJT_DIC_ID = 101
    # AJZT_MAP = {
    #     '111': '起诉',
    #     '109': '撤案',
    #     'other': '其他'
    # }

    # 涉案人员
    _SARYLB_DIC_ID = 517
    # SARYLB_MAP = {
    #     '1': '犯罪嫌疑人',
    #     'other': '其他'
    # }
    # TODO 案件类别未知
    _AJLB_DIC_ID = 517

    # 现实状况
    _XSZK_DIC_ID = 212

    # XSZK_MAP = {
    #     '2': '抓获',
    #     'other': '其他'
    # }

    # def get_mapped_value(value: str, mapping: dict) -> str:
    #     """获取映射值，如果不在映射表中则返回'其他'"""
    #     return mapping.get(value, mapping['other'])

    @staticmethod
    def get_mapped_value_with_dict(code: str, dic_id: int, db: Session) -> str:
        """
        获取字典映射值，使用缓存机制
        
        Args:
            code (str): 代码
            dic_id (int): 字典ID
            db (Session): 数据库会话
            
        Returns:
            str: 映射后的值
        """
        # 生成缓存键
        cache_key = (code, dic_id)

        # 检查缓存
        if cache_key in Scene2Service._dict_cache:
            return Scene2Service._dict_cache[cache_key]

        try:
            # 查询地区字典数据
            dic_query = text(
                "SELECT code, item FROM ods_dic WHERE code = :code and dic_id = :dic_id")
            dics = [row._asdict() for row in
                    db.execute(dic_query, {
                        "code": code,
                        "dic_id": dic_id
                    }).fetchall()]

            # 构建地址信息
            if dics:
                result = dics[0]['item']
            else:
                result = code

            # 更新缓存
            Scene2Service._dict_cache[cache_key] = result

            return result

        except Exception as e:
            LogUtils.error(f"字典查询失败 code: {code}, dic_id: {dic_id}, error: {str(e)}")
            return code

    @staticmethod
    async def search_with_db(query, condition: dict, db: Session = Depends(get_db)) -> any:
        return [row._asdict() for row in db.execute(query, condition).fetchall()]

    @staticmethod
    @transactional
    async def scene_2_sql(ajbh: str, user_id: int, db: Session) -> Scene2PO:
        """
        立而不侦场景SQL查询方法
        
        Args:
            ajbh (str): 案件编号
            user_id (str): 用户ID，默认为system
            db (Session): 数据库会话
            
        Returns:
            Scene2PO: 案件分析记录实体
        """
        result = {}
        # 更新或创建分析记录
        existing_record = db.query(Scene2PO).filter(Scene2PO.ajbh == ajbh).first()
        if existing_record:
            # 使用vars()获取实例的所有属性，并过滤掉SQLAlchemy内部属性
            record_dict = {k: v for k, v in vars(existing_record).items()
                           if not k.startswith('_')}
            return Scene2PO(**record_dict)

        # 查询案件状态和立案时间
        # 111-起诉 / 109-撤案
        # 根据入库时间倒序取第一个
        status_query = text(
            "SELECT ajzt as '案件状态', lasj as '立案时间' FROM ods_aj_jbxx WHERE ajbh = :ajbh order by fh_rksj desc limit 1")
        status_result = [row._asdict() for row in db.execute(status_query, {"ajbh": ajbh}).fetchall()]
        if status_result:
            # 映射案件状态
            status_result[0]['案件状态'] = Scene2Service.get_mapped_value_with_dict(status_result[0]['案件状态'],
                                                                                    Scene2Service._AZJT_DIC_ID, db)
        result["案件状态"] = status_result

        # 查询犯罪嫌疑人(=1)相关信息
        # zjhm (身份证号)
        person_query = text(
            "SELECT 现实状况, 涉案人员, 嫌疑人姓名 FROM ( SELECT xszk as '现实状况', sarylb as '涉案人员', xm as '嫌疑人姓名', ROW_NUMBER() OVER ( PARTITION BY djrzh ORDER BY fh_rksj DESC ) as rn FROM ods_aj_sary WHERE ajbh = :ajbh and sarylb = '1' ) t WHERE rn = 1")
        person_result = [row._asdict() for row in db.execute(person_query, {"ajbh": ajbh}).fetchall()]
        if len(person_result) > 0:
            # 映射涉案人员和现实状况
            for person in person_result:
                person['涉案人员'] = Scene2Service.get_mapped_value_with_dict(person['涉案人员'],
                                                                              Scene2Service._SARYLB_DIC_ID, db)
                person['现实状况'] = Scene2Service.get_mapped_value_with_dict(person['现实状况'],
                                                                              Scene2Service._XSZK_DIC_ID, db)
            # 转换结果格式
            result["涉案人员信息"] = {
                "现实状况": [doc["现实状况"] for doc in person_result],
                "涉案人员": [doc["涉案人员"] for doc in person_result],
                "嫌疑人姓名": [doc["嫌疑人姓名"] for doc in person_result],
            }

        # 查询指定文书
        docs_query = text(
            "SELECT doc_name as '文书名称' FROM ods_aj_flws WHERE ajbh = :ajbh AND wszt = 2 and doc_name IN ('起诉意见书', '终止侦查决定书','撤销案件决定书')")
        docs_result = [row._asdict() for row in db.execute(docs_query, {"ajbh": ajbh}).fetchall()]
        if len(docs_result) > 0:
            # 转换结果格式
            result["终局性文书"] = {
                "文书名称": [doc["文书名称"] for doc in docs_result]
            }

        # 查询强制措施文书
        force_docs_query = text("""
            SELECT doc_name as '文书名称',pzsj as '批准时间' 
            FROM ods_aj_flws 
            WHERE ajbh = :ajbh 
            # AND wszt = 2 
            AND doc_name IN ('拘留证', '取保候审决定书', '监视居住决定书', '执行通知书', '逮捕证')
        """)
        force_docs_result = [row._asdict() for row in db.execute(force_docs_query, {"ajbh": ajbh}).fetchall()]
        if len(force_docs_result) > 0:
            # 转换结果格式
            result["强制措施文书"] = {
                "文书名称": [doc["文书名称"] for doc in force_docs_result],
                "批准时间": [doc["批准时间"] for doc in force_docs_result]
            }

        # 查询解除强制措施文书
        release_docs_query = text("""
            SELECT doc_name as '文书名称', pzsj as '批准时间' 
            FROM ods_aj_flws 
            WHERE ajbh = :ajbh 
            # AND wszt = 2 
            AND doc_name IN ('拘留释放通知书', '解除取保候审决定书', '解除监视居住决定书')
        """)
        release_docs_result = [row._asdict() for row in db.execute(release_docs_query, {"ajbh": ajbh}).fetchall()]
        if len(release_docs_result) > 0:
            # 转换结果格式
            result["解除强制措施文书"] = {
                "文书名称": [doc["文书名称"] for doc in release_docs_result],
                "批准时间": [doc["批准时间"] for doc in release_docs_result]
            }

        result['巡查日期'] = datetime.now().strftime("%Y%m%d")



        new_record = Scene2PO(
            ajbh=ajbh,
            summary=json.dumps(result, ensure_ascii=False),
            analysis_status="init",
            user_id=user_id
        )
        LogUtils.info(f"案件 {ajbh} 数据查询完成，创建新记录")
        return new_record

    @staticmethod
    @transactional
    async def scene_2_data_sync(user_id: int, start_date: str, end_date: str, db: Session) -> Optional[Dict[str, int]]:
        """
        同步立而不侦场景数据
        """
        def convert_start_date(date_str):
            """转换为当天零点"""
            dt = datetime.strptime(date_str, "%Y%m%d")
            return dt.strftime("%Y%m%d%H%M%S")  # 自动补零

        def convert_end_date(date_str):
            """转换为当天最后一秒"""
            dt = datetime.strptime(date_str, "%Y%m%d")
            end_time = dt.replace(hour=23, minute=59, second=59)
            return end_time.strftime("%Y%m%d%H%M%S")

        global ajbh
        LogUtils.info(f"开始同步数据，用户ID: {user_id}")
        try:
            # 计算时间范围
            # end_date = datetime.now() - timedelta(days=90)
            # start_date = end_date - timedelta(days=90)  # 180天前到90天前
            start_date = convert_start_date(start_date)
            end_date = convert_end_date(end_date)
            LogUtils.info(f"查询时间范围: {start_date} 至 {end_date}")
            # 查询时间范围内的案件编号，刑事案件 (bacx = '10')
            case_query = text("""
                SELECT * FROM (
                    SELECT 
                        ajbh,
                        lasj,
                        bacx,
                        ajlb,
                        ajfab,
                        ajmc,
                        ajzt,
                        ajly,
                        sldw,
                        zbdwmc,
                        zbrxm,
                        slsj,
                        ladw,
                        ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY fh_rksj DESC) AS rn
                    FROM ods_aj_jbxx 
                    WHERE bacx = '10' 
                     AND lasj >= :start_date 
                     AND lasj <= :end_date
                ) sub
                WHERE rn = 1
            """)
            cases = [row._asdict() for row in db.execute(case_query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchall()]
            LogUtils.info(f"查询到 {len(cases)} 个案件需要同步")

            success_count = 0
            failed_count = 0
            if len(cases) == 0:
                LogUtils.warning("没有找到需要同步的案件")
                return {
                    "total": len(cases),
                    "success": success_count,
                    "failed": failed_count
                }
            # 遍历案件并处理
            org_map = OrgService(db).get_org_map(db)
            batch_size = 100
            for i, case in enumerate(cases, 1):
                # 案件状态转换
                case['ajzt'] = Scene2Service.get_mapped_value_with_dict(case['ajzt'], Scene2Service._AZJT_DIC_ID, db)
                # 案件类别转换
                case['ajlb'] = Scene2Service.get_mapped_value_with_dict(case['ajlb'], Scene2Service._AJLB_DIC_ID, db)
                # 组织编码映射
                org_code = org_map.get(case["ladw"], "0000")
                try:
                    ajbh = case['ajbh']
                    LogUtils.info(f"开始处理案件 {ajbh}")

                    # 调用场景同步方法
                    record = await Scene2Service.scene_2_sql(ajbh, user_id, db)
                    # 根据id判断是更新还是新增
                    if record.id:
                        # 存在记录跳过
                        continue
                    # 更新记录的基本信息
                    for key, value in case.items():
                        if hasattr(record, key):
                            setattr(record, key, value)
                    record.org_code = org_code
                    # 新增记录
                    db.add(record)

                    # 每100条或最后一条时提交事务
                    if i % batch_size == 0 or i == len(cases):
                        db.commit()
                        LogUtils.info(f"已提交第 {i // batch_size} 批次数据")
                    success_count += 1
                except Exception as e:
                    failed_count += 1
                    LogUtils.error(f"处理案件 {ajbh} 时出错: {str(e)}")
                    continue

            LogUtils.info(f"数据同步完成，成功: {success_count}, 失败: {failed_count}")
            return {
                "total": len(cases),
                "success": success_count,
                "failed": failed_count
            }

        except Exception as e:
            # db.rollback()
            LogUtils.error(f"同步过程出错: {str(e)}")
            return None

    @staticmethod
    async def start_background_processor(task_semaphore):
        """
        启动后台任务处理器

        Args:
            task_semaphore: 任务信号量，用于控制并发数量
        """
        LogUtils.info("启动后台任务处理器")
        while True:
            try:
                # 从队列获取任务，设置超时时间为5秒
                try:
                    id, db = await asyncio.wait_for(analysis_queue.get(), timeout=5.0)
                except asyncio.TimeoutError:
                    LogUtils.debug("队列为空，等待5秒后重新检查")
                    await asyncio.sleep(5)
                    continue

                try:
                    # 创建新的任务，但不等待其完成
                    task = asyncio.create_task(
                        Scene2Service._process_single_case_with_semaphore(id, db, task_semaphore)
                    )
                    # 添加任务完成回调，用于错误处理
                    task.add_done_callback(
                        lambda t: LogUtils.error(f"任务异常: {t.exception()}") if t.exception() else None)
                except Exception as e:
                    LogUtils.error(f"创建任务时出错: {str(e)}")
                finally:
                    # 标记任务完成
                    analysis_queue.task_done()
            except Exception as e:
                LogUtils.error(f"后台处理器出错: {str(e)}")
                await asyncio.sleep(1)  # 出错时等待一秒再继续

    @staticmethod
    async def _process_single_case_with_semaphore(id: int, db: Session, semaphore: asyncio.Semaphore):
        """
        使用信号量处理单个案件的后台方法

        Args:
            record: 案件记录
            db: 数据库会话
            semaphore: 信号量对象
        """
        async with semaphore:  # 使用信号量控制并发
            LogUtils.info(f"任务 Scene2PO.id:{id} 开始处理")
            try:
                record = db.query(Scene2PO).filter(
                    Scene2PO.id == id
                ).first()
                # 更新案件状态为处理中
                record.analysis_status = "processing"
                db.merge(record)
                db.commit()

                # 使用aiohttp进行异步HTTP请求
                timeout = aiohttp.ClientTimeout(total=60)  # 设置30秒总超时
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    headers = {
                        'Authorization': f'Bearer {settings.SCENE2_API_KEY}',
                    }

                    json_data = {
                        'inputs': {'ajbh': record.ajbh},
                        'response_mode': 'blocking',
                        'user': 'abc-123',
                    }

                    try:
                        async with session.post(
                                f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                                headers=headers,
                                json=json_data,
                                timeout=timeout  # 为单个请求设置超时
                        ) as response:
                            if response.status == 200:
                                # 获取响应数据
                                response_data = await response.json()
                                if response_data['data']['status'] != 'succeeded':
                                    record.analysis_status = "failed"
                                    db.merge(record)
                                    db.commit()
                            else:
                                record.analysis_status = "failed"
                                db.merge(record)
                                db.commit()
                    except asyncio.TimeoutError:
                        LogUtils.error(f"任务 {record.ajbh} API请求超时")
                        record.analysis_status = "failed"
                        db.merge(record)
                        db.commit()
                        raise
                LogUtils.info(f"任务 {record.ajbh} 处理完成")
            except Exception as e:
                record.analysis_status = "failed"
                db.merge(record)
                db.commit()
                LogUtils.error(f"分析案件 {record.ajbh} 时出错: {str(e)}")
                raise  # 重新抛出异常，以便在任务完成回调中捕获

    @staticmethod
    async def _analyze_single_case(record: Scene2PO, db: Session) -> bool:
        """
        将案件添加到分析队列

        Args:
            record: 案件记录
            db: 数据库会话

        Returns:
            bool: 是否成功添加到队列
        """
        try:

            # 将任务添加到队列
            await analysis_queue.put((record.id, db))
            return True

        except Exception as e:
            record.analysis_status = "failed"
            db.add(record)
            db.commit()
            LogUtils.error(f"添加案件 {record.ajbh} 到队列时出错: {str(e)}")
            return False

    @staticmethod
    @transactional
    async def scene_2_summary_analyze(ajbhes: [str] = None, db: Session = None):
        """
        立而不侦场景分析方法

        Args:
            ajbhes: 案件编号数组，默认为None（将查询所有状态为init的案件）
            batch_size: 每批处理的案件数量，默认50

        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            # 构建查询条件
            query = db.query(Scene2PO).filter(
                or_(
                    Scene2PO.analysis_status == "init",
                    Scene2PO.analysis_status == "failed"
                )
            )

            # 如果提供了案件编号数组，则添加案件编号过滤条件
            if ajbhes and len(ajbhes) > 0:
                query = query.filter(Scene2PO.ajbh.in_(ajbhes))

            # 获取所有符合条件的记录
            records = query.all()
            total_records = len(records)
            LogUtils.info(f"开始分析案件，总数: {total_records}")

            if total_records == 0:
                LogUtils.warning("没有找到需要分析的案件")
                return {
                    "total": 0,
                    "success": 0,
                    "failed": 0
                }

            # 将任务添加到队列
            tasks = [
                Scene2Service._analyze_single_case(record, db)
                for record in records
            ]

            # 并发添加任务到队列
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            success_count = sum(1 for r in results if r is True)
            failed_count = sum(1 for r in results if r is False)

            LogUtils.info(f"任务已添加到队列，总数: {total_records}, 成功: {success_count}, 失败: {failed_count}")
            return {
                "total": total_records,
                "success": success_count,
                "failed": failed_count,
                "message": "分析任务已添加到队列，将在后台处理"
            }

        except Exception as e:
            db.rollback()
            LogUtils.error(f"分析过程出错: {str(e)}")
            raise Exception(f"分析过程出错: {str(e)}")

    @staticmethod
    @transactional
    async def scene_2_search(search_params: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """
        立而不侦场景查询服务方法

        Args:
            search_params: 查询参数，包含以下可选字段：
                - ajbh: 案件编号
                - ajmc: 案件名称（模糊搜索）
                - analysis_result: 分析结论
                - start_date: 开始日期
                - end_date: 结束日期
                - analysis_status: 分析状态
                - ajzt: 案件状态
                - zbdwmc: 主办单位名称
                - page: 页码（默认1）
                - page_size: 每页数量（默认10）

        Returns:
            Dict[str, Any]: 查询结果，包含：
                - total: 总记录数
                - items: 当前页记录列表
        """
        LogUtils.info(f"开始查询数据，参数: {search_params}")
        try:
            # 构建查询条件
            query = db.query(Scene2PO)
            conditions = []

            # 添加查询条件
            if search_params.get('ajbh'):
                conditions.append(Scene2PO.ajbh == search_params['ajbh'])
            if search_params.get('ajmc'):
                # 案件名称模糊搜索
                conditions.append(Scene2PO.ajmc.like(f"%{search_params['ajmc']}%"))
            if search_params.get('analysis_result'):
                # 分析结论模糊搜索
                conditions.append(Scene2PO.analysis_result == search_params['analysis_result'])
            if search_params.get('start_date'):
                conditions.append(Scene2PO.lasj >= search_params['start_date'])
            if search_params.get('end_date'):
                conditions.append(Scene2PO.lasj <= search_params['end_date'])
            if search_params.get('analysis_status'):
                conditions.append(Scene2PO.analysis_status == search_params['analysis_status'])
            if search_params.get('ajzt'):
                conditions.append(Scene2PO.ajzt == search_params['ajzt'])
            if search_params.get('zbdwmc'):
                conditions.append(Scene2PO.zbdwmc == search_params['zbdwmc'])

            # 应用查询条件
            if conditions:
                query = query.filter(and_(*conditions))
            # 获取当前用户机构
            org_code = UserContext.get_current_user_org().get("org_code")
            # 获取当前用户警号
            police_number = UserContext.current_user.get().police_number

            # 查询当前用户机构权限
            org_info = db.query(Org).filter(Org.org_code == org_code).first()

            # 构建机构权限查询条件
            org_condition = (
                Scene2PO.org_code.like(f"{org_code}%")
                if org_info and org_info.can_sub_query == 1
                else Scene2PO.org_code == org_code
            )

            # 添加机构或警号的查询条件
            query = query.filter(
                or_(
                    org_condition,
                    Scene2PO.org_code == f"-{police_number}#"
                )
            )
            # 获取总记录数
            total = query.count()
            LogUtils.info(f"查询到总记录数: {total}")
            page = search_params.get('page')
            page_size = search_params.get('page_size')

            # 自定义排序
            status_order = scene2_search_order()

            if page is None and page_size is None:
                items = query.order_by(status_order, Scene2PO.lasj.desc()).all()
            else:
                page = 1 if page is None else page
                page_size = 10 if page_size is None else page_size
                # 分页参数
                offset = (page - 1) * page_size

                # 获取分页数据，按分析状态和立案时间排序
                items = query.order_by(status_order, Scene2PO.lasj.desc()).offset(offset).limit(page_size).all()
                LogUtils.info(f"获取到第 {page} 页数据，每页 {page_size} 条")

            # 转换结果为字典列表
            result_items = []
            for item in items:
                item_dict = {k: v for k, v in vars(item).items() if not k.startswith('_')}
                result_items.append(item_dict)

            return {
                "total": total,
                "items": result_items,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"查询数据时出错: {str(e)}")
            raise Exception(f"查询数据时出错: {str(e)}")

    @staticmethod
    def export_excel(records: List[dict]) -> bytes:
        """
        导出Excel文件

        Args:
            records: 要导出的记录列表，每个记录是一个字典

        Returns:
            bytes: Excel文件的二进制内容
        """
        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active

            # 写入表头
            headers = ['序号'] + list(FIELD_MAPPING.values())
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据并设置颜色
            for row_idx, record in enumerate(records, 2):
                # 写入序号
                ws.cell(row=row_idx, column=1, value=row_idx - 1)
                # 写入数据
                for col_idx, field in enumerate(FIELD_MAPPING.keys(), 2):
                    value = record.get(field)
                    # 格式化日期时间
                    if field == 'lasj' and value:
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    ws.cell(row=row_idx, column=col_idx, value=value)

                # 设置行颜色
                analysis_tag = record.get('analysis_tag')
                if analysis_tag in COLOR_MAPPING:
                    color = COLOR_MAPPING[analysis_tag]
                    fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
                    for col in range(1, len(headers) + 1):
                        ws.cell(row=row_idx, column=col).fill = fill

            # 调整列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[chr(64 + col)].width = 15

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return output.getvalue()

        except Exception as e:
            LogUtils.error(f"Excel导出失败: {str(e)}")
            raise Exception(f"Excel导出失败: {str(e)}")
