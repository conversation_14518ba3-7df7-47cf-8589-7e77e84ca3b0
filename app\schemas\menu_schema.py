from pydantic import BaseModel
from typing import List, Optional

class MenuBase(BaseModel):
    name: str
    sort: Optional[int] = None
    is_link: bool
    link_url: Optional[str] = None
    is_catalog: bool
    web_path: Optional[str] = None
    component: Optional[str] = None
    component_name: Optional[str] = None
    status: bool
    cache: bool
    visible: bool
    is_iframe: bool
    is_affix: bool
    parent_id: Optional[int] = None
    icon:str

class MenuCreate(MenuBase):
    pass

class MenuUpdate(MenuBase):
    id: int

class MenuInDB(MenuBase):
    id: int

    class Config:
        orm_mode = True

class MenuTree(MenuInDB):
    children: List['MenuTree'] = []