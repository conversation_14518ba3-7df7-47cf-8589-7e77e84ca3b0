<template>
  <el-dialog
    title="选择日期范围"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    custom-class="date-range-dialog  screen-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="prompt-title"> {{ titleLabel }}</div>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyyMMdd"
        :picker-options="pickerOptions"
      ></el-date-picker>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
      <el-button type="primary" @click="handleConfirm" class="ensure-btn">确定同步</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'DateRangeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    restrictToPastYears: {
      type: Number,
      default: null
    },
    titleLabel :{
      type: String,
      default: "请选择同步日期范围"
    }
  },
  data() {
    return {
      dateRange: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
        if (!val) {
          this.dateRange = []
        }
      }
    },
    pickerOptions() {
      const options = {
        shortcuts: null
      };

      if (this.restrictToPastYears) {
        options.disabledDate = (time) => {
          const now = new Date();
          const twoYearsAgo = new Date();
          twoYearsAgo.setFullYear(now.getFullYear() - this.restrictToPastYears);
          return time.getTime() > twoYearsAgo.getTime();
        };
      }

      return options;
    }
  },
  watch: {
    visible(val) {
      if (val && this.restrictToPastYears) {
        // 设置默认日期范围为两年前到两年前
        const now = new Date();
        const twoYearsAgo = new Date();
        twoYearsAgo.setFullYear(now.getFullYear() - this.restrictToPastYears);
        // 设置开始时间为两年前的第一天
        const startDate = new Date(twoYearsAgo);
        startDate.setMonth(0, 1);
        startDate.setHours(0, 0, 0, 0);
        // 设置结束时间为两年前的最后一天
        const endDate = new Date(twoYearsAgo);
        endDate.setMonth(11, 31);
        endDate.setHours(23, 59, 59, 999);
        this.dateRange = [startDate, endDate];
      }
    }
  },
  methods: {
    handleClose() {
      this.dateRange = []
      this.$emit('close')
    },
    handleCancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    handleConfirm() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning('请选择日期范围')
        return
      }
      this.$emit('confirm', this.dateRange)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px 0;
}

.prompt-title {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

:deep(.date-range-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}



/* 日期选择器样式 */
:deep(.el-date-picker) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-date-picker__header) {
  color: #ffffff !important;
}

:deep(.el-date-picker__header button) {
  color: #ffffff !important;
}

:deep(.el-date-picker__header button:hover) {
  color: #00ffff !important;
}

:deep(.el-date-picker__content) {
  background-color: rgba(0, 48, 102, 0.8) !important;
}

:deep(.el-date-table th) {
  color: #ffffff !important;
}

:deep(.el-date-table td) {
  color: #ffffff !important;
}

:deep(.el-date-table td.available:hover) {
  color: #00ffff !important;
}

:deep(.el-date-table td.current:not(.disabled) span) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-date-table td.in-range div) {
  background-color: rgba(0, 255, 255, 0.1) !important;
}

:deep(.el-date-table td.start-date span),
:deep(.el-date-table td.end-date span) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-date-table td.today) {
  color: #00ffff !important;
}

:deep(.el-date-table td.disabled) {
  color: rgba(255, 255, 255, 0.3) !important;
}

:deep(.el-date-picker__footer) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-top: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-picker-panel__footer .el-button) {
  background: rgba(0, 255, 255, 0.2) !important;
  border-color: #00ffff !important;
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button:hover) {
  background: rgba(0, 255, 255, 0.3) !important;
  border-color: #00ffff !important;
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button--text) {
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button--text:hover) {
  color: #ffffff !important;
}

:deep(.el-range-input) {
  background: none !important;
  color: #ffffff !important;
}

:deep(.el-date-editor--daterange) {
  width: 21.875rem !important;
  height: 2.2rem;
  align-items: baseline;
  padding: 0 5px;
}
</style> 