import asyncio
import hashlib
import io
import json
import re
import aiohttp
import pandas as pd
import pdfplumber
from sqlalchemy import desc

from app.config import settings, upload_file_config
from app.core.task_queue import TaskQueueManager
from app.db.decorators import transactional
from sqlalchemy.orm import Session
from app.log.log_utils import LogUtils
from app.models.dataset_record import DataSetRecord, DataSetTextRecord
from fastapi import UploadFile, HTTPException
from docx import Document
from typing import List


class DatasetService:
    def __init__(self):
        super().__init__()
        # 文档转QA分析队列
        self.doc_to_qa = "doc_to_qa"
        # QA数据集获取模型思考过程队列
        self.qa_get_reasoning = "qa_get_reasoning"
        self.queue_manager = TaskQueueManager()
        self.async_init()

    async def async_init(self):
        # 异步初始化代码
        await self.queue_manager.register_queue(
            queue_name=self.doc_to_qa,
            processor=self._do_text_map_qa,
            max_size=1000,  # 队列容量
            concurrency=5  # 最大并发数
        )
        await self.queue_manager.register_queue(
            queue_name=self.qa_get_reasoning,
            processor=self._qa_get_reasoning,
            max_size=1000,  # 队列容量
            concurrency=5  # 最大并发数
        )

    async def examToQA(self, file: UploadFile):
        await self.pre_dify(file,self.exam_split_file,settings.STJ_API_KEY)

    # @transactional
    # async def examToQA(self,file: UploadFile,db:Session) :
    #     try:
    #         file_extension = file.filename.split('.')[-1].lower()
    #         # 后续补齐，计算文件md5值，若数据库已存在，则不处理该文件
    #         content = await file.read()
    #         md5_id = hashlib.md5(content).hexdigest()
    #
    #         texts: List[str] = await self.exam_split_file(content, file_extension)
    #
    #         if texts:
    #             # 调用dify工作流，利用大模型将试题集处理成QA
    #             textSum = len(texts)
    #             LogUtils.info(f"试题集转换dify 流程开始,处理文件是：{file.filename}，文件分割共：{textSum}块")
    #             for i, text in enumerate(texts):
    #                 LogUtils.info(f"试题集转换dify,第: {i} 块，共：{textSum}块")
    #
    #                 task_data = {
    #                     "text": text
    #                 }
    #                 await self.queue_manager.add_task(self.doc_to_qa, task_data)
    #
    #                 # await self._dify_dataset_process(text)
    #             LogUtils.info(f"试题集转QA调用dify流程开始")
    #     except Exception as e:
    #         LogUtils.error(f"试题集转QA调用dify流程报错: {str(e)}")

    async def _do_text_map_qa(self, task_data: dict):
        text = task_data['text']
        apiKey = task_data['apiKey']
        await self._dify_dataset_process(text,apiKey)

    async def _qa_get_reasoning(self, task_data: dict):
        text = task_data['text']
        apiKey = task_data['apiKey']
        await self._dify_dataset_process(text,apiKey)

    async def exam_split_file(self,content: bytes,file_extension:str) -> List[str]:
        text=  self.resolution_doc(content,file_extension)
        # 智能分割
        return DocumentProcessor.exam_intelligent_split(text)

    async def law_split_file(self,content: bytes,file_extension:str) -> List[str]:
        text=  self.resolution_doc(content,file_extension)
        # 智能分割
        return DocumentProcessor.law_intelligent_split(text)

    @staticmethod
    def resolution_doc(content: bytes,file_extension:str) -> str:
        # 分类型解析
        if file_extension in {'docx', 'doc'}:
            text = DocumentProcessor.parse_word(content, file_extension)
        elif file_extension == 'pdf':
            text = DocumentProcessor.parse_pdf(content)
        else:
            text = DocumentProcessor.parse_excel(content)
        return text

    @staticmethod
    async def _dify_dataset_process(text: str,apiKey:str):
        # 使用aiohttp进行异步HTTP请求
        timeout = aiohttp.ClientTimeout(total=1800)  # 设置30秒总超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            headers = {
                'Authorization': f'Bearer {apiKey}',
            }
            json_data = {
                'inputs': {'text': f'{text}'},
                'response_mode': 'blocking',
                'user': 'abc-123',
            }
            try:
                async with session.post(
                        f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                        headers=headers,
                        json=json_data,
                        timeout=timeout  # 为单个请求设置超时
                ) as response:
                    if response.status == 200:
                        # 获取响应数据
                        response_data = await response.json()
                        LogUtils.info(f"dify数据集返回结果是response_data：{response_data}")
            except asyncio.TimeoutError:
                LogUtils.error(f"试题集转QA API请求超时")

    async def lawToQA(self, file: UploadFile):
        await self.pre_dify(file,self.law_split_file,settings.FLWS_API_KEY)

    @transactional
    async def pre_dify(self, file: UploadFile,splitFunc,workflowApiKey:str, db: Session):
        try:
            file_extension = file.filename.split('.')[-1].lower()
            # 后续补齐，计算文件md5值，若数据库已存在，则不处理该文件
            content = await file.read()
            md5_id = hashlib.md5(content).hexdigest()

            texts: List[str] =await splitFunc(content, file_extension)

            # texts: List[str] = await self.law_split_file(content, file_extension)
            if texts:
                # 调用dify工作流，利用大模型将试题集处理成QA
                textSum = len(texts)
                LogUtils.info(f"QA转换dify 流程开始,处理文件是：{file.filename}，文件分割共：{textSum}块")
                for i, text in enumerate(texts):
                    LogUtils.info(f"QA转换dify,第: {i} 块，共：{textSum}块")

                    task_data = {
                        "text": text,
                        "apiKey": workflowApiKey
                    }
                    await self.queue_manager.add_task(self.doc_to_qa, task_data)

                    # await self._dify_dataset_process(text)
                LogUtils.info(f"转QA调用dify流程开始")
        except Exception as e:
            LogUtils.error(f"转QA调用dify流程报错: {str(e)}")

    @transactional
    async def getSplitText(self, md5: str, db: Session = None) -> list[str]:
        records = db.query(DataSetTextRecord).filter(DataSetTextRecord.md5id == md5).all()
        # 提取 text 字段
        return [str(doc.text) for doc in records if doc.text is not None]

    @transactional
    async def update_dataset_result(self, datalist: list[dict], db: Session = None):
        if not datalist:
            LogUtils.info(f"无QA数据集")
            return
        datasetsDB=[]
        LogUtils.info(f"QA数据集开始入库")
        try :
            for item in datalist:
                if item.get('question') and item.get('answer'):
                    dataset = DataSetRecord(question=item['question'],
                                   answer=item['answer'])
                    datasetsDB.append(dataset)
            db.add_all(datasetsDB)
            db.commit()
            LogUtils.info(f"QA数据集入库成功")
        except Exception as e:
            LogUtils.error(f"QA数据集入库报错: {str(e)}")

    @transactional
    async def getReasoning(self,db: Session = None):
        ## 查询reasoning_content为空的数据
        limitNum=10
        records = (db.query(DataSetRecord)
                   .filter(DataSetRecord.reasoning_content == None)
                   .order_by(desc(DataSetRecord.create_time))
                   .limit(limitNum).all()
                   )
        for i, record in enumerate(records):
            LogUtils.info(f"QA获取Reasoning调用dify,第: {i} 条，共：{limitNum}块")
            json_data={
                "id": str(record.id),
                "question": record.question,
                "answer": record.answer,
            }
            task_data = {
                "text": json.dumps(json_data, ensure_ascii=False),
                "apiKey": settings.REASON_API_KEY
            }
            LogUtils.info(f"需要生成的QA是:{task_data}")
            await self.queue_manager.add_task(self.qa_get_reasoning, task_data)
        LogUtils.info(f"QA获取Reasoning调用dify流程开始")


    @transactional
    async def update_dataset_reasoning(self, data: dict, db: Session = None):
        if not data:
            LogUtils.info(f"无reasoning")
            return

        id:str = data['id']
        reasoning_content:str = data['reasoning']
        cot:str = data['cot']

        record = db.query(DataSetRecord).filter(DataSetRecord.id == id).first()
        record.reasoning_content = reasoning_content
        record.cot=cot
        db.commit()
        LogUtils.info(f"QA数据集reasoning入库成功")


class DocumentProcessor :

    @staticmethod
    def parse_word(content: bytes, ext: str) -> str:
        """解析Word文档"""
        try:
            if ext == 'docx':
                doc = Document(io.BytesIO(content))
                return "\n".join([para.text for para in doc.paragraphs])
            else:
                # 处理旧版doc格式需要转换
                raise Exception(400, "暂不支持doc格式，请转换为docx")
        except Exception as e:
            raise Exception(500, f"Word解析失败: {str(e)}")

    @staticmethod
    def parse_pdf(content: bytes) -> str:
        """解析PDF文档"""
        try:
            text = []
            with pdfplumber.open(io.BytesIO(content)) as pdf:
                for page in pdf.pages:
                    text.append(page.extract_text())
            return "\n".join(text)
        except Exception as e:
            raise HTTPException(500, f"PDF解析失败: {str(e)}")

    @staticmethod
    def parse_excel(content: bytes) -> str:
        """解析Excel文档"""
        try:
            df = pd.read_excel(io.BytesIO(content), engine='openpyxl')
            return "\n".join(df.apply(lambda row: ' '.join(row.astype(str)), axis=1))
        except Exception as e:
            raise HTTPException(500, f"Excel解析失败: {str(e)}")

    @staticmethod
    def exam_intelligent_split(text: str) -> List[str]:
        """智能分割算法"""
        segments = []
        current_segment = []
        current_length = 0

        # 按题目分割的匹配模式
        question_pattern = re.compile(r'(\n\d+[\.．、]|\n[一二三四五六七八九十]+[\.．、])')

        for part in re.split(question_pattern, text):
            if re.match(question_pattern, part):
                # 遇到新题目时处理
                if current_length + len(part) > upload_file_config["max_chunk_size"]:
                    segments.append("\n".join(current_segment))
                    current_segment = [part.strip()]
                    current_length = len(part)
                else:
                    current_segment.append(part.strip())
                    current_length += len(part)
            else:
                # 处理题目内容
                sentences = re.split(r'(?<=[。！？；])', part)
                for sentence in sentences:
                    sentence = sentence.strip()
                    if not sentence:
                        continue
                    if current_length + len(sentence) > upload_file_config["max_chunk_size"]:
                        segments.append("\n".join(current_segment))
                        current_segment = [sentence]
                        current_length = len(sentence)
                    else:
                        current_segment.append(sentence)
                        current_length += len(sentence)
        # 添加最后一个片段
        if current_segment:
            segments.append("\n".join(current_segment))
        return segments

    @staticmethod
    def law_intelligent_split(text: str) -> List[str]:
        splitter = LegalDocumentSplitter(max_length=upload_file_config["max_chunk_size"])
        return splitter.split_document(text)


_instance = None
async def get_dataset_service() -> DatasetService:
    global _instance
    if not _instance:
        _instance = DatasetService()
        await _instance.async_init()
    return _instance

class LegalDocumentSplitter:
    def __init__(self, max_length: int = 4096):
        self.max_length = max_length
        # 优化正则匹配模式
        self.structure_pattern = re.compile(
            r'(?<=\n)(第[一二三四五六七八九十百]+章\s*[^\n]+)'  # 章节识别
            r'|'
            r'(第[一二三四五六七八九十百]+条\s*[^\n]+(?:\n(?!第[一二三四九十百]+章).+)*)'  # 条款识别
        )
        self.sub_clause_pattern = re.compile(
            r'（[一二三四五六七八九十]+）[^）]+?(?=(\n（[一二三四五六七八九十]+）)|$|[\n\r])'
        )

    def _split_into_units(self, text: str) -> List[dict]:
        """核心分割逻辑"""
        units = []
        current_unit = {"type": None, "content": []}

        for line in text.split('\n'):
            line = line.strip()
            if not line:
                continue

            # 检测章节头
            if re.match(r'^第[一二三四五六七八九十百]+章', line):
                if current_unit["content"]:
                    units.append(current_unit)
                    current_unit = {"type": "chapter", "content": [line]}
                else:
                    current_unit["type"] = "chapter"
                    current_unit["content"].append(line)

            # 检测条款头
            elif re.match(r'^第[一二三四五六七八九十百]+条', line):
                if current_unit["type"] == "clause":
                    units.append(current_unit)
                current_unit = {"type": "clause", "content": [line]}

            else:
                if current_unit["type"] is not None:
                    current_unit["content"].append(line)

        if current_unit["content"]:
            units.append(current_unit)

        return units

    def _merge_units(self, units: List[dict]) -> List[str]:
        """智能合并单元"""
        chunks = []
        current_chunk = []
        current_length = 0

        for unit in units:
            unit_text = '\n'.join(unit["content"])
            unit_size = len(unit_text.encode('utf-8'))
            # 章节作为独立块
            if unit["type"] == "chapter":
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                    current_chunk = []
                    current_length = 0
                chunks.append(unit_text)
                continue
            # 条款处理逻辑
            if unit["type"] == "clause":
                full_clause = unit_text
                # 长度检查
                if current_length + unit_size > self.max_length:
                    if current_chunk:
                        chunks.append('\n'.join(current_chunk))
                        current_chunk = [full_clause]
                        current_length = unit_size
                    else:
                        chunks.append(full_clause)
                else:
                    current_chunk.append(full_clause)
                    current_length += unit_size
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        return chunks

    def split_document(self, document: str) -> List[str]:
        """完整处理流程"""
        # 预处理
        document = re.sub(r'[\r\n]+', '\n', document).strip()
        # 结构分割
        units = self._split_into_units(document)
        # 合并处理
        return self._merge_units(units)




