import re
from typing import Tu<PERSON>, List, Dict

from sqlalchemy import text
from sqlalchemy.orm import Session

from app.log.log_utils import LogUtils


class Nl2sqlService:

    @staticmethod
    def parse_select_sql(sql: str) -> Tuple[List[str], List[str]]:
        """
        解析SELECT语句，提取列名和别名
        :param sql: SQL语句
        :return: (原始列名列表, 显示表头列表)
        """
        try:
            # 预处理SQL语句：统一空白字符
            sql = ' '.join(sql.split())

            # 提取SELECT和FROM之间的部分，支持多行
            pattern = r'SELECT\s+(.*?)\s+FROM'
            match = re.search(pattern, sql, re.IGNORECASE | re.DOTALL)
            if not match:
                LogUtils.error("无法解析SELECT语句")
                return [], []

            select_part = match.group(1)

            # 使用状态机解析字段
            columns = []
            current = ""
            parentheses_count = 0  # 跟踪括号嵌套
            in_quotes = False  # 是否在引号中
            quote_char = None  # 当前引号字符

            for char in select_part:
                # 处理引号
                if char in ["'", '"'] and (not in_quotes or quote_char == char):
                    in_quotes = not in_quotes
                    quote_char = char if in_quotes else None
                    current += char
                    continue

                # 在引号内的字符直接添加
                if in_quotes:
                    current += char
                    continue

                # 处理括号
                if char == '(':
                    parentheses_count += 1
                    current += char
                elif char == ')':
                    parentheses_count -= 1
                    current += char
                # 处理分隔符
                elif char == ',' and parentheses_count == 0:
                    if current.strip():
                        columns.append(current.strip())
                    current = ""
                else:
                    current += char

            # 添加最后一个字段
            if current.strip():
                columns.append(current.strip())

            # 解析每个字段的原始名和别名
            raw_columns = []
            display_headers = []

            for col in columns:
                # 处理AS别名（不区分大小写）
                parts = re.split(r'\s+AS\s+', col, flags=re.IGNORECASE)
                if len(parts) > 1:
                    raw_col = parts[0].strip()
                    alias = parts[-1].strip(' "\'\t\n\r')
                    raw_columns.append(raw_col)
                    display_headers.append(alias)
                else:
                    # 没有AS关键字，但可能有空格分隔的别名
                    # 对于复杂表达式，使用整个表达式作为原始列，最后一个词作为别名
                    parts = col.strip().split()
                    if len(parts) > 1 and not any(
                            p.upper() in ('SELECT', 'FROM', 'WHERE', 'GROUP', 'ORDER', 'HAVING') for p in parts):
                        raw_columns.append(col.strip())
                        # 如果最后一个词不是函数调用的一部分，则用作别名
                        last_word = parts[-1]
                        if not any(c in last_word for c in '()'):
                            display_headers.append(last_word.strip(' "\'\t\n\r'))
                        else:
                            # 如果是复杂表达式，使用整个表达式作为表头
                            display_headers.append(col.strip())
                    else:
                        raw_columns.append(col.strip())
                        display_headers.append(col.strip())

            return raw_columns, display_headers

        except Exception as e:
            LogUtils.error(f"解析SELECT语句失败: {str(e)}")
            return [], []

    @staticmethod
    def sql_execute(
            sql: str,
            db: Session,
    ) -> Dict | None:
        try:
            # . 检查是否为SELECT语句
            if not sql.strip().upper().startswith('SELECT'):
                LogUtils.error("非SELECT 查询语句")
                return None
            raw_columns, headers = Nl2sqlService.parse_select_sql(sql)
            if not headers:
                LogUtils.error("sql headers 解析失败")
                return None

            try:
                # 使用SQLAlchemy执行原生SQL
                result = db.execute(text(sql))
                # 获取所有结果
                records = result.fetchall()

                # 将每行记录转换为值列表
                rows = []
                for record in records:
                    # 按照headers的顺序提取值
                    row = []
                    for value in record:
                        row.append(value)
                    rows.append(row)

                return {
                    'header': headers,
                    'rows': rows
                }
            except Exception as e:
                LogUtils.error(f"SQL执行失败: {str(e)}")
                return None

        except Exception as e:
            LogUtils.error(f"自然语言转SQL失败: {str(e)}")
            return None
