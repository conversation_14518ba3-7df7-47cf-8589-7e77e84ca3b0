<template>
  <div class="register-container">
    <!-- 添加粒子效果容器 -->
    <div class="particle-container">
      <canvas class="rain"></canvas>
      <canvas class="dashed"></canvas>
    </div>

    <div class="title">
      <img src="@/assets/images/gongan.png" alt="公安Logo" class="logo">
      <h1>法制监督智能助手</h1>
      <div class="subtitle">· DeepSeek 智能辅助平台 ·</div>
    </div>

    <div class="register-box">
      <div class="bg_bottom">
        <div class="register-box-header">
          <img class="register-box-header-left" src="@/assets/images/svgs/login_register_head_1.svg" alt="">
          <div class="register-box-header-context">注册账号</div>
          <img class="register-box-header-right" src="@/assets/images/svgs/login_register_head_1.svg" alt="">
        </div>
        <el-form ref="registerForm" :model="registerForm" :rules="registerRules" label-width="0" class="register-form">
          <el-form-item prop="username">
            <el-input v-model="registerForm.username" placeholder="姓名" clearable class="custom-input">
              <img slot="prefix" src="@/assets/images/icons/icon-register_name.svg" alt="" />
            </el-input>
          </el-form-item>
          <el-form-item prop="id_card_number">
            <el-input v-model="registerForm.id_card_number" placeholder="身份证号" clearable class="custom-input">
              <img slot="prefix" src="@/assets/images/icons/icon-register_id.svg" alt="" />
            </el-input>
          </el-form-item>
          <el-form-item prop="police_number">
            <el-input v-model="registerForm.police_number" placeholder="警号" clearable class="custom-input" @blur="handlePoliceNumberBlur">
              <img slot="prefix" src="@/assets/images/icons/icon-register_jh.svg" alt="" />
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <PassInput v-model="registerForm.password" placeholder="密码" clearable />
          </el-form-item>
          <el-form-item prop="confirmPassword">
            <PassInput v-model="registerForm.confirmPassword" placeholder="确认密码" clearable />
          </el-form-item>
          <el-form-item prop="police_unit">
            <el-input v-model="registerForm.police_unit" placeholder="所属单位（输入警号后自动带入）" readonly class="custom-input">
              <img slot="prefix" src="@/assets/images/icons/icon-register_ssdw.svg" alt="" />
            </el-input>
          </el-form-item>
          <el-form-item prop="phone_number">
            <el-input v-model="registerForm.phone_number" placeholder="手机号码" clearable class="custom-input">
              <img slot="prefix" src="@/assets/images/icons/icon-register_phone.svg" alt="" />
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button :loading="loading" type="primary" class="register-button" @click="handleRegister">
              注册
            </el-button>
          </el-form-item>
          <div class="register-options">
            <div class="register-options-left">
              <span class="option-text">已有账号？</span>
              <router-link to="/login" class="login-link">立即登录</router-link>
            </div>
            <div class="tutorial-container">
              <a :href="tutorialUrl" class="tutorial-link" target="_blank">
                <span>使用教程&nbsp;&nbsp;</span>
                点击下载
              </a>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { register } from '@/api/auth'
import PassInput from '@/components/PassInput.vue'
import { getOrgByPoliceCode } from '@/api/auth'

export default {
  name: 'Register',
  components: { PassInput },
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        id_card_number: '',
        police_number: '',
        police_unit: '',
        phone_number: '',
        org_code: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, message: '姓名长度至少为2个字符', trigger: 'blur' },
          { max: 20, message: '姓名长度不能超过20个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' },
          { max: 12, message: '密码长度至长为12个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        id_card_number: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        police_number: [
          { required: true, message: '请输入警号', trigger: 'blur' },
          { pattern: /^\d{4,10}$/, message: '警号格式不正确', trigger: 'blur' }
        ],
        // police_unit: [
        //   { required: true, message: '请输入所属单位', trigger: 'blur' }
        // ],
        phone_number: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      loading: false,
      rainCtx: null,
      dashedCtx: null,
      animationFrame: null
    }
  },
  computed: {
    tutorialUrl() {
      return `${window.location.origin}/downloads/法制智能监督助手操作手册.doc`
    }
  },
  mounted() {
    this.initParticleEffect()
  },
  beforeDestroy() {
    this.stopParticleEffect()
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            const { username, password, id_card_number, police_number, police_unit, phone_number, org_code } = this.registerForm
            const res = await register({
              username,
              password,
              id_card_number,
              police_number,
              police_unit,
              phone_number,
              org_code
            })
            if (res.code === 200) {
              this.$store.commit('SET_TOKEN', res.data.access_token)
              this.$store.commit('SET_USER', { police_number, username, police_unit, phone_number })
              this.$message({
                message: '注册成功，请登录',
                type: 'success'
              })
              this.$router.push('/')
            } else {
              this.$message.error(res.message || '注册失败')
            }
          } catch (error) {
            console.error(error)
          } finally {
            this.loading = false
          }
        }
      })
    },
    async handlePoliceNumberBlur() {
      if (!this.registerForm.police_number) return
      
      try {
        const res = await getOrgByPoliceCode(this.registerForm.police_number)
        if (res.code === 200 && res.data) {
          this.registerForm.police_unit = res.data.police_unit
          this.registerForm.org_code = res.data.org_code
        } else {
          this.$message.error('获取单位信息失败')
        }
      } catch (error) {
        console.error('获取单位信息失败:', error)
        // this.$message.error('获取单位信息失败')
      }
    },
    initParticleEffect() {
      const rainCanvas = document.querySelector('.rain')
      const dashedCanvas = document.querySelector('.dashed')

      if (!rainCanvas || !dashedCanvas) return

      const rainCtx = rainCanvas.getContext('2d')
      // const dashedCtx = dashedCanvas.getContext('2d')

      // 设置画布大小
      const resizeCanvas = () => {
        const container = document.querySelector('.particle-container')
        if (!container) return

        const rect = container.getBoundingClientRect()
        rainCanvas.width = rect.width
        rainCanvas.height = rect.height
        dashedCanvas.width = rect.width
        dashedCanvas.height = rect.height
      }

      resizeCanvas()
      window.addEventListener('resize', resizeCanvas)

      // 粒子效果
      class Particle {
        constructor() {
          this.init()
        }

        init() {
          this.x = Math.random() * rainCanvas.width
          this.y = rainCanvas.height // 从底部开始
          this.vy = -(Math.random() * 1 + 1) // 向上移动的速度
          this.size = Math.random() * 2 + 1 // 随机大小
          this.alpha = Math.random() * 0.5 + 0.5 // 随机透明度
          this.color = this.getRandomColor()
        }

        getRandomColor() {
          const colors = [
            { r: 0, g: 255, b: 255 },  // 青色
            { r: 0, g: 195, b: 255 },  // 浅蓝色
            { r: 20, g: 120, b: 156 }  // 深蓝色
          ]
          const color = colors[Math.floor(Math.random() * colors.length)]
          return `rgba(${color.r}, ${color.g}, ${color.b}, ${this.alpha})`
        }

        draw() {
          rainCtx.beginPath()
          const gradient = rainCtx.createLinearGradient(this.x, this.y, this.x, this.y - this.size * 10)
          gradient.addColorStop(0, this.color)
          gradient.addColorStop(1, 'rgba(0, 255, 255, 0)')
          rainCtx.fillStyle = gradient
          rainCtx.fillRect(this.x, this.y, this.size, -this.size * 10) // 向上延伸的光效
          rainCtx.closePath()

          this.update()
        }

        update() {
          this.y += this.vy

          // 如果粒子超出顶部，重新初始化
          if (this.y < 0) {
            this.init()
          }
        }
      }

      // 初始化粒子
      const particles = []
      const particleCount = 80 // 粒子数量

      let timer = 0;
      for (let i = 0; i < particleCount; i++) {
        timer = setTimeout(() => {
          const particle = new Particle()
          particles.push(particle)
          clearTimeout(timer)
          timer = 0;
        }, i * 100) // 错开创建时间
      }

      // 虚线效果
      const drawDashedLines = () => {
        const w = dashedCanvas.width
        const h = dashedCanvas.height

        dashedCtx.lineWidth = 2
        dashedCtx.setLineDash([4, 4])
        dashedCtx.shadowOffsetX = 0
        dashedCtx.shadowOffsetY = 0
        dashedCtx.shadowColor = '#00ffff'
        dashedCtx.shadowBlur = 10

        // 第一条曲线
        dashedCtx.beginPath()
        let grd = dashedCtx.createLinearGradient(w / 11 * 2, h / 3, w / 5 * 2, h)
        grd.addColorStop(0, "#00ffff")
        grd.addColorStop(1, "#065261")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2, h)
        dashedCtx.quadraticCurveTo(w / 5, h / 6 * 5, w / 11 * 2, h / 3)
        dashedCtx.stroke()

        // 第二条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 11 * 3.3, h / 2, w / 3 * 1.1, h / 6 * 5)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 3 * 1.1, h / 6 * 5)
        dashedCtx.quadraticCurveTo(w / 5 * 1.5, h / 6 * 4.2, w / 11 * 3.3, h / 2)
        dashedCtx.stroke()

        // 第三条曲线
        dashedCtx.beginPath()
        grd = dashedCtx.createLinearGradient(w / 3 * 1.4, h / 5, w / 5 * 2, h / 2)
        grd.addColorStop(0, "#00c3ff")
        grd.addColorStop(1, "#0a4f6c")
        dashedCtx.strokeStyle = grd
        dashedCtx.moveTo(w / 5 * 2, h / 2)
        dashedCtx.quadraticCurveTo(w / 3 * 1.2, h / 4 * 1.4, w / 3 * 1.4, h / 5)
        dashedCtx.stroke()
      }

      let timerAnimat = 0;
      // 动画循环
      const animate = () => {
        rainCtx.clearRect(0, 0, rainCanvas.width, rainCanvas.height)
        // dashedCtx.clearRect(0, 0, dashedCanvas.width, dashedCanvas.height)

        // 绘制粒子
        particles.forEach(particle => particle.draw())

        // 绘制虚线
        // drawDashedLines()
        this.animationFrame = requestAnimationFrame(animate)
      }

      animate()
    },
    stopParticleEffect() {
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
      window.removeEventListener('resize', this.resizeCanvas)
    }
  }
}
</script>

<style lang="less" scoped>
.register-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 20px 0;
  /* 添加上下内边距 */
  flex-direction: column;
}

.particle-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  pointer-events: none;
}

.dashed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  pointer-events: none;
}

.register-box {
  width: 500px;
  background: #07182799;
  border: 1px solid #04d8be9e;
  border-radius: 2px;
  /* box-shadow: 0 0 30px rgba(0, 255, 255, 0.2),
              inset 0 0 40px rgba(0, 150, 255, 0.08); */
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;

  .bg_bottom {
    width: 100%;
    padding: 40px;
    background-image: url('@/assets/images/svgs/box_bg_bottom.svg');
    background-size: 99%;
    background-position: 5px 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;

    &::before {
      content: '';
      position: absolute;
      right: -1px;
      bottom: 0;
      width: 1px;
      height: 22px;
      background-color: #071827d2;
    }
    
    &::after {
      content: '';
      position: absolute;
      right: 0;
      bottom: -1px;
      width: 22px;
      height: 1px;
      background-color: #071827d2;
    }
  }
}

.register-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: -2px;
  right: -2px;
  bottom: 0;
  border-radius: 3px;
  padding: 3px;
  background: linear-gradient(180deg, rgba(0, 255, 255, 0) 0%, #00FFFF 32%, rgba(0, 255, 255, 0) 55%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.register-box::after {
  content: '';
  position: absolute;
  top: -1.5px;
  left: 0;
  right: 0;
  /* bottom: 0; */
  border-radius: 3px;
  padding: 1.5px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0) 0%, #00FFFF 50%, rgba(0, 255, 255, 0) 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.register-box-header {
  width: 100%;
  height: 40px;
  z-index: 5;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.register-box-header> :nth-child(1) {
  margin-left: -28px;
}

.register-box-header> :nth-child(2) {
  height: 100%;
  flex: 1;
  background: url('@/assets/images/svgs/login_register_head_2.svg') repeat center;
  text-align: center;
  font-size: 16px;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  padding: 0 28px;
  line-height: 230%;
}

.register-box-header img {
  height: 100%;
}

.register-box-header> :nth-child(3) {
  transform: scaleX(-1);
  /* 水平镜像翻转 */
  margin-right: -28px;
}

.title {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
  /* filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5)); */
}

.title h1 {
  font-weight: 600;
  font-size: 28px;
  letter-spacing: 3px;
  margin-bottom: 3px;
  background: #00FFFF;
  background-image: linear-gradient(180deg, #10E7EF 0%, #1299EF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  /* 防止背景溢出文字范围 */
}

.subtitle {
  color: rgba(0, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 5px;
  letter-spacing: 1px;
}

.register-form {
  margin-top: 40px;
  &::after {
    content: '';
    position: absolute;
    bottom: 10px;
    right: -5px;
    width: 33px;
    height: 1px;
    background-color: #04d8be9e;
    transform: rotate(-45deg);
  }
}

:deep(.register-form .el-form-item) {
  margin-bottom: 20px !important;
}

:deep(.register-form .el-form-item__content) {
  line-height: 30px !important;
}

:deep(.custom-input .el-input__inner) {
  background: #09354D !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  color: #fff !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  padding-left: 48px !important;
  box-shadow: inset 0 0 20px rgba(0, 150, 255, 0.05) !important;
  transition: all 0.3s !important;
  height: 48px;
  line-height: 48px !important;
}

:deep(.custom-input .el-input__inner):focus {
  border-color: rgba(0, 255, 255, 0.6) !important;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3) !important;
}

:deep(.custom-input .el-input__icon) {
  color: rgba(0, 255, 255, 0.8) !important;
  font-size: 16px !important;
  line-height: 30px !important;
}

:deep(.custom-input .el-input__inner):-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  color: #fff !important;
  background-color: #09354D !important;
  font-size: 12px !important;
  transition: all 600s ease-in-out 0s !important;
}

:deep(.custom-input .el-input__prefix) {
  left: 12px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

:deep(.custom-input .el-input__prefix img) {
  height: 50%;
}

.register-button {
  width: 100%;
  margin-top: 15px;
  background: #00FFFF !important;
  color: #071827;
  border: none !important;
  height: 48px !important;
  font-size: 14px !important;
  letter-spacing: 2px !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border-radius: 4px !important;
}

.register-button:hover {
  background: linear-gradient(90deg, #00a2f8, #00d4ff) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4) !important;
  transform: translateY(-2px);
}

.register-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  transition: 0.5s;
}

.register-button:hover::before {
  left: 100%;
}

.register-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  font-size: 13px;
}

.tutorial-container {
  display: flex;
  justify-content: center;
}

.option-text {
  color: rgba(255, 255, 255, 0.7);
}

.login-link {
  color: #06C0C6;
  text-decoration: none;
  margin-left: 5px;
  transition: all 0.3s;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  padding: 0 5px;
}

.tutorial-link {
  color: #06C0C6;
  text-decoration: none;
  transition: all 0.3s;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.tutorial-link span {
  margin-right: 3px;
  color: #FFFFFFB3;
}

/* 自定义表单验证错误信息样式 */
.el-form-item__error {
  color: #f56c6c !important;
  text-shadow: 0 0 5px rgba(245, 108, 108, 0.3) !important;
}

/* 自定义滚动条样式 */
.register-box::-webkit-scrollbar {
  width: 6px;
}

.register-box::-webkit-scrollbar-track {
  background: rgba(0, 36, 89, 0.3);
  border-radius: 3px;
}

.register-box::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 3px;
}

.register-box::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}
</style>