#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的 cluster_cases_with_shared_entities 函数
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from app.services.scene_qz.scene_qz_service import SceneQzService
from app.log.log_utils import LogUtils


async def test_cluster_cases_with_shared_entities():
    """测试 cluster_cases_with_shared_entities 函数"""
    print("=" * 80)
    print("开始测试重构后的 cluster_cases_with_shared_entities 函数")
    print("=" * 80)
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 测试用例1：基本功能测试（无时间过滤）
        print("\n【测试用例1】基本功能测试（无时间过滤）")
        print("-" * 50)
        
        result1 = await SceneQzService.cluster_cases_with_shared_entities(
            page=1,
            page_size=5,
            db=db
        )
        
        print(f"返回结果总数: {result1['total']}")
        print(f"当前页: {result1['page']}")
        print(f"每页大小: {result1['page_size']}")
        print(f"返回条目数: {len(result1['items'])}")
        
        if result1['items']:
            print("\n前几个聚类结果:")
            for i, cluster in enumerate(result1['items'][:2]):
                print(f"\n聚类 {i+1}:")
                print(f"  案件类型: {cluster['case_type']}")
                print(f"  共享实体数量: {len(cluster['involved'])}")
                print(f"  案件数量: {len(cluster['cases'])}")
                
                print("  共享实体:")
                for person in cluster['involved']:
                    print(f"    - {person['name']} ({person.get('id_card', '无身份证')})")
                
                print("  案件列表:")
                for case in cluster['cases'][:2]:  # 只显示前2个案件
                    print(f"    - 案件: {case['case']}")
                    print(f"      时间: {case['police_time']}")
                    print(f"      共享实体角色: {len(case['roles'])}个")
                    print(f"      其他涉及人员: {len(case['other_involved'])}个")
        
        # 测试用例2：带时间过滤的测试
        print("\n\n【测试用例2】带时间过滤的测试")
        print("-" * 50)
        
        # 设置时间范围（最近30天）
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        
        start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
        end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"时间范围: {start_time_str} 到 {end_time_str}")
        
        result2 = await SceneQzService.cluster_cases_with_shared_entities(
            page=1,
            page_size=3,
            start_time=start_time_str,
            end_time=end_time_str,
            db=db
        )
        
        print(f"返回结果总数: {result2['total']}")
        print(f"返回条目数: {len(result2['items'])}")
        
        # 测试用例3：分页测试
        print("\n\n【测试用例3】分页测试")
        print("-" * 50)
        
        # 测试第2页
        result3 = await SceneQzService.cluster_cases_with_shared_entities(
            page=2,
            page_size=3,
            db=db
        )
        
        print(f"第2页结果总数: {result3['total']}")
        print(f"第2页条目数: {len(result3['items'])}")
        
        # 测试用例4：边界条件测试
        print("\n\n【测试用例4】边界条件测试")
        print("-" * 50)
        
        # 测试无效时间格式
        try:
            await SceneQzService.cluster_cases_with_shared_entities(
                page=1,
                page_size=5,
                start_time="invalid-time-format",
                db=db
            )
            print("错误：应该抛出时间格式异常")
        except ValueError as e:
            print(f"正确捕获时间格式异常: {e}")
        
        # 测试用例5：性能对比测试
        print("\n\n【测试用例5】性能测试")
        print("-" * 50)
        
        import time
        
        start_perf = time.time()
        result_perf = await SceneQzService.cluster_cases_with_shared_entities(
            page=1,
            page_size=10,
            db=db
        )
        end_perf = time.time()
        
        print(f"查询耗时: {end_perf - start_perf:.2f} 秒")
        print(f"返回结果数: {result_perf['total']}")
        
        print("\n" + "=" * 80)
        print("测试完成！重构后的函数运行正常")
        print("=" * 80)
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


async def compare_with_original():
    """对比原始函数和重构后函数的结果（如果需要）"""
    print("\n" + "=" * 80)
    print("对比测试（可选）")
    print("=" * 80)
    
    # 这里可以添加与原始函数的对比逻辑
    # 由于原始函数已被重构，这部分主要用于验证业务逻辑的一致性
    
    print("重构后的函数应该保持以下业务逻辑:")
    print("1. 找到至少出现在两个案件中的实体")
    print("2. 智能聚类同名但身份证不同的实体")
    print("3. 筛选包含至少2个共享实体的案件")
    print("4. 按案件类型和共享实体组合进行聚类")
    print("5. 返回分页结果")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_cluster_cases_with_shared_entities())
    asyncio.run(compare_with_original())
