<template>
  <div class="option-box">
    <!-- <el-tooltip content="新增" placement="top">
      <el-button type="text" @click="handleAdd">
        <i class="el-icon-plus"></i>
      </el-button>
    </el-tooltip> -->
    <el-tooltip content="编辑" placement="top">
      <el-button type="text" @click="handleEdit">
        <i class="el-icon-edit"></i>
      </el-button>
    </el-tooltip>
    <!-- <el-tooltip content="上移" placement="top">
      <el-button type="text" @click="handleUp">
        <i class="el-icon-arrow-up"></i>
      </el-button>
    </el-tooltip>
    <el-tooltip content="下移" placement="top">
      <el-button type="text" @click="handleDown">
        <i class="el-icon-arrow-down"></i>
      </el-button>
    </el-tooltip> -->
    <el-tooltip content="删除" placement="top">
      <el-button type="text" @click="handleDelete">
        <i class="el-icon-delete"></i>
      </el-button>
    </el-tooltip>
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      custom-class="abnormal-dialog screen-dialog"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="24" v-if="form.parent_id !== 0">
            <el-form-item label="上级部门" prop="parent_org_code">
              <treeselect
                v-model="form.parent_org_code"
                :options="deptOptions"
                :normalizer="normalizer"
                placeholder="选择上级部门"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="部门名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" v-if="form.parent_id !== 0">
            <el-form-item label="部门角色" prop="role_ids">
              <el-select
                v-model="form.role_ids"
                :options="roleOptions"
                placeholder="选择部门角色,支持多选"
                style="width: 100%"
                multiple
              >
                <el-option
                  v-for="item in roleOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                controls-position="right"
                :min="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="子部门数据可见" prop="can_sub_query">
              <el-switch v-model="form.can_sub_query" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="ensure-btn" @click="submitForm">确 定</el-button>
        <el-button type="primary" class="cancel-btn" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {
  getDepartment,
  addDepartment,
  getRole,
  updateDepartment,
  deleteDepartment,
  getDepartmentDetail,
} from "@/api/department";
export default {
  name: "OptionBox",
  props: {
    currentDepartment: {
      type: Object,
      required: true,
    },
  },
  components: {
    Treeselect,
  },
  data() {
    return {
      deptOptions: [],
      // 部门树选项
      deptOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        id: undefined,
        parent_org_code: undefined,
        name: "",
        sort: 1,
        role: [],
        can_sub_query: true,
      },
      // 表单校验
      rules: {
        parent_org_code: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
        name: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
        sort: [{ required: true, message: "显示排序不能为空", trigger: "blur" }],
        role_ids: [{ required: true, message: "请选择部门角色", trigger: "blur" }],
      },
      roleOptions: [],
      a: "",
    };
  },
  mounted() {},
  watch: {
    open(newValue) {
      if (newValue) {
        this.getDeptTree();
        this.getRoleOptions();
      }
    },
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      getDepartment().then(response => {
        if (response.code === 200) {
          this.deptOptions = response.data;
        }
      });
    },
    getRoleOptions() {
      getRole({
        name: "",
        status: "1",
        page_size: 999,
        page: 1,
      })
        .then(res => {
          if (res.code === 200 && res.data) {
            this.roleOptions = res.data.items || [];
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.org_code,
        label: node.name,
        children: node.children,
      };
    },
    resetForm(refName) {
      // 表单重置
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        parent_org_code: undefined,
        name: "",
        sort: 1,
        role_ids: [],
        can_sub_query: true,
      };
      this.resetForm("form");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const params = {
            ...this.form,
            can_sub_query: this.form.can_sub_query === true ? 1 : 2,
          };
          console.log(params);
          if (this.form.id != undefined) {
            updateDepartment(params)
              .then(response => {
                this.$message.success("修改成功");
                this.open = false;
                this.$emit("updateCallback");
              })
              .catch(err => {
                this.$message.error("修改失败");
              });
          } else {
            addDepartment(params)
              .then(response => {
                this.$message.success("新增成功");
                this.open = false;
                this.$emit("addCallback");
              })
              .catch(err => {
                this.$message.error("新增失败");
              });
          }
        }
      });
    },
    handleAdd() {
      // 这里可以弹窗或跳转等
      this.reset();
      if (this.currentDepartment.id) {
        this.form.parent_org_code = this.currentDepartment.org_code;
        this.form.status = true;
      }
      this.open = true;
      this.title = "添加部门";
      console.log(this.currentDepartment);
    },
    handleEdit() {
      // 编辑逻辑
      this.reset();
      getDepartmentDetail(this.currentDepartment.id).then(res => {
        if (res.code === 200 && res.data) {
          this.form = {
            ...res.data.org_info,
            can_sub_query: res.data.org_info.can_sub_query === 1 ? true : false,
          };
          console.log(this.form);
          this.open = true;
          this.title = "编辑部门";
        }
      });
      this.open = true;
      this.title = "编辑部门";
      // console.log(this.form);
      // 这里可以弹窗或跳转等
    },
    handleUp() {
      // 上移逻辑
      this.$message.success(`上移，depId: ${this.currentDepartment.id}`);
      // 这里可以调用接口等
    },
    handleDown() {
      // 下移逻辑
      this.$message.success(`下移，depId: ${this.currentDepartment.id}`);
      // 这里可以调用接口等
    },
    handleDelete() {
      // 删除逻辑
      this.$confirm(`确定要删除${this.currentDepartment.name}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 这里可以调用删除接口
          deleteDepartment(this.currentDepartment.id).then(res => {
            if (res.code === 200) {
              this.$message.success("删除成功");
              this.$emit("deleteCallback");
            }
          });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
  },
};
</script>

<style scoped lang="less">
@import url("./index.less");
</style>
