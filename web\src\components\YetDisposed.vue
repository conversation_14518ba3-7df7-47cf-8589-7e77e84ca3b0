<template>
  <div class="outer-container">
    <div class="search-section">
      <el-form :inline="true" :model="searchForm" class="common-search search-form" label-width="140px">
        <div class="search-left">
          <el-form-item label="案件编号">
            <el-input v-model="searchForm.ajbh" placeholder="请输入案件编号"></el-input>
          </el-form-item>
          <el-form-item label="物品名称">
            <el-input v-model="searchForm.cwmc" placeholder="请输入物品名称"></el-input>
          </el-form-item>
          <el-form-item label="主办单位">
            <el-input v-model="searchForm.djdwmc" placeholder="请输入主办单位"></el-input>
          </el-form-item>
          <el-form-item label="措施批准时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyyMMdd"
              :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="search-right">
          <el-form-item >
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
          </div>
          <!-- <el-upload
              action="1233122312"
              :http-request="handleUpload"
              :show-file-list="false"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls"
            >
              <el-button type="primary">
                <i class="el-icon-upload2"></i>
                <span>导入数据</span>
              </el-button>
            </el-upload> -->

          <!-- <el-button type="primary" size="small" :loading="analysisLoading" @click="handleSync">
            <i class="el-icon-refresh"></i>
            生成预警
          </el-button> -->
      </el-form>
    </div>

    <div class="table-container">
      <el-table :data="tableData" stripe style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
        v-loading="loading">
        <el-table-column type="expand" width="28">
          <template slot-scope="props">
            <div class="detail-container">
              <div class="detail-header">
                  <span class="detail-title">详细信息</span>
                </div>
              <div class="detail-content">
                <div class="detail-section">
                  <div class="section-title">扣押财物记录</div>
                  <div class="detail-grid">
                    <div class="detail-item">
                      <span class="item-label">案件编号：</span>
                      <span class="item-value">{{ props.row.case_code }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">物品名称：</span>
                      <span class="item-value">{{ props.row.measure_object_name }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">案件名称：</span>
                      <span class="item-value">{{ props.row.case_name }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">主办单位：</span>
                      <span class="item-value">{{ props.row.handle_org_code }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">主办人：</span>
                      <span class="item-value">{{ props.row.hoster_name }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">处理措施：</span>
                      <span class="item-value">{{ props.row.measure_type }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">措施批准时间：</span>
                      <span class="item-value">
                        {{ formatDateTime(props.row, null, props.row.mod_time) }}
                      </span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">扣押财物数量：</span>
                      <span class="item-value">{{ props.row.seized_quantity }}</span>
                    </div>
                  </div>
                </div>
                <div class="detail-section">
                  <div class="section-title">入库涉案财物记录</div>
                  <div class="detail-grid">
                    <div class="detail-item">
                      <span class="item-label">案件编号：</span>
                      <span class="item-value">{{ props.row.case_code }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">物品名称：</span>
                      <span class="item-value">{{ props.row.measure_object_name }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">案件名称：</span>
                      <span class="item-value">{{ props.row.case_name }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">登记入库数量：</span>
                      <span class="item-value">{{ props.row.registration_quantity }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">登记入库时间：</span>
                      <span class="item-value">
                        {{ formatShortDate(props.row.registration_time) }}
                      </span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">操作时间：</span>
                      <span class="item-value">
                        {{ formatDateTime(props.row, null, props.row.operation_time) }}
                      </span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">入库保管期限：</span>
                      <span class="item-value">{{ props.row.storage_period }}天</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">保管到期时间：</span>
                      <span class="item-value">
                        {{ formatShortDate(props.row.storage_expire_time) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="detail-section">
                  <div class="section-title">对比分析结果</div>
                  <div class="detail-grid">
                    <div class="detail-item">
                      <span class="item-label">分析结论：</span>
                      <span class="item-value">{{ props.row.analysis_result }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">超期时间：</span>
                      <span class="item-value">{{ props.row.overdue_time }}天</span>
                    </div>
                    <div class="detail-item">
                      <span class="item-label">当前状态：</span>
                      <span class="item-value">{{ props.row.current_status }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="index" label="序号" width="70">
        </el-table-column>
        <el-table-column prop="case_code" label="案件编号" show-overflow-tooltip min-width="200"></el-table-column>
        <el-table-column prop="case_name" label="案件名称" show-overflow-tooltip min-width="200"></el-table-column>
        <el-table-column prop="measure_object_name" label="物品名称" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="handle_org_code" label="主办单位" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="hoster_name" label="主办人" width="180" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="measure_type" label="处理措施" min-width="200" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="mod_time" label="措施批准时间" width="200" align="center" show-overflow-tooltip
          :formatter="formatDate"></el-table-column>
        <el-table-column prop="analysis_status" label="分析状态" width="120" align="center">
          <template slot-scope="scope">
              <StatusView :statusOption="formatAnalysisStatus(scope.row)"></StatusView>
          </template>
        </el-table-column>
        <el-table-column prop="analysis_result" label="分析结论" min-width="200" align="center"
          show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="total" background></el-pagination>
      </div>
    </div>
  </div>

</template>

<script>
import { getYetDisposedList, getYetDisposedAnalysis, uploadDisposedExcl } from "@/api/amount";
import StatusView from "@/components/StatusView.vue";
import { TableRowHeightAttributes } from "docx";
export default {
  name: "YetDisposed",
  components: {
    StatusView
  },
  data() {
    return {
      searchForm: {
        ajbh: "",
        cwmc: "",
        djdwmc: "",
        dateRange: []
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
      analysisLoading: false,
      total: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      analysisFormatMap: {
        0: { text: "未分析", color: "#E6A23C", iconType: 'clock', bgColor: "rgba(245,108,108,0.1)" },
        1: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        2: { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
        4: { text: "无需分析", color: "#409EFF", iconType: 'refresh', bgColor: "rgba(245,108,108,0.1)" },
      },
    };
  },
  created() {
    this.loadTableData();
    // 使用实例属性保存定时器ID，便于后续清除
  },
  beforeDestroy() {

  },
  methods: {
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.analysis_status];
      return status || {};
    },
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        if (this.searchForm.ajbh) {
          params.ajbh = this.searchForm.ajbh;
        }
        if (this.searchForm.cwmc) {
          params.cwmc = this.searchForm.cwmc;
        }
        if (this.searchForm.djdwmc) {
          params.djdwmc = this.searchForm.djdwmc;
        }
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.start_date = this.searchForm.dateRange[0];
          params.end_date = this.searchForm.dateRange[1];
        }

        const res = await getYetDisposedList(params);
        if (res.code === 200) {
          this.tableData = res.data.items || [];
          this.total = res.data.total || 0;
        } else {
          throw new Error(res.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;
      }
    },
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#ffffffcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    async handleSearch() {
      this.currentPage = 1;
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        ajbh: "",
        cwmc: "",
        djdwmc: "",
        dateRange: []
      };
      this.handleSearch();
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },
    beforeUpload(file) {
      const isExcel =
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("只能上传 Excel 文件!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    async handleUpload(options) {
      try {
        const file = options.file;
        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              扣押涉案款处置情况数据，确认导入此excel文件数据吗？
            </div>
          </div>
        `,
          "导入确认",
          {
            confirmButtonText: "确定导入",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const formData = new FormData();
          formData.append("file", file);

          const res = await uploadDisposedExcl(formData);
          if (res.code === 200) {
            this.$message.success("文件上传成功");
            await this.loadTableData();
          } else {
            throw new Error(res.message || "文件上传失败");
          }
        } else {
          this.$message.info("已取消导入");
        }
      } catch (error) {

      } finally {
        this.loading = false;
      }
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      if (!/^\d{14}$/.test(cellValue)) {
        return cellValue
      }
      return cellValue.replace(
        /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/,
        '$1-$2-$3 $4:$5:$6'
      );
    },
    formatDate(row, column, cellValue) {
      if (!cellValue) return "";
      if (!/^\d{14}$/.test(cellValue)) {
        return cellValue
      }
      return cellValue.replace(
        /(\d{4})(\d{2})(\d{2}).*/,
        '$1-$2-$3'
      );
    },
    formatShortDate(dateStr) {
      if (!dateStr) return "";
      if (/^\d{8}$/.test(dateStr)) {
        return dateStr.replace(
          /(\d{4})(\d{2})(\d{2})/,
          '$1-$2-$3'
        );
      }
      if (/^\d{14}$/.test(dateStr)) {
        return dateStr.replace(
          /(\d{4})(\d{2})(\d{2}).*/,
          '$1-$2-$3'
        );
      }
      return dateStr;
    },
    getStatusType(status) {
      // 根据分析状态返回相应的类型
      switch (status) {
        case 0:
          return "info";
        case 1:
          return "warning";
        case 2:
          return 'success'
        case 3:
          return 'danger'
        default:
          return "";
      }
    },
    getStatusText(status) {
      // 根据分析状态返回相应的文本
      switch (status) {
        case 0:
          return "未分析";
        case 1:
          return "分析中";
        case 2:
          return '分析完成'
        case 3:
          return '分析失败'
        default:
          return "无需分析";
      }
    },
    async handleSync() {

      try {
        this.loading = true;
        this.analysisLoading = true;
        this.$notify({
          title: "生成预警",
          message: "数据对比分析中，耐心等待生成预警结果",
          type: "info",
          position: "top-right",
          duration: 3000,
          offset: 50,
        });
        const res = await getYetDisposedAnalysis();
        console.log('getYetDisposedAnalysis res= ', res);
        if (res.code === 200) {
          this.$message.success("生成预警成功");
          await this.loadTableData();
        }
      } catch (error) {
      } finally {
        this.loading = false;
        this.analysisLoading = false;
      }

    },
  },
  computed: {

  },
};
</script>

<style lang="less" scoped>
.outer-container{
  flex:1;
  display: flex;
  flex-direction: column;
  height: 10vh;
}
.search-form {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
}

// .search-right {
//   display: flex;
//   align-items: center;
//   gap: 10px;
//   margin-left: 10px;
// }

:deep(.el-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
  height: 0 !important;
}

.pagination-container {
  margin-top: 20px;
  margin-right: 60px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* Element UI 样式覆盖 */
:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 1);
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}

:deep(.el-button) {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
  padding: 0 20px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 80px;
}

:deep(.el-button:hover) {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  color: #00ffff;
}

:deep(.el-table) {
  background-color: transparent !important;
  flex: 1;
  min-height: 0;
}

// :deep(.el-table__header) {
//   background-color: rgba(0, 48, 102, 0.5) !important;
// }

// :deep(.el-table__body-wrapper::-webkit-scrollbar) {
//   width: 6px;
//   height: 6px;
// }

// :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
//   background: rgba(0, 255, 255, 0.4);
//   border-radius: 3px;
// }

// :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
//   background: rgba(0, 48, 102, 0.3);
// }

// :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
//   background-color: rgba(0, 48, 102, 0.5) !important;
// }

// :deep(.el-table td, .el-table th.is-leaf) {
//   border-bottom: 1px solid rgba(0, 255, 255, 0.1);
//   background-color: rgba(0, 48, 102, 0.3) !important;
// }

// :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
//   background-color: rgba(0, 255, 255, 0.2) !important;
//   color: #00ffff !important;
// }

// :deep(.el-table__row) {
//   transition: all 0.3s;
// }

// :deep(.el-table__body tr:hover > td) {
//   background-color: rgba(0, 255, 255, 0.2) !important;
//   color: #00ffff !important;
// }

// :deep(.el-table__header-wrapper) {
//   background-color: transparent !important;
// }

// :deep(.el-table__body tr) {
//   background-color: transparent !important;
// }

// :deep(.el-table__body tr td) {
//   background-color: rgba(0, 48, 102, 0.3) !important;
// }

// :deep(.el-table__body tr:hover td) {
//   background-color: rgba(0, 255, 255, 0.1) !important;
// }

// .detail-container {
//   padding: 20px;
//   background: rgba(0, 48, 102, 0.2);
//   border-radius: 4px;
//   margin: 10px 20px;
// }

// .detail-header {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   margin-bottom: 20px;
//   padding-bottom: 10px;
//   border-bottom: 1px solid rgba(0, 255, 255, 0.2);
// }

// .detail-title {
//   font-size: 18px;
//   color: #00ffff;
//   font-weight: bold;
// }

// .detail-number {
//   color: rgba(0, 255, 255, 0.7);
//   font-size: 16px;
// }

// .detail-content {
//   display: flex;
//   flex-direction: column;
//   gap: 20px;
// }

// .detail-section {
//   background: rgba(0, 48, 102, 0.3);
//   border: 1px solid rgba(0, 255, 255, 0.1);
//   border-radius: 4px;
//   padding: 15px;
// }

// .section-title {
//   color: #00ffff !important;
//   font-size: 14px;
//   font-weight: bold;
//   margin-bottom: 15px;
//   position: relative;
//   padding-left: 12px;
// }

// .section-title::before {
//   content: "";
//   position: absolute;
//   left: 0;
//   top: 50%;
//   transform: translateY(-50%);
//   width: 4px;
//   height: 16px;
//   background: #00ffff;
//   border-radius: 2px;
// }

// .detail-grid {
//   display: grid;
//   grid-template-columns: repeat(3, 1fr);
//   gap: 15px;
// }

// .detail-item {
//   display: flex;
//   align-items: center;
// }

// .item-label {
//   color: rgba(255, 255, 255, 0.7);
//   margin-right: 8px;
//   font-size: 14px;
//   white-space: nowrap;
// }

// .item-value {
//   color: #ffffff;
//   font-size: 14px;
// }

// .detail-text {
//   color: #ffffff;
//   font-size: 14px;
//   line-height: 1.6;
//   text-align: justify;
// }

:deep(.el-table__expanded-cell) {
  background-color: transparent !important;
}

:deep(.el-table__expand-icon) {
  color: #00ffff;
}

:deep(.el-table__expand-icon .el-icon) {
  font-size: 16px;
}

:deep(.el-table__expand-icon--expanded) {
  transform: rotate(90deg);
}

:deep(.el-table__expand-icon:hover) {
  color: #ffffff;
}

.pagination-container {
  margin-top: 20px;
}

:deep(.el-pagination) {
  padding: 0;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

:deep(.el-select:hover .el-input__inner) {
  border-color: #00ffff;
}

:deep(.el-select .el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.1);
  color: #ffffff;
}

:deep(.el-select-dropdown) {
  background-color: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}
</style>
