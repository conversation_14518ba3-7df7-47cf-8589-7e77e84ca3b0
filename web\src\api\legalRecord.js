import request from '@/utils/request'
import { mockLegalRecordList, mockSyncLegalRecordData, mockBatchAnalyzeRecords } from '@/api/mock/legalRecordMock'

// 获取法律文书列表
export function getLegalRecordList(params) {
  // 调用模拟数据函数，便于开发和测试
  // return mockLegalRecordList(params);
  
  // 实际API调用，注释掉以便使用模拟数据
  return request({
    url: '/api/scene_3/legal/search',
    method: 'post',
    params
  })
}

// 同步法律文书数据
export function syncLegalRecordData(params) {
  // 调用模拟同步数据函数
  // return mockSyncLegalRecordData(params);
  
  // 实际API调用，注释掉以便使用模拟数据
  return request({
    url: '/api/scene_3/sync',
    method: 'post',
    params
  })
}

// 批量分析法律文书
export function batchAnalyzeRecords(recordIds) {
  // 调用模拟批量分析函数
  // return mockBatchAnalyzeRecords(recordIds);
  
  // 实际API调用，注释掉以便使用模拟数据
  return request({
    url: '/api/scene_3/analyze',
    method: 'post',
    data: {
      jlbhs: recordIds
    }
  })
}

// 获取单个法律文书详情
export function getLegalRecordDetail(jlbh) {
  return request({
    url: `/api/legal-records/${jlbh}`,
    method: 'get'
  })
} 