<template>
  <div class="app-wrapper">
    <!-- 左侧导航栏 -->
    <div class="sidebar" :class="{ collapsed: isSidebarCollapsed }">
      <div class="police-badge" v-show="!isSidebarCollapsed">
        <img src="@/assets/images/gongan.png" alt="公安徽章" class="badge-image" />
        <div class="police-motto">
          <p>对党忠诚 服务人民</p>
          <p>执法公正 纪律严明</p>
        </div>
      </div>
      <div class="police-badge" v-show="isSidebarCollapsed">
        <img src="@/assets/images/gongan.png" alt="公安徽章" class="badge-image-left" />
      </div>
      <div class="sidebar-content">
        <ul class="nav-menu">
          <li
            class="nav-item"
            v-for="(nav, index) in routerList"
            :key="index"
            :title="isSidebarCollapsed ? nav.title : ''"
          >
            <router-link :to="nav.path" :exact="nav.title === '首页'">
              <img :src="nav.iconActive" alt="" v-if="$route.path === nav.path" />
              <img :src="nav.icon" alt="" v-else />
              <span v-show="!isSidebarCollapsed">{{ nav.title }}</span>
            </router-link>
          </li>
        </ul>
      </div>
      <div class="sidebar-footer">
        <div class="user-info" v-show="!isSidebarCollapsed">
          <img src="@/assets/images/svgs/编组.svg" class="user-avatar" alt="" />
          <div class="user-details">
            <div class="user-name">
              {{ username }}
              <div class="user-police-number">&nbsp;- {{ police_number }}</div>
            </div>
            <el-tooltip :content="police_unit" placement="top" :disabled="!isTextOverflow">
              <div class="user-unit" ref="unitText">{{ police_unit }}</div>
            </el-tooltip>
          </div>
        </div>
        <el-button v-if="!isSidebarCollapsed" type="text" class="logout-button" @click="logout">
          <i class="el-icon-switch-button"></i>
          <span>退出登录</span>
        </el-button>
        <el-button v-else type="text" class="logout-button-small" @click="logout">
          <i class="el-icon-switch-button"></i>
        </el-button>
      </div>
      <!-- 收起/展开按钮 -->
      <div class="sidebar-toggle" @click="toggleSidebar">
        <i :class="isSidebarCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-container" :class="{ 'with-collapsed-sidebar': isSidebarCollapsed }">
      <router-view />
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: 'Layout',
  data() {
    return {
      isSidebarCollapsed: false,
      isTextOverflow: false,
      routerList: []
    }
  },
  computed: {
    ...mapGetters(["isAuthenticated"]),
    police_number() {
      return this.$store.state.user ? this.$store.state.user.police_number : "";
    },
    username() {
      return this.$store.state.user ? this.$store.state.user.username : "";
    },
    police_unit() {
      return this.$store.state.user ? this.$store.state.user.police_unit : "";
    }
  },
  created() {
    // 从本地存储中恢复侧边栏状态
    const savedState = localStorage.getItem("sidebarCollapsed");
    if (savedState !== null) {
      this.isSidebarCollapsed = JSON.parse(savedState);
    }
    
    // 从store中获取路由列表
    // this.routerList = this.$store.getters.permission_routes[0]?.children || [];
    // console.log('菜单数据1：',this.routerList)
    // 从store中获取路由列表，使用正确的命名空间
const routes = this.$store.state.permission.routes;
console.log('All routes:', routes);

// 找到根路由（path为'/'的路由）
const rootRoute = routes.find(route => route.path === '/');
console.log('Root route:', rootRoute);

// 获取子路由列表
this.routerList = rootRoute?.children || [];
console.log('Router list:', this.routerList);
    this.loadImg();
  },
  mounted() {
    this.checkTextOverflow();
    window.addEventListener("resize", this.checkTextOverflow);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.checkTextOverflow);
  },
  methods: {
    async loadImg() {
      let routerList = JSON.parse(JSON.stringify(this.routerList));

      let promiseList = [];
      async function loadImg(i) {
        try {
          const module = await import(`@/assets/images/icons/icon-nav${i + 1}.svg`);
          const moduleActive = await import(`@/assets/images/icons/icon-nav${i + 1}${i + 1}.svg`);
          routerList[i].icon = module.default;
          routerList[i].iconActive = moduleActive.default;
        } catch (error) {
          console.error(error);
        }
      }

      routerList.forEach((r, i) => {
        promiseList.push(loadImg(i));
      });

      Promise.all(promiseList).then(() => {
        this.routerList = routerList;
      });
    },
    logout() {
      try {
        this.$store.dispatch("logout");
        this.$message({
          message: "已成功退出登录",
          type: "success",
        });
        this.$router.push("/login");
      } catch (error) {
        console.error("退出登录出错:", error);
      }
    },
    toggleSidebar() {
      this.isSidebarCollapsed = !this.isSidebarCollapsed;
      localStorage.setItem("sidebarCollapsed", JSON.stringify(this.isSidebarCollapsed));
    },
    checkTextOverflow() {
      if (this.$refs.unitText) {
        const element = this.$refs.unitText
        this.isTextOverflow = element.scrollWidth > element.clientWidth
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100vw;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 260px;
  height: 100vh;
  border-right: 1px solid #06445d;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  background-image: linear-gradient(180deg, #031d2e 0%, #091822 14%);
  flex-shrink: 0;
  
  &.collapsed {
    width: 60px;
    background: #031f2e;
  }
}

.main-container {
  flex: 1;
  min-height: 100vh;
  transition: all 0.3s ease;
  position: relative;
  margin-left: 0;
  background: url("@/assets/images/bg.png") no-repeat center center;
  background-size: cover;
  overflow: auto;
  
  &.with-collapsed-sidebar {
    margin-left: 0;
  }
}

.police-badge {
  background-image: url('@/assets/images/left-top.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .badge-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 10px;
    filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.4));
  }
  
  .badge-image-left {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-bottom: 10px;
    filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.4));
  }
  
  .police-motto {
    width: 100%;
    text-align: center;
    color: #ffffff;
    font-size: 13px;
    line-height: 1.5;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    font-family: "SimSun", serif;
    
    p {
      margin: 2px 0;
      letter-spacing: 1px;
    }
  }
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  background-color: #031D2E;
  
  .nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    
    .nav-item {
      margin-bottom: 5px;
      
      a {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        transition: all 0.3s;
        border-left: 3px solid transparent;
        font-size: 14px;
        white-space: nowrap;
        
        img {
          width: 18px;
          margin-right: 12px;
        }
        
        &:hover {
          color: #00ffff;
          background: rgba(0, 255, 255, 0.05);
        }
        
        &.router-link-active {
          color: #00ffff;
          background: rgba(0, 255, 255, 0.1);
        }
      }
    }
  }
}

.sidebar-footer {
  padding: 15px 20px;
  background-color: #031D2E;
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 0;
    
    .user-avatar {
      width: 44px;
      height: 44px;
      flex-shrink: 0;
    }
    
    .user-details {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      min-height: 32px;
      margin-left: 20px;
      
      .user-name {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        
        .user-police-number {
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .user-unit {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: default;
      }
    }
  }
  
  .logout-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(0, 255, 255, 0.8) !important;
    background: rgba(0, 255, 255, 0.1) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    padding: 8px 0 !important;
    font-size: 14px !important;
    transition: all 0.3s !important;
    margin-left: 0px !important;
    
    &:hover {
      color: #00ffff !important;
      background: rgba(0, 255, 255, 0.15) !important;
      border-color: rgba(0, 255, 255, 0.3) !important;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.1) !important;
    }
    
    i {
      margin-right: 5px;
      font-size: 16px;
    }
  }
  
  .logout-button-small {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(0, 255, 255, 0.8) !important;
    background: rgba(0, 255, 255, 0.1) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    border-radius: 4px !important;
    padding: 0 !important;
    font-size: 14px !important;
    transition: all 0.3s !important;
    margin-left: 0px !important;
    
    &:hover {
      color: #00ffff !important;
      background: rgba(0, 255, 255, 0.15) !important;
      border-color: rgba(0, 255, 255, 0.3) !important;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.1) !important;
    }
    
    i {
      margin-right: 0;
      font-size: 16px;
    }
  }
}

.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background: rgba(0, 24, 51, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  color: rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
  
  &:hover {
    background: rgba(0, 36, 77, 0.9);
    color: #00ffff;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }
  
  i {
    font-size: 14px;
  }
}

/* 自定义滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #031D2E;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-track {
  background-color: #031D2E;
  border-radius: 2px;
}

.main-container::-webkit-scrollbar {
  width: 4px;
}

.main-container::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
}

.main-container::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.05);
  border-radius: 2px;
}
</style> 