<template>
  <div class="chat-container">
    <!-- 左侧会话列表 -->
    <div class="chatai-sidebar" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
      <div class="sidebar-header">
        <el-button type="primary" class="new-chat-btn" @click="createNewChat">
          <i class="el-icon-plus"></i> 新建会话
        </el-button>
      </div>
      
      <div class="chat-list">
        <div
          v-for="chat in chatList"
          :key="chat.id"
          class="chat-item"
          :class="{ active: currentChat === chat.id }"
          @click="selectChat(chat.id)"
        >
          <i class="el-icon-chat-dot-round"></i>
          <span class="chat-title">{{ chat.title }}</span>
          <el-dropdown class="chat-actions" trigger="click">
            <i class="el-icon-more"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="renameChat(chat)">重命名</el-dropdown-item>
              <el-dropdown-item @click.native="deleteChat(chat)" class="danger">
                删除会话
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      
      <div class="sidebar-footer">
        <el-dropdown trigger="click">
          <div class="user-info">
            <el-avatar size="small">User</el-avatar>
            <span class="username">用户名</span>
          </div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>个人设置</el-dropdown-item>
            <el-dropdown-item>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <!-- 中间聊天内容区 -->
    <div class="chat-main">
      <div class="chat-header">
        <i 
          class="el-icon-s-fold sidebar-toggle" 
          @click="toggleSidebar"
        ></i>
        <span class="chat-title">{{ currentChatTitle }}</span>
        <i 
          class="el-icon-setting settings-toggle" 
          @click="toggleSettings"
        ></i>
      </div>

      <div class="chat-messages" ref="messageList">
        <div v-for="message in messages" :key="message.id" class="message-item">
          <div class="message-avatar">
            <el-avatar :size="40">
              {{ message.role === 'user' ? 'U' : 'A' }}
            </el-avatar>
          </div>
          <div class="message-content">
            <div class="message-role">
              {{ message.role === 'user' ? '用户' : 'AI助手' }}
            </div>
            <div class="message-text" v-html="formatMessage(message.content)"></div>
          </div>
        </div>
      </div>

      <div class="chat-input">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="3"
          placeholder="输入消息..."
          @keyup.enter.native.exact="sendMessage"
        ></el-input>
        <div class="input-actions">
          <el-button type="primary" @click="sendMessage">发送</el-button>
        </div>
      </div>
    </div>

    <!-- 右侧设置面板 -->
    <div class="settings-panel" v-show="showSettings">
      <div class="settings-header">
        <span>设置</span>
        <i class="el-icon-close" @click="toggleSettings"></i>
      </div>
      
      <div class="settings-content">
        <div class="setting-section">
          <h3>模型设置</h3>
          <el-form label-position="top">
            <el-form-item label="选择模型">
              <el-select v-model="selectedModel" placeholder="选择模型">
                <el-option label="GPT-3.5" value="gpt-3.5"></el-option>
                <el-option label="GPT-4" value="gpt-4"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="温度">
              <el-slider v-model="temperature" :min="0" :max="1" :step="0.1"></el-slider>
            </el-form-item>
            
            <el-form-item label="最大长度">
              <el-input-number v-model="maxLength" :min="1" :max="4000"></el-input-number>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="setting-section">
          <h3>界面设置</h3>
          <el-form label-position="top">
            <el-form-item label="主题">
              <el-radio-group v-model="theme">
                <el-radio label="light">浅色</el-radio>
                <el-radio label="dark">深色</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatView',
  data() {
    return {
      isSidebarCollapsed: false,
      showSettings: false,
      currentChat: null,
      inputMessage: '',
      chatList: [
        { id: 1, title: '会话 1' },
        { id: 2, title: '会话 2' },
      ],
      messages: [
        { id: 1, role: 'user', content: '你好！' },
        { id: 2, role: 'assistant', content: '你好！我是AI助手，很高兴为你服务。' },
      ],
      selectedModel: 'gpt-3.5',
      temperature: 0.7,
      maxLength: 2000,
      theme: 'light'
    }
  },
  computed: {
    currentChatTitle() {
      const chat = this.chatList.find(c => c.id === this.currentChat)
      return chat ? chat.title : '新会话'
    }
  },
  methods: {
    toggleSidebar() {
      this.isSidebarCollapsed = !this.isSidebarCollapsed
    },
    toggleSettings() {
      this.showSettings = !this.showSettings
    },
    createNewChat() {
      const newChat = {
        id: Date.now(),
        title: `新会话 ${this.chatList.length + 1}`
      }
      this.chatList.push(newChat)
      this.selectChat(newChat.id)
    },
    selectChat(chatId) {
      this.currentChat = chatId
    },
    renameChat(chat) {
      this.$prompt('请输入新的会话名称', '重命名会话', {
        inputValue: chat.title,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        const index = this.chatList.findIndex(c => c.id === chat.id)
        if (index !== -1) {
          this.chatList[index].title = value
        }
      })
      .catch(() => {})
    },
    deleteChat(chat) {
      this.$confirm('确定要删除这个会话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.chatList.findIndex(c => c.id === chat.id)
        if (index !== -1) {
          this.chatList.splice(index, 1)
          if (this.currentChat === chat.id) {
            this.currentChat = this.chatList[0]?.id || null
          }
        }
      })
      .catch(() => {})
    },
    sendMessage() {
      if (!this.inputMessage.trim()) return
      
      const newMessage = {
        id: Date.now(),
        role: 'user',
        content: this.inputMessage
      }
      this.messages.push(newMessage)
      this.inputMessage = ''
      
      // 模拟AI回复
      setTimeout(() => {
        this.messages.push({
          id: Date.now(),
          role: 'assistant',
          content: '这是一个模拟的AI回复消息。'
        })
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }, 1000)
    },
    scrollToBottom() {
      const messageList = this.$refs.messageList
      messageList.scrollTop = messageList.scrollHeight
    },
    formatMessage(content) {
      // 这里可以添加markdown渲染等格式化逻辑
      return content
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-container {
  display: flex;
  height: 100%;
  background-color: #f5f5f5;
  color: #1a1a1a;
}

.chatai-sidebar {
  width: 260px;
  background-color: #fff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  
  &.sidebar-collapsed {
    width: 0;
    overflow: hidden;
  }
  
  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    
    .new-chat-btn {
      width: 100%;
    }
  }
  
  .chat-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    
    .chat-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      margin: 4px 0;
      border-radius: 6px;
      cursor: pointer;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.active {
        background-color: #e6f7ff;
      }
      
      .chat-title {
        flex: 1;
        margin-left: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .chat-actions {
        opacity: 0;
      }
      
      &:hover .chat-actions {
        opacity: 1;
      }
    }
  }
  
  .sidebar-footer {
    padding: 16px;
    border-top: 1px solid #e0e0e0;
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .username {
        margin-left: 8px;
      }
    }
  }
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .chat-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    
    .sidebar-toggle,
    .settings-toggle {
      font-size: 20px;
      cursor: pointer;
      padding: 8px;
      
      &:hover {
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }
    
    .chat-title {
      flex: 1;
      margin: 0 20px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    
    .message-item {
      display: flex;
      margin-bottom: 20px;
      
      .message-avatar {
        margin-right: 12px;
      }
      
      .message-content {
        flex: 1;
        
        .message-role {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }
        
        .message-text {
          background-color: #fff;
          padding: 12px;
          border-radius: 8px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .chat-input {
    padding: 20px;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
    
    .input-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 8px;
    }
  }
}

.settings-panel {
  width: 300px;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
  
  .settings-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #e0e0e0;
    
    .el-icon-close {
      cursor: pointer;
      padding: 8px;
      
      &:hover {
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }
  }
  
  .settings-content {
    padding: 20px;
    
    .setting-section {
      margin-bottom: 24px;
      
      h3 {
        margin: 0 0 16px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// 深色主题样式
.dark-theme {
  background-color: #1a1a1a;
  color: #fff;
  
  .sidebar,
  .chat-main,
  .settings-panel {
    background-color: #2d2d2d;
    border-color: #3d3d3d;
  }
  
  .chat-item:hover {
    background-color: #3d3d3d;
  }
  
  .message-text {
    background-color: #3d3d3d;
    color: #fff;
  }
}
</style>