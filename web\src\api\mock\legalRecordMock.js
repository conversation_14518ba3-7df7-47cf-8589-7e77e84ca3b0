// 文书名称列表
const docNames = [
  '刑事判决书',
  '民事裁定书',
  '行政处罚决定书',
  '起诉书',
  '不起诉决定书',
  '逮捕证',
  '搜查证',
  '拘留证',
  '调查笔录',
  '立案决定书'
];

// 案件名称列表
const caseNames = [
  '张三盗窃案',
  '李四故意伤害案',
  '王五贪污受贿案',
  '赵六诈骗案',
  '钱七危险驾驶案',
  '孙八聚众斗殴案',
  '周九非法持有毒品案',
  '吴十抢劫案',
  '郑十一职务侵占案',
  '刘十二非法拘禁案'
];

// 文书简要列表
const briefs = [
  '犯罪嫌疑人涉嫌盗窃罪，建议批准逮捕',
  '被告人犯盗窃罪，判处有期徒刑一年六个月',
  '犯罪嫌疑人涉嫌故意伤害罪，建议提起公诉',
  '当事人违反治安管理规定，处以罚款五千元',
  '经调查，不构成犯罪，决定不予起诉',
  '经审理，被告的行为不构成犯罪，判决无罪',
  '犯罪嫌疑人有自首情节，建议从轻处罚',
  '被告人认罪认罚，依法可以从宽处理',
  '涉案物品已返还被害人，建议对被告人适用缓刑',
  '犯罪情节轻微，社会危害性不大，不予立案'
];

// 填发人姓名列表
const authors = [
  '李警官',
  '张检察官',
  '王法官',
  '赵警官',
  '钱检察官'
];

// 文书字号前缀
const docNumPrefixes = [
  '京公刑诉字',
  '京检刑诉字',
  '京法刑诉字',
  '京公行管字',
  '京检民诉字'
];

// 生成随机日期
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// 生成指定范围内的随机整数
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 模拟法律文书列表数据
export function mockLegalRecordList(params) {
  // 模拟数据
  const mockData = [];
  
  // 模拟记录总数
  const total = 156;
  
  // 计算分页数据
  const page = params.page || 1;
  const pageSize = params.page_size || 10;
  const startIndex = (page - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, total);
  
  // 生成模拟数据
  for (let i = 0; i < (endIndex - startIndex); i++) {
    const id = startIndex + i + 1;
    const jlbh = `JL${String(id).padStart(6, '0')}`;
    const ajbh = `AJ${String(id).padStart(6, '0')}`;
    
    // 随机分析状态：0-未分析，1-分析中，2-分析完成，3-分析失败
    const analysisStatus = getRandomInt(0, 3);
    
    // 如果分析完成，随机生成检查状态：0-无问题，1-有问题
    const checkStatus = analysisStatus === 2 ? getRandomInt(0, 1) : null;
    
    // 随机生成填发时间和批准时间
    const tfsj = randomDate(new Date(2023, 0, 1), new Date()).toISOString();
    const pzsj = randomDate(new Date(2023, 0, 1), new Date()).toISOString();
    
    // 构建记录对象
    const record = {
      jlbh,
      ajbh,
      doc_name: docNames[getRandomInt(0, docNames.length - 1)],
      cqsx: briefs[getRandomInt(0, briefs.length - 1)],
      ajmc: caseNames[getRandomInt(0, caseNames.length - 1)],
      doc_num: `${docNumPrefixes[getRandomInt(0, docNumPrefixes.length - 1)]}[${new Date().getFullYear()}]${String(getRandomInt(1, 999)).padStart(3, '0')}号`,
      tfrxm: authors[getRandomInt(0, authors.length - 1)],
      tfsj,
      pzsj,
      analysis_status: analysisStatus,
      check_status: checkStatus
    };
    
    // 如果分析完成且有问题，添加分析结果
    if (analysisStatus === 2 && checkStatus === 1) {
      record.analysis_result = '文书存在问题，需要修改';
      record.analysis_tag = 'danger';
      
      // 添加模拟的检测结果
      const detectionResults = {};
      
      // 随机选择1-3个问题类型
      const problemTypes = ['output1', 'output2', 'output3', 'output4', 'output5', 'output6', 'output7', 'output8', 'output9'];
      const selectedProblems = [];
      const numProblems = getRandomInt(1, 3);
      
      while (selectedProblems.length < numProblems) {
        const problemType = problemTypes[getRandomInt(0, problemTypes.length - 1)];
        if (!selectedProblems.includes(problemType)) {
          selectedProblems.push(problemType);
        }
      }
      
      // 为选中的问题类型生成错误信息
      selectedProblems.forEach(problemType => {
        detectionResults[problemType] = {
          status: 'ERROR',
          title: `存在${problemType}问题`,
          reason: `文书中存在${problemType}相关问题，需要修改和完善。`
        };
      });
      
      // 添加未选中的问题类型为正确
      problemTypes.forEach(problemType => {
        if (!selectedProblems.includes(problemType)) {
          detectionResults[problemType] = {
            status: 'RIGHT'
          };
        }
      });
      
      // 添加文书内容检验结果
      detectionResults['doc_data_content'] = {
        status: 'RIGHT'
      };
      
      record.detection_result = JSON.stringify(detectionResults);
    } else if (analysisStatus === 2 && checkStatus === 0) {
      record.analysis_result = '文书检查无问题';
      record.analysis_tag = 'success';
      
      // 所有检测项都为正确
      const detectionResults = {};
      const problemTypes = ['output1', 'output2', 'output3', 'output4', 'output5', 'output6', 'output7', 'output8', 'output9', 'doc_data_content'];
      
      problemTypes.forEach(problemType => {
        detectionResults[problemType] = {
          status: 'RIGHT'
        };
      });
      
      record.detection_result = JSON.stringify(detectionResults);
    }
    
    mockData.push(record);
  }
  
  // 处理搜索条件
  let filteredData = [...mockData];
  
  // 应用搜索过滤（如果有条件的话）
  if (params.ajbh) {
    filteredData = filteredData.filter(item => item.ajbh.includes(params.ajbh));
  }
  
  if (params.doc_name) {
    filteredData = filteredData.filter(item => item.doc_name.includes(params.doc_name));
  }
  
  if (params.cqsx) {
    filteredData = filteredData.filter(item => item.cqsx.includes(params.cqsx));
  }
  
  if (params.analysis_status !== undefined && params.analysis_status !== '') {
    filteredData = filteredData.filter(item => item.analysis_status === Number(params.analysis_status));
  }
  
  if (params.check_status !== undefined && params.check_status !== '') {
    filteredData = filteredData.filter(item => item.check_status === Number(params.check_status));
  }
  
  if (params.ajmc) {
    filteredData = filteredData.filter(item => item.ajmc.includes(params.ajmc));
  }
  
  if (params.doc_num) {
    filteredData = filteredData.filter(item => item.doc_num.includes(params.doc_num));
  }
  
  if (params.tfrxm) {
    filteredData = filteredData.filter(item => item.tfrxm.includes(params.tfrxm));
  }
  
  // 模拟搜索结果返回
  return Promise.resolve({
    code: 200,
    message: '获取成功',
    data: {
      records: filteredData,
      total: total, // 在实际场景中，这里应该返回过滤后的总数
      page,
      page_size: pageSize
    }
  });
}

// 模拟同步法律文书数据
export function mockSyncLegalRecordData(params) {
  // 模拟1-2秒的延迟
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '数据同步成功',
        data: {
          sync_count: Math.floor(Math.random() * 50) + 5, // 随机同步5-55条记录
          start_date: params.start_date,
          end_date: params.end_date
        }
      });
    }, Math.floor(Math.random() * 1000) + 1000);
  });
}

// 模拟批量分析法律文书
export function mockBatchAnalyzeRecords(recordIds) {
  // 模拟1-2秒的延迟
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '批量分析任务已提交',
        data: {
          task_id: 'TASK_' + Date.now(),
          record_count: recordIds.length,
          submitted_at: new Date().toISOString()
        }
      });
    }, Math.floor(Math.random() * 1000) + 1000);
  });
} 