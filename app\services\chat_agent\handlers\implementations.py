"""
聊天智能体处理器实现
"""
import json
import os
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Type, Tuple, AsyncIterable

from sqlalchemy.orm import Session, Query

from app.config import settings
from app.log.log_utils import LogUtils
from app.models.chat import ChatAgentsMsg, Dataset, DocFeature
from app.services.chat_agent.base import AgentType, UserRoleType
from app.services.chat_agent.handlers.interfaces import (
    KnowledgeBaseHandler,
    QueryProcessingHandler,
    SessionHandler
)
from app.services.dify.dify_params import DifyDatasetParams
from app.services.dify.service import DifyService


class SecretarialKnowledgeBaseHandler(KnowledgeBaseHandler):

    def __init__(self, db: Session, dify_service: Any, agent_config: Dict[str, Any]):
        self.db = db
        self.dify_service = dify_service
        self.agent_config = agent_config

    async def check_duplicate_upload(self, document_data: Dict[str, Any], user_context: Dict[str, Any], agent_id: str):
        user_id = user_context.get("id")
        # 计算文档MD5并校验
        # 根据 文档名称进行 过滤, 查询到数据后,根据数据的MD5进行过滤,如果不同走更新,  相同则跳过(文档:{文档名称},已上传)
        file_name = document_data.get('file_name', '')

        # 关联dataset表进行联合过滤
        existing_doc = self.db.query(DocFeature).join(
            Dataset,
            DocFeature.local_dataset_id == Dataset.id
        ).filter(
            DocFeature.doc_name == file_name,
            DocFeature.user_id == user_id,
            Dataset.agent_id == agent_id  # 添加agent_id过滤条件
        ).first()
        return None if existing_doc is None else existing_doc.segment_id, existing_doc

    async def get_or_create_dataset(self, user: Dict[str, any], agent_id: str):
        try:
            user_id = str(user.get("id"))
            # 查询现有知识库
            dataset = self.db.query(Dataset).filter(
                Dataset.user == user_id,
                Dataset.agent_id == agent_id
            ).first()

            if dataset:
                return dataset.dataset_id, dataset.document_id, dataset

            # 创建新的Dify知识库
            # 生成这种类型的20241020150209时间戳
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            dataset_name = f"用户{user_id}_{timestamp}的文秘知识库"
            dataset_description = f"{self.agent_config.get('name')}的个性化文档知识库"

            # TODO 创建个人知识库
            dify_dataset_id = self.dify_service.create_dataset(
                payload=DifyDatasetParams.create_dataset_default_params(name=dataset_name,
                                                                        description=dataset_description))

            # 创建/更新dataset记录
            dataset = Dataset(
                user=str(user_id),
                agent_id=agent_id,
                dataset_id=dify_dataset_id,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            self.db.add(dataset)
            self.db.commit()
            self.db.flush()  # 获取生成的dataset ID

            LogUtils.info(f'创建知识库dataset_id: {dify_dataset_id}')
            return dataset.dataset_id, None, dataset

        except Exception as e:
            LogUtils.error(f"文秘知识库创建失败: {str(e)}", exc_info=True)
            raise RuntimeError(f"无法创建文秘知识库: {str(e)}") from e

    async def push_document_to_dataset(self, dataset_id: str, document_id: str, context: Dict[str, Any],
                                       user: Dict[str, any]):
        local_dataset = context.get('local_dataset', {})
        if dataset_id and document_id:
            return local_dataset

        # 文件生成文档，上传知识库
        # 创建个人知识库 文档
        document_data = self.dify_service.create_document_by_text(
            dataset_id=dataset_id,
            payload=DifyDatasetParams.create_document_by_text_default_params())

        dify_document_id = document_data.get('id', '')

        batch = document_data.get('batch')

        # 检查文档索引状态
        indexing_status = self.dify_service.get_indexing_status(dataset_id=dataset_id,
                                                                batch=batch)
        # 判断indexing_status 第一步校验:如果为["error", "paused"] 则文档创建失败;第二步校验如果不为completed则等待1秒再调用一次get_indexing_status, 直到返回completed或者["error", "paused"]终结流程
        if indexing_status in ["error", "paused"]:
            raise Exception(
                f"知识库dataset_id:{dataset_id},文档document_id:{dify_document_id},创建失败,失败状态:{indexing_status}")

        # 等待索引完成
        while indexing_status not in ["completed", "error", "paused"]:
            LogUtils.info(f"文档索引中，当前状态: {indexing_status}，等待1秒...")
            time.sleep(1)
            indexing_status = self.dify_service.get_indexing_status(dataset_id=dataset_id,
                                                                    batch=batch)
            if indexing_status in ["error", "paused"]:
                raise Exception(
                    f"知识库dataset_id:{dataset_id},文档document_id:{dify_document_id},创建失败,失败状态:{indexing_status}")

        if indexing_status == "completed":
            LogUtils.info(
                f"知识库dataset_id:{dataset_id},文档document_id:{dify_document_id},文档创建成功状态:{indexing_status}")

        # 知识库绑定文档
        dataset = self.db.query(Dataset).filter(
            Dataset.dataset_id == dataset_id
        ).first()
        dataset.document_id = dify_document_id
        self.db.commit()
        self.db.refresh(dataset)
        return dataset

    async def push_segment_to_document(self, dataset_id: str, document_id: str, document_data: Dict[str, Any],
                                       segment_id: str = None):
        # 生成知识库 文档
        # 新增分段
        features = document_data.get('features', {})

        if not features:
            return None

        structure_feature_str = features.get('structure_feature', "")
        doc_name = os.path.splitext(features.get('doc_name', ''))[0]

        # 新增分段上传知识库
        segment_content = f"{doc_name}-文稿创作（写作）的结构特征|{structure_feature_str}"

        if segment_id:
            return await  self.dify_service.update_segment(
                dataset_id=dataset_id,
                document_id=document_id,
                segment_id=segment_id,
                payload=DifyDatasetParams.update_segment_default_params(content=segment_content)
            )
        else:
            return self.dify_service.create_segments(
                dataset_id=dataset_id,
                document_id=document_id,
                payload=DifyDatasetParams.create_segments_default_params(content=segment_content)
            )

    async def search_documents(self, dataset_id: str, query: str) -> str:

        try:
            # 调用Dify服务进行文档检索
            return self.dify_service.retrieve_dataset(
                dataset_id=dataset_id,
                payload=DifyDatasetParams.retrieve_dataset_default_params(query)
            )
        except Exception as e:
            LogUtils.error(f"文档检索失败: {str(e)}")
            return ''

    async def extract_document_features(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        pass

    async def extract_feature(self, document_data: Dict[str, Any], existing_features):
        # 获取结构特征和写作风格特征
        structure_feature = existing_features.get("structure", "")
        writing_style_feature = existing_features.get("style", "")

        # 导入特征模板
        from app.services.chat_agent.doc_feature_template import (
            STRUCTURE_FEATURE_TEMPLATE,
            WRITING_STYLE_FEATURE_TEMPLATE
        )

        # 处理特征输入
        structure_input = (
            STRUCTURE_FEATURE_TEMPLATE
            if structure_feature == ""
            else json.dumps(structure_feature, ensure_ascii=False)
        )

        writing_style_input = (
            WRITING_STYLE_FEATURE_TEMPLATE
            if writing_style_feature == ""
            else json.dumps(writing_style_feature, ensure_ascii=False)
        )

        result = await self.dify_service.chatflow(
            chatflow_endpoint="/v1/chat-messages",
            api_key=settings.EXTRACT_FEATURES,
            message="提取特征",
            file_ids=[document_data.get("file_id")],
            inputs={
                "structure_feature": structure_input,
                "writing_style_feature": writing_style_input
            }
        )

        if result["success"]:
            feature_data = result["data"]['answer']
            result = feature_data.split("||")
            return {
                "features": {
                    "structure_feature": result[0],
                    "writing_style_feature": result[1],
                    "doc_name": document_data.get("file_name", "")
                }
            }
        else:
            return None

    async def list_datasets(self, agent_id: str, user_context: Dict[str, Any],
                            page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取知识库列表 - 文秘专用实现
        
        Args:
            agent_id: 智能体ID
            user_context: 用户上下文
            is_personal: 是否只返回个人知识库
            limit: 返回数量限制
            
        Returns:
            知识库列表
        """
        try:
            user_id = user_context.get("id")

            # 关联dataset表进行联合过滤
            query = self.db.query(DocFeature).join(
                Dataset,
                DocFeature.local_dataset_id == Dataset.id
            ).filter(
                DocFeature.user_id == str(user_id),
                Dataset.agent_id == agent_id  # 添加agent_id过滤条件
            )
            total_count = query.count()
            # 添加分页
            skip = (page - 1) * limit
            docs = query.order_by(DocFeature.id.desc()).offset(skip).limit(limit).all()
            # 根据 total_count 和 page,limit 计算是否还有下一页
            has_more = total_count > (page * limit)

            if docs:
                return {
                    "has_more": has_more,
                    "datasets": [{"id": doc.id, "doc_name": doc.doc_name} for doc in docs]
                }
            return {"has_more": False, "datasets": []}
        except Exception as e:
            LogUtils.error(f"获取文秘知识库列表失败: {str(e)}")
            raise RuntimeError(f"获取知识库列表失败: {str(e)}")


class DefaultKnowledgeBaseHandler(KnowledgeBaseHandler):
    """默认的知识库处理器实现"""

    def __init__(self, db: Session, dify_service: Any, agent_config: Dict[str, Any]):
        self.db = db
        self.dify_service = dify_service
        self.agent_config = agent_config

    async def check_duplicate_upload(self, document_data: Dict[str, Any], user_context: Dict[str, Any], agent_id: str):
        role = user_context.get("role")
        if not role or role != UserRoleType.DATASET_MANAGER.name:
            raise Exception("暂无知识库操作权限,请联系管理员添加权限后操作")
        # 计算文档MD5并校验
        # 根据 文档名称进行 过滤, 查询到数据后,根据数据的MD5进行过滤,如果不同走更新,  相同则跳过(文档:{文档名称},已上传)
        file_name = document_data.get('file_name', '')

        # 关联dataset表进行联合过滤
        existing_doc = self.db.query(DocFeature).join(
            Dataset,
            DocFeature.local_dataset_id == Dataset.id
        ).filter(
            DocFeature.doc_name == file_name,
            Dataset.user == role,
            Dataset.agent_id == agent_id  # 添加agent_id过滤条件
        ).first()
        return None if existing_doc is None else existing_doc.segment_id, existing_doc

    async def search_documents(self, dataset_id: str, query: str) -> str:
        """
        根据查询和过滤条件搜索文档

        Args:
            query: 搜索查询
            filters: 过滤条件

        Returns:
            匹配的文档列表
        """
        try:
            # 调用Dify服务进行文档检索
            return self.dify_service.retrieve_dataset(
                dataset_id=dataset_id,
                payload=DifyDatasetParams.retrieve_dataset_default_params(query)
            )
        except Exception as e:
            LogUtils.error(f"文档检索失败: {str(e)}")
            return ''

    async def extract_document_features(self, doc_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取文档特征
        
        Args:
            doc_data: 文档数据
            
        Returns:
            文档特征
        """
        try:
            # 调用Dify服务提取文档特征
            features = await self.dify_service.extract_document_features(doc_data)

            return features
        except Exception as e:
            LogUtils.error(f"提取文档特征失败: {str(e)}")
            return {}

    async def extract_feature(self, document_data: Dict[str, Any], existing_features):
        """
        通过dify流程进行文档特征提取
        """

        # 实现特征提取逻辑
        pass

    async def get_or_create_dataset(self, user: Dict[str, any], agent_id: str):
        """
        获取或创建知识库
        """
        dataset_id: str
        agent_tpye = self.agent_config.get('type', '')
        if agent_tpye == AgentType.LEGAL_CODE.name:
            dataset_id = settings.LEGAL_CODE_DATASET_ID
        elif agent_tpye == AgentType.DETENTION_CENTRE.name:
            dataset_id = settings.DETENTION_CENTRE_DATASET_ID
        else:
            raise Exception(f'{agent_tpye}类型 agent 暂无知识库功能')

        return dataset_id, None, None

    async def push_document_to_dataset(self, dataset_id: str, document_id: str, context: Dict[str, Any],
                                       user: Dict[str, any]):
        document_data = await self.dify_service.create_document_by_file(
            dataset_id=dataset_id,
            document_data=context.get("document_data", {}),
            payload=DifyDatasetParams.create_document_by_file_default_params())

        dify_document_id = document_data.get('id', '')

        batch = document_data.get('batch')

        indexing_status = self.dify_service.get_indexing_status(dataset_id=dataset_id,
                                                                batch=batch)
        # 判断indexing_status 第一步校验:如果为["error", "paused"] 则文档创建失败;第二步校验如果不为completed则等待1秒再调用一次get_indexing_status, 直到返回completed或者["error", "paused"]终结流程
        if indexing_status in ["error", "paused"]:
            raise Exception(
                f"知识库dataset_id:{dataset_id},文档document_id:{dify_document_id},创建失败,失败状态:{indexing_status}")

        # 等待索引完成
        while indexing_status not in ["completed", "error", "paused"]:
            LogUtils.info(f"文档索引中，当前状态: {indexing_status}，等待1秒...")
            time.sleep(1)
            indexing_status = self.dify_service.get_indexing_status(dataset_id=dataset_id,
                                                                    batch=batch)
            if indexing_status in ["error", "paused"]:
                raise Exception(
                    f"知识库dataset_id:{dataset_id},文档document_id:{dify_document_id},创建失败,失败状态:{indexing_status}")

        if indexing_status == "completed":
            LogUtils.info(
                f"知识库dataset_id:{dataset_id},文档document_id:{dify_document_id},文档创建成功状态:{indexing_status}")

        # 知识库绑定文档
        dataset = self.db.query(Dataset).filter(
            Dataset.document_id == dify_document_id,
            Dataset.dataset_id == dataset_id
        ).first()

        if not dataset:
            # 创建/更新dataset记录
            role = user.get("role")
            dataset = Dataset(
                user=role,
                agent_id=context.get("agent_id"),
                dataset_id=dataset_id,
                document_id=dify_document_id
            )
            self.db.add(dataset)
            self.db.commit()
            self.db.flush()  # 获取生成的dataset ID
        return dataset

    async def push_segment_to_document(self, dataset_id: str, document_id: str, document_data: Dict[str, Any],
                                       segment_id: str = None):
        """
        上传文档到知识库
        """
        return ''

    async def list_datasets(self, agent_id: str, user_context: Dict[str, Any],
                            page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取知识库列表
        
        Args:
            agent_id: 智能体ID
            user_context: 用户上下文
            limit: 返回数量限制
            
        Returns:
            知识库列表
        """
        """
        获取知识库列表 - 文秘专用实现

        Args:
            agent_id: 智能体ID
            user_context: 用户上下文
            is_personal: 是否只返回个人知识库
            limit: 返回数量限制

        Returns:
            知识库列表
        """
        try:
            role = user_context.get("role")
            # 关联dataset表进行联合过滤
            query = self.db.query(DocFeature).join(
                Dataset,
                DocFeature.local_dataset_id == Dataset.id
            ).filter(
                Dataset.agent_id == agent_id,  # 添加agent_id过滤条件
                Dataset.user == role
            )
            total_count = query.count()
            # 添加分页
            skip = (page - 1) * limit
            docs = query.order_by(DocFeature.id.desc()).offset(skip).limit(limit).all()
            # 根据 total_count 和 page,limit 计算是否还有下一页
            has_more = total_count > (page * limit)

            if docs:
                return {
                    "has_more": has_more,
                    "datasets": [{"id": doc.id, "doc_name": doc.doc_name} for doc in docs]
                }
            return {"has_more": False, "datasets": []}
        except Exception as e:
            LogUtils.error(f"获取文秘知识库列表失败: {str(e)}")
            raise RuntimeError(f"获取知识库列表失败: {str(e)}")


class DifyQueryProcessingHandler(QueryProcessingHandler):
    """基于Dify的查询处理器实现"""

    def __init__(self, dify_service: Any, agent_config: Dict[str, Any]):
        self.dify_service = dify_service
        self.agent_config = agent_config

    async def generate_answer(self,
                              query: str,
                              context: List[Dict[str, Any]],
                              user_profile: Dict[str, Any],
                              agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成回答
        
        Args:
            query: 用户查询
            context: 上下文信息
            user_profile: 用户信息
            agent_config: 智能体配置
            
        Returns:
            生成的回答
        """
        try:
            # 准备提示词
            prompt_template = agent_config.get("prompt_template", "")
            system_prompt = agent_config.get("system_prompt", "")

            # 调用Dify服务生成回答
            response = await self.dify_service.generate_answer(
                query=query,
                context=context,
                prompt_template=prompt_template,
                system_prompt=system_prompt,
                model_name=agent_config.get("model_name", ""),
                temperature=agent_config.get("temperature", "0.7")
            )

            return {
                "text": response.get("text", ""),
                "sources": response.get("sources", []),
                "metadata": response.get("metadata", {})
            }
        except Exception as e:
            LogUtils.error(f"生成回答失败: {str(e)}")
            return {"text": f"生成回答失败: {str(e)}"}

    async def plan_actions(self,
                           query: str,
                           user_profile: Dict[str, Any],
                           agent_config: Dict[str, Any]) -> List[str]:
        """
        规划执行的动作序列
        
        Args:
            query: 用户查询
            user_profile: 用户信息
            agent_config: 智能体配置
            
        Returns:
            动作名称列表
        """
        # 简单实现，根据查询内容决定执行哪些动作
        actions = ["document_retrieval", "text_generation"]

        # 如果查询中包含特定关键词，可以添加特定动作
        query_lower = query.lower()
        if "法律" in query_lower or "法规" in query_lower:
            actions.insert(1, "legal_citation")

        return actions

    async def execute_action(self,
                             action_name: str,
                             params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行指定动作
        
        Args:
            action_name: 动作名称
            params: 动作参数
            
        Returns:
            动作执行结果
        """
        # 这个方法在当前架构中可能不会直接使用，因为我们使用了Action类
        # 但保留此方法以满足接口要求
        return {"message": f"动作 {action_name} 未实现"}

    async def generate_answer_stream(self,
                                     query: Dict[str, Any],
                                     global_context: Dict[str, Any],
                                     dataset_context: str,
                                     user_profile: Dict[str, Any],
                                     agent_config: Dict[str, Any]) -> AsyncIterable[str]:
        try:

            # 调用Dify服务生成回答流，传递完整的查询字典
            async for response in self.dify_service.generate_answer_stream(
                    dataset_context=dataset_context,
                    input=global_context.get("chatflow_params", {}),
                    conversation_id=global_context.get("conversation_id"),
                    session_id=global_context.get("session_id"),
                    query_dict=query
            ):
                yield response
        except Exception as e:
            LogUtils.error(f"生成回答流失败: {str(e)}")
            yield {"error": f"生成回答流失败: {str(e)}"}


class DifySessionHandler(SessionHandler):
    """基于数据库的会话管理器实现"""

    def __init__(self, db: Session, dify_service: DifyService, agent_id: str):
        self.db = db
        self.agent_id = agent_id
        self.dify_service = dify_service

    async def start_new_session(self, user_id: int, agent_id: str) -> str:
        """
        开始新会话
        
        Args:
            user_id: 用户ID
            agent_id: 智能体ID
            
        Returns:
            会话ID
        """
        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 创建欢迎消息
        welcome_message = ChatAgentsMsg(
            agent_id=str(agent_id),
            user_id=str(user_id),
            msg_session_id=session_id,
            create_time=datetime.now(),
        )

        # 保存到数据库
        self.db.add(welcome_message)
        self.db.commit()

        return session_id

    async def clear_session_context(self, session_id: str) -> bool:
        """
        清除会话上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否成功
        """
        try:
            # 这里可以实现清除会话上下文的逻辑
            # 例如，可以删除所有消息，或者添加一个特殊的清除上下文消息

            # 添加一个系统消息表示上下文已清除
            system_message = ChatAgentsMsg(
                agent_id=str(self.agent_id),  # agent_id现在是字符串
                user_id="0",  # 系统用户, 现在是字符串
                msg_session_id=session_id,  # 使用msg_session_id替代session_id
                create_time=datetime.now()  # 使用create_time替代created_at
            )

            self.db.add(system_message)
            self.db.commit()

            return True
        except Exception as e:
            LogUtils.error(f"清除会话上下文失败: {str(e)}")
            return False

    def _query_agent_messages(self, user_id: int) -> Query[Type[ChatAgentsMsg]]:
        """
        查询会话消息

        Args:
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            查询对象
        """
        return self.db.query(ChatAgentsMsg).filter(
            ChatAgentsMsg.agent_id == self.agent_id,
            ChatAgentsMsg.user_id == user_id
        )

    async def get_recent_history(self, conversation_id: str, user_id: int, first_id: str = None, limit: int = 10) -> \
            Tuple[bool, List[Dict[str, Any]]]:
        """
        获取最近的会话历史
        
        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            limit: 限制数量
            
        Returns:
            会话历史列表
        """
        # 查询最近的消息
        msg_session_id = self._query_agent_messages(user_id).first().msg_session_id

        return await self.dify_service.get_messages(user_id=msg_session_id, conversation_id=conversation_id,
                                                    limit=limit, first_id=first_id)

    async def add_message_to_history(self,
                                     session_id: str,
                                     message_data: Dict[str, Any]) -> bool:
        """
        添加消息到历史
        
        Args:
            session_id: 会话ID
            message_data: 消息数据
            
        Returns:
            是否成功
        """
        try:
            # 创建消息对象
            message = ChatAgentsMsg(
                agent_id=str(self.agent_id),  # agent_id现在是字符串
                user_id=str(message_data.get("user_id", "0")),  # user_id现在是字符串
                msg_session_id=session_id,  # 使用msg_session_id替代session_id
                create_time=datetime.now()  # 使用create_time替代created_at
            )

            # 保存到数据库
            self.db.add(message)
            self.db.commit()

            return True
        except Exception as e:
            LogUtils.error(f"添加消息到历史失败: {str(e)}")
            return False

    async def get_conversations(self,
                                session_id: str,
                                limit: int = 20,
                                last_id: str = None
                                ) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        获取用户的会话列表
        
        Args:
            session_id: 会话ID
            limit: 限制数量
            
        Returns:
            会话列表
        """
        return await self.dify_service.get_conversations(user_id=session_id, limit=limit, last_id=last_id)

    async def update_conversation_name(self,
                                       conversation_id, name, user_id):

        """
        修改会话名称

        Args:
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        return await self.dify_service.update_conversation_name(conversation_id=conversation_id, name=name,
                                                                user_id=user_id)

    async def file_upload(self,
                          document_data: Dict[str, Any],
                          session_id: str):
        return await self.dify_service.upload_file(document_data=document_data, session_id=session_id)
