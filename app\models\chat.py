from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, Text, JSON, func
from sqlalchemy.orm import DeclarativeBase
from typing import Dict, Any

class ChatBase(DeclarativeBase):
    """聊天相关模型的基类"""
    pass

class ChatAgent(ChatBase):
    """聊天智能体模型"""
    __tablename__ = "chat_agents"

    id = Column(Integer, primary_key=True, index=True, comment='唯一标识')
    agent_id = Column(String(100), nullable=False, comment='agent标识')
    api_key = Column(String(100), nullable=False, comment='dify侧流程的api_key')
    org_code = Column(String(50), nullable=True, comment='组织代码')
    name = Column(String(100), nullable=False, comment='agent名称')
    type = Column(String(50), nullable=False, comment='agent类型')
    describe = Column(Text, nullable=True, comment='agent描述')
    
    # 不使用外键关联，将通过手动JOIN方式关联
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "api_key": self.api_key,
            "org_code": self.org_code,
            "name": self.name,
            "type": self.type,
            "description": self.describe  # 使用description作为键名，与现有代码保持一致
        }

class ChatAgentsMsg(ChatBase):
    """聊天消息模型"""
    __tablename__ = "chat_agents_msg"

    id = Column(Integer, primary_key=True, index=True, comment='唯一标识')
    agent_id = Column(String(100), nullable=False, comment='agent标识')
    msg_session_id = Column(String(100), nullable=False, index=True, comment='自建构，对应user,用户标识（UUID）')
    user_id = Column(String(100), nullable=False, comment='用户标识')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    
    # 不使用外键关联，将通过手动JOIN方式关联
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "session_id": self.msg_session_id,  # 使用session_id作为键名，与现有代码保持一致
            "user_id": self.user_id,
            "created_at": self.create_time.isoformat() if self.create_time else None  # 使用created_at作为键名
        }

class Dataset(ChatBase):
    """数据集模型"""
    __tablename__ = "dataset"

    id = Column(Integer, primary_key=True, index=True, comment='唯一标识')
    user = Column(String(100), nullable=False, comment='上传用户/用户角色')
    dataset_id = Column(String(100), nullable=False, comment='dify侧知识库id')
    document_id = Column(String(100), nullable=True, comment='dify侧文档id')
    agent_id = Column(String(100), nullable=False, comment='agent标识')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 不使用外键关联，将通过手动JOIN方式关联
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "user": self.user,
            "dataset_id": self.dataset_id,
            "document_id": self.document_id,
            "agent_id": self.agent_id,
            "created_at": self.create_time.isoformat() if self.create_time else None,
            "updated_at": self.update_time.isoformat() if self.update_time else None
        }

class DocFeature(ChatBase):
    """文档特征模型"""
    __tablename__ = "doc_feature"

    id = Column(Integer, primary_key=True, index=True, comment='唯一标识')
    user_id = Column(String(100), nullable=False, comment='用户标识')
    doc_name = Column(String(255), nullable=False, comment='文档名称')
    structure_feature = Column(Text, nullable=True, comment='结构特征 (冗余)')
    writing_style_feature = Column(Text, nullable=True, comment='写作风格特征 (冗余)')
    local_msg_id = Column(Integer, nullable=True, comment='对应消息会话id（chat_agents_msg.id）')
    local_dataset_id = Column(Integer, nullable=True, comment='知识库id (dataset.id)')
    segment_id = Column(String(100), nullable=True, comment='知识库文档分段id')
    
    # 不使用外键关联，将通过手动JOIN方式关联
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.doc_name,
            "structure_feature": self.structure_feature,
            "writing_style_feature": self.writing_style_feature,
            "local_msg_id": self.local_msg_id,
            "local_dataset_id": self.local_dataset_id,
            "segment_id": self.segment_id
        }

class AgentAction(ChatBase):
    """智能体动作模型"""
    __tablename__ = "agent_actions"

    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, nullable=False, comment='关联的智能体ID')
    action_name = Column(String(100), nullable=False, comment='动作名称')
    action_description = Column(Text, nullable=True, comment='动作描述')
    action_type = Column(String(50), nullable=False, comment='动作类型(api/tool/function)')
    action_config = Column(JSON, nullable=False, comment='动作配置')
    is_active = Column(Boolean, default=True, comment='是否激活')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 不使用外键关联，将通过手动JOIN方式关联
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "action_name": self.action_name,
            "action_description": self.action_description,
            "action_type": self.action_type,
            "action_config": self.action_config,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }