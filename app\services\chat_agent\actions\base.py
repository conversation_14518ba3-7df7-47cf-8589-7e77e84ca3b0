"""
聊天智能体动作基础类
"""
from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>, AsyncGenerator, AsyncIterable

from app.services.chat_agent.base import ActionResult
from app.log.log_utils import LogUtils


class Action(ABC):
    """动作接口"""
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> <PERSON><PERSON>[ActionResult, Dict[str, Any]]:
        """
        执行动作
        
        Args:
            context: 上下文信息
            
        Returns:
            执行结果和更新后的上下文
        """
        pass
    
    async def execute_stream(self, context: Dict[str, Any]) -> AsyncIterable[Dict[str, Any]]:
        """
        流式执行动作
        
        Args:
            context: 上下文信息
            
        Yields:
            执行过程中产生的数据块
        """
        # 默认实现：调用非流式方法并一次性返回结果
        result, updated_context = await self.execute(context)
        
        # 只返回结果的内容部分
        if "generated_answer" in updated_context:
            yield {"text_chunk": updated_context["generated_answer"].get("text", "")}
            
        # 返回元数据
        yield {
            "end": True,
            "metadata": {
                "sources": updated_context.get("retrieved_documents", ""),
                "processing_time": updated_context.get("processing_time", 0)
            }
        }


class ActionHandler:
    """动作处理器，实现责任链模式"""
    
    def __init__(self, action: Action):
        self.action = action
        self.next_handler = None
    
    def set_next(self, handler: 'ActionHandler') -> 'ActionHandler':
        """设置下一个处理器"""
        self.next_handler = handler
        return handler
    
    async def handle(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理请求"""
        result, updated_context = await self.action.execute(context)
        
        if result == ActionResult.CONTINUE and self.next_handler:
            return await self.next_handler.handle(updated_context)
        
        return updated_context
        
    async def handle_stream(self, context: Dict[str, Any]) -> AsyncIterable[Dict[str, Any]]:
        """流式处理请求，实现责任链模式"""
        try:
            # 调用当前动作的流式执行方法
            has_content = False
            async for chunk in self.action.execute_stream(context):
                has_content = True
                yield chunk
                
                # 如果是结束标记，检查是否继续链
                if chunk.get("end") and self.next_handler:
                    # 如果结果表明应该继续处理链，则调用下一个处理器
                    # 但需要移除end标记以防止流提前终止
                    if chunk.get("continue_chain", True):
                        next_context = context.copy()
                        next_context.update(chunk.get("context_update", {}))
                        async for next_chunk in self.next_handler.handle_stream(next_context):
                            yield next_chunk
                    break
                    
            # 如果动作没有产生任何内容，且有下一个处理器，则直接传递给下一个
            if not has_content and self.next_handler:
                async for next_chunk in self.next_handler.handle_stream(context):
                    yield next_chunk
                    
        except Exception as e:
            LogUtils.error(f"流式动作执行失败: {str(e)}")
            yield {"error": str(e)}
