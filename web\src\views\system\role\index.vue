<template>
  <div class="bigscreen">
    <PageHeader title="角色管理" subtitle="ROLE MANAGE" />
    <div class="content-wrapper">
      <div class="search-section">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" class="search-form common-search" label-width="120px">
          <div class="search-left">
            <el-form-item label="角色名称" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入角色名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <!-- <el-form-item label="角色编码" prop="code">
              <el-input
                v-model="queryParams.code"
                placeholder="请输入角色编码"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item> -->
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="角色状态"
                clearable
              >
                <el-option label="启用" value="1"></el-option>
                <el-option label="停用" value="0"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item> -->
          </div>
          <el-form-item class="search-right">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

    <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          class="tool-btn"
        >新增</el-button>
        </div>
      </div>
      <div class="table-container">
        <el-table  :data="roleList" @selection-change="handleSelectionChange">
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column label="角色名称" prop="name" :show-overflow-tooltip="true"/>
          <el-table-column label="角色编码" prop="code" :show-overflow-tooltip="true" />
          <el-table-column label="显示顺序" prop="sort" width="160" />
          <el-table-column label="状态" align="center" width="120">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                 active-color="#00ffffbc"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="380">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                class="view-btn"
                @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                class="view-btn"
                @click="handleDelete(scope.row)"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-setting"
                class="view-btn"
                @click="handleMenuAuth(scope.row)"
              >菜单授权</el-button>
              <!-- <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
                <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="handleDataScope" icon="el-icon-circle-check">数据权限</el-dropdown-item>
                  <el-dropdown-item command="handleAuthUser" icon="el-icon-user">分配用户</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown> -->
            </template>
          </el-table-column>
        </el-table>
      
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
  </div>
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body custom-class="screen-dialog">
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入角色编码" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">停用</el-radio>
            <el-radio :label="1">正常</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" class="ensure-btn">确 定</el-button>
        <el-button @click="cancel" class="cancel-btn">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 菜单授权抽屉 -->
    <el-drawer
      title="菜单授权"
      :visible.sync="menuAuthDrawer"
      direction="rtl"
      size="500px"
      custom-class="screen-drawer"
      :before-close="handleMenuAuthClose"
    >
      <div class="drawer-content">
        <div class="drawer-header">
          <span>角色名称：{{ currentRole ? currentRole.name : '' }}</span>
        </div>
        <div class="drawer-toolbar">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="form.menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
        </div>
        <el-tree
          class="tree-border"
          :data="menuOptions"
          show-checkbox
          ref="menuAuth"
          node-key="id"
          :check-strictly="!form.menuCheckStrictly"
          empty-text="加载中，请稍候"
          :props="defaultProps"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ data.name }}</span>
            <span v-if="data.perms" class="tree-perms">{{ data.perms }}</span>
          </span>
        </el-tree>
      </div>
      <div class="drawer-footer">
        <el-button type="primary" @click="submitMenuAuth" class="ensure-btn">确 定</el-button>
        <el-button @click="handleMenuAuthClose" class="cancel-btn">取 消</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, setRoleMenus, getRoleMenuList } from "@/api/system/role";
import { listMenu } from "@/api/system/menu";
import PageHeader from "@/components/PageHeader.vue";
export default {
  name: "Role",
  components: { PageHeader },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限"
        },
        {
          value: "2",
          label: "自定数据权限"
        },
        {
          value: "3",
          label: "本部门数据权限"
        },
        {
          value: "4",
          label: "本部门及以下数据权限"
        },
        {
          value: "5",
          label: "仅本人数据权限"
        }
      ],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        code: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        status: 1
      },
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "角色名称不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "角色编码不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "显示顺序不能为空", trigger: "blur" }
        ]
      },
      // 是否显示菜单授权抽屉
      menuAuthDrawer: false,
      // 当前选中的角色
      currentRole: null,
      // 菜单选项
      menuOptions: [],
      // 菜单展开/折叠
      menuExpand: false,
      // 菜单全选/全不选
      menuNodeAll: false,
      // 菜单父子联动
      menuCheckStrictly: true,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listRole(this.queryParams).then(response => {
          this.roleList = response.data.items;
          this.total = response.data.total;
          this.loading = false;
        }
      ).catch(err=>{
        console.log(err)
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$confirm('确认要"' + text + '""' + row.name + '"角色吗？').then(function() {
          return updateRole({...row})
      }).then(() => {
        this.$message.success(text + "成功");
      }).catch(function() {
        row.status = row.status === 1 ? 0 : 1;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.deptExpand = true,
      this.deptNodeAll = false,
      this.form = {
        id: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        status: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == 'dept') {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == 'menu') {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menuAuth.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == 'dept') {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);
      } else if (type == 'menu') {
        this.$refs.menuAuth.setCheckedNodes(value ? this.menuOptions: []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type == 'dept') {
        this.form.deptCheckStrictly = value ? true: false;
      } else if (type == 'menu') {
        this.form.menuCheckStrictly = value ? true: false;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.id || this.ids
      getRole(roleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改角色";
      }).catch(err=>{
        console.log(err)
      });
    },
    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if(value !== '2') {
        this.$refs.dept.setCheckedKeys([]);
      }
    },


    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateRole(this.form).then(response => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            }).catch(err=>{
              console.log(err)
            });
          } else {
            addRole(this.form).then(response => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
            }).catch(err=>{
              console.log(err)
            });
          }
        }
      });
    },
  
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.id || this.ids;
      this.$confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
        return delRole(roleIds);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      }).catch(err=>{
        console.log(err)
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/role/export', {
        ...this.queryParams
      }, `role_${new Date().getTime()}.xlsx`)
    },
    /** 菜单授权按钮操作 */
    handleMenuAuth(row) {
      this.currentRole = row;
      this.menuAuthDrawer = true;
      this.loading = true;
      this.menuExpand = true;  // 设置默认展开状态
      
      // 获取所有菜单
      listMenu().then(response => {
        this.menuOptions = this.handleTree(response.data, "id");
        // 获取角色已有菜单权限
        getRoleMenuList(row.id).then(res1 => {
          this.$nextTick(() => {
            let res = res1.data;
            if (res.check_code && res.check_code.length > 0) {
              res.check_code.forEach((v) => {
                this.$nextTick(() => {
                  this.$refs.menuAuth.setChecked(v, true, false);
                });
              });
            }
            // 设置树默认展开
            this.handleCheckedTreeExpand(true, 'menu');
            this.loading = false;
          });
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 关闭菜单授权抽屉 */
    handleMenuAuthClose() {
      this.menuAuthDrawer = false;
      this.currentRole = null;
      this.menuOptions = [];
      this.menuExpand = false;
      this.menuNodeAll = false;
      this.menuCheckStrictly = true;
      if (this.$refs.menuAuth) {
        this.$refs.menuAuth.setCheckedKeys([]);
      }
    },
    /** 提交菜单授权 */
    submitMenuAuth() {
      if (!this.currentRole) return;
      this.loading = true;
      
      const menuIds = this.$refs.menuAuth.getCheckedKeys();
      const halfCheckedKeys = this.$refs.menuAuth.getHalfCheckedKeys();
      menuIds.unshift.apply(menuIds, halfCheckedKeys);
      
      const data = {
        role_id: this.currentRole.id,
        menu_ids: menuIds
      };
      
      setRoleMenus(data).then(response => {
        this.$message.success("菜单授权成功");
        this.handleMenuAuthClose();
        this.loading = false;
      }).catch(err => {
        console.log(err);
        this.loading = false;
      });
    },
    /** 转换菜单数据结构 */
    handleTree(data, id) {
      const map = {};
      const result = [];

      // 构建映射表
      data.forEach(item => {
        map[item[id]] = {
          ...item,
          children: []
        };
      });

      // 构建树形结构
      data.forEach(item => {
        const node = map[item[id]];
        if (item.parent_id === 0 || !map[item.parent_id]) {
          result.push(node);
        } else {
          map[item.parent_id].children.push(node);
        }
      });

      return result;
    },
  }
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 20px;
  .drawer-header {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
  }
  .drawer-toolbar {
    margin-bottom: 10px;
    .el-checkbox {
      margin-right: 15px;
    }
  }
  .tree-border {
    margin-top: 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 10px;
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 8px;
      .tree-perms {
        color: #999;
        font-size: 12px;
      }
    }
  }
}
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e6e6e6;
  text-align: right;
}



.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

</style>