<template>
  <el-dialog
    title="选择月份范围"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    custom-class="month-range-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="prompt-title">请选择同步月份范围</div>
      <el-date-picker
        v-model="dateRange"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        value-format="yyyyMM"
        :picker-options="{
        }"
      ></el-date-picker>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定同步</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'MonthRangeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dateRange: []
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
        if (!val) {
          this.dateRange = []
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.dateRange = []
      this.$emit('close')
    },
    handleCancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    handleConfirm() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning('请选择月份范围')
        return
      }
      this.$emit('confirm', this.dateRange)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 20px 0;
}

.prompt-title {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

:deep(.month-range-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}

:deep(.month-range-dialog .el-dialog__header) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.month-range-dialog .el-dialog__title) {
  color: #00ffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

:deep(.month-range-dialog .el-dialog__body) {
  padding: 20px !important;
  background: rgba(0, 24, 51, 0.4) !important;
}

:deep(.month-range-dialog .el-dialog__footer) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.6) 0%, rgba(0, 48, 102, 0.4) 100%) !important;
  border-top: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 0 0 8px 8px !important;
}

:deep(.el-date-editor) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-date-editor .el-input__inner) {
  background-color: transparent !important;
  border: none !important;
  color: #ffffff !important;
}

:deep(.el-date-editor .el-range-separator) {
  color: #ffffff !important;
}

:deep(.el-date-editor .el-range__icon),
:deep(.el-date-editor .el-range__close-icon) {
  color: #ffffff !important;
  line-height: 24px !important;
}

:deep(.el-date-editor:hover) {
  border-color: #00ffff !important;
}

:deep(.el-date-editor.is-focus) {
  border-color: #00ffff !important;
}

:deep(.el-date-editor .el-range__close-icon:hover) {
  color: #00ffff !important;
}

/* 月份选择器样式 */
:deep(.el-date-picker) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-date-picker__header) {
  color: #ffffff !important;
}

:deep(.el-date-picker__header button) {
  color: #ffffff !important;
}

:deep(.el-date-picker__header button:hover) {
  color: #00ffff !important;
}

:deep(.el-date-picker__content) {
  background-color: rgba(0, 48, 102, 0.8) !important;
}

:deep(.el-date-table th) {
  color: #ffffff !important;
}

:deep(.el-date-table td) {
  color: #ffffff !important;
}

:deep(.el-date-table td.available:hover) {
  color: #00ffff !important;
}

:deep(.el-date-table td.current:not(.disabled) span) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-date-table td.in-range div) {
  background-color: rgba(0, 255, 255, 0.1) !important;
}

:deep(.el-date-table td.start-date span),
:deep(.el-date-table td.end-date span) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-date-table td.today) {
  color: #00ffff !important;
}

:deep(.el-date-table td.disabled) {
  color: rgba(255, 255, 255, 0.3) !important;
}

:deep(.el-date-picker__footer) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-top: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-picker-panel__footer .el-button) {
  background: rgba(0, 255, 255, 0.2) !important;
  border-color: #00ffff !important;
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button:hover) {
  background: rgba(0, 255, 255, 0.3) !important;
  border-color: #00ffff !important;
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button--text) {
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button--text:hover) {
  color: #ffffff !important;
}
:deep(.el-range-input) {
  background: none !important;
  color: #ffffff !important;
}
</style> 