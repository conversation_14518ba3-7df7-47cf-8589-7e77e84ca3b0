import chatApi from "@/api/chat";
import {
  fetchConversations as fetchConversationsApi,
  fetchConversationHistory as fetchConversationHistoryApi,
  get_session as get_sessionApi,
  create_session as create_sessionApi,
  get_conversation_list as get_conversation_listApi,
  get_conversation_history,
} from "@/api/chat";

// 聊天模块
const state = {
  conversations: [], // { id, name, messages: [{ id, content, role, created_at }] }
  currentConversationId: null,
  loading: false,
  error: null,
  hasMore: false,
  lastId: null,
  historyHasMore: false,
  agent_id: "6f7c4966-17d4-4630-b47d-a51d67c10909",
  session_id: null,
};

const mutations = {
  SET_HISTORY_HAS_MORE(state, hasMore) {
    state.historyHasMore = hasMore;
  },
  SET_HAS_MORE(state, hasMore) {
    state.hasMore = hasMore;
  },
  SET_LAST_ID(state, lastId) {
    state.lastId = lastId;
  },
  SET_CONVERSATIONS(state, { conversations, hasMore, append }) {
    if (append) {
      // 追加模式：将新数据追加到现有数据后面
      state.conversations = [...state.conversations, ...conversations];
    } else {
      // 非追加模式：直接替换数据
      state.conversations = conversations;
    }
    state.hasMore = hasMore;
  },
  SET_CURRENT_CONVERSATION(state, id) {
    state.currentConversationId = id;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_ERROR(state, error) {
    state.error = error;
  },
  ADD_MESSAGE(state, { conversationId, message }) {
    // console.log('ADD_MESSAGE mutation called:', { conversationId, message });
    // console.log('Current state:', JSON.stringify(state, null, 2));

    // 找到或创建会话
    let convIndex = state.conversations.findIndex(c => c.id === conversationId);
    if (convIndex === -1) {
      // console.log('Creating new conversation:', conversationId);
      state.conversations.push({
        id: conversationId,
        name: "新对话",
        messages: [],
      });
      convIndex = state.conversations.length - 1;
    }

    // 确保 messages 数组存在
    if (!state.conversations[convIndex].messages) {
      state.conversations[convIndex].messages = [];
    }

    // 创建新的会话对象
    const newConv = { ...state.conversations[convIndex] };

    // 如果是用户消息，直接添加
    if (message.role === "user") {
      // console.log('Adding user message');
      newConv.messages = [...newConv.messages, { ...message }];
    }
    // 如果是 AI 消息，更新最后一条消息
    else if (message.role === "assistant") {
      // console.log('Processing AI message');
      const lastMessage = newConv.messages[newConv.messages.length - 1];
      if (lastMessage && lastMessage.role === "assistant" && lastMessage.id === message.id) {
        // console.log('Updating existing AI message');
        newConv.messages = [...newConv.messages.slice(0, -1), { ...message }];
      } else {
        // console.log('Adding new AI message');
        newConv.messages = [...newConv.messages, { ...message }];
      }
    }

    // 更新会话
    state.conversations = [
      ...state.conversations.slice(0, convIndex),
      newConv,
      ...state.conversations.slice(convIndex + 1),
    ];

    // 如果是新会话，设置为当前会话
    if (state.currentConversationId !== conversationId) {
      state.currentConversationId = conversationId;
    }

    // console.log('Updated state:', JSON.stringify(state, null, 2));
  },
  UPDATE_CONVERSATION_NAME(state, { id, name }) {
    const conversation = state.conversations.find(c => c.id === id);
    if (conversation) {
      conversation.name = name;
    }
  },
  SET_SESSION_ID(state, session_id) {
    state.session_id = session_id;
  },
  RESET_STORE(state) {
    state.conversations = [];
    state.currentConversationId = null;
    state.loading = false;
    state.error = null;
    state.hasMore = false;
    state.lastId = null;
    state.historyHasMore = false;
    state.session_id = null;
  },
};

const actions = {
  async fetchConversations({ commit, state, dispatch }, params) {
    const { agent_id, session_id } = state;
    // try {
    //   commit("SET_LOADING", true);
    //   const response = await fetchConversationsApi(params);
    //   commit("SET_CONVERSATIONS", {
    //     conversations: response.data.data,
    //     hasMore: response.data.has_more,
    //     append: params.append,
    //   });
    //   // commit('SET_HAS_MORE', response.data.has_more);
    //   commit("SET_LAST_ID", response.data.conversations[response.data.conversations.length - 1].id);
    // } catch (error) {
    //   commit("SET_ERROR", error.message);
    // } finally {
    //   commit("SET_LOADING", false);
    // }
    console.log("fetchConversations");
    try {
      commit("SET_LOADING", true);
      let sessionIdRes = session_id;
      if (!sessionIdRes) {
        sessionIdRes = await dispatch("get_session");
      }
      const response = await get_conversation_listApi(agent_id, sessionIdRes, params);
      commit("SET_CONVERSATIONS", {
        conversations: response.data.sessions,
        hasMore: response.data.has_more,
        append: params.append,
      });
      // commit('SET_HAS_MORE', response.data.has_more);
      commit("SET_LAST_ID", response.data.conversations[response.data.conversations.length - 1].id);
    } catch (error) {
      commit("SET_ERROR", error.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async setCurrentConversation({ commit }, id) {
    commit("SET_CURRENT_CONVERSATION", id);
  },

  async setConversations({ commit }, { conversations, hasMore, append }) {
    commit("SET_CONVERSATIONS", { conversations, hasMore, append });
  },

  async addMessage({ commit }, { conversationId, message }) {
    commit("ADD_MESSAGE", { conversationId, message });
  },

  async fetchConversationHistory({ commit, state }, { conversation_id, first_id, append = false }) {
    const { agent_id } = state;
    try {
      commit("SET_LOADING", true);
      const response = await get_conversation_history(agent_id, {
        conversation_id,
        first_id,
        limit: 20,
      });
      // 区分 user/assistant 消息
      const messages = response.data.history
        .flatMap(msg => {
          return [
            msg.answer
              ? {
                  id: msg.id + "_a",
                  content: msg.answer,
                  role: "assistant",
                  created_at: msg.created_at * 1000,
                }
              : null,
            {
              id: msg.id + "_q",
              content: msg.query,
              message_files: msg.message_files,
              role: "user",
              created_at: msg.created_at * 1000,
            },
          ];
        })
        .filter(Boolean)
        .slice()
        .reverse();

      // 更新是否有更多历史消息的状态
      commit("SET_HISTORY_HAS_MORE", response.data.has_more);
      console.log("response.data.has_more", response.data.has_more);
      // 找到会话并更新消息
      const convIndex = state.conversations.findIndex(c => c.id === conversation_id);
      if (convIndex !== -1) {
        const conv = state.conversations[convIndex];
        if (append) {
          // 追加模式：将新消息添加到现有消息前面
          const updatedMessages = [...messages, ...conv.messages];
          // 创建新的会话对象以触发响应式更新
          const updatedConv = {
            ...conv,
            messages: updatedMessages,
          };
          // 使用数组替换方式更新
          state.conversations = [
            ...state.conversations.slice(0, convIndex),
            updatedConv,
            ...state.conversations.slice(convIndex + 1),
          ];
          console.log("追加模式", updatedMessages);
        } else {
          // 覆盖模式：直接替换消息
          const updatedConv = {
            ...conv,
            messages: messages,
          };
          state.conversations = [
            ...state.conversations.slice(0, convIndex),
            updatedConv,
            ...state.conversations.slice(convIndex + 1),
          ];
        }
      }
      console.log("会话ID", conversation_id);
      commit("SET_CURRENT_CONVERSATION", conversation_id);
    } catch (error) {
      commit("SET_ERROR", error.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async createConversation({ commit, dispatch }, name) {
    try {
      commit("SET_LOADING", true);
      await dispatch("fetchConversations");
      commit("SET_CURRENT_CONVERSATION", response.data.id);
    } catch (error) {
      commit("SET_ERROR", error.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async deleteConversation({ commit, dispatch }, id) {
    try {
      commit("SET_LOADING", true);
      await dispatch("fetchConversations");
    } catch (error) {
      commit("SET_ERROR", error.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async renameConversation({ commit }, { id, name }) {
    try {
      commit("SET_LOADING", true);
      // 直接更新本地状态
      commit("UPDATE_CONVERSATION_NAME", { id, name });
    } catch (error) {
      commit("SET_ERROR", error.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  // async fetchConversationHistory({ commit }, conversation) {
  //   let conversationId = conversation.conversation_id;
  //   console.log('会话ID',conversationId)
  //   console.log('会话数据',state.conversations)
  //   try {
  //     commit('SET_LOADING', true);
  //     const response = await chatApi.getConversationHistory(conversation);
  //     const conv = state.conversations.find(c => c.id === conversationId);
  //     if (conv) {
  //       conv.messages = response.data;
  //     }
  //     console.log('会话数据2',conv)
  //   } catch (error) {
  //     commit('SET_ERROR', error.message);
  //   } finally {
  //     commit('SET_LOADING', false);
  //   }
  // }
  async get_session({ commit, state }) {
    const { agent_id } = state;
    try {
      const res = await get_sessionApi(agent_id);
      const session_id = res.data.msg_session_id;
      commit("SET_SESSION_ID", session_id);
      if (session_id) {
        commit("SET_SESSION_ID", session_id);
        return session_id;
      } else {
        const res2 = await create_sessionApi(agent_id);
        commit("SET_SESSION_ID", res2.data.session_id);
        return res2.data.session_id;
      }
    } catch (error) {
      commit("SET_ERROR", error.message);
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

const getters = {
  conversations: state => state.conversations,
  currentConversation: state =>
    state.conversations.find(c => c.id === state.currentConversationId) || { messages: [] },
  currentConversationId: state => state.currentConversationId,
  loading: state => state.loading,
  error: state => state.error,
  historyHasMore: state => state.historyHasMore,
  agent_id: state => state.agent_id,
  session_id: state => state.session_id,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
