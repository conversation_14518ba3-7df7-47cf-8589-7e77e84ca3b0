<template>
  <div class="ai-chat-container">
    <!-- 固定图标 -->
    <div class="chat-icon" @mousedown.stop="startIconDrag" @touchstart.stop="startIconDragTouch"
      :class="{ 'is-dragging': isIconDragging }" :style="iconStyle">
      <img :src="require('@/assets/images/ai-logo.png')" alt="AI Chat" class="chat-icon-img" />
    </div>

    <!-- 聊天框 -->
    <transition name="chat-box">
      <div v-if="isOpen" class="chat-box" :class="{ 'is-maximized': isMaximized }" :style="chatBoxStyle"
        @mousedown.self="startDrag">
        <div class="chat-header" @mousedown="startDrag">
          <span>{{ currentModuleName }}</span>
          <div class="header-controls" @mousedown.stop>
            <i class="el-icon-minus" @click="minimizeChat"></i>
            <i :class="isMaximized ? 'el-icon-copy-document' : 'el-icon-full-screen'" @click="toggleMaximize"></i>
            <i class="el-icon-close" @click="toggleChatBox"></i>
          </div>
        </div>

        <!-- 功能模块选择界面 -->
        <div v-if="!showChat" class="module-selection">
          <div class="module-grid">
            <div v-for="module in modules" :key="module.id"
              :class="['module-item', { active: selectedModule === module.id }]" @click="selectModule(module)">
              <i :class="module.icon"></i>
              <span>{{ module.name }}</span>
              <span class="subtitle">{{ module.subtitle }}</span>
            </div>
          </div>
        </div>

        <!-- 对话内容区域 -->
        <div v-else class="chat-messages allow-select" ref="messageContainer" @scroll="handleScroll">
          <div v-for="(message, index) in messages" :key="index"
            :class="['message', message.type === 'user' ? 'user-message' : 'ai-message']">
            <!-- 如果是AI消息且有 <think> 标签 -->
            <div v-if="
              (message.type === 'ai' || message.type === 'ai-end') &&
              message.content.includes('<think') &&
              message.content.includes('</think>')
            ">
              <!-- 提取 <think> 标签的内容 -->
              <div class="think-container">
                <div class="think-header" @click="toggleThink(message)">
                  <span>已进行深度思考</span>
                  <button class="toggle-button">{{ message.thinkExpanded ? "▲" : "▼" }}</button>
                </div>
                <div v-if="message.thinkExpanded" class="think-content allow-select">
                  <div class="think-text" v-html="parseMarkdown(
                    message.content
                      .match(/<think\b[^>]*>([\s\S]*?)<\/think>/gi)?.[0]
                      .replace(/<think\b[^>]*>([\s\S]*?)<\/think>/gi, '$1') || ''
                  )
                    "></div>
                </div>
              </div>

              <!-- 提取 </think> 后的内容 -->
              <div class="message-content allow-select">
                <div v-html="parseMarkdown(
                  message.content.replace(/<think\b[^>]*>([\s\S]*?)<\/think>/gi, '')
                )
                  "></div>
              </div>
            </div>

            <!-- 如果是进度提示消息 -->
            <div v-else-if="message.content.startsWith('AI正在')">
              <div class="message-content allow-select">
                <div class="progress-indicator">
                  {{ message.content.substring(0, message.content.lastIndexOf(".")) }}
                  <span class="dynamic-dots">
                    <span class="dot">.</span>
                    <span class="dot">.</span>
                    <span class="dot">.</span>
                  </span>
                </div>
              </div>
            </div>

            <!-- 如果是用户消息或普通AI消息 -->
            <div v-else>
              <div class="message-content allow-select" v-html="parseMarkdown(message.content)"></div>
            </div>
          </div>
          <div v-if="isLoading" class="message ai-message">
            <div class="message-content">
              <div class="progress-indicator">
                <span class="typing-indicator">AI正在思考</span>
                <span class="dynamic-dots">
                  <span class="dot">.</span>
                  <span class="dot">.</span>
                  <span class="dot">.</span>
                </span>
              </div>
            </div>
          </div>
          <!-- 添加跳转到底部的按钮 -->
          <div v-show="showScrollBottom" class="scroll-bottom-btn" @click="scrollToBottom">
            <i class="el-icon-bottom"></i>
          </div>
          <!-- 添加开启新对话按钮 -->
          <div class="new-chat-btn" @click="startNewChat">
            <i class="el-icon-plus"></i>
            <span>开启新对话</span>
          </div>
        </div>

        <div class="chat-input">
          <el-input v-model="inputMessage" type="textarea" :rows="3" :placeholder="inputPlaceholder"
            @keyup.enter.native.exact="sendMessage" @keyup.enter.native.shift="newline"></el-input>
          <el-button type="primary" @click="sendMessage" :loading="isLoading">
            <i class="el-icon-position" v-if="!isLoading"></i>
          </el-button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { chatWithAI, chatNl2sqlFlow, chatNl2sql, chatOnly } from "@/api/chat";
import MarkdownIt from "markdown-it";
import * as echarts from "echarts";
import mdEcharts from "markdown-it-echarts";

export default {
  name: "AIChatBox",
  data() {
    return {
      isOpen: false,
      isMinimized: false,
      isMaximized: false,
      messages: [],
      messagesByModule: {
        criminal: [],
        ai_chat: [],
        nl2sql: []
      },
      inputMessage: "",
      isLoading: false,
      isNewMessage: true, // 标记是否是新消息
      currentAiMessage: "",
      md: new MarkdownIt({ html: true }).use(mdEcharts),
      conversationIds: {
        criminal: "",
        ai_chat: "",
        nl2sql: ""
      },
      activeReader: null, // 当前活动的Reader对象
      position: {
        x: 0,
        y: 0,
      },
      transform: {
        x: 0,
        y: 0,
      },
      iconPosition: {
        x: 20, // 初始右侧距离
        y: 20, // 初始底部距离
      },
      iconTransform: {
        x: 0,
        y: 0,
      },
      isIconDragging: false,
      iconDragOffset: {
        x: 0,
        y: 0,
      },
      isDragging: false,
      dragOffset: {
        x: 0,
        y: 0,
      },
      showScrollBottom: false,
      showChat: false,
      selectedModule: "criminal",
      modules: [
        {
          id: "ai_chat",
          name: "智能对话",
          icon: "el-icon-chat-line-round",
          available: true,
          subtitle: "",
        },
        {
          id: "criminal",
          name: "案件类警情流失专项对话窗口",
          icon: "el-icon-warning",
          available: true,
          subtitle: "",
        },
        // {
        //   id: "nl2sql",
        //   name: "刑事案件辅助",
        //   icon: "el-icon-data-analysis",
        //   available: true,
        //   subtitle: "基于NLP2SQL实现",
        // },
        // {
        //   id: "administrative",
        //   name: "行政案辅助审核",
        //   icon: "el-icon-office-building",
        //   available: false,
        // },
        // {
        //   id: "document",
        //   name: "法律文书核查",
        //   icon: "el-icon-document-checked",
        //   available: false,
        // },
        // {
        //   id: "record",
        //   name: "笔录巡查",
        //   icon: "el-icon-notebook",
        //   available: false,
        // },
      ],
      inputPlaceholder: "请输入案件类警情流失巡查相关内容...",
    };
  },
  computed: {
    currentModuleName() {
      const module = this.modules.find(m => m.id === this.selectedModule);
      return module ? module.name : "AI 助手";
    },
    iconStyle() {
      return {
        right: `${this.iconPosition.x - this.iconTransform.x}px`,
        bottom: `${this.iconPosition.y - this.iconTransform.y}px`,
        transition: this.isIconDragging ? "none" : "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      };
    },
    chatBoxStyle() {
      if (this.isMaximized) {
        return {
          transform: "none",
          left: "0px",
          top: "0px",
        };
      }
      return {
        left: `${this.position.x + this.transform.x}px`,
        top: `${this.position.y + this.transform.y}px`,
        transition: this.isDragging ? "none" : "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      };
    },
  },
  created() {
    // 监听AI消息事件
    window.addEventListener("aiMessage", this.handleAiMessage);
    // 监听窗口大小变化
    window.addEventListener("resize", this.handleResize);

    // 从本地存储中恢复图标位置
    const savedIconPosition = localStorage.getItem("ai-chat-icon-position");
    if (savedIconPosition) {
      try {
        this.iconPosition = JSON.parse(savedIconPosition);
      } catch (e) {
        console.error("恢复图标位置失败:", e);
        this.updateIconPosition(); // 如果恢复失败，使用默认位置
      }
    } else {
      this.updateIconPosition(); // 如果没有保存位置，使用默认位置
    }

    // 其他初始化代码
    let rs = this.md.render("暂未查到信息");
    console.log("转换成功的结果222：" + rs);
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("aiMessage", this.handleAiMessage);
    window.removeEventListener("resize", this.handleResize);
    // 清空会话ID
    this.conversationIds = { criminal: "", ai_chat: "", nl2sql: "" };
  },
  methods: {
    parseMarkdown(text) {
      // console.log(text)
      if (!text) return "";
      text = text.replace(/^```markdown\s*\n([\s\S]+?)\n```$/g, "$1");
      text = this.removeFunctionsFromString(text);
      let rs = this.md.render(text);
      if (rs.includes("SyntaxError")) {
        rs = "暂无信息";
      }

      // 处理echarts图表的渲染
      this.$nextTick(() => {
        // 等待DOM更新完成后再渲染图表
        setTimeout(() => {
          const charts = document.querySelectorAll(".echarts");
          charts.forEach(container => {
            try {
              // 获取图表数据
              const dataElement = container.querySelector(".echarts-data");
              if (!dataElement) {
                console.warn("图表数据元素未找到，跳过渲染");
                return;
              }

              // 设置容器的尺寸
              container.style.width = "100%";
              container.style.height = "400px";
              container.style.marginTop = "10px";
              container.style.marginBottom = "10px";

              // 初始化图表
              const chart = echarts.init(container, "dark");

              // 解析JSON配置
              let options;
              try {
                options = JSON.parse(dataElement.textContent);
              } catch (e) {
                console.error("图表配置解析失败:", e);
                container.innerHTML = '<div class="chart-error">图表配置解析失败</div>';
                return;
              }

              // 设置主题色
              const theme = {
                backgroundColor: "rgba(0, 24, 51, 0.6)",
                textStyle: {
                  color: "#00ffff",
                },
              };

              // 合并配置并设置
              chart.setOption({
                ...theme,
                ...options,
              });

              // 添加响应式调整
              const resizeChart = () => {
                if (chart && !chart.isDisposed()) {
                  chart.resize();
                }
              };

              // 使用防抖处理resize事件
              let resizeTimer = null;
              window.addEventListener("resize", () => {
                if (resizeTimer) {
                  clearTimeout(resizeTimer);
                }
                resizeTimer = setTimeout(resizeChart, 100);
              });

              // 在组件销毁时清理
              this.$once("hook:beforeDestroy", () => {
                window.removeEventListener("resize", resizeChart);
                if (chart && !chart.isDisposed()) {
                  chart.dispose();
                }
              });
            } catch (error) {
              console.error("图表渲染失败:", error);
              container.innerHTML = '<div class="chart-error">图表渲染失败</div>';
            }
          });
        }, 100); // 给DOM一些时间完成更新
      });

      return rs;
    },
    // 添加柱状图示例
    // ```mermaid
    // bar
    //     title 警情分析统计
    //     xlabel 时间
    //     ylabel 数量
    //     xaxis 1月 2月 3月 4月 5月 6月
    //     series 标红数量
    //         data 120 150 180 200 220 250
    //     series 标黄数量
    //         data 80 100 120 140 160 180
    //     series 标蓝数量
    //         data 60 80 100 120 140 160
    // ```
    toggleThink(message) {
      message.thinkExpanded = !message.thinkExpanded;
    },
    handleAiMessage(event) {
      // 如果聊天框未打开或当前模块不是案件类警情流失巡查，则不处理消息
      if (!this.isOpen || this.selectedModule !== "criminal") {
        return;
      }

      this.currentAiMessage += event.detail.answer;

      // 先移除进度提示消息
      this.removeProgressIndicator();

      // 如果是新消息或没有最后一条消息，添加新消息
      if (this.isNewMessage || !this.findLastNonProgressMessage()) {
        this.addNodeMessage(this.currentAiMessage);
        this.isNewMessage = false; // 标记为非新消息
      } else {
        // 更新最后一条消息
        const lastMessage = this.findLastNonProgressMessage();
        if (lastMessage) {
          lastMessage.content = this.currentAiMessage;
        }
      }

      // 重新添加进度提示消息
      this.addProgressIndicator("AI正在分析您的问题...");
    },
    toggleChatBox() {
      // 如果正在拖动，不执行切换操作
      if (this.isIconDragging) return;

      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.updatePosition();
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } else {
        // 关闭聊天框时清空会话ID
        this.conversationIds = { criminal: "", ai_chat: "", nl2sql: "" };
      }
    },
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return;

      const userMessage = this.inputMessage.trim();
      this.messages.push({
        type: "user",
        content: userMessage,
      });

      this.inputMessage = "";
      this.isLoading = true;
      this.showChat = true;
      this.currentAiMessage = "";
      this.isNewMessage = true; // 标记这是一个新消息

      try {
        let response;
        switch (this.selectedModule) {
          case "criminal":
            response = await chatWithAI(userMessage, this.conversationIds.criminal);
            this.decodeAiRes(response);

            break;
          case "ai_chat":
            response = await chatOnly(userMessage, this.conversationIds.ai_chat);
            this.decodeAiRes(response);
            break;
          case "nl2sql":
            response = await chatNl2sql(userMessage);
            this.decodeNl2sqlRes(response);
            break;
          default:
            throw new Error("未知的功能模块");
        }
      } catch (error) {
        console.error("发送消息失败:", error);
        this.messages.push({
          type: "ai",
          content: "抱歉，发生了一些错误，请稍后重试。",
        });
      } finally {
        this.isLoading = false;
        this.scrollToBottom();
      }
    },
    removeFunctionsFromString(fixedStr) {
      // 1️⃣ 移除 function(){} 和箭头函数 () => {} 及其内容
      fixedStr = fixedStr.replace(
        /:\s*(function\s*\([^)]*\)|\([^)]*\)\s*=>)\s*\{[\s\S]*?\}\s*(?=,|\})/g,
        ': ""'
      );

      // 2️⃣ 处理 "valueFormatter": (value) => value + "%"
      fixedStr = fixedStr.replace(/:\s*\([^)]*\)\s*=>\s*[^,}\n]+/g, ': ""');

      // 3️⃣ 修复 ECharts 可能出现的非法 {{a0}} → {a0}
      fixedStr = fixedStr.replace(/{{(.*?)}}/g, "{$1}");

      // 4️⃣ 修复 JSON key 少引号 (支持 key 含 `-` 和 `_`)
      fixedStr = fixedStr.replace(/(?<=\{|,)\s*([\w-]+)\s*:/g, '"$1":');

      // 5️⃣ 删除 `/* ... */` 块注释 和 `//` 行注释
      fixedStr = fixedStr.replace(/\/\*[\s\S]*?\*\//g, "");
      fixedStr = fixedStr.replace(/(^|\s|[,{\[])\s*\/\/.*/g, "$1");

      // 6️⃣ 修复 `+` 连接的多行字符串
      fixedStr = fixedStr.replace(/"\s*\+\s*"/g, "");

      // 7️⃣ 移除 `,]` 或 `,}`
      fixedStr = fixedStr.replace(/,(\s*[}\]])/g, "$1");

      // 8️⃣ 修复多个 `,,` 变 `,`
      fixedStr = fixedStr.replace(/,,+/g, ",");
      // 增加三个反斜杠
      fixedStr = fixedStr.replace(/(?<!```)\s*(echarts)/g, "```$1");

      return fixedStr;
    },
    async decodeNl2sqlRes(response) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let partialLine = "";
      let accumulatedText = "";
      let thinkingContent = "";
      let hasFinishedWorkflow = false; // 标记工作流是否已完成

      // 先添加一个生成中的消息
      this.addProgressIndicator("AI正在分析您的问题...");

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        partialLine += chunk;

        // 按换行符分割处理
        const lines = partialLine.split("\n");
        partialLine = lines.pop(); // 保存未完成的行

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine) continue;

          if (trimmedLine.startsWith("data: ")) {
            try {
              const jsonData = JSON.parse(trimmedLine.slice(6));
              if (jsonData.event === "workflow_finished") {
                // 标记工作流已完成
                hasFinishedWorkflow = true;
                // 移除所有"AI正在分析您的问题..."消息
                this.removeProgressIndicator();
                // 添加最终结果作为独立消息
                this.isLoading = false;
              }
              // 处理最终完成事件
              if (
                jsonData.event === "workflow_finished" &&
                jsonData.data &&
                jsonData.data.outputs &&
                jsonData.data.outputs.result
              ) {
                // 获取最终结果
                const finalResult = jsonData.data.outputs.result;

                // 标记工作流已完成
                hasFinishedWorkflow = true;

                // 移除所有"AI正在分析您的问题..."消息
                this.removeProgressIndicator();

                // 添加最终结果作为独立消息
                this.isLoading = false; // 先将isLoading设为false以添加新消息
                // this.updateOrAddAIMessage(finalResult)

                // 确保在函数返回前清除提示消息
                this.removeProgressIndicator();
                return;
              }

              // 处理节点完成事件（包含思考过程和结果）
              if (
                jsonData.event === "node_finished" &&
                jsonData.data &&
                jsonData.data.outputs &&
                jsonData.data.outputs.text
              ) {
                // 获取节点内容
                let nodeContent = jsonData.data.outputs.text;

                // 处理包含 <details> 标签的内容
                if (nodeContent.includes("<details") && nodeContent.includes("</details>")) {
                  // 提取思考过程和结果内容
                  const detailsPattern = /<details[^>]*>([\s\S]*?)<\/details>/i;
                  const detailsMatch = nodeContent.match(detailsPattern);

                  if (detailsMatch) {
                    // 思考内容（去除summary标签）
                    let thinkingText = detailsMatch[1];
                    thinkingText = thinkingText.replace(/<summary[^>]*>.*?<\/summary>/i, "").trim();

                    // 结果内容
                    const resultText = nodeContent.replace(detailsPattern, "").trim();

                    // 按照图示格式构建思考过程和结果对
                    if (resultText) {
                      // 如果有结果，创建思考+结果块
                      nodeContent = `<think>${thinkingText}</think>${resultText}`;
                    } else {
                      // 如果没有结果，只有思考过程
                      nodeContent = `<think>${thinkingText}</think>`;
                    }
                  }
                }

                // 暂时将isLoading设为false以添加新消息而不是更新
                const wasLoading = this.isLoading;
                this.isLoading = false;

                // 先移除进度提示（如果有）
                this.removeProgressIndicator();

                // 添加节点内容作为新消息
                this.addNodeMessage(nodeContent);

                // 恢复isLoading状态和进度提示
                this.isLoading = wasLoading;

                // 只有在工作流尚未完成时添加进度提示消息
                if (!hasFinishedWorkflow) {
                  this.addProgressIndicator(this.getProgressText(thinkingContent));
                }
              }

              // 处理文本块事件，只更新进度指示器文本
              if (jsonData.event === "text_chunk" && jsonData.data && jsonData.data.text) {
                // 获取文本内容
                const text = jsonData.data.text;
                thinkingContent += text;

                // 更新进度提示文本，只有在工作流尚未完成时
                if (!hasFinishedWorkflow) {
                  this.updateProgressIndicator(this.getProgressText(thinkingContent));
                }
              }
            } catch (e) {
              console.error("解析响应数据失败:", e);
            }
          }
        }
      }

      // 如果最后有未处理的行
      if (partialLine.trim()) {
        if (partialLine.startsWith("data: ")) {
          try {
            const jsonData = JSON.parse(partialLine.slice(6));

            // 处理最终完成事件
            if (
              jsonData.event === "workflow_finished" &&
              jsonData.data &&
              jsonData.data.outputs &&
              jsonData.data.outputs.result
            ) {
              // 获取最终结果
              const finalResult = jsonData.data.outputs.result;

              // 标记工作流已完成
              hasFinishedWorkflow = true;

              // 移除所有"AI正在分析您的问题..."消息
              this.removeProgressIndicator();

              // 添加最终结果作为独立消息
              this.isLoading = false;
              this.updateOrAddAIMessage(finalResult);

              // 确保在函数返回前清除提示消息
              this.removeProgressIndicator();
              return;
            }

            // 处理节点完成事件
            if (
              jsonData.event === "node_finished" &&
              jsonData.data &&
              jsonData.data.outputs &&
              jsonData.data.outputs.text
            ) {
              // 获取节点内容
              let nodeContent = jsonData.data.outputs.text;

              // 处理包含 <details> 标签的内容
              if (nodeContent.includes("<details") && nodeContent.includes("</details>")) {
                // 提取思考过程和结果内容
                const detailsPattern = /<details[^>]*>([\s\S]*?)<\/details>/i;
                const detailsMatch = nodeContent.match(detailsPattern);

                if (detailsMatch) {
                  // 思考内容（去除summary标签）
                  let thinkingText = detailsMatch[1];
                  thinkingText = thinkingText.replace(/<summary[^>]*>.*?<\/summary>/i, "").trim();

                  // 结果内容
                  const resultText = nodeContent.replace(detailsPattern, "").trim();

                  // 按照图示格式构建思考过程和结果对
                  if (resultText) {
                    // 如果有结果，创建思考+结果块
                    nodeContent = `<think>${thinkingText}</think>${resultText}`;
                  } else {
                    // 如果没有结果，只有思考过程
                    nodeContent = `<think>${thinkingText}</think>`;
                  }
                }
              }

              // 暂时将isLoading设为false以添加新消息
              const wasLoading = this.isLoading;
              this.isLoading = false;

              // 先移除进度提示（如果有）
              this.removeProgressIndicator();

              // 添加节点内容作为新消息
              this.addNodeMessage(nodeContent);

              // 恢复isLoading状态和进度提示
              this.isLoading = wasLoading;

              // 只有在工作流尚未完成时添加进度提示消息
              if (!hasFinishedWorkflow) {
                this.addProgressIndicator(this.getProgressText(thinkingContent));
              }
            }
          } catch (e) {
            console.error("解析最后一行数据失败:", e);
          }
        }
      }

      // 如果工作流没有正常完成，但处理已结束，需要关闭加载状态
      this.isLoading = false;

      // 如果流程已经完成，确保移除提示消息
      if (hasFinishedWorkflow) {
        this.removeProgressIndicator();
      }

    },
    // 获取进度文本
    getProgressText(thinkingContent) {
      let displayText;
      if (thinkingContent.length < 10) {
        displayText = "AI正在思考";
      } else if (thinkingContent.length < 30) {
        displayText = "AI正在分析数据";
      } else if (thinkingContent.length < 50) {
        displayText = "AI正在生成回复";
      } else {
        displayText = "AI正在整合分析结果";
      }
      return displayText;
    },
    // 添加节点消息（不处理进度提示）
    addNodeMessage(content, id = null) {
      this.messages.push({
        type: "ai",
        content: content,
        thinkExpanded: true,
        id: id || Date.now(), // 如果没有提供ID，生成一个新的
      });
      this.scrollToBottom();
    },
    // 添加进度提示消息
    addProgressIndicator(text) {
      // 确保文本末尾有点，以便我们的模板正确处理
      if (!text.endsWith("...")) {
        text = text + "...";
      }

      // 先检查是否已经有进度提示消息
      for (let i = 0; i < this.messages.length; i++) {
        if (this.messages[i].type === "ai" && this.messages[i].content.startsWith("AI正在")) {
          this.messages[i].content = text;
          return;
        }
      }

      // 如果没有进度提示消息，添加一个
      this.messages.push({
        type: "ai",
        content: text,
        thinkExpanded: true,
      });
      this.scrollToBottom();
    },
    // 更新进度提示消息
    updateProgressIndicator(text) {
      let foundIndicator = false;
      for (let i = 0; i < this.messages.length; i++) {
        if (this.messages[i].type === "ai" && this.messages[i].content.startsWith("AI正在")) {
          this.messages[i].content = text;
          foundIndicator = true;
          break;
        }
      }

      // 如果没找到进度提示消息，添加一个
      if (!foundIndicator) {
        this.addProgressIndicator(text);
      }
    },
    // 移除进度提示消息
    removeProgressIndicator() {
      this.messages = this.messages.filter(
        msg => !(msg.type === "ai" && msg.content.startsWith("AI正在"))
      );
    },
    async decodeAiRes(response) {
      // 保存当前模块ID，因为在长时间响应过程中用户可能切换模块
      const currentModule = this.selectedModule;

      const reader = response.body.getReader();
      this.activeReader = reader; // 保存当前活动的reader

      const decoder = new TextDecoder();
      let accumulatedText = "";
      let partialLine = "";
      let thinkingContent = "";
      let messageId = Date.now(); // 为每个消息生成唯一ID
      let hasFinishedWorkflow = false; // 标记工作流是否已完成

      // 先添加一个生成中的消息
      this.addProgressIndicator("AI正在分析您的问题...");

      try {
        while (true) {
          // 检查用户是否已切换模块，如果是则停止处理
          if (this.selectedModule !== currentModule) {
            console.log("用户已切换模块，停止处理消息");
            reader.cancel();
            break;
          }
        
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          partialLine += chunk;

          // 按换行符分割处理
          const lines = partialLine.split("\n");
          partialLine = lines.pop(); // 保存未完成的行

          for (const line of lines) {
            if (!line.trim() || !line.startsWith("data: ")) continue;

            try {
              console.log(line)
              const data = JSON.parse(line.trim().slice(6));
              console.log(data)
              if (data.event === "message" && data.answer) {
                // 检查用户是否已切换模块
                if (this.selectedModule !== currentModule) {
                  console.log("用户已切换模块，停止处理消息");
                  return;
                }

                // 保存会话ID
                if (data.conversation_id) {
                  this.conversationIds[currentModule] = data.conversation_id;
                }

                const content = data.answer;

                // 提取思考内容 - 检测各种<details>格式
                if (this.extractThinkingContent(content, thinkingContent, accumulatedText)) {
                  // 思考内容已提取，内容已累加到thinkingContent和accumulatedText
                  thinkingContent = this.extractThinkingContent(
                    content,
                    thinkingContent,
                    accumulatedText
                  );
                } else {
                  // 普通内容，直接添加到累积文本
                  accumulatedText += content;
                }

                // 更新显示的消息内容
                this.updateMessageContent(messageId, thinkingContent, accumulatedText);
              } else if (data.event === "message_end") {
                // 消息结束，标记工作流已完成
                hasFinishedWorkflow = true;
                this.removeProgressIndicator();
              }
            } catch (e) {
              console.error("解析响应数据失败:", e);
            }
          }
        }

        // 处理最后可能的未完成行
        if (partialLine.trim() && partialLine.startsWith("data: ")) {
          try {
            const data = JSON.parse(partialLine.slice(6));
            if (data.event === "message" && data.answer) {
              // 检查用户是否已切换模块
              if (this.selectedModule !== currentModule) {
                console.log("用户已切换模块，停止处理消息");
                return;
              }

              const content = data.answer;

              // 提取思考内容
              if (this.extractThinkingContent(content, thinkingContent, accumulatedText)) {
                thinkingContent = this.extractThinkingContent(
                  content,
                  thinkingContent,
                  accumulatedText
                );
              } else {
                accumulatedText += content;
              }

              // 更新显示的消息内容
              this.updateMessageContent(messageId, thinkingContent, accumulatedText);
            } else if (data.event === "message_end") {
              hasFinishedWorkflow = true;
              this.removeProgressIndicator();
            }
          } catch (e) {
            console.error("解析最后一行数据失败:", e);
          }
        }
      } catch (error) {
        console.error("处理AI响应时发生错误:", error);
      } finally {
        // 清除当前活动的reader
        this.activeReader = null;

        // 完成处理
        this.isLoading = false;
        if (hasFinishedWorkflow) {
          this.removeProgressIndicator();
        }
      }
    },
    endLastMsg(content) {
      // 先移除进度提示消息
      this.removeProgressIndicator();

      // 获取最后一条非进度消息
      const lastMessage = this.findLastNonProgressMessage();
      if (lastMessage) {
        lastMessage.type = "ai-end";
        lastMessage.content = content;
      } else {
        // 如果没有最后一条消息，则添加一个
        this.messages.push({
          type: "ai-end",
          content: content,
          thinkExpanded: true,
        });
      }

      this.scrollToBottom();
    },
    updateOrAddAIMessage(content, removeProgressIndicator = true) {
      // 设置标志标识消息类型
      const isThinkingContent = content.includes("<think>") && content.includes("</think>");
      const isNl2sql = this.selectedModule === "nl2sql";
      const isCriminal = this.selectedModule === "criminal" || this.selectedModule === "ai_chat";
      const isProgressIndicator = content.startsWith("AI正在") && content.endsWith("...");

      // 处理方式1：如果是进度指示器，使用专门的方法处理
      if (isProgressIndicator) {
        this.updateProgressIndicator(content);
        return; // 直接返回，不执行后续逻辑
      }

      // 如果需要移除进度指示器，先执行移除
      if (removeProgressIndicator) {
        this.removeProgressIndicator();
      }

      // 处理方式2：正在加载中的消息处理
      if (this.isLoading) {
        // 如果是来自 nl2sql 的新消息且不是思考内容，添加为新消息
        if ((isNl2sql && !isThinkingContent) || this.isNewMessage) {
          // 添加新消息
          this.addNodeMessage(content);
          this.isNewMessage = false; // 标记为非新消息

          // 如果需要显示进度指示器，添加在节点消息后面
          if (!removeProgressIndicator) {
            this.addProgressIndicator("AI正在分析您的问题...");
          }
        } else {
          // criminal模式或普通chatWithAI模式，更新最后一条消息
          const lastMessage = this.findLastNonProgressMessage();
          if (lastMessage) {
            lastMessage.content = content;
          } else {
            // 如果没有最后一条消息（首次回复），添加新消息
            this.addNodeMessage(content);
          }

          // 如果需要显示进度指示器，添加在节点消息后面
          if (!removeProgressIndicator) {
            this.addProgressIndicator("AI正在分析您的问题...");
          }
        }
      } else {
        // 处理方式3：生成完成后的消息处理

        // 如果是新消息或nl2sql模式，添加新消息
        if (this.isNewMessage || isNl2sql) {
          this.addNodeMessage(content);
          this.isNewMessage = false; // 标记为非新消息
          return;
        }

        // 对于criminal模式，如果已经有AI消息，则更新它而不是添加新消息
        if (isCriminal) {
          const lastMessage = this.findLastNonProgressMessage();
          if (lastMessage) {
            lastMessage.content = content;
            return;
          }
        }

        // 添加新的完整消息
        this.addNodeMessage(content);
      }
    },

    // 查找最后一条非进度提示的消息
    findLastNonProgressMessage() {
      for (let i = this.messages.length - 1; i >= 0; i--) {
        const msg = this.messages[i];
        if (msg.type === "ai" && !msg.content.startsWith("AI正在")) {
          return msg;
        }
      }
      return null;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messageContainer;
        if (container) {
          container.scrollTop = container.scrollHeight;
        }
      });
    },
    newline(e) {
      e.preventDefault();
      this.inputMessage += "\n";
    },
    minimizeChat() {
      this.isMinimized = !this.isMinimized;
      if (this.isMinimized) {
        this.isMaximized = false;
      }
    },
    toggleMaximize() {
      this.isMaximized = !this.isMaximized;
      if (this.isMaximized) {
        this.isMinimized = false;
        this.lastPosition = { ...this.position };
      } else {
        if (this.lastPosition) {
          this.position = { ...this.lastPosition };
        }
      }

      // 重新渲染echarts图表
      this.$nextTick(() => {
        setTimeout(() => {
          const charts = document.querySelectorAll(".echarts");
          charts.forEach(container => {
            try {
              const chart = echarts.getInstanceByDom(container);
              if (chart && !chart.isDisposed()) {
                chart.resize();
              }
            } catch (error) {
              console.error("图表重新渲染失败:", error);
            }
          });
        }, 500); // 给DOM一些时间完成更新
      });
    },
    startDrag(e) {
      if (e.target.tagName === "TEXTAREA" || this.isMaximized) return;

      const isHeader = e.target.closest(".chat-header");
      const isBox = e.target.closest(".chat-box");
      if (!isHeader && !isBox) return;

      this.isDragging = true;
      this.transform = { x: 0, y: 0 };
      this.dragOffset = {
        x: e.clientX - this.position.x,
        y: e.clientY - this.position.y,
      };

      document.addEventListener("mousemove", this.handleDrag);
      document.addEventListener("mouseup", this.stopDrag);

      document.body.classList.add("no-select");
    },
    handleDrag(e) {
      if (!this.isDragging) return;

      // 获取窗口可视区域的大小
      const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
      const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

      // 聊天框尺寸
      const chatBoxWidth = 600;
      const headerHeight = 60; // 标题栏高度
      const chatBoxHeight = this.isMinimized ? headerHeight : window.innerHeight - 20; // 使用窗口高度减20px

      // 计算新位置
      let newX = e.clientX - this.dragOffset.x;
      let newY = e.clientY - this.dragOffset.y;

      // 严格的边界限制，确保整个对话框都在可视区域内
      newX = Math.min(Math.max(0, newX), viewportWidth - chatBoxWidth);
      newY = Math.min(
        Math.max(0, newY),
        viewportHeight - (this.isMinimized ? headerHeight : chatBoxHeight)
      );

      this.transform = {
        x: newX - this.position.x,
        y: newY - this.position.y,
      };
    },
    stopDrag() {
      if (!this.isDragging) return;

      this.isDragging = false;
      this.position = {
        x: this.position.x + this.transform.x,
        y: this.position.y + this.transform.y,
      };
      this.transform = { x: 0, y: 0 };

      document.removeEventListener("mousemove", this.handleDrag);
      document.removeEventListener("mouseup", this.stopDrag);

      document.body.classList.remove("no-select");
    },
    updatePosition() {
      // 计算窗口可视区域的大小
      const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
      const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

      // 确保图标不超出边界
      const iconSize = 60;
      if (this.iconPosition.x > viewportWidth - iconSize) {
        this.iconPosition.x = viewportWidth - iconSize - 20;
        this.saveIconPosition();
      }
      if (this.iconPosition.y > viewportHeight - iconSize) {
        this.iconPosition.y = viewportHeight - iconSize - 20;
        this.saveIconPosition();
      }

      // 聊天框尺寸
      const chatBoxWidth = 600;
      const chatBoxHeight = this.isMinimized ? 60 : window.innerHeight - 20; // 使用窗口高度减20px

      // 计算居中位置
      this.position = {
        x: Math.max(0, Math.floor((viewportWidth - chatBoxWidth) / 2)),
        y: Math.max(0, Math.floor((viewportHeight - chatBoxHeight) / 2)),
      };
    },
    handleScroll() {
      const container = this.$refs.messageContainer;
      if (!container) return;

      // 当距离底部超过200px时显示按钮
      const { scrollTop, scrollHeight, clientHeight } = container;
      this.showScrollBottom = scrollHeight - scrollTop - clientHeight > 200;
    },
    selectModule(module) {
      if (!module.available) {
        this.$notify({
          title: "功能开发中",
          message: "该功能正在开发中，敬请期待",
          type: "warning",
          position: "top-right",
          duration: 3000,
          offset: 50,
        });
        return;
      }

      // 取消正在进行的请求
      if (this.activeReader) {
        try {
          this.activeReader.cancel("用户切换了模块");
          this.activeReader = null;
        } catch (e) {
          console.error("取消请求失败:", e);
        }
      }

      // 强制设置为非加载状态
      this.isLoading = false;

      // 保存当前模块的消息
      if (this.selectedModule) {
        // 移除当前模块的进度提示消息
        this.messages = this.messages.filter(
          msg => !(msg.type === "ai" && msg.content.startsWith("AI正在"))
        );
        this.messagesByModule[this.selectedModule] = [...this.messages];
      }

      // 切换到新模块
      this.selectedModule = module.id;
      this.inputPlaceholder = `请输入${module.name}相关内容...`;

      // 加载新模块的消息
      this.messages = [...this.messagesByModule[module.id]];

      // 切换模块后不立即显示聊天界面，除非已有消息
      this.showChat = this.messages.length > 0;
    },
    startNewChat() {
      // 取消正在进行的所有请求
      if (this.activeReader) {
        try {
          this.activeReader.cancel("用户开启了新对话");
          this.activeReader = null;
        } catch (e) {
          console.error("取消请求失败:", e);
        }
      }

      // 强制设置为非加载状态
      this.isLoading = false;

      this.showChat = false; // 切回模块选择界面

      // 清空当前模块的消息记录
      this.messages = [];
      this.messagesByModule[this.selectedModule] = [];

      // 清空当前模块的会话ID
      this.conversationIds[this.selectedModule] = "";

      // 重置其他状态
      this.inputMessage = "";
      this.currentAiMessage = "";
      this.isNewMessage = true; // 标记为新消息

      // 更新输入提示
      this.inputPlaceholder = `请输入${this.currentModuleName}相关内容...`;

      // 移除所有进度提示消息(包括其他模块)
      this.removeAllProgressIndicators();

      // 强制视图更新
      this.$nextTick(() => {
        // 防止消息容器仍然显示旧内容
        if (this.$refs.messageContainer) {
          this.$refs.messageContainer.scrollTop = 0;
        }
      });
    },

    // 移除所有模块中的进度提示消息
    removeAllProgressIndicators() {
      // 清除当前显示的进度消息
      this.removeProgressIndicator();

      // 清除所有模块中的进度消息
      for (const moduleId in this.messagesByModule) {
        this.messagesByModule[moduleId] = this.messagesByModule[moduleId].filter(
          msg => !(msg.type === "ai" && msg.content.startsWith("AI正在"))
        );
      }
    },
    async mockSummaryAPI(text) {
      return new Response(
        new ReadableStream({
          start(controller) {
            const message = {
              event: "message",
              answer: "这是文本摘要的模拟响应：\n\n" + text.substring(0, 100) + "...",
              conversation_id: "summary-" + Date.now(),
            };
            controller.enqueue(
              new TextEncoder().encode("data: " + JSON.stringify(message) + "\n\n")
            );
            controller.close();
          },
        })
      );
    },
    async mockNL2SQLAPI(text) {
      return new Response(
        new ReadableStream({
          start(controller) {
            const message = {
              event: "message",
              answer: "这是 NL2SQL 的模拟响应：\n\nSELECT * FROM table WHERE condition;",
              conversation_id: "nl2sql-" + Date.now(),
            };
            controller.enqueue(
              new TextEncoder().encode("data: " + JSON.stringify(message) + "\n\n")
            );
            controller.close();
          },
        })
      );
    },
    // 添加进度提示消息
    addProgressIndicator(text) {
      // 确保文本末尾有点，以便我们的模板正确处理
      if (!text.endsWith("...")) {
        text = text + "...";
      }

      // 先检查是否已经有进度提示消息
      for (let i = 0; i < this.messages.length; i++) {
        if (this.messages[i].type === "ai" && this.messages[i].content.startsWith("AI正在")) {
          this.messages[i].content = text;
          return;
        }
      }

      // 如果没有进度提示消息，添加一个
      this.messages.push({
        type: "ai",
        content: text,
        thinkExpanded: true,
      });
      this.scrollToBottom();
    },
    // 提取思考内容，返回更新后的思考内容
    extractThinkingContent(content, thinkingContent, accumulatedText) {
      // 常见的思考标记
      const thinkingStartPatterns = [
        '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open> <summary> Thinking... </summary>',
        "<details open><summary>思考过程</summary>",
        "<details><summary>思考过程</summary>",
        "<details open><summary>Thinking</summary>",
        "<details><summary>Thinking</summary>",
      ];

      // 检查是否有思考开始标记
      for (const pattern of thinkingStartPatterns) {
        if (content.includes(pattern)) {
          return thinkingContent + content.replace(pattern, "");
        }
      }

      // 检查是否有一般<details>标签
      if (content.includes("<details") && content.includes("</details>")) {
        const detailsPattern = /<details[^>]*>([\s\S]*?)<\/details>/i;
        const detailsMatch = content.match(detailsPattern);

        if (detailsMatch) {
          // 提取思考内容（去除summary标签）
          let newThinkingText = detailsMatch[1];
          newThinkingText = newThinkingText.replace(/<summary[^>]*>.*?<\/summary>/i, "").trim();

          // 提取结果内容
          const resultText = content.replace(detailsPattern, "").trim();
          if (resultText) {
            accumulatedText += resultText;
          }

          return thinkingContent + newThinkingText;
        }
      }

      // 检查是否有结束标记
      if (content.includes("</details>")) {
        return thinkingContent.replace("</details>", "") + content.replace("</details>", "");
      }

      // 无思考内容
      return null;
    },

    // 更新消息内容
    updateMessageContent(messageId, thinkingContent, accumulatedText) {
      // 移除进度提示
      this.removeProgressIndicator();

      if (thinkingContent) {
        // 有思考内容时，添加<think>标签
        const messageContent = `<think>${thinkingContent}</think>${accumulatedText}`;

        // 查找现有消息
        const currentMessage = this.messages.find(msg => msg.id === messageId);
        if (currentMessage) {
          // 更新现有消息
          currentMessage.content = messageContent;
          this.$set(currentMessage, "thinkExpanded", true);
        } else if (this.isNewMessage) {
          // 添加新消息
          this.addNodeMessage(messageContent, messageId);
          this.isNewMessage = false;
        } else {
          // 更新最后一条消息
          const lastMessage = this.findLastNonProgressMessage();
          if (lastMessage) {
            lastMessage.content = messageContent;
            this.$set(lastMessage, "thinkExpanded", true);
          } else {
            this.addNodeMessage(messageContent, messageId);
          }
        }
      } else if (accumulatedText) {
        // 没有思考内容，只有普通文本
        const messageContent = accumulatedText;

        // 更新或添加消息
        const currentMessage = this.messages.find(msg => msg.id === messageId);
        if (currentMessage) {
          currentMessage.content = messageContent;
        } else if (this.isNewMessage) {
          this.addNodeMessage(messageContent, messageId);
          this.isNewMessage = false;
        } else {
          const lastMessage = this.findLastNonProgressMessage();
          if (lastMessage) {
            lastMessage.content = messageContent;
          } else {
            this.addNodeMessage(messageContent, messageId);
          }
        }
      }

      // 添加进度提示
      this.addProgressIndicator("AI正在分析您的问题...");
    },
    // 开始拖动图标
    startIconDrag(e) {
      // 如果不是鼠标左键，则直接返回
      if (e.button !== 0) return;

      // 阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();

      // 如果是直接点击图标而非拖动行为，不启动拖动状态
      const isDragHandleClick =
        e.target === this.$el.querySelector(".chat-icon") ||
        e.target === this.$el.querySelector(".chat-icon-img");

      // 设定一个标记，用于区分点击和拖动
      this._iconClickStartTime = Date.now();
      this._iconClickStartPos = { x: e.clientX, y: e.clientY };

      // 启用拖动状态
      this.isIconDragging = true;
      this.iconTransform = { x: 0, y: 0 };

      // 计算相对于右下角的偏移量
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      this.iconDragOffset = {
        x: viewportWidth - e.clientX - this.iconPosition.x,
        y: viewportHeight - e.clientY - this.iconPosition.y,
      };

      document.addEventListener("mousemove", this.handleIconDrag);
      document.addEventListener("mouseup", this.stopIconDrag);

      document.body.classList.add("no-select");
    },

    // 处理图标拖动
    handleIconDrag(e) {
      if (!this.isIconDragging) return;

      // 阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();

      // 获取窗口可视区域的大小
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 图标尺寸
      const iconSize = 60;

      // 计算相对于右下角的新位置
      const newRight = viewportWidth - e.clientX - this.iconDragOffset.x;
      const newBottom = viewportHeight - e.clientY - this.iconDragOffset.y;

      // 限制不超出屏幕边界
      const constrainedRight = Math.min(Math.max(20, newRight), viewportWidth - iconSize);
      const constrainedBottom = Math.min(Math.max(20, newBottom), viewportHeight - iconSize);

      // 应用变换
      this.iconTransform = {
        x: this.iconPosition.x - constrainedRight,
        y: this.iconPosition.y - constrainedBottom,
      };
    },

    // 停止图标拖动
    stopIconDrag(e) {
      if (!this.isIconDragging) return;

      // 计算是否是点击行为
      const isClick =
        Date.now() - this._iconClickStartTime < 200 && // 时间小于200ms
        Math.abs(e.clientX - this._iconClickStartPos.x) < 5 && // X位移小于5px
        Math.abs(e.clientY - this._iconClickStartPos.y) < 5; // Y位移小于5px

      // 更新位置
      this.iconPosition = {
        x: this.iconPosition.x - this.iconTransform.x,
        y: this.iconPosition.y - this.iconTransform.y,
      };
      this.iconTransform = { x: 0, y: 0 };

      // 保存位置到本地存储
      this.saveIconPosition();

      // 移除拖动状态
      this.isIconDragging = false;

      // 清除事件监听
      document.removeEventListener("mousemove", this.handleIconDrag);
      document.removeEventListener("mouseup", this.stopIconDrag);

      document.body.classList.remove("no-select");

      // 如果是点击行为，则触发聊天框切换
      if (isClick) {
        this.toggleChatBox();
      }
    },
    // 设置图标默认位置
    updateIconPosition() {
      // 默认距右侧20px，距底部20px
      this.iconPosition = {
        x: 20,
        y: 20,
      };
      // 保存到本地存储
      this.saveIconPosition();
    },
    // 保存图标位置到本地存储
    saveIconPosition() {
      try {
        localStorage.setItem("ai-chat-icon-position", JSON.stringify(this.iconPosition));
      } catch (e) {
        console.error("保存图标位置失败:", e);
      }
    },
    // 防抖处理窗口大小变化
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      this.resizeTimer = setTimeout(() => {
        this.updatePosition();
      }, 200);
    },
    // 开始触摸拖动图标（移动设备支持）
    startIconDragTouch(e) {
      // 阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();

      const touch = e.touches[0];

      // 设定一个标记，用于区分点击和拖动
      this._iconClickStartTime = Date.now();
      this._iconClickStartPos = { x: touch.clientX, y: touch.clientY };

      // 启用拖动状态
      this.isIconDragging = true;
      this.iconTransform = { x: 0, y: 0 };

      // 计算相对于右下角的偏移量
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      this.iconDragOffset = {
        x: viewportWidth - touch.clientX - this.iconPosition.x,
        y: viewportHeight - touch.clientY - this.iconPosition.y,
      };

      document.addEventListener("touchmove", this.handleIconDragTouch, { passive: false });
      document.addEventListener("touchend", this.stopIconDragTouch);
      document.addEventListener("touchcancel", this.stopIconDragTouch);

      document.body.classList.add("no-select");
    },
    // 处理图标触摸拖动
    handleIconDragTouch(e) {
      if (!this.isIconDragging) return;

      // 阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();

      const touch = e.touches[0];

      // 获取窗口可视区域的大小
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 图标尺寸
      const iconSize = 60;

      // 计算相对于右下角的新位置
      const newRight = viewportWidth - touch.clientX - this.iconDragOffset.x;
      const newBottom = viewportHeight - touch.clientY - this.iconDragOffset.y;

      // 限制不超出屏幕边界
      const constrainedRight = Math.min(Math.max(20, newRight), viewportWidth - iconSize);
      const constrainedBottom = Math.min(Math.max(20, newBottom), viewportHeight - iconSize);

      // 应用变换
      this.iconTransform = {
        x: this.iconPosition.x - constrainedRight,
        y: this.iconPosition.y - constrainedBottom,
      };
    },
    // 停止图标触摸拖动
    stopIconDragTouch(e) {
      if (!this.isIconDragging) return;

      // 计算是否是点击行为
      const isClick = Date.now() - this._iconClickStartTime < 200; // 触摸设备只检查时间

      // 更新位置
      this.iconPosition = {
        x: this.iconPosition.x - this.iconTransform.x,
        y: this.iconPosition.y - this.iconTransform.y,
      };
      this.iconTransform = { x: 0, y: 0 };

      // 保存位置到本地存储
      this.saveIconPosition();

      // 移除拖动状态
      this.isIconDragging = false;

      // 清除事件监听
      document.removeEventListener("touchmove", this.handleIconDragTouch);
      document.removeEventListener("touchend", this.stopIconDragTouch);
      document.removeEventListener("touchcancel", this.stopIconDragTouch);

      document.body.classList.remove("no-select");

      // 如果是点击行为，则触发聊天框切换
      if (isClick) {
        // 延迟执行，防止与mouseup冲突
        setTimeout(() => {
          this.toggleChatBox();
        }, 10);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.ai-chat-container {
  position: fixed;
  /* right: 20px;
  bottom: 20px; */
  z-index: 9999;
  width: auto;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  pointer-events: auto;
}

.chat-icon {
  position: fixed;
  /* 移除固定的 right 和 bottom 值，使用动态样式绑定 */
  width: 60px;
  height: 60px;
  background: rgba(0, 255, 255, 0.2);
  border: 2px solid #00ffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  /* 更改为 move 光标，表明可拖动 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
  animation: pulse 2s infinite;
  margin-bottom: 10px;
  pointer-events: auto;
  overflow: hidden;
  z-index: 8888;

  &:hover {
    transform: scale(1.1) rotate(5deg);
    background: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 40px rgba(0, 255, 255, 0.7);
  }

  &:active {
    transform: scale(0.95) rotate(-5deg);
  }
}

/* 添加一个类指示图标正在被拖动 */
.chat-icon.is-dragging {
  animation: none;
  transform: scale(1.05);
  background: rgba(0, 255, 255, 0.4);
  opacity: 0.8;
  transition: none;
}

.chat-icon-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-icon i {
  font-size: 28px;
  color: #00ffff;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    transform: scale(1);
  }

  50% {
    box-shadow: 0 0 40px rgba(0, 255, 255, 0.8);
    transform: scale(1.05);
  }

  100% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
    transform: scale(1);
  }
}

.chat-box {
  position: fixed;
  width: v-bind("isMaximized ? '100vw' : '600px'");
  height: v-bind("isMaximized ? '100vh' : isMinimized ? '60px' : 'calc(100vh - 20px)'");
  background: linear-gradient(135deg, rgba(0, 24, 48, 0.95) 0%, rgba(0, 36, 72, 0.95) 100%);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: v-bind("isMaximized ? '0' : '12px'");
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.15), inset 0 0 20px rgba(0, 255, 255, 0.05),
    0 0 0 1px rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  pointer-events: auto;
  z-index: 9999;
  user-select: none;
  overflow: hidden;
  transform-origin: center;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.chat-box.is-maximized {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-header {
  padding: 20px 25px;
  background: linear-gradient(90deg, rgba(0, 48, 96, 0.95) 0%, rgba(0, 72, 144, 0.95) 100%);
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: v-bind("isMaximized ? '0' : '12px 12px 0 0'");
  cursor: move;
  position: relative;

  /* 添加发光边框效果 */
  &::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: -1px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.5) 50%,
        transparent 100%);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }

  span {
    color: #00ffff;
    font-size: 20px;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    position: relative;
    padding-left: 24px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      height: 16px;
      background: rgba(0, 255, 255, 0.2);
      border: 2px solid rgba(0, 255, 255, 0.5);
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }
  }

  .header-controls {
    display: flex;
    gap: 15px;
    align-items: center;
  }

  .header-controls i {
    color: #00ffff;
    cursor: pointer;
    transition: all 0.3s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background: rgba(0, 255, 255, 0.1);

    &:hover {
      transform: scale(1.1);
      background: rgba(0, 255, 255, 0.2);
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  display: v-bind("isMinimized ? 'none' : 'flex'");
  flex-direction: column;
  gap: 25px;
  background: rgba(0, 24, 48, 0.95);
  position: relative;
  user-select: text;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 24, 48, 0.95);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 192, 255, 0.3) 100%);
    border-radius: 4px;
    border: 2px solid rgba(0, 24, 48, 0.95);
    transition: all 0.3s;

    &:hover {
      background: linear-gradient(180deg, rgba(0, 255, 255, 0.5) 0%, rgba(0, 192, 255, 0.5) 100%);
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.3) rgba(0, 24, 48, 0.95);

  /* 添加网格背景 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px) 0 0 / 20px 20px,
      linear-gradient(0deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px) 0 0 / 20px 20px;
    pointer-events: none;
  }
}

.chat-input {
  padding: 20px;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  background: linear-gradient(0deg, rgba(0, 48, 96, 0.95) 0%, rgba(0, 36, 72, 0.95) 100%);
  border-radius: 0 0 12px 12px;
  position: relative;
  display: v-bind("isMinimized ? 'none' : 'block'");

  /* 添加发光边框效果 */
  &::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: -1px;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.5) 50%,
        transparent 100%);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  }
}

/* 修改输入框样式 */
:deep(.el-textarea__inner) {
  background: rgba(0, 24, 48, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  resize: none;
  font-size: 16px;
  line-height: 1.8;
  padding: 15px;
  padding-right: 50px;
  /* 减小右侧内边距 */
  transition: all 0.3s;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE and Edge */
  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }

  &:focus {
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
  }
}

/* 修改发送按钮样式 */
:deep(.el-button--primary) {
  position: absolute;
  right: 35px;
  bottom: 35px;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 192, 255, 0.2) 100%);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  /* 改为圆形 */
  color: #00ffff;
  font-weight: 500;
  padding: 8px;
  /* 减小内边距 */
  width: 32px;
  /* 固定宽度 */
  height: 32px;
  /* 固定高度 */
  transition: all 0.3s;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 192, 255, 0.3) 100%);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(1px);
  }
}

/* 消息气泡样式优化 */
.message {
  margin-bottom: 15px;
  max-width: 80%;
  min-width: 50%;
  user-select: text;
}

.user-message {
  margin-left: auto;
}

.ai-message {
  margin-right: auto;
}

.message-content {
  padding: 20px 25px;
  border-radius: 15px;
  word-break: break-word;
  font-size: 18px;
  line-height: 1.8;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.message-content :deep(p) {
  margin: 0 0 10px 0;
  color: #ffffff;
}

.message-content :deep(p:last-child) {
  margin-bottom: 0;
}

.message-content :deep(code) {
  background: #1a1a1a;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  color: #00ffff;
  font-size: 16px;
  user-select: text;
}

.message-content :deep(pre) {
  background: rgba(0, 24, 48, 0.95);
  padding: 15px;
  border-radius: 8px;
  overflow-x: auto;
  border: 1px solid rgba(0, 255, 255, 0.2);
  margin: 10px 0;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.message-content :deep(pre code) {
  background: none;
  padding: 0;
  color: #00ffff;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.message-content :deep(blockquote) {
  border-left: 4px solid #00ffff;
  margin: 10px 0;
  padding: 10px 15px;
  color: #00ffff;
  background: rgba(0, 255, 255, 0.05);
  border-radius: 0 8px 8px 0;
}

.message-content :deep(ul),
.message-content :deep(ol) {
  padding-left: 20px;
  color: #ffffff;
  margin: 10px 0;
}

.message-content :deep(li) {
  margin: 5px 0;
}

.message-content :deep(a) {
  color: #00ffff;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s;
}

.message-content :deep(a:hover) {
  border-bottom-color: #00ffff;
}

.message-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
  background: rgba(0, 48, 96, 0.95);
  border-radius: 8px;
  overflow: hidden;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.message-content :deep(th),
.message-content :deep(td) {
  border: 1px solid rgba(0, 255, 255, 0.2);
  padding: 12px;
  text-align: left;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

.message-content :deep(th) {
  background: rgba(0, 24, 48, 0.95);
  color: #00ffff;
  font-weight: 500;
}

.message-content :deep(hr) {
  border: none;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  margin: 20px 0;
}

.message-content :deep(h1),
.message-content :deep(h2),
.message-content :deep(h3),
.message-content :deep(h4),
.message-content :deep(h5),
.message-content :deep(h6) {
  color: #00ffff;
  margin: 15px 0 10px 0;
  line-height: 1.4;
}

.message-content :deep(h1) {
  font-size: 24px;
}

.message-content :deep(h2) {
  font-size: 22px;
}

.message-content :deep(h3) {
  font-size: 20px;
}

.message-content :deep(h4) {
  font-size: 18px;
}

.message-content :deep(h5) {
  font-size: 16px;
}

.message-content :deep(h6) {
  font-size: 14px;
}

.user-message .message-content {
  background: rgba(0, 96, 192, 0.95);
  color: white;
}

.ai-message .message-content {
  background: rgba(0, 48, 96, 0.95);
  color: #ffffff;
}

.chat-input {
  display: v-bind("isMinimized ? 'none' : 'flex'");
}

.chat-input .el-input {
  flex: 1;
}

.chat-input .el-button {
  align-self: flex-end;
}

.think-container {
  margin: 0 0 10px 0;
  background: rgba(0, 32, 64, 0.95);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 255, 0.2);
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  width: 100%;
}

.think-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  background: rgba(0, 36, 72, 0.95);
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  cursor: pointer;

  span {
    color: #00ffff;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;

    &::before {
      content: "🤔";
      margin-right: 8px;
      font-size: 16px;
    }
  }

  .toggle-button {
    background: none;
    border: none;
    color: #00ffff;
    cursor: pointer;
    font-size: 12px;
    padding: 4px 8px;
    transition: transform 0.3s;
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 4px;

    &:hover {
      background: rgba(0, 255, 255, 0.1);
    }
  }
}

.think-content {
  padding: 15px;
  color: #e6f7ff;
  font-size: 14px;
  line-height: 1.6;
  user-select: text;
  pointer-events: auto;
  background: rgba(0, 24, 48, 0.95);
  border-top: 1px dashed rgba(0, 255, 255, 0.2);
  max-height: 300px;
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 24, 48, 0.95);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.3);
    border-radius: 4px;
  }

  .think-text {
    white-space: pre-wrap;

    :deep(p) {
      margin: 8px 0;
      padding-left: 16px;
      border-left: 2px solid rgba(0, 255, 255, 0.2);

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(strong) {
      color: #00ffff;
      font-weight: 600;
    }

    :deep(em) {
      color: #ffd700;
      font-style: normal;
    }

    :deep(ul),
    :deep(ol) {
      margin: 8px 0;
      padding-left: 24px;

      li {
        margin: 4px 0;
      }
    }

    :deep(code) {
      background: rgba(0, 0, 0, 0.2);
      padding: 2px 6px;
      border-radius: 4px;
      color: #00ffff;
      font-family: monospace;
    }
  }
}

/* 添加聊天框过渡动画 */
.chat-box-enter-active {
  animation: chatBoxIn 0.4s cubic-bezier(0.3, 1, 0.3, 1);
}

.chat-box-leave-active {
  animation: chatBoxOut 0.4s cubic-bezier(0.7, 0, 0.7, 0);
}

@keyframes chatBoxIn {
  0% {
    opacity: 0;
    transform: scale(0.6) translateY(40px);
    filter: blur(10px);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-10px);
    filter: blur(0px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes chatBoxOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(0px);
  }

  100% {
    opacity: 0;
    transform: scale(0.8) translateY(40px);
    filter: blur(10px);
  }
}

/* 添加全局样式 */
:global(.no-select) {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 添加允许文本选择的全局样式 */
:global(.allow-select) {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* 添加跳转到底部按钮样式 */
.scroll-bottom-btn {
  position: fixed;
  right: 20px;
  bottom: 180px;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 192, 255, 0.2) 100%);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(4px);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
  z-index: 10000;

  i {
    color: #00ffff;
    font-size: 20px;
    transition: transform 0.3s;
  }

  &:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 192, 255, 0.3) 100%);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);

    i {
      transform: translateY(2px);
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.module-selection {
  flex: 1;
  padding: 30px;
  background: rgba(0, 24, 48, 0.95);
  position: relative;
  overflow-y: auto;

  /* 添加网格背景 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px) 0 0 / 20px 20px,
      linear-gradient(0deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px) 0 0 / 20px 20px;
    pointer-events: none;
  }
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  position: relative;
  z-index: 1;
}

.module-item {
  // aspect-ratio: 16/9;
  background: linear-gradient(135deg, rgba(0, 48, 96, 0.95) 0%, rgba(0, 72, 144, 0.95) 100%);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 添加发光效果 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 255, 255, 0.1);

    &::before {
      opacity: 1;
    }
  }

  &.active {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 192, 255, 0.2) 100%);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 0%, rgba(0, 255, 255, 0.1) 100%);
      animation: shine 2s infinite;
    }
  }

  i {
    font-size: 36px;
    color: #00ffff;
    margin-bottom: 15px;
    transition: transform 0.3s;
  }

  span {
    font-size: 18px;
    color: #ffffff;
    font-weight: 500;
    text-align: center;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
  }

  .subtitle {
    font-size: 12px;
    color: rgba(0, 255, 255, 0.7);
    margin-top: 5px;
    text-shadow: none;
  }
}

.new-chat-btn {
  position: fixed;
  left: 50%;
  bottom: 180px;
  transform: translateX(-50%);
  height: 40px;
  padding: 0 25px;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 192, 255, 0.2) 100%);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(4px);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.1);
  z-index: 10;

  i {
    color: #00ffff;
    font-size: 20px;
    margin-right: 8px;
    transition: transform 0.3s;
  }

  span {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
  }

  &:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 192, 255, 0.3) 100%);
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);

    i {
      transform: rotate(90deg);
    }
  }

  &:active {
    transform: translateX(-50%) scale(0.95);
  }
}

@keyframes shine {
  0% {
    opacity: 0;
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 0;
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 添加echarts相关样式 */
:deep(.echarts-container) {
  background: rgba(0, 24, 51, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.1);
}

:deep(.chart-error) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4d4f;
  font-size: 14px;
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.2);
  border-radius: 8px;
}

.message-content .typing-indicator,
.message-content .dot {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 进度提示消息样式 */
.progress-indicator {
  display: flex;
  align-items: center;
  color: #00ffff;
}

.dynamic-dots {
  display: inline-flex;
  margin-left: 2px;
}

.dynamic-dots .dot {
  opacity: 0;
  animation: dotFade 1.4s infinite;
  font-size: 20px;
  line-height: 1;
  transform: translateY(-2px);
}

.dynamic-dots .dot:nth-child(1) {
  animation-delay: 0s;
}

.dynamic-dots .dot:nth-child(2) {
  animation-delay: 0.4s;
}

.dynamic-dots .dot:nth-child(3) {
  animation-delay: 0.8s;
}

@keyframes dotFade {

  0%,
  80%,
  100% {
    opacity: 0.2;
    transform: translateY(-2px) scale(0.8);
  }

  40% {
    opacity: 1;
    transform: translateY(-2px) scale(1.2);
  }
}

.module-item span {
  font-size: 16px;
}
</style>
