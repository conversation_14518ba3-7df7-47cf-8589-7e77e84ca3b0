from datetime import datetime

from sqlalchemy import Column, Integer, String, Boolean, BigInteger, DateTime
from app.db.database import Base

class Role(Base):
    __tablename__ = "role_info"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(64), nullable=False, comment="角色名称")
    code = Column(String(64), nullable=False, comment="code码")
    sort = Column(Integer, nullable=False, comment="排序")
    status = Column(Boolean, nullable=False, comment="状态")
    create_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='创建时间')
    update_time= Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='修改时间')

class UserToRole(Base):
    __tablename__ = "user_to_role"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True)
    role_id = Column(Integer, nullable=False, index=True)

class RoleMenuPermission(Base):
    __tablename__ = "role_menu_permission"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    role_id = Column(BigInteger, nullable=False, index=True)
    menu_id = Column(BigInteger, nullable=False, index=True) 