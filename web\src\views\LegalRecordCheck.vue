<template>
  <div class="legal-record-check bigscreen">
    <PageHeader title="法律文书核查" subtitle="Legal Record Check" />

    <div class="content-wrapper">
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="common-search search-form">
          <div class="search-left" :class="[showMoreSearch && 'has-height']">
            <!-- <el-form-item label="文书编号">
                <el-input v-model="searchForm.jlbh" placeholder="请输入文书编号"></el-input>
              </el-form-item> -->
            <el-form-item label="案件编号">
              <el-input v-model="searchForm.ajbh" placeholder="请输入案件编号"></el-input>
            </el-form-item>
            <el-form-item label="文书名称">
              <el-input v-model="searchForm.doc_name" placeholder="请输入文书名称"></el-input>
            </el-form-item>
            <el-form-item label="文书简要">
              <el-input v-model="searchForm.cqsx" placeholder="请输入文书简要"></el-input>
            </el-form-item>
            <el-form-item label="分析状态">
              <el-select v-model="searchForm.analysis_status" placeholder="请选择分析状态">
                <el-option label="未分析" :value="0"></el-option>
                <el-option label="分析中" :value="1"></el-option>
                <el-option label="分析完成" :value="2"></el-option>
                <el-option label="分析失败" :value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分析结果">
              <el-select v-model="searchForm.check_status" placeholder="请选择分析结果">
                <el-option label="无问题" :value="0"></el-option>
                <el-option label="有问题" :value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="案件名称">
              <el-input v-model="searchForm.ajmc" placeholder="请输入案件名称"></el-input>
            </el-form-item>
            <el-form-item label="文书字号">
              <el-input v-model="searchForm.doc_num" placeholder="请输入文书字号"></el-input>
            </el-form-item>
            <el-form-item label="填发人姓名">
              <el-input v-model="searchForm.tfrxm" placeholder="请输入填发人姓名"></el-input>
            </el-form-item>
            <el-form-item label="填发时间">
              <el-date-picker v-model="searchForm.tfsjRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyyMMdd" :picker-options="pickerOptions"
                class="date-range-picker"></el-date-picker>
            </el-form-item>
            <el-form-item label="批准时间">
              <el-date-picker v-model="searchForm.pzsjRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyyMMdd" :picker-options="pickerOptions"
                class="date-range-picker"></el-date-picker>
            </el-form-item>
          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <div class="el-button_text" @click="toggleMoreSearch">
                {{ !showMoreSearch ? '收起' : '展开' }}
                <i v-show="showMoreSearch" class="el-icon-arrow-down"></i>
                <i v-show="!showMoreSearch" class="el-icon-arrow-up"></i>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button type="primary" @click="handleSyncData" class="tool-btn">
            <i class="el-icon-refresh"></i>
            同步数据
          </el-button>
          <el-button type="primary" @click="handleBatchAnalysis" :disabled="selectedRecords.length === 0" 
            class="batch-analysis-btn tool-btn">
            <i class="el-icon-s-operation"></i>
            批量分析
          </el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table :data="tableData" stripe style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          :row-class-name="rowClassName" v-loading="loading" @selection-change="handleSelectionChange"
          height="calc(100vh - 21.875rem)">
          <el-table-column type="selection" width="60" align="center" :selectable="checkSelectable"></el-table-column>
          <el-table-column type="expand" widht="28">
            <template slot-scope="props">
              <div class="detail-container">
                <div class="detail-content">
                  <div class="detail-section">
                    <div class="section-title">基本信息</div>
                    <div class="detail-grid">
                      <div class="detail-item">
                        <span class="item-label">文书编号：</span>
                        <span class="item-value">{{ props.row.jlbh }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">案件编号：</span>
                        <span class="item-value">{{ props.row.ajbh }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">文书名称：</span>
                        <span class="item-value">{{ props.row.doc_name }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">批准时间：</span>
                        <span class="item-value">{{ formatDateTime(null, null, props.row.pzsj) }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">填发时间：</span>
                        <span class="item-value">{{ formatDateTime(null, null, props.row.tfsj) }}</span>
                      </div>

                      <div class="detail-item">
                        <span class="item-label">案件名称：</span>
                        <span class="item-value">{{ props.row.ajmc }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">文书字号：</span>
                        <span class="item-value">{{ props.row.doc_num }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">填发人姓名：</span>
                        <span class="item-value">{{ props.row.tfrxm }}</span>
                      </div>

                      <div class="detail-item">
                        <span class="item-label">分析状态：</span>
                        <span class="item-value">
                          <!-- <el-tag :type="getStatusType(props.row.analysis_status)">
                            {{ getStatusText(props.row.analysis_status) }}
                          </el-tag> -->
                          <StatusView :status-option="formatAnalysisStatus(props.row)"></StatusView>
                        </span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">分析结果：</span>
                        <span class="item-value">
                          <div v-if="props.row.analysis_status === 2" class="check-result" :class="props.row.check_status === 0 ? 'check-result-success' : 'check-result-error'">
                            {{ props.row.check_status === 0 ? '无问题' : '有问题' }}
                          </div>
                          <span v-else>-</span>
                        </span>
                      </div>

                    </div>
                  </div>
                  <!-- <div class="detail-section">
                    <div class="section-title">文书简要</div>
                    <div class="detail-text">{{ props.row.cqsx }}</div>
                  </div> -->
                  <div class="detail-section" v-if="props.row.analysis_status === 2 && props.row.analysis_result">
                    <div class="section-title">分析结论</div>
                    <div class="detail-text">
                      <el-tag :type="getAnalysisTagType(props.row.analysis_tag)">
                        {{ props.row.analysis_result }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号" width="70">
          </el-table-column>
          <!-- <el-table-column prop="jlbh" width="150" label="文书编号" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="ajbh" width="150" label="案件编号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cqsx" label="文书名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="pzsj" label="批准时间" width="160" align="center" show-overflow-tooltip
            :formatter="formatDateTime"></el-table-column>
          <el-table-column prop="tfsj" label="填发时间" width="160" align="center" show-overflow-tooltip
            :formatter="formatDateTime"></el-table-column>
          <!-- <el-table-column prop="cqsx" width="150" label="文书简要" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="ajmc" width="120" label="案件名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doc_num" width="120" label="文书字号" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tfrxm" width="120" label="填发人姓名" show-overflow-tooltip></el-table-column>
          <el-table-column prop="analysis_status" label="分析状态" width="120" align="center">
            <template slot-scope="scope">
              <!-- <el-tag :type="getStatusType(scope.row.analysis_status)">
                {{ getStatusText(scope.row.analysis_status) }}
              </el-tag> -->
              <StatusView :status-option="formatAnalysisStatus(scope.row)"></StatusView>
            </template>
          </el-table-column>
          <el-table-column prop="check_status" label="分析结果" width="120" align="center">
            <template slot-scope="scope">
              <!-- <div v-if="scope.row.analysis_status === 2" style="width: 70px;margin: 0 auto;">
                <StatusView :status-option="formatCheckResults(scope.row)"></StatusView>
            </div> -->
            <div v-if="scope.row.analysis_status === 2" class="check-result" :class="scope.row.check_status === 0 ? 'check-result-success' : 'check-result-error'">
                {{ scope.row.check_status === 0 ? '无问题' : '有问题' }}
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button v-if="scope.row.analysis_status === 2 && scope.row.check_status === 1" type="text" size="small"
                @click="handleViewResult(scope.row)" class="view-btn">
                <i class="el-icon-view"></i>
                查看结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>
    </div>

    <!-- 查看结果弹框 -->
    <el-dialog title="问题展示" :visible.sync="resultDialogVisible" width="60%" :before-close="handleResultClose"
      custom-class="result-dialog" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="true">
      <div class="result-content">
        <div v-if="currentRecord?.detection_result" class="result-list">
          <div v-for="(item, key) in parseDetectionResult(currentRecord.detection_result)" :key="key"
            class="result-item" v-if="item.status === 'ERROR' || item.status === 'AI-ERROR'"
            :class="{ 'error-item': item.status === 'ERROR' || item.status === 'AI-ERROR', 'right-item': item.status === 'RIGHT' }">
            <div class="result-header">
              <span class="result-title">规则:{{ getOutputTitle(key) }}</span>
              <el-tag :type="item.status === 'ERROR' || item.status === 'AI-ERROR' ? 'danger' : 'success'" size="small">
                {{ item.status === 'ERROR' || item.status === 'AI-ERROR' ? '错误' : '正确' }}
              </el-tag>
            </div>
            <div v-if="item.status === 'ERROR' || item.status === 'AI-ERROR'" class="result-details">
              <div v-if="item.title" class="result-subtitle">{{ item.title }}</div>
              <div v-if="item.reason" class="result-reason">{{ item.reason }}</div>
            </div>
          </div>
        </div>
        <div v-else class="result-text">暂无检测结果</div>
      </div>
    </el-dialog>

    <!-- 日期范围选择弹框 -->
    <DateRangeDialog :visible.sync="dateRangeDialogVisible" @confirm="handleDateRangeConfirm"
      @cancel="handleDateRangeCancel" @close="handleDateRangeClose" />
  </div>
</template>

<script>
import { getLegalRecordList, syncLegalRecordData, batchAnalyzeRecords } from "@/api/legalRecord";
import PageHeader from "@/components/PageHeader.vue";
import DateRangeDialog from "@/components/DateRangeDialog.vue";
import StatusView from "@/components/StatusView.vue";

export default {
  name: "LegalRecordCheck",
  components: {
    PageHeader,
    DateRangeDialog,
    StatusView
  },
  data() {
    return {
      searchForm: {
        jlbh: "",
        ajbh: "",
        doc_name: "",
        cqsx: "",
        analysis_status: "",
        check_status: "",
        ajmc: "",
        doc_num: "",
        tfrxm: "",
        tfsjRange: [],
        pzsjRange: []
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
      total: 0,
      resultDialogVisible: false,
      currentRecord: null,
      selectedRecords: [],
      dateRangeDialogVisible: false,
      showMoreSearch: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      analysisFormatMap: {
        0: { text: "未分析", color: "#909399", iconType: '', bgColor: "rgba(144,147,153,0.1)" },
        1: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        2: { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
      },
      checkFormatMap: {
        "无问题": { iconColor: "#999999", iconType: 'speck' },
        "有问题": { iconColor: "#F56C6C", iconType: 'speck' },
      },
    };
  },
  created() {
    this.loadTableData();
  },
  methods: {
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.analysis_status];
      return status || {};
    },
    formatCheckResults(row) {
      let str = row.check_status==0?'无问题':'有问题';
      let status = this.checkFormatMap[str];
      status = status || {};
      status.text = str
      return status || {};
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      try {
        const date = new Date(cellValue);
        if (isNaN(date.getTime())) return cellValue; // 如果转换失败，返回原始值

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error("日期格式化错误:", error);
        return cellValue; // 发生错误时返回原始值
      }
    },
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchForm.jlbh) {
          params.jlbh = this.searchForm.jlbh;
        }
        if (this.searchForm.ajbh) {
          params.ajbh = this.searchForm.ajbh;
        }
        if (this.searchForm.doc_name) {
          params.doc_name = this.searchForm.doc_name;
        }
        if (this.searchForm.cqsx) {
          params.cqsx = this.searchForm.cqsx;
        }
        if (this.searchForm.analysis_status !== "") {
          params.analysis_status = this.searchForm.analysis_status;
        }
        if (this.searchForm.check_status !== "") {
          params.check_status = this.searchForm.check_status;
        }
        if (this.searchForm.ajmc) {
          params.ajmc = this.searchForm.ajmc;
        }
        if (this.searchForm.doc_num) {
          params.doc_num = this.searchForm.doc_num;
        }
        if (this.searchForm.tfrxm) {
          params.tfrxm = this.searchForm.tfrxm;
        }
        if (this.searchForm.tfsjRange && this.searchForm.tfsjRange.length === 2) {
          params.tfsj_start_time = this.searchForm.tfsjRange[0];
          params.tfsj_end_time = this.searchForm.tfsjRange[1];
        }
        if (this.searchForm.pzsjRange && this.searchForm.pzsjRange.length === 2) {
          params.pzsj_start_time = this.searchForm.pzsjRange[0];
          params.pzsj_end_time = this.searchForm.pzsjRange[1];
        }

        const res = await getLegalRecordList(params);
        if (res.code === 200) {
          this.tableData = res.data.records || [];
          this.total = res.data.total || 0;
        } else {
          throw new Error(res.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;
      }
    },
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#ffffffcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    rowClassName({ row }) {
      //   if (row.analysis_status === 0) {
      //     return "warning-row";
      //   }
      return "";
    },
    async handleSearch() {
      this.currentPage = 1;
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        jlbh: "",
        ajbh: "",
        doc_name: "",
        cqsx: "",
        analysis_status: "",
        check_status: "",
        ajmc: "",
        doc_num: "",
        tfrxm: "",
        tfsjRange: [],
        pzsjRange: []
      };
      this.handleSearch();
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },

    getStatusType(status) {
      switch (status) {
        case 0:
          return "info";
        case 1:
          return "warning";
        case 2:
          return "success";
        case 3:
          return "danger";
        default:
          return "";
      }
    },
    getStatusText(status) {
      switch (status) {
        case 0:
          return "未分析";
        case 1:
          return "分析中";
        case 2:
          return "分析完成";
        case 3:
          return "分析失败";
        default:
          return "未知状态";
      }
    },
    async handleSyncData() {
      this.dateRangeDialogVisible = true;
    },
    async handleDateRangeConfirm(dateRange) {
      try {
        this.loading = true;
        const params = {
          start_date: dateRange[0], // 添加日期为当月第一天
          end_date: dateRange[1]   // 添加日期为当月第一天
        };
        const res = await syncLegalRecordData(params);
        if (res.code === 200) {
          this.$message.success("数据同步成功");
          await this.loadTableData();
        } else {
          throw new Error(res.message || "数据同步失败");
        }
      } catch (error) {
        console.error("数据同步失败:", error);

      } finally {
        this.loading = false;
        this.dateRangeDialogVisible = false;
      }
    },
    handleDateRangeCancel() {
      this.dateRangeDialogVisible = false;
    },
    handleDateRangeClose() {
      this.dateRangeDialogVisible = false;
    },
    checkSelectable(row) {
      return row.analysis_status === 0 || row.analysis_status === 3;
    },
    handleSelectionChange(selection) {
      this.selectedRecords = selection;
    },
    async handleBatchAnalysis() {
      try {
        if (this.selectedRecords.length === 0) {
          this.$message.warning('请选择要分析的文书');
          return;
        }

        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              确认对选中的 ${this.selectedRecords.length} 个文书进行批量分析吗？
            </div>
          </div>
        `,
          "分析确认",
          {
            confirmButtonText: "确定分析",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const caseIds = this.selectedRecords.map(item => item.jlbh);
          const res = await batchAnalyzeRecords(caseIds);
          if (res.code === 200) {
            this.$message.success("批量分析任务已启动");
            await this.loadTableData();
            this.selectedRecords = [];
          } else {
            throw new Error(res.message || "批量分析失败");
          }
        } else {
          this.$message.info("已取消分析");
        }
      } catch (error) {

      } finally {
        this.loading = false;
      }
    },
    handleViewResult(row) {
      if (!row) {
        this.$message.warning('无法获取文书信息');
        return;
      }
      this.currentRecord = row;
      this.resultDialogVisible = true;
    },
    handleResultClose(done) {
      done();
    },
    getAnalysisTagType(tag) {
      switch (tag) {
        case 'success':
          return 'success';
        case 'warning':
          return 'warning';
        case 'danger':
          return 'danger';
        case 'info':
          return 'info';
        default:
          return '';
      }
    },
    parseDetectionResult(result) {
      try {
        return JSON.parse(result);
      } catch (error) {
        console.error('解析检测结果失败:', error);
        return {};
      }
    },
    getOutputTitle(key) {
      const titleMap = {
        "output1": "文书语言逻辑不清;文书多字/少字;文书存在错别字",
        "output2": "人员信息未填写完整",
        "output3": "有无违法犯罪经历未填写",
        "output4": "案件办理及案件事实描述不清",
        "output5": "未写明犯罪嫌疑人归案情况",
        "output6": "未写明犯罪嫌疑人认罪认罚情况",
        "output7": "行政处罚决定书法律条款引用不;行政处罚决定书引用错误;提请批准逮捕书犯罪性质认定及呈捕依据错误;起诉意见书犯罪性质认定及移送审查起诉依据错误",
        "output8": "执行期限未填写;执行期限填写错误",
        "output9": "所附清单错误",
        "doc_data_content": "法律文书内容是否为空检验"
      };
      return titleMap[key] || key;
    },
    toggleMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
    },
  },
};
</script>

<style scoped lang="less">
.legal-record-check {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.search-form {
  width: 100%;

  .search-left {
    height: 230px;
  }
}

.search-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 10px;
}

.search-row.hidden {
  height: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
  pointer-events: none;
}

.toggle-search-container {
  position: absolute;
  right: 0;
  top: 5px;
  z-index: 2;
}

.toggle-search-text {
  color: #00ffff;
  padding: 5px 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  user-select: none;
  margin-left: 10px;
}

.toggle-search-text:hover {
  color: #ffffff;
}

.toggle-search-text i {
  transition: transform 0.3s;
}

.toggle-search-text i.rotate {
  transform: rotate(180deg);
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

.pagination-container {
  margin-top: 20px;
  margin-right: 60px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* Element UI 样式覆盖 */
:deep(.el-table) {
  background-color: transparent !important;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
  background-color: transparent !important;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
  height: 0 !important;
}

:deep(.el-table__header) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table__header tr th) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-bottom: 2px solid rgba(0, 255, 255, 0.2);
  color: #00ffff;
  font-size: 16px;
  font-weight: bold;
}

:deep(.el-table__body tr) {
  background-color: transparent !important;
}

:deep(.el-table__body tr td) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  color: #ffffff;
  font-size: 14px;
  border-color: rgba(0, 255, 255, 0.1);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  // background-color: rgba(0, 48, 102) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  // background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-table__row) {
  transition: all 0.3s;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0, 48, 102, 0.3);
}

/* 详情展开样式 */
.detail-container {
  padding: 20px;
  background: rgba(0, 48, 102, 0.2);
  border-radius: 4px;
  margin: 10px 20px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  padding: 15px;
}

.section-title {
  color: #00ffff !important;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 15px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #00ffff;
  border-radius: 2px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

@media screen and (max-width: 1400px) {
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
}

.detail-item {
  display: flex;
  align-items: center;
}

.item-label {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
  font-size: 14px;
  white-space: nowrap;
}

.item-value {
  color: #ffffff;
  font-size: 14px;
}

.detail-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
  text-align: justify;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  color: #ffffff !important;
  padding-right: 8px;
  font-size: 14px;
  white-space: nowrap;
  width: auto;
}

:deep(.el-form-item__content) {
  display: flex;
  flex-direction: row;
  gap: 10px;
  flex-wrap: nowrap;
  margin-left: 0 !important;
  flex: 1;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-select) {
  width: 100%;
}

@media screen and (max-width: 1200px) {
  :deep(.el-form-item) {
    // max-width: calc(33.33% - 10px);
  }
}

@media screen and (max-width: 992px) {
  :deep(.el-form-item) {
    // max-width: calc(50% - 10px);
  }
}

@media screen and (max-width: 768px) {
  :deep(.el-form-item) {
    max-width: 100%;
  }
}

:deep(.toggle-search-item .el-form-item__content) {
  flex: 0 0 auto !important;
  justify-content: flex-end;
}

/* 按钮样式 */
:deep(.el-button) {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
  height: 32px;
  padding: 0 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 80px;
  gap: 5px;
}

:deep(.el-button:hover) {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  color: #00ffff;
}

/* 查看结果按钮 */
.view-btn {
  color: #00ffff;
  background: transparent;
  border: none;
  padding: 0;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 16px;
}

.view-btn:hover {
  color: #ffffff;
  background: transparent;
}

.view-btn i {
  font-size: 16px;
}

/* 分页样式 */
:deep(.el-pagination) {
  padding: 0;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

/* 加载样式 */
:deep(.el-loading-mask) {
  background-color: rgba(0, 24, 51, 0.9) !important;
}

:deep(.el-loading-spinner .el-loading-text) {
  color: #00ffff !important;
}

:deep(.el-loading-spinner .path) {
  stroke: #00ffff !important;
}

/* 警告行样式 */
:deep(.warning-row) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.warning-row td) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.el-table--striped .el-table__body tr.warning-row td) {
  background-color: rgba(255, 0, 0, 0.2) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr.warning-row:hover > td) {
  background-color: rgba(255, 0, 0, 0.3) !important;
}

/* 结果弹框样式 */
:deep(.result-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  height: 60% !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}

:deep(.result-dialog .el-dialog__header) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.result-dialog .el-dialog__title) {
  color: #00ffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

:deep(.result-dialog .el-dialog__body) {
  padding: 20px !important;
  background: rgba(0, 24, 51, 0.4) !important;
  height: 90% !important;
  overflow-y: auto !important;
}

.result-content {
  padding: 20px;
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
}

.result-text {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.6;
  text-align: justify;
  white-space: pre-wrap;
}

.detail-text :deep(.el-tag) {
  margin-right: 8px;
  font-size: 14px;
  padding: 0 10px;
  height: 24px;
  line-height: 24px;
}

/* 批量分析按钮样式 */
.batch-analysis-btn {
  position: relative;
  transition: all 0.3s;
}

.batch-analysis-btn:disabled {
  background: rgba(0, 48, 102, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
  color: rgba(0, 255, 255, 0.5);
  cursor: not-allowed;
  box-shadow: none;
}

.batch-analysis-btn:disabled:hover {
  background: rgba(0, 48, 102, 0.3);
  border-color: rgba(0, 255, 255, 0.2);
  color: rgba(0, 255, 255, 0.5);
}

.batch-analysis-btn:disabled::after {
  content: "请选择要分析的文书";
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 48, 102, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #00ffff;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  pointer-events: none;
}

.batch-analysis-btn:disabled:hover::after {
  opacity: 1;
  visibility: visible;
}

/* 日期范围选择对话框样式 */
:deep(.date-range-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}

:deep(.date-range-dialog .el-dialog__header) {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.date-range-dialog .el-dialog__title) {
  color: #00ffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

:deep(.date-range-dialog .el-dialog__body) {
  padding: 20px !important;
  background: rgba(0, 24, 51, 0.4) !important;
}

.date-range-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.prompt-title {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 10px;
}

:deep(.el-range-input) {
  background-color: transparent !important;
}

// :deep(.el-date-editor .el-input__inner) {
//   background-color: transparent !important;
//   border: none !important;
//   color: #ffffff !important;
// }

// :deep(.el-date-editor .el-range-input) {
//   background: none !important;
//   color: #ffffff !important;
// }

// :deep(.el-date-editor .el-range-separator) {
//   color: #ffffff !important;
// }

// :deep(.el-date-editor .el-range__icon),
// :deep(.el-date-editor .el-range__close-icon) {
//   color: #ffffff !important;
// }

// :deep(.el-date-editor:hover) {
//   border-color: #00ffff !important;
// }

// :deep(.el-date-editor.is-focus) {
//   border-color: #00ffff !important;
// }

// :deep(.el-date-editor .el-range__close-icon:hover) {
//   color: #00ffff !important;
// }

/* 月份选择器样式 */
:deep(.el-date-picker) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-date-picker__header) {
  color: #ffffff !important;
}

:deep(.el-date-picker__header button) {
  color: #ffffff !important;
}

:deep(.el-date-picker__header button:hover) {
  color: #00ffff !important;
}

:deep(.el-date-picker__content) {
  background-color: rgba(0, 48, 102, 0.8) !important;
}

:deep(.el-date-table th) {
  color: #ffffff !important;
}

:deep(.el-date-table td) {
  color: #ffffff !important;
}

:deep(.el-date-table td.available:hover) {
  color: #00ffff !important;
}

:deep(.el-date-table td.current:not(.disabled) span) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-date-table td.in-range div) {
  background-color: rgba(0, 255, 255, 0.1) !important;
}

:deep(.el-date-table td.start-date span),
:deep(.el-date-table td.end-date span) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-date-table td.today) {
  color: #00ffff !important;
}

:deep(.el-date-table td.disabled) {
  color: rgba(255, 255, 255, 0.3) !important;
}

:deep(.el-date-picker__footer) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-top: 1px solid rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-picker-panel__footer .el-button) {
  background: rgba(0, 255, 255, 0.2) !important;
  border-color: #00ffff !important;
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button:hover) {
  background: rgba(0, 255, 255, 0.3) !important;
  border-color: #00ffff !important;
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button--text) {
  color: #00ffff !important;
}

:deep(.el-picker-panel__footer .el-button--text:hover) {
  color: #ffffff !important;
}

:deep(.el-icon-arrow-right) {
  color: #00ffff;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 16px;
  transition: all 0.3s;
}

.error-item {
  border-color: rgba(255, 0, 0, 0.3);
  background: rgba(255, 0, 0, 0.1);
}

.right-item {
  border-color: rgba(0, 255, 0, 0.3);
  background: rgba(0, 255, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-title {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.result-details {
  margin-top: 8px;
}

.result-subtitle {
  color: #ff6b6b;
  font-size: 13px;
  margin-bottom: 4px;
}

.result-reason {
  color: #ffffff;
  font-size: 13px;
  line-height: 1.5;
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}

:deep(.el-tag--danger) {
  background-color: rgba(255, 0, 0, 0.2);
  border-color: rgba(255, 0, 0, 0.3);
  color: #ff6b6b;
}

:deep(.el-tag--success) {
  background-color: rgba(0, 255, 0, 0.2);
  border-color: rgba(0, 255, 0, 0.3);
  color: #00ff00;
}

.button-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.button-group-right {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-left: auto;
}

.button-group-right .el-button {
  margin-left: 0;
}

.search-row-buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-bottom: 10px;
}

/* 日期范围选择器样式 */
.date-range-picker {
  width: 280px !important;
}

:deep(.el-date-editor) {
  // background-color: rgba(0, 48, 102, 0.3) !important;
  // border: 1px solid rgba(0, 255, 255, 0.2) !important;
}

/* 固定列样式修复 */
:deep(.el-table__fixed-right-patch) {
  background-color: rgb(0, 48, 102) !important;
  border-bottom: 2px solid rgba(0, 255, 255, 0.2);
}

/* :deep(.el-table__fixed-right .el-table__fixed-header-wrapper th) {
  background-color: rgb(0, 48, 102) !important;
} */

:deep(.el-table__fixed-right .el-table__fixed-body-wrapper td) {
  // background-color: rgb(0, 48, 102) !important;
}

:deep(.el-table__fixed-right .el-table__fixed-body-wrapper .el-table__row--striped td) {
  // background-color: rgb(0, 48, 102) !important;
}

:deep(.el-table__fixed-right .el-table__fixed-body-wrapper .el-table__row:hover > td) {
  background-color: rgb(0, 102, 153) !important;
  color: #00ffff !important;
}
</style>