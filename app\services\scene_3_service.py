import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Optional

import aiohttp
from sqlalchemy import text, or_
from sqlalchemy.orm import Session

from app.config import settings
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.scene_3_record import Scene_3_Record
from app.services.db_service import DatabaseService


# 创建任务队列
analysis_queue = asyncio.Queue()


class Scene_3_Service:
    @staticmethod
    def cover_record_exists(record: Scene_3_Record) -> Dict:

        doc_data = json.loads(record.doc_data)

        if record.doc_name == "行政处罚决定书":
            csrg = str(doc_data.get('csrg', ''))
            birth_year = csrg[:4] if len(csrg) >= 4 else ''
            birth_month = csrg[4:6] if len(csrg) >= 6 else ''
            birth_day = csrg[6:8] if len(csrg) >= 8 else ''

            # 生成新的字段
            return {
                'doc_name': record.doc_name,
                'xz_person_info': f"法行为人姓名{doc_data.get('xm', '')}，性别{doc_data.get('xbhidden', '')}，"
                                  f"年龄{doc_data.get('nl', '')}，出生日期{birth_year}年{birth_month}月{birth_day}日，"
                                  f"身份证件种类{doc_data.get('zjlxhidden', '')}，证件号{doc_data.get('zjhm', '')}，"
                                  f"户籍所在地{doc_data.get('hjxz', '')}，现住址{doc_data.get('xzxz', '')}，"
                                  f"工作单位{doc_data.get('fwcs', '')}，违法经历{doc_data.get('wfjl', '')}",
                'xz_jyaq': doc_data.get('jyaq', ''),
                'xz_zjcl': f"以上事实有{doc_data.get('zjcl', '')}等证据证实",
                'xz_zsss_flyj': f"{doc_data.get('zsss', '')}根据{doc_data.get('flyj', '')}之规定，现决定{doc_data.get('cfcs', '')}",
                'xz_zxfs': doc_data.get('zxfs', ''),
                'xz_ssdw': f"如果您对本决定不服，可以在收到本决定书之日起六十日内向{doc_data.get('ssdw', '')}人民政府申请行政复议或者在六个月内依法向{doc_data.get('ssdw', '')}人民法院提起行政诉讼。",
                'xz_qd': f"{doc_data.get('qd')}",
                'xz_fs': f"{doc_data.get('fs')}"
            }

        elif record.doc_name == "起诉意见书":
            return {
                'doc_name': record.doc_name,
                'qs_wfjl': doc_data.get('wfjl', ''),
                'qs_text_1': doc_data.get('text_1', ''),
                'qs_text_3': doc_data.get('text_3', ''),
                'qs_text_5': doc_data.get('text_5', ''),
                'qs_text_6': doc_data.get('text_6', ''),
                'qs_text_7': doc_data.get('text_7', '')
            }
        elif record.doc_name == "提请批准逮捕书":
            return {
                'doc_name': record.doc_name,
                'tq_xyr': doc_data.get('xyr', ''),
                'tq_ajly': doc_data.get('ajly', ''),
                'tq_fzss': doc_data.get('fzss', ''),
                'tq_xgzj': doc_data.get('xgzj', ''),
                'tq_rzrf': doc_data.get('rzrf', ''),
                'tq_flyj': doc_data.get('flyj', '')
            }
        else:
            return {}

    @staticmethod
    @transactional
    async def scene_3_data_sync(user_id: int, start_date: str, end_date: str, db: Session) -> Optional[Dict[str, int]]:
        # 将YYYYMMDD格式转换为时间戳
        start_timestamp = int(datetime.strptime(start_date, '%Y%m%d').timestamp())
        end_timestamp = int(datetime.strptime(end_date, '%Y%m%d').timestamp())
        # end_timestamp后面加一天
        end_timestamp = end_timestamp + 86400
        global ajbh
        LogUtils.info(f"开始同步数据，用户ID: {user_id}")

        try:
            LogUtils.info(f"查询时间范围: {start_timestamp} 至 {end_timestamp}")

            # 查询时间范围内的法律文书
            case_query = text("""
                SELECT * FROM (
                    SELECT 
                        jlbh ,
                        ajbh,
                        doc_name,
                        cqsx,
                        doc_data,
                        pzsj,
                        doc_num,
                        tfrxm,
                        tfsj,
                        fh_rksj,
                        ROW_NUMBER() OVER (PARTITION BY jlbh ORDER BY fh_rksj DESC) AS rn
                    FROM ods_aj_flws 
                    WHERE fh_rksj >= :start_date 
                    AND fh_rksj <= :end_date
                    AND doc_name IS NOT NULL AND doc_name IN ('提请批准逮捕书', '起诉意见书','行政处罚决定书')
                ) sub
                WHERE rn = 1
            """)
            cases = [row._asdict() for row in db.execute(case_query, {
                "start_date": start_timestamp,
                "end_date": end_timestamp
            }).fetchall()]
            LogUtils.info(f"查询到 {len(cases)} 个法律文书需要同步")

            success_count = 0
            failed_count = 0
            if len(cases) == 0:
                LogUtils.warning("没有找到需要同步的法律文书")
                return {
                    "total": len(cases),
                    "success": success_count,
                    "failed": failed_count
                }
            # 遍历法律文书并处理
            for case in cases:
                try:
                    jlbh = case['jlbh']
                    ajbh = case['ajbh']
                    LogUtils.info(f"开始处理法律文书 {jlbh}")

                    # 调用场景同步方法
                    flag, record = await Scene_3_Service.scene_3_sql(ajbh, jlbh, user_id, case, db)

                    # 根据id判断是更新还是新增
                    if flag:
                        LogUtils.info(f"法律文书 {jlbh} 数据，已存在")
                        continue
                    else:
                        # 新增记录
                        db.add(record)

                    # 提交事务
                    db.commit()

                    success_count += 1
                except Exception as e:
                    failed_count += 1
                    print(f"处理法律文书 {jlbh} 时出错: {str(e)}")
                    # db.rollback()
                    continue
            LogUtils.info(f"数据同步完成，成功: {success_count}, 失败: {failed_count}")
            return {
                "total": len(cases),
                "success": success_count,
                "failed": failed_count
            }

        except Exception as e:
            # db.rollback()
            print(f"同步过程出错: {str(e)}")
            return None

    @staticmethod
    @transactional
    async def scene_3_sql(ajbh: str, jlbh: str, user_id: int, ods_aj_flws: Dict, db: Session) -> Scene_3_Record:
        # 更新或创建分析记录
        try:
            existing_record = DatabaseService.check_legal_record_exists(db, jlbh)
        except Exception as e:
            print(f"处理法律文书 {jlbh} 时出错: {str(e)}")

        if existing_record:
            # 判断 ods_aj_flws 中的 fh_rksj 是否大于 existing_record.fh_rksj ,如果大于则删除 existing_record
            ods_fh_rksj = int(ods_aj_flws.get('fh_rksj', 0))
            existing_fh_rksj = int(existing_record.fh_rksj.timestamp()) if existing_record.fh_rksj else 0

            if ods_fh_rksj > existing_fh_rksj:
                # 清空所有分析结果相关字段
                existing_record.detection_result = None
                existing_record.check_status = 0
                existing_record.analysis_status = 0
                existing_record.jlbh = ods_aj_flws.get('jlbh', '')
                existing_record. ajbh = ods_aj_flws.get('ajbh', ''),
                existing_record.doc_name = ods_aj_flws.get('doc_name', ''),
                existing_record.doc_num = ods_aj_flws.get('doc_num', ''),
                existing_record.tfrxm = ods_aj_flws.get('tfrxm', ''),
                existing_record.tfsj = datetime.strptime(ods_aj_flws.get('tfsj', ''), '%Y%m%d%H%M%S') if ods_aj_flws.get(
                    'tfsj') else None,
                existing_record.cqsx = ods_aj_flws.get('cqsx', ''),
                existing_record.doc_data = ods_aj_flws.get('doc_data', ''),
                # 将YYYYMMDD格式转换为datetime
                existing_record.pzsj = datetime.strptime(ods_aj_flws.get('pzsj', ''), '%Y%m%d%H%M%S') if ods_aj_flws.get(
                    'pzsj') else None,
                existing_record.fh_rksj = datetime.fromtimestamp(int(ods_aj_flws.get('fh_rksj', 0))) if ods_aj_flws.get(
                    'fh_rksj') else None,
                existing_record.user_id = user_id
                new_record = existing_record
                LogUtils.info(f"法律文书 旧数据更新 {existing_record.jlbh}")
            else:
                # 如果新记录的时间戳不大于已存在记录，则直接返回已存在的记录
                record_dict = {k: v for k, v in vars(existing_record).items()
                               if not k.startswith('_')}
                return True, Scene_3_Record(**record_dict)
        else:
            new_record = Scene_3_Record(
                jlbh=ods_aj_flws.get('jlbh', ''),
                ajbh=ods_aj_flws.get('ajbh', ''),
                doc_name=ods_aj_flws.get('doc_name', ''),
                doc_num=ods_aj_flws.get('doc_num', ''),
                tfrxm=ods_aj_flws.get('tfrxm', ''),
                tfsj=datetime.strptime(ods_aj_flws.get('tfsj', ''), '%Y%m%d%H%M%S') if ods_aj_flws.get(
                    'tfsj') else None,
                cqsx=ods_aj_flws.get('cqsx', ''),
                doc_data=ods_aj_flws.get('doc_data', ''),
                # 将YYYYMMDD格式转换为datetime
                pzsj=datetime.strptime(ods_aj_flws.get('pzsj', ''), '%Y%m%d%H%M%S') if ods_aj_flws.get(
                    'pzsj') else None,
                fh_rksj=datetime.fromtimestamp(int(ods_aj_flws.get('fh_rksj', 0))) if ods_aj_flws.get(
                    'fh_rksj') else None,
                analysis_status=0,
                user_id=user_id
            )

        # 案件信息补充
        related_aj_query = text(
            "SELECT ajmc FROM ods_aj_jbxx WHERE ajbh = :ajbh  order by fh_rksj desc limit 1")
        ajs = [row._asdict() for row in db.execute(related_aj_query, {"ajbh": ajbh}).fetchall()]
        if not ajs:
            LogUtils.info(f"法律文书关联案件信息为空,案件编号为 ajbh: {ajbh}")
            return False, new_record
        new_record.ajmc = ajs[0].get("ajmc", "")

        LogUtils.info(f"法律文书 {jlbh} 数据查询完成，创建新记录")
        return False, new_record

    @staticmethod
    @transactional
    async def scene_3_doc_data_analyze(jlbhs: [str] = None, db: Session = None):
        try:
            # 构建查询条件
            query = db.query(Scene_3_Record).filter(
                or_(
                    Scene_3_Record.analysis_status == 0,
                    Scene_3_Record.analysis_status == 3
                ))

            # 如果提供了法律文书编号数组，则添加法律文书编号过滤条件
            if jlbhs and len(jlbhs) > 0:
                query = query.filter(Scene_3_Record.jlbh.in_(jlbhs))

            # 获取所有符合条件的记录
            records = query.all()
            total_records = len(records)
            LogUtils.info(f"开始分析法律文书，总数: {total_records}")

            if total_records == 0:
                LogUtils.warning("没有找到需要分析的法律文书")
                return {
                    "total": 0,
                    "success": 0,
                    "failed": 0
                }

            # 将任务添加到队列
            tasks = [
                Scene_3_Service._analyze_single_case(record, db)
                for record in records
            ]

            # 并发添加任务到队列
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            success_count = sum(1 for r in results if r is True)
            failed_count = sum(1 for r in results if r is False)

            LogUtils.info(f"任务已添加到队列，总数: {total_records}, 成功: {success_count}, 失败: {failed_count}")
            return {
                "total": total_records,
                "success": success_count,
                "failed": failed_count,
                "message": "分析任务已添加到队列，将在后台处理"
            }

        except Exception as e:
            db.rollback()
            LogUtils.error(f"分析过程出错: {str(e)}")
            raise Exception(f"分析过程出错: {str(e)}")

    @staticmethod
    async def _analyze_single_case(record: Scene_3_Record, db: Session) -> bool:
        """
        将法律文书添加到分析队列

        Args:
            record: 法律文书记录
            db: 数据库会话

        Returns:
            bool: 是否成功添加到队列
        """
        try:
            # 将任务添加到队列
            await analysis_queue.put((record.id, db))
            return True
        except Exception as e:
            record.analysis_status = 3
            db.add(record)
            db.commit()
            LogUtils.error(f"添加法律文书 {record.ajbh} 到队列时出错: {str(e)}")
            return False

    @staticmethod
    async def start_background_processor(task_semaphore):
        """
        启动后台任务处理器

        Args:
            task_semaphore: 任务信号量，用于控制并发数量
        """
        LogUtils.info("启动后台任务处理器")
        while True:
            try:
                # 从队列获取任务，设置超时时间为5秒
                try:
                    id, db = await asyncio.wait_for(analysis_queue.get(), timeout=5.0)
                except asyncio.TimeoutError:
                    LogUtils.debug("队列为空，等待5秒后重新检查")
                    await asyncio.sleep(5)
                    continue

                try:
                    # 创建新的任务，但不等待其完成
                    task = asyncio.create_task(
                        Scene_3_Service._process_single_case_with_semaphore(id, db, task_semaphore)
                    )
                    # 添加任务完成回调，用于错误处理
                    task.add_done_callback(
                        lambda t: LogUtils.error(f"任务异常: {t.exception()}") if t.exception() else None)
                except Exception as e:
                    LogUtils.error(f"创建任务时出错: {str(e)}")
                finally:
                    # 标记任务完成
                    analysis_queue.task_done()
            except Exception as e:
                LogUtils.error(f"后台处理器出错: {str(e)}")
                await asyncio.sleep(1)  # 出错时等待一秒再继续

    @staticmethod
    async def _process_single_case_with_semaphore(id: int, db: Session, semaphore: asyncio.Semaphore):
        """
        使用信号量处理单个法律文书的后台方法

        Args:
            record: 法律文书记录
            db: 数据库会话
            semaphore: 信号量对象
        """
        async with semaphore:  # 使用信号量控制并发
            LogUtils.info(f"任务 Scene_3_Record.id: {id} 开始处理")
            try:
                record = db.query(Scene_3_Record).filter(
                    Scene_3_Record.id == id
                ).first()

                # 若已存在记录，检查src是否有变更
                if not record.doc_data:
                    record.analysis_status = 2
                    detection_result = {"doc_data_content": {"status": "ERROR", "title": "法律文书内容为空",
                                                             "reason": "法律文书内容为空"}}
                    record.detection_result = json.dumps(detection_result, ensure_ascii=False)
                    db.merge(record)
                    db.commit()
                    return True
                # 更新法律文书状态为处理中
                record.analysis_status = 1
                db.merge(record)
                db.commit()

                # 使用aiohttp进行异步HTTP请求
                timeout = aiohttp.ClientTimeout(total=120)  # 设置30秒总超时
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    headers = {
                        'Authorization': f'Bearer {settings.SCENE3_API_KEY}',
                    }

                    json_data = {
                        'inputs': {'jlbh': record.jlbh},
                        'response_mode': 'blocking',
                        'user': 'abc-123',
                    }

                    try:
                        async with session.post(
                                f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                                headers=headers,
                                json=json_data,
                                timeout=timeout  # 为单个请求设置超时
                        ) as response:
                            if response.status == 200:
                                # 获取响应数据
                                response_data = await response.json()
                                if response_data['data']['status'] != 'succeeded':
                                    record.analysis_status = 3
                                    db.merge(record)
                                    db.commit()
                            else:
                                record.analysis_status = 3
                                db.merge(record)
                                db.commit()
                    except asyncio.TimeoutError:
                        LogUtils.error(f"任务 {record.jlbh} API请求超时")
                        record.analysis_status = 3
                        db.merge(record)
                        db.commit()
                        raise
                LogUtils.info(f"任务 {record.jlbh} 处理完成")
            except Exception as e:
                record.analysis_status = 3
                db.merge(record)
                db.commit()
                LogUtils.error(f"分析法律文书 {record.jlbh} 时出错: {str(e)}")
                raise  # 重新抛出异常，以便在任务完成回调中捕获
