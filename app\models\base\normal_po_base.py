from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

"""
普通表构建基类
"""

NormalBase = declarative_base()

class NormalPoBase(NormalBase):
    """抽象的基类模型 (不会创建实际表)"""
    __abstract__ = True
    __allow_unmapped__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    update_time = Column(
        DateTime,
        nullable=True,
        server_default=func.now(),
        onupdate=func.now(),
        index=True,
        comment="最后修改时间"
    )
    
    def __repr__(self):
        return f"<SceneAnalysis(id={self.id})>"
