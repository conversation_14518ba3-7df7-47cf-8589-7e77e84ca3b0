<template>
  <div class="left-panel">
    <div class="panel-item">
      <div class="panel-title">办件总量统计</div>
      <div class="panel-content">
        <div class="data-item">
          <span class="number">{{ totalCount }}</span>
          <span class="label">总数量</span>
        </div>
        <div class="data-item">
          <span class="number">{{ todayCount }}</span>
          <span class="label">今日新增</span>
        </div>
      </div>
      <div class="chart" ref="totalChart"></div>
    </div>
    <div class="panel-item">
      <div class="panel-title">业务类型分布</div>
      <div class="chart" ref="typeChart"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'LeftPanel',
  data() {
    return {
      totalCount: '256,789',
      todayCount: '1,234',
      totalChart: null,
      typeChart: null
    }
  },
  mounted() {
    this.initTotalChart()
    this.initTypeChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.totalChart && this.totalChart.resize()
      this.typeChart && this.typeChart.resize()
    },
    initTotalChart() {
      this.totalChart = echarts.init(this.$refs.totalChart)
      const option = {
        color: ['#00ffff'],
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          axisLine: {
            lineStyle: {
              color: '#ffffff'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ffffff'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [{
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          }
        }]
      }
      this.totalChart.setOption(option)
    },
    initTypeChart() {
      this.typeChart = echarts.init(this.$refs.typeChart)
      const option = {
        color: ['#00ffff', '#00ff00', '#ffff00', '#ff00ff'],
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          textStyle: {
            color: '#ffffff'
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            data: [
              { value: 1048, name: '个人业务' },
              { value: 735, name: '企业业务' },
              { value: 580, name: '特殊业务' },
              { value: 484, name: '其他业务' }
            ],
            label: {
              color: '#ffffff'
            }
          }
        ]
      }
      this.typeChart.setOption(option)
    }
  }
}
</script>

<style scoped>
.left-panel {
  width: 400px;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.panel-item {
  width: 100%;
  height: 45%;
  background: rgba(0,48,102,0.3);
  border: 1px solid rgba(0,255,255,0.2);
  margin-bottom: 20px;
  padding: 15px;
  box-sizing: border-box;
}

.panel-title {
  color: #00ffff;
  font-size: 18px;
  margin-bottom: 15px;
}

.panel-content {
  display: flex;
  justify-content: space-around;
  margin-bottom: 15px;
}

.data-item {
  text-align: center;
}

.number {
  display: block;
  font-size: 24px;
  color: #00ffff;
  margin-bottom: 5px;
}

.label {
  color: #ffffff;
  font-size: 14px;
}

.chart {
  width: 100%;
  height: calc(100% - 80px);
}
</style> 