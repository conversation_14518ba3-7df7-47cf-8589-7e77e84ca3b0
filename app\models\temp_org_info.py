
from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, BigInteger, DateTime
from app.db.database import Base

class temp_org_map(Base):
    __tablename__ = "temp_org_map"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    org_code = Column(String(255), nullable=False, comment="我们的orgCode")
    org_name = Column(String(255), nullable=False, comment="单位名称")
    origin_code = Column(String(255), nullable=False, comment="映射源orgCode")
    police_unit = Column(String(255), nullable=False, comment="警综单位")
    police_level = Column(String(255), nullable=False, comment="警综单位")
    origin_partent_code = Column(String(255), nullable=False, comment="警综单位")

