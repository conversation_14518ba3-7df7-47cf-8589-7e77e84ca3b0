/**
 * 笔录数据模拟API
 */

/**
 * 模拟笔录列表数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回模拟的数据
 */
export function mockGetRecordList(params) {
  return new Promise(resolve => {
    console.log('请求参数:', params);
    
    // 模拟分页
    const page = params.page || 1;
    const pageSize = params.page_size || 10;
    
    // 创建模拟数据
    const allRecords = generateMockRecords();
    
    // 根据搜索条件筛选数据
    let filteredRecords = allRecords;
    
    if (params.jlbh) {
      filteredRecords = filteredRecords.filter(record => record.jlbh.includes(params.jlbh));
    }
    
    if (params.ajbh) {
      filteredRecords = filteredRecords.filter(record => record.ajbh.includes(params.ajbh));
    }
    
    if (params.bldd) {
      filteredRecords = filteredRecords.filter(record => record.bldd.includes(params.bldd));
    }
    
    if (params.asker) {
      filteredRecords = filteredRecords.filter(record => record.mjyxm.includes(params.asker));
    }
    
    if (params.analysis_status !== undefined && params.analysis_status !== null) {
      filteredRecords = filteredRecords.filter(record => record.analysis_status === params.analysis_status);
    }
    
    if (params.check_status !== undefined && params.check_status !== null) {
      filteredRecords = filteredRecords.filter(record => record.check_status === params.check_status);
    }
    
    // 实现分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedRecords = filteredRecords.slice(startIndex, endIndex);
    
    // 延迟200ms模拟网络请求
    setTimeout(() => {
      resolve({
        code: 200,
        message: '获取数据成功',
        data: {
          records: paginatedRecords,
          total: filteredRecords.length,
          page: page,
          pageSize: pageSize
        }
      });
    }, 200);
  });
}

/**
 * 生成模拟笔录数据
 * @returns {Array} - 返回模拟数据数组
 */
function generateMockRecords() {
  const records = [];
  
  // 笔录地点列表
  const locations = ['某市公安局侦查办案中心', '某区分局审讯室', '某派出所办公室', '现场勘查地点', '嫌疑人住所'];
  
  // 民警姓名列表
  const policeNames = ['张警官', '李警官', '王警官', '赵警官', '刘警官'];
  
  // 案件类型
  const caseTypes = ['盗窃案', '诈骗案', '伤害案', '交通肇事案', '故意毁坏财物案'];
  
  // 生成30条模拟数据
  for (let i = 1; i <= 30; i++) {
    // 随机日期，最近3个月内
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 90));
    const formattedDate = date.toISOString().split('T')[0];
    
    // 随机时间
    const hours = String(Math.floor(Math.random() * 24)).padStart(2, '0');
    const minutes = String(Math.floor(Math.random() * 60)).padStart(2, '0');
    const seconds = String(Math.floor(Math.random() * 60)).padStart(2, '0');
    const formattedTime = `${hours}:${minutes}:${seconds}`;
    
    // 随机分析状态(0-未分析,1-已分析)
    const analysisStatus = Math.random() > 0.3 ? 1 : 0;
    
    // 随机审核状态(0-正常,1-异常)
    const checkStatus = Math.random() > 0.7 ? 1 : 0;
    
    // 随机地点
    const location = locations[Math.floor(Math.random() * locations.length)];
    
    // 随机民警
    const policeName = policeNames[Math.floor(Math.random() * policeNames.length)];
    
    // 随机案件类型
    const caseType = caseTypes[Math.floor(Math.random() * caseTypes.length)];
    
    // 生成笔录编号(年月日+4位序号)
    const jlbh = `BL${formattedDate.replace(/-/g, '')}${String(i).padStart(4, '0')}`;
    
    // 生成案件编号
    const ajbh = `A${formattedDate.split('-')[0]}${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;
    
    // 模拟检测结果 (仅对已分析的记录)
    let detectionResult = null;
    if (analysisStatus === 1) {
      // 生成基础信息问题
      const statusOptions = ['NULL', 'ERROR'];
      const timeCheckStatusOptions = ['TIME_FORMAT_ERROR', 'START_END_NULL', 'START_NULL', 'END_NULL', 'TIME_LIMIT'];
      
      // 初始化基础信息字段, 所有字段默认为RIGHT状态
      const basicInfo = {
        // 询问人1信息
        mjyxm: {
          status: 'RIGHT'
        },
        // 询问人2信息
        mjexm: {
          status: 'RIGHT'
        },
        // 记录人信息
        jlrxm: {
          status: 'RIGHT'
        },
        // 手机号信息
        tel_num: {
          status: 'RIGHT'
        },
        // 时间检查
        timecheck: {
          status: 'RIGHT'
        },
        // 询问地点
        bldd: {
          status: 'RIGHT',
          reason: ''
        },
        // 户籍地址
        hjszd: {
          status: 'RIGHT',
          reason: ''
        },
        // 现住址
        xzz: {
          status: 'RIGHT',
          reason: ''
        }
      };
      
      // 如果状态为异常, 则随机生成异常字段
      if (checkStatus === 1) {
        // 随机选择2-4个字段设置为异常
        const fieldCount = Math.floor(Math.random() * 3) + 2;
        const allFields = Object.keys(basicInfo);
        const shuffledFields = [...allFields].sort(() => 0.5 - Math.random());
        const selectedFields = shuffledFields.slice(0, fieldCount);
        
        for (const field of selectedFields) {
          // 时间检查字段特殊处理
          if (field === 'timecheck') {
            basicInfo[field].status = timeCheckStatusOptions[Math.floor(Math.random() * timeCheckStatusOptions.length)];
          } 
          // 地点相关字段添加原因
          else if (['bldd', 'hjszd', 'xzz'].includes(field)) {
            basicInfo[field].status = statusOptions[Math.floor(Math.random() * statusOptions.length)];
            basicInfo[field].reason = basicInfo[field].status === 'NULL' ? '为空' : '信息不完整，缺少详细地址';
          }
          // 其他字段
          else {
            basicInfo[field].status = statusOptions[Math.floor(Math.random() * statusOptions.length)];
          }
        }
      }
      
      // 生成笔录内容问题
      const blnrContentStatus = Math.random() > 0.8 ? 'NULL' : 'RIGHT';
      const blnrChartStatus = Math.random() > 0.7 ? 'ERROR' : 'RIGHT';
      const blnrPunctuationStatus = Math.random() > 0.7 ? 'ERROR' : 'RIGHT';
      
      // 为笔录内容问题生成数据
      const blnrContent = {
        status: blnrContentStatus
      };
      
      // 生成错别字数据
      const blnrChart = {
        status: blnrChartStatus,
        jump_result: [],
        final_text: ''
      };
      
      if (blnrChartStatus === 'ERROR') {
        // 生成1-3个错别字问题
        const errorCount = Math.floor(Math.random() * 3) + 1;
        const errorWords = [
          '询问笔录中"被告知"错写为"被高知"',
          '询问笔录中"到场"错写为"道场"',
          '询问笔录中"事实"错写为"事实上"',
          '询问笔录中"证件"错写为"正件"',
          '询问笔录中"现场"错写为"线场"'
        ];
        
        // 随机选择错别字
        for (let j = 0; j < errorCount; j++) {
          if (j < errorWords.length) {
            blnrChart.jump_result.push(errorWords[j]);
          }
        }
        
        // 生成问答示例HTML
        const qaExamples = [
          {
            question: "请问你是什么时候发现财物被盗的？",
            answer: "昨天晚上9点左右回家的时候发现门锁被撬，进屋后发现财物被盗。",
            error: "询问时间不明确，未追问具体日期"
          },
          {
            question: "你有看到嫌疑人的样子吗？",
            answer: "没有，我回来的时候嫌疑人已经离开了。",
            error: "未详细询问周边是否有监控设备或目击证人"
          },
          {
            question: "是不是王某让你这么做的？",
            answer: "是的。",
            error: "存在诱导性提问，暗示被询问人回答特定内容"
          }
        ];
        
        // 生成问答展示的HTML
        blnrChart.final_text = qaExamples.map((qa, index) => {
          return `
            <div class="qa-pair" id="qa-pair-${index}">
              <div class="qa-question">问：${qa.question}</div>
              <div class="qa-answer">答：${qa.answer}</div>
              <div class="qa-error">问题：<span class="error-text">${qa.error}</span></div>
            </div>
          `;
        }).join('<hr />');
      }
      
      // 生成标点符号问题
      const blnrPunctuation = {
        status: blnrPunctuationStatus,
        sentences: [],
        amend: []
      };
      
      if (blnrPunctuationStatus === 'ERROR') {
        // 生成1-3个标点符号问题
        const punctuationCount = Math.floor(Math.random() * 3) + 1;
        const punctuationErrors = [
          { original: '我昨天晚上在家', amended: '我昨天晚上在家。' },
          { original: '他说他不知道这件事', amended: '他说他不知道这件事。' },
          { original: '被询问人称没有参与盗窃', amended: '被询问人称：没有参与盗窃。' },
          { original: '我在现场看到了三个人', amended: '我在现场看到了三个人。' },
          { original: '当时天已经黑了我看不清楚', amended: '当时天已经黑了，我看不清楚。' }
        ];
        
        // 随机选择标点符号错误
        const selectedErrors = [...punctuationErrors]
          .sort(() => 0.5 - Math.random())
          .slice(0, punctuationCount);
        
        for (const error of selectedErrors) {
          blnrPunctuation.sentences.push(error.original);
          blnrPunctuation.amend.push(error.amended);
        }
      }
      
      // 生成摘要
      const blnrRole = {
        reason: `这是一份关于${caseType}的询问笔录，由${policeName}在${location}询问嫌疑人/证人。笔录主要内容围绕案件发生经过展开。`
      };
      
      // 合并所有检测结果
      detectionResult = JSON.stringify({
        check_status: checkStatus,
        ...basicInfo,
        blnr_content: blnrContent,
        blnr_chart: blnrChart,
        blnr_punctuation: blnrPunctuation,
        blnr_role: blnrRole
      });
    }
    
    records.push({
      id: i,
      jlbh: jlbh,
      ajbh: ajbh,
      ajmc: `${caseType}(${ajbh})`,
      blsj: `${formattedDate} ${formattedTime}`,
      bldd: location,
      mjyxm: policeName,
      brnm: `被询问人${i}`,
      brxb: Math.random() > 0.7 ? '女' : '男',
      brnl: Math.floor(Math.random() * 40) + 18,
      blnr: `这是第${i}份笔录的内容摘要，案件类型为${caseType}，由${policeName}询问...`,
      analysis_status: analysisStatus,
      check_status: checkStatus,
      detection_result: detectionResult,
      create_time: `${formattedDate} ${formattedTime}`,
      update_time: `${formattedDate} ${formattedTime}`
    });
  }
  
  return records;
} 