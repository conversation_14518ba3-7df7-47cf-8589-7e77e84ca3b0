from sqlalchemy import Column, String, Integer, DateTime, Text, case
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Scence_4_Base = declarative_base()


class Scene4PO(Scence_4_Base):
    """笔录记录数据模型，临时存储案件笔录相关的记录信息，包括笔录基本信息、人员信息、时间地点信息等"""
    __tablename__ = 'scene_4_record'
    # 记录ID，主键
    id = Column(Integer, primary_key=True)
    # User ID
    user_id = Column(Integer, index=True)

    # 唯一标识
    md_id = Column(String(50), index=True, comment='唯一标识')

    jlbh = Column(String(200), index=True, comment='记录编号')

    ajbh = Column(String(200), index=True, comment='关联_案件编号')

    ajmc = Column(String(200), index=True, comment='案件名称')

    blmc = Column(String(200), index=True, comment='笔录名称')

    bldd = Column(String(200), index=True, comment='笔录_地点_地点名称')

    mjyxm = Column(String(100), index=True, comment='民警_一_姓名')
    mjexm = Column(String(100), index=True, comment='民警_二_姓名')

    jlrxm = Column(String(100), index=True, comment='记录人_姓名')
    tel_num = Column(String(200), index=True, comment='被询问人_电话号码')
    sary_xm = Column(String(200), index=True, comment='被询问人_姓名')
    xzz = Column(String(100), index=True, comment='现住址')
    hjszd = Column(String(100), index=True, comment='户籍所在地')

    # 笔录内容 - 修改为Text类型，并使用前缀索引
    blnr = Column(Text, comment='笔录_内容_简要情况')

    # 时间信息
    kssj = Column(DateTime, index=True, comment='开始时间')
    jssj = Column(DateTime, index=True, comment='结束时间')
    djsj = Column(DateTime, index=True, comment='登记时间')

    # 检测相关字段
    detection_result = Column(Text, comment='笔录检测结果')
    # 分析状态 0:无问题 1:有问题
    check_status = Column(Integer, default=0, nullable=False, index=True)
    # 分析状态 0:未分析 1:分析中 2:分析完成 3:分析失败
    analysis_status = Column(Integer, default=0, nullable=False, index=True)

    created_at = Column(DateTime, nullable=False, server_default=func.now(), index=True, comment="创建时间")
    org_code =Column(String(255), index=True, comment='机构code')
    # 文件上传时间
    upload_time = Column(
        DateTime,
        nullable=True,
        server_default=func.now(),
        onupdate=func.now(),
        index=True,
        comment="最后修改时间"
    )

    fh_rksj = Column(DateTime, index=True, comment='元数据_入库时间')
def scene4_search_order():
    # 定义分析状态的排序优先级
    analysis_status_order = {
        1: 1,  # 分析中
        2: 2,  # 已分析
        0: 3,      # 未分析
        3: 4     # 分析失败
    }

    # 使用case语句进行自定义排序
    return case(
        {status: order for status, order in analysis_status_order.items()},
        value=Scene4PO.analysis_status
    )

