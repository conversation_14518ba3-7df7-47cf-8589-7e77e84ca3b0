import asyncio
from datetime import datetime
from typing import Dict, Optional

import aiohttp
from sqlalchemy import text, or_
from sqlalchemy.orm import Session

from app.config import settings
from app.core.context_handle import UserContext
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.scene_4_po import Scene4PO
from app.services.user.org_service import OrgService

# 创建任务队列
analysis_queue = asyncio.Queue()


class Scene4Service:
    @staticmethod
    async def start_background_processor(task_semaphore):
        """
        启动后台任务处理器

        Args:
            task_semaphore: 任务信号量，用于控制并发数量
        """
        LogUtils.info("启动后台任务处理器")
        while True:
            try:
                # 从队列获取任务，设置超时时间为5秒
                try:
                    id, db = await asyncio.wait_for(analysis_queue.get(), timeout=5.0)
                except asyncio.TimeoutError:
                    LogUtils.debug("队列为空，等待5秒后重新检查")
                    await asyncio.sleep(5)
                    continue

                try:
                    # 创建新的任务，但不等待其完成
                    task = asyncio.create_task(
                        Scene4Service._process_single_case_with_semaphore(id, db, task_semaphore)
                    )
                    # 添加任务完成回调，用于错误处理
                    task.add_done_callback(
                        lambda t: LogUtils.error(f"任务异常: {t.exception()}") if t.exception() else None)
                except Exception as e:
                    LogUtils.error(f"创建任务时出错: {str(e)}")
                finally:
                    # 标记任务完成
                    analysis_queue.task_done()
            except Exception as e:
                LogUtils.error(f"后台处理器出错: {str(e)}")
                await asyncio.sleep(1)  # 出错时等待一秒再继续

    @staticmethod
    async def _process_single_case_with_semaphore(id: int, db: Session,
                                                  semaphore: asyncio.Semaphore):
        """
        使用信号量处理单个案件的后台方法

        Args:
            record: 案件记录
            db: 数据库会话
            semaphore: 信号量对象
        """
        async with semaphore:  # 使用信号量控制并发
            LogUtils.info(f"任务 Scene4PO.id: {id} 开始处理")
            try:
                record = db.query(Scene4PO).filter(
                    Scene4PO.id == id
                ).first()
                # 更新案件状态为处理中
                record.analysis_status = 1
                db.merge(record)
                db.commit()
                LogUtils.info(f"笔录分析中 {record.id}")
                # 使用aiohttp进行异步HTTP请求
                timeout = aiohttp.ClientTimeout(total=180)  # 设置60秒总超时
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    headers = {
                        'Authorization': f'Bearer {settings.SCENE4_API_KEY}',
                    }

                    json_data = {
                        'inputs': {'id': f'{record.id}'},
                        'response_mode': 'blocking',
                        'user': 'abc-123',
                    }

                    try:
                        async with session.post(
                                f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                                headers=headers,
                                json=json_data,
                                timeout=timeout  # 为单个请求设置超时
                        ) as response:
                            if response.status == 200:
                                # 获取响应数据
                                response_data = await response.json()
                                if response_data['data']['status'] != 'succeeded':
                                    record.analysis_status = 3
                                    db.merge(record)
                                    db.commit()
                            else:
                                record.analysis_status = 3
                                db.merge(record)
                                db.commit()
                    except asyncio.TimeoutError:
                        LogUtils.error(f"任务 {record.jlbh} API请求超时")
                        record.analysis_status = 3
                        db.merge(record)
                        db.commit()
                        raise
                LogUtils.info(f"任务 {record.jlbh} 处理完成")
            except Exception as e:
                record.analysis_status = 3
                db.merge(record)
                db.commit()
                LogUtils.error(f"分析笔录 {record.jlbh} 时出错: {str(e)}")
                raise  # 重新抛出异常，以便在任务完成回调中捕获

    @staticmethod
    async def _analyze_single_case(record: Scene4PO, db: Session) -> bool:
        """
        将笔录数据添加到分析队列

        Args:
            record: 笔录记录
            db: 数据库会话

        Returns:
            bool: 是否成功添加到队列
        """
        try:
            await analysis_queue.put((record.id, db))
            return True
        except Exception as e:
            record.analysis_status = 3
            db.add(record)
            db.commit()
            LogUtils.error(f"添加案件 {record.id} 到队列时出错: {str(e)}")
            return False

    @staticmethod
    @transactional
    async def scene_4_summary_analyze(ids: [int] = None, db: Session = None):
        """
        笔录场景分析方法

        Args:
            md_ids: 案件编号数组，默认为None（将查询所有状态为init的案件）

        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            # 构建查询条件
            query = db.query(Scene4PO).filter(
                or_(
                    Scene4PO.analysis_status == 0,
                    Scene4PO.analysis_status == 3
                )
            )

            # 如果提供了md_ids，则添加md_id过滤条件
            if ids and len(ids) > 0:
                query = query.filter(Scene4PO.id.in_(ids))

            # 获取所有符合条件的记录
            records = query.all()
            total_records = len(records)
            LogUtils.info(f"开始分析案件，总数: {total_records}")

            if total_records == 0:
                LogUtils.warning("没有找到需要分析的案件")
                return {
                    "total": 0,
                    "success": 0,
                    "failed": 0
                }

            # 将任务添加到队列
            tasks = [
                Scene4Service._analyze_single_case(record, db)
                for record in records
            ]

            # 并发添加任务到队列
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            success_count = sum(1 for r in results if r is True)
            failed_count = sum(1 for r in results if r is False)

            LogUtils.info(f"任务已添加到队列，总数: {total_records}, 成功: {success_count}, 失败: {failed_count}")
            return {
                "total": total_records,
                "success": success_count,
                "failed": failed_count,
                "message": "分析任务已添加到队列，将在后台处理"
            }

        except Exception as e:
            db.rollback()
            LogUtils.error(f"分析过程出错: {str(e)}")
            raise Exception(f"分析过程出错: {str(e)}")

    @staticmethod
    @transactional
    async def scene_4_data_sync(user_id: int, start_date: str, end_date: str, db: Session) -> Optional[Dict[str, int]]:
        """传入时间格式20250401
        同步笔录数据 根据登记时间djsj(时间格式为:20250207162505)
        """

        start_timestamp = start_date + '000000'
        end_timestamp = end_date + '235959'

        LogUtils.info(f"开始同步数据，用户ID: {user_id}")
        try:
            # 计算时间范围
            # end_date = datetime.now() - timedelta(days=90)
            # start_date = end_date - timedelta(days=90)  # 180天前到90天前
            LogUtils.info(f"查询时间范围: {start_timestamp} 至 {end_timestamp}")


            # org = UserContext.get_current_user_org()
            # org_service = OrgService(db)
            # 根据当前用户 获取 当前下所有子部门 orgCode 关系映射
            # origin_org_code = org_service.getCurrentOrgList(db)

            org_map = OrgService(db).get_org_map(db)
            # 查询时间范围内的案件编号
            case_query = text("""
                SELECT * FROM (
                    SELECT 
                      blxx.  md_id,
                      blxx.bldd,
                        blxx.jlbh,
                       blxx.mjyxm,
                      blxx.mjexm,
                      blxx.jlrxm,
                     blxx. blnr,
                     blxx.kssj,
                      blxx.jssj,
                       blxx.fh_rksj,
                       blxx.glajbh,
                       blxx.blmc,
                        blxx.djsj,
                        blxx.djdw as djdw,
                        ROW_NUMBER() OVER (PARTITION BY jlbh ORDER BY fh_rksj DESC) AS rn
                    FROM ods_aj_blxx blxx 
                    WHERE blxx.djsj >= :start_date 
                    AND blxx.djsj <= :end_date
                ) sub
                WHERE rn = 1
            """)
            cases = [row._asdict() for row in db.execute(case_query, {
                "start_date": start_timestamp,
                "end_date": end_timestamp
            }).fetchall()]
            LogUtils.info(f"查询到 {len(cases)} 个案件需要同步")

            success_count = 0
            failed_count = 0
            if len(cases) == 0:
                LogUtils.warning("没有找到需要同步的笔录")
                return {
                    "total": len(cases),
                    "success": success_count,
                    "failed": failed_count
                }
            # 遍历笔录并处理
            jlbh = ''
            for case in cases:
                try:
                    glajbh = case['glajbh']
                    blmc = case['blmc']
                    jlbh = case['jlbh']
                    # blmc根据"-"分割，取第一个元素
                    name = blmc.split("-")[0]
                    LogUtils.info(f"开始处理笔录 {jlbh}")

                    # 调用场景同步方法
                    flag, record = await Scene4Service.scene_4_sql(glajbh, name, jlbh, user_id, case, db)
                    # 根据id判断是更新还是新增
                    if flag:
                        # 存在记录跳过
                        LogUtils.info(f"笔录 {jlbh} 数据，已存在")
                        continue
                    # 更新记录的基本信息
                    record.md_id = case['md_id']
                    record.jlbh = case['jlbh']
                    record.bldd = case['bldd']
                    record.mjexm = case['mjexm']
                    record.jlrxm = case['jlrxm']
                    record.blnr = case['blnr']
                    record.ajbh = case['glajbh']
                    record.fh_rksj = datetime.fromtimestamp(int(case.get('fh_rksj', 0))) if case.get(
                        'fh_rksj') else None
                    # case['kssj']与case['jssj']的格式为时间戳（示例：'20230410100100'），需要转换为datetime格式
                    record.kssj = datetime.strptime(case.get('kssj', ''), '%Y%m%d%H%M%S') if case.get(
                        'kssj') else None
                    record.jssj = datetime.strptime(case.get('jssj', ''), '%Y%m%d%H%M%S') if case.get(
                        'jssj') else None
                    record.djsj = datetime.strptime(case.get('djsj', ''), '%Y%m%d%H%M%S') if case.get(
                        'djsj') else None
                    record.org_code=org_map.get(case['djdw'],"0000")

                    # 新增记录
                    db.add(record)

                    # 提交事务
                    db.commit()
                    success_count += 1
                    print(f"同步 笔录jlbh: {record.jlbh}")
                except Exception as e:
                    failed_count += 1
                    print(f"处理笔录 {jlbh} 时出错: {str(e)}")
                    # db.rollback()
                    continue

            LogUtils.info(f"数据同步完成，成功: {success_count}, 失败: {failed_count}")
            return {
                "total": len(cases),
                "success": success_count,
                "failed": failed_count
            }

        except Exception as e:
            # db.rollback()
            print(f"同步过程出错: {str(e)}")
            return None

    @staticmethod
    @transactional
    async def scene_4_sql(ajbh: str, name: str, jlbh: str, user_id: int, ods_aj_blxx: Dict, db: Session):
        """
        立而不侦场景SQL查询方法

        Args:
            glajbh (str): 案件编号
            name (str): 姓名
            user_id (int): 用户ID
            db (Session): 数据库会话

        Returns:
            Scene4PO: 笔录记录实体
        """

        # 更新或创建分析记录
        existing_record = db.query(Scene4PO).filter(Scene4PO.jlbh == jlbh).first()
        if existing_record:
            # 判断 ods_aj_flws 中的 fh_rksj 是否大于 existing_record.fh_rksj ,如果大于则删除 existing_record
            ods_fh_rksj = int(ods_aj_blxx.get('fh_rksj', 0))
            existing_fh_rksj = int(existing_record.fh_rksj.timestamp()) if existing_record.fh_rksj else 0
            # 当任一时间戳为空或新数据时间戳更新时，更新记录
            if not ods_fh_rksj or not existing_fh_rksj or ods_fh_rksj > existing_fh_rksj:
                # 重置记录状态
                existing_record.user_id = user_id
                existing_record.check_status = 0
                existing_record.analysis_status = 0
                existing_record.detection_result = None
                new_record = existing_record
                LogUtils.info(f"笔录 旧数据更新 {existing_record.jlbh}")
            else:
                # 如果新记录的时间戳不大于已存在记录，则直接返回已存在的记录
                record_dict = {k: v for k, v in vars(existing_record).items()
                               if not k.startswith('_')}
                return True, Scene4PO(**record_dict)
        else:
            new_record = Scene4PO(
                user_id=user_id,
                check_status=0,
                analysis_status=0,
                detection_result=None,
            )

        # 案件信息补充
        related_aj_query = text(
            "SELECT ajmc FROM ods_aj_jbxx WHERE ajbh = :ajbh  order by fh_rksj desc limit 1")
        ajs = [row._asdict() for row in db.execute(related_aj_query, {"ajbh": ajbh}).fetchall()]
        if not ajs:
            LogUtils.info(f"案件信息，创建空记录 jlbh: {jlbh}")
            return False, new_record
        new_record.ajmc = ajs[0].get("ajmc", "")

        # 涉案人员信息补充
        related_user_query = text(
            "SELECT xzqh, xzxz, hjqh, hjxz, xm, ajbh, lxdh FROM ods_aj_sary WHERE ajbh = :ajbh AND xm = :name order by fh_rksj desc limit 1")
        users = [row._asdict() for row in db.execute(related_user_query, {"ajbh": ajbh, "name": name}).fetchall()]
        if not users:
            LogUtils.info(f"未找到用户数据，创建空记录 jlbh: {jlbh}")
            return False, new_record

        user = users[0]
        new_record.tel_num = user.get("lxdh", "")
        new_record.sary_xm = user.get("xm", "")
        new_record.xzz = user.get('xzxz', '')
        new_record.hjszd = user.get('hjxz', '')
        LogUtils.info(f"笔录数据查询完成，创建新记录 jlbh: {jlbh}")
        return False, new_record
