import asyncio
from collections import defaultdict
from typing import Any, Callable, Dict, Optional, List, Coroutine

from app.log.log_utils import LogUtils


class TaskQueueManager:
    """
    异步任务队列管理器
    实现生产消费模式，支持队列上限控制
    """
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._queues: Dict[str, asyncio.Queue] = defaultdict(asyncio.Queue)
            self._semaphores: Dict[str, asyncio.Semaphore] = {}
            self._max_sizes: Dict[str, int] = {}
            self._processors: Dict[str, Callable] = {}
            self._background_tasks: Dict[str, asyncio.Task] = {}
            self._initialized = True

    async def register_queue(
            self,
            queue_name: str,
            processor: Callable,
            max_size: int = 100,
            concurrency: int = 10
    ) -> bool:
        """
        注册一个新队列
        
        Args:
            queue_name: 队列名称
            processor: 处理函数
            max_size: 队列最大大小
            concurrency: 最大并发数
            
        Returns:
            bool: 是否注册成功
        """
        if queue_name in self._queues:
            LogUtils.warning(f"队列 {queue_name} 已存在")
            return False

        self._queues[queue_name] = asyncio.Queue(maxsize=max_size)
        self._semaphores[queue_name] = asyncio.Semaphore(concurrency)
        self._max_sizes[queue_name] = max_size
        self._processors[queue_name] = processor

        # 启动消费者
        self._background_tasks[queue_name] = asyncio.create_task(
            self._processor_loop(queue_name)
        )
        LogUtils.info(f"已注册队列 {queue_name}, 最大队列大小: {max_size}, 并发数: {concurrency}")
        return True

    async def add_task(self, queue_name: str, data: Any) -> bool:
        """
        添加任务到指定队列
        
        Args:
            queue_name: 队列名称
            data: 任务数据
            
        Returns:
            bool: 是否添加成功
        """
        if queue_name not in self._queues:
            LogUtils.error(f"队列 {queue_name} 未注册")
            return False

        try:
            # 使用wait_for防止永久阻塞
            await asyncio.wait_for(
                self._queues[queue_name].put(data),
                timeout=1.0
            )
            return True
        except asyncio.TimeoutError:
            LogUtils.warning(f"队列 {queue_name} 已满，无法添加新任务")
            return False
        except Exception as e:
            LogUtils.error(f"添加任务到队列 {queue_name} 失败: {str(e)}")
            return False

    async def _processor_loop(self, queue_name: str) -> None:
        """
        消费者处理循环
        
        Args:
            queue_name: 队列名称
        """
        queue = self._queues[queue_name]

        LogUtils.info(f"启动 {queue_name} 队列处理器")
        while True:
            try:
                # 从队列获取任务，设置超时时间为5秒
                try:
                    data = await asyncio.wait_for(queue.get(), timeout=5.0)
                except asyncio.TimeoutError:
                    LogUtils.debug("队列为空，等待5秒后重新检查")
                    await asyncio.sleep(5)
                    continue

                # 创建新的任务，但不等待其完成
                task = asyncio.create_task(
                    self._do_process_with_semaphore(queue_name, data)
                )
            except Exception as e:
                LogUtils.error(f"{queue_name} 队列处理器出错: {str(e)}")
                await asyncio.sleep(1)

    async def shutdown(self) -> None:
        """关闭所有队列处理器"""
        for name, task in self._background_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                LogUtils.info(f"已关闭 {name} 队列处理器")

    def get_queue_stats(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """
        获取队列统计信息
        
        Args:
            queue_name: 队列名称
            
        Returns:
            dict: 包含队列状态的信息，或None如果队列不存在
        """
        if queue_name not in self._queues:
            return None

        queue = self._queues[queue_name]
        return {
            "queue_size": queue.qsize(),
            "max_size": self._max_sizes[queue_name],
            "concurrency": self._semaphores[queue_name]._value,
            # "active_tasks": self._semaphores[queue_name]._value - self._semaphores[queue_name]._count
        }

    async def execute_after_batch(
            self,
            queue_name: str,
            batch_data: List[Any],
            callback_params: Dict[str, Any],
            callback: Callable[[Dict[str, Any]], Coroutine[Any, Any, Any]],
            timeout: float = 300
    ) -> bool:
        """
        添加一批任务到队列并在所有任务完成后执行回调函数
        
        实现要点：
        1. 使用asyncio.gather批量提交任务
        2. 添加完成计数器监控进度
        3. 异常处理保证回调执行

        Args:
            queue_name: 队列名称
            batch_data: 批量任务数据列表
            callback_params: 回调函数所需的参数字典
            callback: 所有任务完成后执行的回调函数，接收参数字典作为参数
            timeout: 等待所有任务完成的超时时间

        Returns:
            bool: 是否成功添加任务并设置回调
        """
        if queue_name not in self._queues:
            LogUtils.error(f"队列 {queue_name} 未注册")
            return False

        try:
            # 批量提交任务并等待结果
            tasks = []
            for data in batch_data:
                task = asyncio.create_task(
                    self._queues[queue_name].put(data),
                )
                tasks.append(task)

            # done, _ = await asyncio.wait(tasks, timeout=5.0)
            # success = sum(1 for t in done if not t.exception())
            # failed = len(batch_data) - success
            #
            # # 更新回调参数
            # callback_params.update({
            #     "total_tasks": len(batch_data),
            #     "success_count": success,
            #     "failed_count": failed
            # })

            # 监控任务完成并执行回调
            async def wait_and_callback():
                try:
                    # 使用wait_for避免无限等待
                    await asyncio.wait_for(self._queues[queue_name].join(), timeout=timeout)
                    # await self.destroy_queue(queue_name)
                    await callback(callback_params)
                    # LogUtils.info(f"批次任务处理完成, 成功: {success}, 失败: {failed}")
                except Exception as e:
                    LogUtils.error(f"回调执行失败: {str(e)}")
                    raise
                finally:
                    await self.destroy_queue(queue_name)

            asyncio.create_task(wait_and_callback())
            return True

        except Exception as e:
            LogUtils.error(f"设置批次任务回调失败: {str(e)}")
            return False

    async def destroy_queue(self, queue_name: str) -> bool:
        """
        销毁指定队列，清理相关资源并从管理器中移除
        
        Args:
            queue_name: 队列名称
            wait_for_completion: 是否等待队列中的任务完成
            timeout: 等待队列完成的超时时间（秒）
            
        Returns:
            bool: 是否成功销毁队列
        """
        if queue_name not in self._queues:
            LogUtils.warning(f"队列 {queue_name} 不存在，无需销毁")
            return False

        try:
            background_task = self._background_tasks.get(queue_name)

            # 取消后台处理任务
            if background_task and not background_task.done():
                LogUtils.info(f"取消队列 {queue_name} 的处理任务")
                background_task.cancel()
                try:
                    await background_task
                except asyncio.CancelledError as e:
                    pass

            # 清理资源
            del self._queues[queue_name]
            if queue_name in self._semaphores:
                del self._semaphores[queue_name]
            if queue_name in self._max_sizes:
                del self._max_sizes[queue_name]
            if queue_name in self._processors:
                del self._processors[queue_name]
            if queue_name in self._background_tasks:
                del self._background_tasks[queue_name]

            LogUtils.info(f"队列 {queue_name} 已销毁")
            return True

        except Exception as e:
            LogUtils.error(f"销毁队列 {queue_name} 失败: {str(e)}")
            return False

    async def _do_process_with_semaphore(self, queue_name, data):
        queue = self._queues[queue_name]
        semaphore = self._semaphores[queue_name]
        processor = self._processors[queue_name]

        async with semaphore:
            try:
                await processor(data)
            except Exception as e:
                LogUtils.error(f"处理 {queue_name} 队列任务失败: {str(e)}")
            finally:
                queue.task_done()
