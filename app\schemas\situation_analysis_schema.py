from pydantic import BaseModel, Field, validator
from typing import Optional
import json


class ReportGenerateRequest(BaseModel):
    start_time: str = Field(...,
                            min_length=10,
                            max_length=10,
                            pattern=r"^\d{4}-\d{2}-\d{2}$",
                            example="2024-12-01",
                            description="统计开始日期（格式：YYYY-MM-DD）")

    end_time: str = Field(...,
                          min_length=10,
                          max_length=10,
                          pattern=r"^\d{4}-\d{2}-\d{2}$",
                          example="2024-12-01",
                          description="统计结束日期（格式：YYYY-MM-DD）")

class UpdateReportRequest(BaseModel):
    report_id: int = Field(..., gt=0, description="报告ID（必须大于0）")
    content: dict = Field(..., min_length=1, description="Markdown内容（不能为空）")
    @validator('content', pre=True)
    def parse_content(cls, value):
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                raise ValueError("内容格式无效，必须为合法JSON字符串")
        return value

class ListReportRequest(BaseModel):
    report_name: Optional[str] = Field(
        default=None,
        example="执法分析",
        description="报告名称模糊查询关键词（可选）"
    )
    page: int = Field(
        default=1,
        gt=0,
        example=1,
        description="页码（从1开始）"
    )
    page_size: int = Field(
        default=10,
        gt=0,
        le=100,
        example=10,
        description="每页数量（1-100）"
    )


class DetailReportRequest(BaseModel):
    report_id: int = Field(...,
                           gt=0,
                           example=1,
                           description="需要查询的报告ID（必须大于0）")

class DeleteReportRequest(BaseModel):
    report_id: int = Field(...,
                          gt=0,
                          example=1,
                          description="需要删除的报告ID（必须大于0）")