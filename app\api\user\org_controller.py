from typing import Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.permission_check import require_permissions
from app.db.database import get_db
from app.schemas.org_schema import (
    OrgCreate, OrgUpdate
)
from app.services.user.org_service import OrgService

router = APIRouter(prefix="/org", tags=["org"])

@router.post("/list")
@require_permissions(permission_key="org:list")
def get_orgs(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取组织列表"""
    org_service = OrgService(db)
    result =org_service.get_orgs(skip=skip, limit=limit)
    return  create_response(code=200, data=result)

@router.get("/tree")
@require_permissions(permission_key="org:tree")
def get_org_tree(name: Optional[str] = None,db: Session = Depends(get_db)):
    """获取组织树结构"""
    org_service = OrgService(db)
    return org_service.get_org_tree(str)


@router.get("/tree2")
@require_permissions(permission_key="org:tree2")
def get_org_tree2(name: Optional[str] = None,db: Session = Depends(get_db)):
    """获取组织树结构"""
    org_service = OrgService(db)
    result= org_service.get_org_tree2(str)
    return  create_response(code=200, data=result)

@router.get("/byid/{org_id}")
@require_permissions(permission_key="org:byId")
def get_org(org_id: int, db: Session = Depends(get_db)):
    """获取组织详情"""
    org_service = OrgService(db)
    org = org_service.get_org(org_id)
    if not org:
        create_response(code=500, data="机构不存在")
    return  create_response(code=200, data=org)


@router.post("/addOrg")
@require_permissions(permission_key="org:addOrg")
def create_org(org: OrgCreate, db: Session = Depends(get_db)):
    """创建组织"""
    org_service = OrgService(db)
    return org_service.create_org(org)


@router.put("/updateOrg")
@require_permissions(permission_key="org:updateOrg")
def update_org( org: OrgUpdate, db: Session = Depends(get_db)):
    """更新组织"""
    org_service = OrgService(db)
    updated_org = org_service.update_org(org.id, org)
    if not updated_org:
        raise HTTPException(status_code=404, detail="Organization not found")
    return  create_response(code=200, data=updated_org)

@router.delete("/delete/{org_id}")
@require_permissions(permission_key="org:delete")
def delete_org(org_id: int, db: Session = Depends(get_db)):
    """删除组织"""
    org_service = OrgService(db)
    if not org_service.delete_org(org_id):
        raise HTTPException(status_code=404, detail="Organization not found")
    return  create_response(code=200, data="success")



