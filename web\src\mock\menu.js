import Mock from 'mockjs'

// 模拟菜单数据
const menuList = [
  {
    id: 1,
    parent_id: 0,
    name: '系统管理',
    icon: 'system',
    is_catalog: '0',
    sort: 1,
    perms: 'system',
    component_name: 'System',
    status: '0',
    createTime: '2024-01-01 00:00:00',
    children: [
      {
        id: 2,
        parent_id: 1,
        name: '用户管理',
        icon: 'user',
        is_catalog: '1',
        sort: 1,
        perms: 'system:user:list',
        component_name: 'User',
        component: 'system/user/index',
        status: '0',
        createTime: '2024-01-01 00:00:00'
      },
      {
        id: 3,
        parent_id: 1,
        name: '角色管理',
        icon: 'peoples',
        is_catalog: '1',
        sort: 2,
        perms: 'system:role:list',
        component_name: 'Role',
        component: 'system/role/index',
        status: '0',
        createTime: '2024-01-01 00:00:00'
      },
      {
        id: 4,
        parent_id: 1,
        name: '菜单管理',
        icon: 'tree-table',
        is_catalog: '1',
        sort: 3,
        perms: 'system:menu:list',
        component_name: 'Menu',
        component: 'system/menu/index',
        status: '0',
        createTime: '2024-01-01 00:00:00'
      }
    ]
  },
  {
    id: 5,
    parent_id: 0,
    name: '系统监控',
    icon: 'monitor',
    is_catalog: '0',
    sort: 2,
    perms: 'monitor',
    component_name: 'Monitor',
    status: '0',
    createTime: '2024-01-01 00:00:00',
    children: [
      {
        id: 6,
        parent_id: 5,
        name: '在线用户',
        icon: 'online',
        is_catalog: '1',
        sort: 1,
        perms: 'monitor:online:list',
        component_name: 'Online',
        component: 'monitor/online/index',
        status: '0',
        createTime: '2024-01-01 00:00:00'
      }
    ]
  }
]

// 模拟接口响应
Mock.mock(/\/api\/menu\/get_TotalMenu/, 'get', () => {
  return {
    code: 200,
    msg: 'success',
    data: menuList
  }
})

Mock.mock(/\/system\/menu\/\d+/, 'get', (options) => {
  const id = parseInt(options.url.match(/\/system\/menu\/(\d+)/)[1])
  const menu = findMenuById(menuList, id)
  return {
    code: 200,
    msg: 'success',
    data: menu
  }
})

Mock.mock(/\/system\/menu/, 'post', (options) => {
  return {
    code: 200,
    msg: 'success',
    data: null
  }
})

Mock.mock(/\/system\/menu/, 'put', (options) => {
  return {
    code: 200,
    msg: 'success',
    data: null
  }
})

Mock.mock(/\/system\/menu\/\d+/, 'delete', (options) => {
  return {
    code: 200,
    msg: 'success',
    data: null
  }
})

// 辅助函数：根据ID查找菜单
function findMenuById(menus, id) {
  for (let menu of menus) {
    if (menu.id === id) {
      return menu
    }
    if (menu.children) {
      const found = findMenuById(menu.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
} 