# # 使用Python完整版镜像
# FROM python:3.10
#
# # 设置工作目录
# WORKDIR /app
#
# # 设置环境变量
# ENV PYTHONDONTWRITEBYTECODE 1
# ENV PYTHONUNBUFFERED 1
# ENV LLM_API_URL=""
# ENV LLM_API_TOKEN=""
#
# # 设置pip镜像源
# RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
#     pip config set install.trusted-host mirrors.aliyun.com
#
# # 复制依赖文件
# COPY app/requirements.txt .
#
# # 安装Python依赖
# RUN pip install --no-cache-dir -r requirements.txt
#
# # 复制项目文件
# COPY . .
#
# # 创建目录用于挂载SQLite数据库文件
# RUN mkdir -p /app/data
#
# # 暴露端口
# EXPOSE 9904
#
# # 启动命令
# CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "9904"]



# 使用Python完整版镜像
FROM python:3.10

# 设置工作目录
WORKDIR /app

# 配置阿里云镜像源并安装依赖
RUN apt-get install -y gnupg && \
    apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 871920D1991BC93C && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb-src http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb-src http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb-src http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb-src http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    apt -y update && \
    apt-get -y update && \
    apt-get -y install libgl1-mesa-glx && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 设置pip镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    pip install --upgrade pip

# 复制依赖文件
COPY ./requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 暴露端口
EXPOSE 9904

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "9904", "--workers", "4"]