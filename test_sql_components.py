#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的 cluster_cases_with_shared_entities 函数的SQL组件
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.database import get_db
from sqlalchemy import text
from app.log.log_utils import LogUtils


async def test_sql_components():
    """测试SQL查询的各个组件"""
    print("=" * 80)
    print("测试重构后的SQL查询组件")
    print("=" * 80)
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 测试1：基础数据透视
        print("\n【测试1】基础数据透视 - pivot_data")
        print("-" * 50)
        
        pivot_sql = """
        SELECT
            qcf.case_id,
            qcf.entity_id,
            MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
            MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
            MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
            MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
        FROM qz_case_feature qcf
        JOIN police_records pr ON qcf.case_id = pr.police_number
        WHERE pr.source = 0
          AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
          AND qcf.entity_id > 0
        GROUP BY qcf.case_id, qcf.entity_id
        LIMIT 10
        """
        
        result = db.execute(text(pivot_sql))
        pivot_data = result.fetchall()
        print(f"透视数据样本数量: {len(pivot_data)}")
        
        if pivot_data:
            print("前几条透视数据:")
            for i, row in enumerate(pivot_data[:3]):
                print(f"  {i+1}. 案件:{row[0]}, 实体:{row[1]}, 身份证:{row[2]}, 姓名:{row[3]}, 类型:{row[4]}")
        
        # 测试2：找到多案件实体
        print("\n【测试2】找到至少出现在两个案件中的实体")
        print("-" * 50)
        
        multi_case_sql = """
        WITH pivot_data AS (
            SELECT
                qcf.case_id,
                qcf.entity_id,
                MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
                MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
            FROM qz_case_feature qcf
            JOIN police_records pr ON qcf.case_id = pr.police_number
            WHERE pr.source = 0
              AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
            GROUP BY qcf.case_id, qcf.entity_id
        )
        SELECT
            ENTITY_NAME,
            ID_CARD,
            COUNT(DISTINCT case_id) as case_count
        FROM pivot_data
        WHERE ENTITY_NAME IS NOT NULL AND entity_id > 0
        GROUP BY ENTITY_NAME, ID_CARD
        HAVING COUNT(DISTINCT case_id) >= 2
        LIMIT 10
        """
        
        result = db.execute(text(multi_case_sql))
        multi_case_entities = result.fetchall()
        print(f"多案件实体数量: {len(multi_case_entities)}")
        
        if multi_case_entities:
            print("多案件实体样本:")
            for i, row in enumerate(multi_case_entities[:5]):
                print(f"  {i+1}. 姓名:{row[0]}, 身份证:{row[1]}, 案件数:{row[2]}")
        
        # 测试3：智能聚类
        print("\n【测试3】智能聚类 - 同名实体合并")
        print("-" * 50)
        
        cluster_sql = """
        WITH pivot_data AS (
            SELECT
                qcf.case_id,
                qcf.entity_id,
                MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
                MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
            FROM qz_case_feature qcf
            JOIN police_records pr ON qcf.case_id = pr.police_number
            WHERE pr.source = 0
              AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
            GROUP BY qcf.case_id, qcf.entity_id
        ),
        entities_with_multiple_cases AS (
            SELECT
                ENTITY_NAME,
                ID_CARD,
                COUNT(DISTINCT case_id) as case_count
            FROM pivot_data
            WHERE ENTITY_NAME IS NOT NULL AND entity_id > 0
            GROUP BY ENTITY_NAME, ID_CARD
            HAVING COUNT(DISTINCT case_id) >= 2
        ),
        name_id_mapping AS (
            SELECT
                ENTITY_NAME,
                MAX(ID_CARD) as representative_id_card
            FROM entities_with_multiple_cases
            WHERE ID_CARD IS NOT NULL
            GROUP BY ENTITY_NAME
        )
        SELECT
            emc.ENTITY_NAME,
            emc.ID_CARD,
            CASE
                WHEN nim.representative_id_card IS NOT NULL THEN nim.representative_id_card
                ELSE CONCAT('NO_ID_CARD_', emc.ENTITY_NAME)
            END as cluster_key
        FROM entities_with_multiple_cases emc
        LEFT JOIN name_id_mapping nim ON emc.ENTITY_NAME = nim.ENTITY_NAME
        LIMIT 10
        """
        
        result = db.execute(text(cluster_sql))
        cluster_data = result.fetchall()
        print(f"聚类结果数量: {len(cluster_data)}")
        
        if cluster_data:
            print("聚类结果样本:")
            for i, row in enumerate(cluster_data[:5]):
                print(f"  {i+1}. 姓名:{row[0]}, 身份证:{row[1]}, 聚类键:{row[2]}")
        
        # 测试4：案件共享实体统计
        print("\n【测试4】案件共享实体统计")
        print("-" * 50)
        
        case_stats_sql = """
        WITH pivot_data AS (
            SELECT
                qcf.case_id,
                qcf.entity_id,
                MAX(CASE WHEN qcf.feature_type = 'ID_CARD' THEN qcf.feature_value END) AS ID_CARD,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_NAME' THEN qcf.feature_value END) AS ENTITY_NAME,
                MAX(CASE WHEN qcf.feature_type = 'ENTITY_TYPE' THEN qcf.feature_value END) AS ENTITY_TYPE,
                MAX(CASE WHEN qcf.feature_type = 'CASE_TYPE' THEN qcf.feature_value END) AS CASE_TYPE
            FROM qz_case_feature qcf
            JOIN police_records pr ON qcf.case_id = pr.police_number
            WHERE pr.source = 0
              AND qcf.feature_type IN ('ID_CARD', 'ENTITY_NAME', 'ENTITY_TYPE', 'CASE_TYPE')
            GROUP BY qcf.case_id, qcf.entity_id
        ),
        entities_with_multiple_cases AS (
            SELECT
                ENTITY_NAME,
                ID_CARD,
                COUNT(DISTINCT case_id) as case_count
            FROM pivot_data
            WHERE ENTITY_NAME IS NOT NULL AND entity_id > 0
            GROUP BY ENTITY_NAME, ID_CARD
            HAVING COUNT(DISTINCT case_id) >= 2
        )
        SELECT
            pd.case_id,
            pd.CASE_TYPE,
            COUNT(DISTINCT CONCAT(emc.ENTITY_NAME, '|', COALESCE(emc.ID_CARD, ''))) as shared_entity_count
        FROM pivot_data pd
        JOIN entities_with_multiple_cases emc ON (
            pd.ENTITY_NAME = emc.ENTITY_NAME AND
            (pd.ID_CARD = emc.ID_CARD OR (pd.ID_CARD IS NULL AND emc.ID_CARD IS NULL))
        )
        WHERE pd.entity_id > 0
        GROUP BY pd.case_id, pd.CASE_TYPE
        HAVING shared_entity_count >= 2
        LIMIT 10
        """
        
        result = db.execute(text(case_stats_sql))
        case_stats = result.fetchall()
        print(f"包含多个共享实体的案件数量: {len(case_stats)}")
        
        if case_stats:
            print("案件共享实体统计样本:")
            for i, row in enumerate(case_stats[:5]):
                print(f"  {i+1}. 案件:{row[0]}, 类型:{row[1]}, 共享实体数:{row[2]}")
        
        print("\n" + "=" * 80)
        print("SQL组件测试完成！")
        print("=" * 80)
        
        # 总结
        print(f"\n总结:")
        print(f"- 透视数据样本: {len(pivot_data)} 条")
        print(f"- 多案件实体: {len(multi_case_entities)} 个")
        print(f"- 聚类结果: {len(cluster_data)} 个")
        print(f"- 符合条件的案件: {len(case_stats)} 个")
        
        if len(case_stats) == 0:
            print("\n注意: 当前数据库中没有包含至少2个共享实体的案件")
            print("这可能是因为:")
            print("1. 数据量不足")
            print("2. 实体关联度不高")
            print("3. 需要调整聚类条件")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_sql_components())
