import hashlib
from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Depends, UploadFile, File, Form, Body, FastAPI
from pydantic import BaseModel
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.db.database import get_db
from app.log.log_utils import LogUtils
from app.models.SacwRecords import ImportedFiles
from app.models.scene_6_po import finance_search_order, scene_6_record
from app.services.db_service import DatabaseService
from app.services.excel_service import ExcelService
from app.services.scene6 import case_measure_service
from app.services.scene6.scene_6_service import get_scene_6_service, Scene6Service
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR

router = APIRouter()


@router.post("/finance/object/search")
async def finance_search(
        measure_type: Optional[str] = None,
        measure_object_name: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    query = DatabaseService.build_finance_search_query(
        db, measure_type, measure_object_name
    )

    # 计算总记录数
    total_count = query.count()

    # 添加分页
    skip = (page - 1) * page_size
    query = query.order_by(finance_search_order(scene_6_record.finance_analysis_state),
                           scene_6_record.measure_approve_date.desc()).offset(skip).limit(page_size)

    results = query.all()
    return create_response(data={
        "records": results,
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": (total_count + page_size - 1)
    })


@router.post("/finance/money/search")
async def money_search(
        measure_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    # 涉案款未及时处置 场景 措施对象为 人民币
    query = DatabaseService.build_finance_search_query(
        db, measure_type, "人民币"
    )

    # 计算总记录数
    total_count = query.count()

    # 添加分页
    skip = (page - 1) * page_size
    query = query.order_by(finance_search_order(scene_6_record.money_analysis_state),
                           scene_6_record.measure_approve_date.desc()).offset(skip).limit(page_size)

    results = query.all()
    return create_response(data={
        "records": results,
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": (total_count + page_size - 1)
    })


@router.post("/finance/object/analysis")
async def finance_object_analysis(
        db: Session = Depends(get_db)
):
    try:
        case_measure_service.finance_object_analysis(db)
        return create_response(code=200, message="success")
    except Exception as e:
        LogUtils.error(f"finance_search error: {str(e)}")
        return create_response(code=500, message=f"分析失败: {str(e)}")


@router.post("/finance/money/analysis")
async def finance_money_analysis(
        db: Session = Depends(get_db)
):
    try:
        case_measure_service.finance_money_analysis(db)
        return create_response(code=200, message="success")
    except Exception as e:
        LogUtils.error(f"finance_search error: {str(e)}")
        return create_response(code=500, message=f"分析失败: {str(e)}")


@router.post("/finance/file/upload")
async def finance_object_analysis(
        file: UploadFile = File(...),
        file_type: int = Form(0),
        db: Session = Depends(get_db)
):
    try:
        # 检查文件类型
        if not file.filename.endswith('.xlsx'):
            return create_response(code=400, message="只支持.xlsx格式的文件")

        contents = await file.read()

        # 计算文件MD5
        file_md5 = hashlib.md5(contents).hexdigest()

        # 检查文件是否已导入
        existing_file = db.query(ImportedFiles).filter(
            ImportedFiles.file_md5 == file_md5
        ).first()

        if existing_file:
            return create_response(code=400, message="该文件已经导入过，请勿重复导入")

        # 记录文件导入信息
        imported_file = ImportedFiles(
            file_name=file.filename,
            file_md5=file_md5,
            file_type=file_type
        )
        db.add(imported_file)

        # 解析Excel
        time_fields = []
        if file_type == 0:
            time_fields = ["措施批准时间"]
        records = await ExcelService.parse_excel(contents, time_fields)
        LogUtils.info(f"records: {len(records)}")

        try:
            for record in records:
                if file_type == 0:
                    # 转换字段名从中文到英文
                    english_data = DatabaseService.map_fields_to_english(record, DatabaseService.MEASURE_FIELD_MAPPING)
                    # 创建记录
                    DatabaseService.create_casemeasure_record(db, english_data)
                elif file_type == 1:
                    # 转换字段名从中文到英文
                    english_data = DatabaseService.map_fields_to_english(record, DatabaseService.CASW_FIELD_MAPPING)
                    # 创建记录
                    DatabaseService.create_sacw_record(db, english_data)

            # 所有记录处理成功后提交事务
            db.commit()
            return create_response(data={"processed": True})

        except Exception as e:
            db.rollback()
            LogUtils.error(f"数据处理失败: {str(e)}")
            return create_response(code=500, message=f"数据处理失败: {str(e)}")

    except IntegrityError as e:
        db.rollback()
        LogUtils.error(f"文件重复导入: {str(e)}")
        return create_response(code=400, message="该文件已经导入过，请勿重复导入")
    except Exception as e:
        db.rollback()
        LogUtils.error(f"文件上传失败: {str(e)}")
        return create_response(code=500, message=f"处理失败: {str(e)}")


@router.post("/scene6/sync")
async def scene_5_sync(
        start_date: str = Body(..., embed=True),
        end_date: str = Body(..., embed=True),
        user_id: str = Depends(UserContext.get_current_id),
):
    """
    同步场景6的案件数据
    参数:
        start_date: 开始日期(YYYYMMDD)
        end_date: 结束日期(YYYYMMDD)
    """
    try:
        # 调用服务层方法进行同步
        scene_6_service = await get_scene_6_service()
        result = await scene_6_service.scene_6_data_sync(
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
        )

        if not result:
            return create_response(code=500, message="同步失败")

        return create_response(
            data=result,
            message="同步成功"
        )
    except Exception as e:
        LogUtils.error(f"场景5数据同步出错: {str(e)}")
        return create_response(code=500, message=f"同步出错: {str(e)}")


class Scene6SearchParams(BaseModel):
    """立而不侦场景查询参数模型"""
    finance_type: int = 0
    ajbh: Optional[str] = None
    cwmc: Optional[str] = None  # 物品名称
    djdwmc: Optional[str] = None  # 主办单位
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    page: Optional[int] = 1
    page_size: Optional[int] = 10


@router.post("/scene6/search")
async def scene_6_query(
        search_params: Scene6SearchParams = Body(...)
) -> Dict[str, Any]:
    try:
        result = await Scene6Service.scene_6_search(search_params=search_params.__dict__)
        return create_response(
            message="查询成功",
            data=result
        )
    except Exception as e:
        return create_response(
            message=f"查询异常: {str(e)}",
            code=500
        )


@router.post("/scene6/auto-sync")
async def scene_6_auto_sync(
        user_id: str = Depends(UserContext.get_current_id),
        is_first_sync: bool = False
):
    """自动同步场景6的案件数据(定时任务调用)
    参数:
        user_id: 用户ID
        is_first_sync: 是否为启动时的第一次同步，默认为False
    """
    try:
        # 如果是系统自动调用，则使用默认用户ID
        if user_id == "system_auto_sync":
            user_id = str(int(datetime.now().timestamp()))
            
        end_date = datetime.now().strftime("%Y%m%d%H%M%S")
        start_date = (datetime.now() - timedelta(minutes=60)).strftime("%Y%m%d%H%M%S")
        
        # 如果是第一次同步，则同步近18个月的数据
        if is_first_sync:
            start_date = (datetime.now() - timedelta(days=540)).strftime("%Y%m%d%H%M%S")
            LogUtils.info("执行启动时的第一次同步，同步最近18个月的数据")

        scene_6_service = await get_scene_6_service()
        result = await scene_6_service.scene_6_data_sync(
            user_id=1,
            start_date=start_date,
            end_date=end_date
        )

        if not result:
            LogUtils.error("自动同步失败")
            return create_response(code=500, message="自动同步失败")

        LogUtils.info("自动同步成功")
        return create_response(data=result, message="自动同步成功")

    except Exception as e:
        LogUtils.error(f"场景6自动数据同步出错: {str(e)}")
        return create_response(code=500, message=f"自动同步出错: {str(e)}")


@router.post("/scene6/auto-analyze")
async def scene_6_auto_analyze(
        user_id: str = Depends(UserContext.get_current_id)
):
    """自动分析场景6的案件数据(定时任务调用)
        参数:
            user_id: 用户ID
    """
    try:
        # 如果是系统自动调用，则使用默认用户ID
        if user_id == "system_auto_analyze":
            user_id = str(int(datetime.now().timestamp()))

        scene_6_service = await get_scene_6_service()
        result = await scene_6_service.scene_6_analyze(
            user_id=2
        )

        return create_response(
            message="分析指令已发送",
            data=result
        )
    except Exception as e:
        LogUtils.error(f"场景6自动数据分析出错: {str(e)}")
        return create_response(code=500, message=f"自动分析出错: {str(e)}")


# 初始化调度器
scheduler = AsyncIOScheduler()


def init_scheduler(app: FastAPI):
    """注册调度器到FastAPI应用"""
    if not scheduler.running:
        try:
            scheduler.configure(timezone="Asia/Shanghai")
            scheduler.start()

            # 项目启动时立即执行任务 - 同步数据
            scheduler.add_job(
                scene_6_auto_sync,
                trigger=DateTrigger(run_date=datetime.now(tz=timezone.utc)),
                kwargs={"user_id": "system_auto_sync", "is_first_sync": True},
                misfire_grace_time=30,
                id="scene6_immediate_sync_job"
            )
            
            # 项目启动时立即执行任务 - 分析数据
            scheduler.add_job(
                scene_6_auto_analyze,
                trigger=DateTrigger(run_date=datetime.now(tz=timezone.utc) + timedelta(seconds=5)),
                kwargs={"user_id": "system_auto_analyze"},
                misfire_grace_time=30,
                id="scene6_immediate_analyze_job"
            )
            LogUtils.info(" 已创建场景6立即同步和分析任务")

            # 定时周期任务 - 同步数据
            scheduler.add_job(
                scene_6_auto_sync,
                trigger=IntervalTrigger(minutes=60),
                kwargs={"user_id": "system_auto_sync"},
                max_instances=1,
                coalesce=True,
                id="scene6_periodic_sync_job"
            )
            
            # 定时周期任务 - 分析数据
            scheduler.add_job(
                scene_6_auto_analyze,
                trigger=IntervalTrigger(minutes=60, start_date=datetime.now(tz=timezone.utc) + timedelta(minutes=1)),
                kwargs={"user_id": "system_auto_analyze"},
                max_instances=1,
                coalesce=True,
                id="scene6_periodic_analyze_job"
            )
            LogUtils.info(" 已创建场景6周期同步和分析任务，每60分钟执行一次")

            # 添加任务执行监听器
            def job_listener(event):
                if event.code == EVENT_JOB_EXECUTED:
                    LogUtils.info(f" 任务执行成功: {event.job_id}")
                elif event.code == EVENT_JOB_ERROR:
                    LogUtils.error(f" 任务执行失败: {event.job_id},  异常: {event.exception}")

            scheduler.add_listener(job_listener,
                                   EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)

            LogUtils.info(" 场景6自动同步和分析定时任务已启动")
        except Exception as e:
            LogUtils.error(f" 定时任务启动失败: {str(e)}")
            raise
