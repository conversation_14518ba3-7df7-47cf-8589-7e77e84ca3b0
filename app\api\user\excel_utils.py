from io import BytesIO

import pandas as pd
from typing import List, Dict

from fastapi import UploadFile
from sqlalchemy.orm import Session

from app.models.temp_org_mapping import temp_org_to_user


async def read_org_excel(file: UploadFile) -> Dict:
    """
    读取组织机构Excel文件，并对数据进行去重
    """
    try:
        # 读取Excel文件

        # 读取上传文件内容
        contents =await  file.read()

        # 使用BytesIO处理文件内容
        excel_data = BytesIO(contents)

        # 读取Excel数据
        df = pd.read_excel(excel_data,dtype={'警综单位':str, '警综层级':str, '单位名称':str, '机构代码':str,'父级':str})

        # 确保必要的列存在
        required_columns = ['警综单位', '警综层级', '单位名称', '机构代码', '父级']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Excel缺少必要的列: {col}")

        # 转换为字典列表并去重
        org_list = []
        seen_codes = set()  # 用于记录已存在的机构代码
        total_count = len(df)  # 原始数据总数

        for _, row in df.iterrows():
            org_code = str(row['机构代码']).strip()

            # 跳过重复的机构代码
            if org_code in seen_codes:
                continue

            org_data = {
                'police_unit': str(row['警综单位']).strip(),
                'police_level': str(row['警综层级']).strip(),
                'org_name': str(row['单位名称']).strip(),
                'org_code': org_code,
                'parent_code': str(row['父级']).strip()
            }

            seen_codes.add(org_code)
            org_list.append(org_data)

        return {
            'total': total_count,
            'unique': len(org_list),
            'duplicate': total_count - len(org_list),
            'data': org_list
        }

    except Exception as e:
        raise Exception(f"读取Excel文件失败: {str(e)}")


def build_org_tree(org_list: List[Dict]) -> List[Dict]:
    """
    构建组织机构树形结构
    """
    try:
        # 按警综层级排序
        sorted_orgs = sorted(org_list, key=lambda x: x['police_level'])

        # 构建组织字典，用于快速查找
        org_dict = {}
        tree = []

        # 第一次遍历：构建所有节点
        for org in sorted_orgs:
            node = {
                'police_unit': org['police_unit'],
                'police_level': org['police_level'],
                'org_name': org['org_name'],
                'org_code': org['org_code'],
                'parent_code': org['parent_code'],
                'children': []
            }
            org_dict[org['org_code']] = node

        # 第二次遍历：构建树形结构
        for org_code, node in org_dict.items():
            parent_code = node['parent_code']
            if not parent_code or parent_code == '000000000000':
                # 根节点
                tree.append(node)
            elif parent_code in org_dict:
                # 将当前节点添加到父节点的children中
                parent = org_dict[parent_code]
                parent['children'].append(node)

        return tree

    except Exception as e:
        raise Exception(f"构建组织树失败: {str(e)}")


# 使用示例
async def process_org_excel(file: UploadFile) -> Dict:
    """
    处理Excel并返回树形结构
    """
    try:
        # 读取并去重
        result = await read_org_excel(file)
        # 构建树形结构
        tree = build_org_tree(result['data'])

        return {
            'total': result['total'],
            'unique': result['unique'],
            'duplicate': result['duplicate'],
            'tree': tree
        }

    except Exception as e:
        raise Exception(f"处理组织数据失败: {str(e)}")


def process_org_tree(tree_data: List[Dict]) -> List[Dict]:
    """处理组织树结构，生成新的数据格式"""
    if not tree_data:
        return []

    result = []
    root = tree_data[0]  # 获取第一个对象

    def process_node(node: Dict, parent_org_code: str = None, index: int = None) -> Dict:
        """处理单个节点"""
        # 处理一级节点
        if node['police_level'] == '1':
            org_code = '4203'
            parent_org_code = '0000'
        else:
            # 处理子节点
            suffix = str(index + 1).zfill(4)  # 补充4位
            org_code = f"{parent_org_code}{suffix}"

        # 构建当前节点数据
        current_node = {
            'name': node['police_unit'],
            'org_code': org_code,
            'parent_org_code': parent_org_code,
            'police_level':node['police_level'],
            'sort': 1 ,
            'origin_org_code':node['org_code'],
            'origin_parent_org_code':node['parent_code']

        }
        result.append(current_node)

        # 处理子节点
        if node['children']:
            for i, child in enumerate(node['children']):
                process_node(child, org_code, i)

    # 开始处理
    process_node(root)
    return result


async def process_org_user_excel(file: UploadFile, db: Session) -> Dict:
    """处理组织用户Excel文件"""
    try:
        # 读取上传文件内容
        contents = await file.read()
        excel_data = BytesIO(contents)

        # 读取Excel数据
        df = pd.read_excel(excel_data,dtype={'层级':str, '代码':str, '父级':str, '人员编号':str})

        # 检查必要的列
        required_columns = ['层级', '代码', '父级', '人员编号']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Excel缺少必要的列: {col}")
        # 读取时指定数据类型
        df['人员编号'] = df['人员编号'].astype(str)
        # 转换数据为实体类格式
        org_mappings = []
        for _, row in df.iterrows():
            mapping_data = {
                'origin_org_code': str(row['代码']).strip(),
                'origin_parent_org_code': str(row['父级']).strip(),
                'person_code': str(row['人员编号']).strip(),
                'level_code': str(row['层级']).strip()
            }
            org_mappings.append(mapping_data)

        # 检查表是否有数据
        existing_count = db.query(temp_org_to_user).count()

        if existing_count == 0:
            # 批量插入新数据
            db.bulk_insert_mappings(temp_org_to_user, org_mappings)
        else:
            # 更新已存在的数据
            for mapping in org_mappings:
                existing_record = db.query(temp_org_to_user).filter(
                    temp_org_to_user.person_code == mapping['person_code']
                ).first()

                if existing_record:
                    if existing_record.origin_org_code != mapping['origin_org_code']:
                        # 更新不一致的数据
                        existing_record.origin_org_code = mapping['origin_org_code']
                        existing_record.origin_parent_org_code = mapping['origin_parent_org_code']
                        existing_record.level_code = mapping['level_code']
                else:
                    # 插入新记录
                    new_record = temp_org_to_user(**mapping)
                    db.add(new_record)

        db.commit()

        return {
            'total': len(org_mappings),
            'message': '数据处理成功'
        }

    except Exception as e:
        db.rollback()
        raise Exception(f"处理组织用户数据失败: {str(e)}")
    finally:
        await file.close()



# 使用示例
if __name__ == "__main__":
    file_path = "D:\\OneDrive\\桌面\\text.xlsx"
    org_data = process_org_excel(file_path)
    tree= org_data['tree']
    result=process_org_tree(tree)
    print(result)
