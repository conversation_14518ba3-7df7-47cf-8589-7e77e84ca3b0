<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="950px"
    :close-on-click-modal="false"
    custom-class="import-files-dialog"
    @closed="handleDialogClosed"
  >
    <template #title>
      <div class="custom-dialog-title">导入文件列表</div>
    </template>
    <div class="import-files-content">
      <div class="import-date-filter">
        <div class="filter-header">
          <div class="filter-title">手动导入数据查询</div>
          <div class="filter-desc">可查看本系统手动导入的Excel数据文件记录</div>
        </div>
        <div class="filter-form">
          <div class="form-item">
            <span class="item-label">选择日期：</span>
            <el-date-picker
              v-model="selectedDate"
              type="date"
              placeholder="选择日期"
              format="yyyy-MM-dd"
              value-format="yyyyMMdd"
              @change="handleDateChange"
              :clearable="false"
              class="date-picker"
            ></el-date-picker>
          </div>
          <div class="form-item">
            <span class="item-label">警情类型：</span>
            <el-select 
              v-model="caseType" 
              placeholder="请选择警情类型" 
              @change="handleCaseTypeChange"
              class="case-type-select"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="刑事类警情" value="0"></el-option>
              <el-option label="行政(治安)类警情" value="1"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      
      <el-table
        :data="filesList"
        style="width: 100%; margin-top: 20px;"
        :header-cell-style="headerStyle"
        :cell-style="cellStyle"
        v-loading="loading"
        border
        highlight-current-row
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="file_name"
          label="文件名称"
          min-width="280"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="import_time"
          label="导入时间"
          width="180"
          :formatter="formatDate"
        ></el-table-column>
        <el-table-column
          prop="record_count"
          label="记录数量"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="case_type"
          label="警情类型"
          width="150"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag :type="scope.row.case_type === 0 ? 'danger' : 'warning'" effect="dark" size="small">
              {{ scope.row.case_type === 0 ? '刑事类警情' : '行政（治安）类警情' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleViewDetail(scope.row)"
              class="view-btn"
            >查看数据</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- <div class="table-empty" v-if="filesList.length === 0 && !loading">
        <i class="el-icon-document"></i>
        <p>{{ emptyText }}</p>
      </div> -->
      
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          background
        ></el-pagination>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getImportFileList } from "@/api/case";

export default {
  name: 'ImportFilesDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    defaultCaseType: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      filesList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      selectedDate: null,
      dateRange: [],
      caseType: "",
      emptyText: "当前日期没有导入记录"
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.init();
      }
    },
    defaultCaseType: {
      immediate: true,
      handler(val) {
        if (val !== "") {
          this.caseType = val;
        }
      }
    }
  },
  methods: {
    init() {
      // 默认加载当天的数据
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const formattedDate = `${year}${month}${day}`;
      
      this.selectedDate = formattedDate;
      this.dateRange = [formattedDate, formattedDate];
      this.loadFilesList();
    },
    
    // 加载导入文件列表
    async loadFilesList() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
          import_type: 'manual', // 只查询手动导入的数据
        };
        
        // 添加日期
        if (this.dateRange && this.dateRange.length === 2) {
          params.date = this.dateRange[0]; // 使用单一日期
        }
        
        // 添加警情类型
        if (this.caseType !== "") {
          params.case_type = this.caseType;
        }
        
        const res = await getImportFileList(params);
        if (res.code === 200 && res.data) {
          this.filesList = res.data.records || [];
          this.total = res.data.total || 0;
          
          // 自定义空状态提示文本
          if (this.filesList.length === 0) {
            // 格式化日期为易读形式 (例如: 2023年05月01日)
            let formattedDateText = "";
            if (this.selectedDate) {
              const year = this.selectedDate.substring(0, 4);
              const month = this.selectedDate.substring(4, 6);
              const day = this.selectedDate.substring(6, 8);
              formattedDateText = `${year}年${month}月${day}日`;
            }
            
            if (this.caseType !== "") {
              const caseTypeText = this.caseType === "0" ? "刑事类警情" : "行政（治安）类警情";
              this.emptyText = `${formattedDateText} 没有${caseTypeText}的导入记录`;
            } else {
              this.emptyText = `${formattedDateText} 没有导入记录`;
            }
          }
        }
      } catch (error) {
        console.error("获取导入文件列表失败:", error);
      } finally {
        this.loading = false;
      }
    },
    
    // 日期变化事件
    handleDateChange(value) {
      this.selectedDate = value;
      this.currentPage = 1;
      this.dateRange = [value, value];
      this.loadFilesList();
    },
    
    // 警情类型变化事件
    handleCaseTypeChange() {
      this.currentPage = 1;
      this.loadFilesList();
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadFilesList();
    },
    
    // 页码变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadFilesList();
    },
    
    // 查看详情
    handleViewDetail(row) {
      this.$emit('view-detail', {
        id: row.id,
        case_type: row.case_type,
        import_date: row.import_time ? this.formatDateString(row.import_time) : ''
      });
      this.dialogVisible = false;
    },
    
    // 关闭对话框
    handleCancel() {
      this.dialogVisible = false;
    },
    
    // 对话框完全关闭后的回调
    handleDialogClosed() {
      this.$emit('update:visible', false);
      this.filesList = [];
      this.currentPage = 1;
    },
    
    // 格式化日期
    formatDate(row, column, cellValue) {
      if (!cellValue) return "";
      return this.formatDateString(cellValue);
    },
    
    // 格式化日期字符串
    formatDateString(dateStr) {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    
    // 表头样式
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#00ffff",
        fontSize: "14px",
        fontWeight: "bold",
        textAlign: "left",
        height: "48px",
        padding: "0 12px",
      };
    },
    
    // 单元格样式
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffff",
        fontSize: "13px",
        borderColor: "rgba(0,255,255,0.1)",
        padding: "8px 12px",
      };
    }
  }
};
</script>

<style lang="scss" scoped>
// 为了解决scoped样式中的选择器权重问题，使用:deep代替::v-deep，确保样式正确应用到元素
:deep(.import-files-dialog) {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(0, 24, 51, 0.85) !important;
  border: 1px solid rgba(0, 255, 255, 0.4) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
  
  .el-dialog__header {
    background: linear-gradient(180deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
    padding: 12px 16px 10px !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 8px 8px 0 0 !important;
  }
  
  .custom-dialog-title {
    color: #00ffff !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
  }
  
  .el-dialog__headerbtn {
    top: 12px;
    right: 16px;
    
    .el-dialog__close {
      color: rgba(0, 255, 255, 0.7) !important;
      font-size: 16px !important;
      transition: all 0.3s !important;
      
      &:hover {
        color: #00ffff !important;
        transform: rotate(90deg) !important;
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.4) !important;
      }
    }
  }
  
  .el-dialog__body {
    background: rgba(0, 24, 51, 0.4) !important;
    padding: 20px 16px !important;
    max-height: 60vh;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, rgba(0, 180, 255, 0.5), rgba(0, 255, 255, 0.5));
      border-radius: 3px;
      box-shadow: inset 0 0 3px rgba(0, 255, 255, 0.2);
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(180deg, rgba(0, 180, 255, 0.8), rgba(0, 255, 255, 0.8));
      box-shadow: inset 0 0 5px rgba(0, 255, 255, 0.4);
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 48, 102, 0.2);
      border-radius: 3px;
    }
  }
  
  .el-dialog__footer {
    background: linear-gradient(0deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
    padding: 12px 16px !important;
    border-top: 1px solid rgba(0, 255, 255, 0.3) !important;
    border-radius: 0 0 8px 8px !important;
  }
}

.import-files-content {
  .import-date-filter {
    background: rgba(0, 48, 102, 0.4);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2), inset 0 0 8px rgba(0, 255, 255, 0.05);
    
    .filter-header {
      margin-bottom: 15px;
      
      .filter-title {
        color: #00ffff;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 5px;
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
      }
      
      .filter-desc {
        color: rgba(255, 255, 255, 0.7);
        font-size: 13px;
      }
    }
    
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      
      .form-item {
        display: flex;
        align-items: center;
        
        .item-label {
          color: #ffffff;
          font-size: 14px;
          margin-right: 8px;
          white-space: nowrap;
        }
        
        .date-picker,
        .case-type-select {
          width: 180px;
        }
      }
    }
  }
  
  .table-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: rgba(255, 255, 255, 0.5);
    
    i {
      font-size: 48px;
      margin-bottom: 10px;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
    }
    
    p {
      font-size: 14px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  ::v-deep .el-table {
    background-color: transparent;
    
    &::before {
      display: none;
    }
    
    .el-table__header-wrapper th {
      border-bottom: 1px solid rgba(0, 255, 255, 0.2);
    }
    
    .el-table__body-wrapper {
      background-color: transparent;
      
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, rgba(0, 180, 255, 0.5), rgba(0, 255, 255, 0.5));
        border-radius: 3px;
        box-shadow: inset 0 0 3px rgba(0, 255, 255, 0.2);
        transition: background 0.3s ease-in-out;
      }
      
      &::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(180deg, rgba(0, 180, 255, 0.8), rgba(0, 255, 255, 0.8));
        box-shadow: inset 0 0 5px rgba(0, 255, 255, 0.4);
      }
      
      &::-webkit-scrollbar-track {
        background: rgba(0, 48, 102, 0.2);
        border-radius: 3px;
      }
    }
    
    .el-table__row {
      transition: all 0.3s;
      
      &:hover > td {
        background-color: rgba(0, 255, 255, 0.1) !important;
      }
      
      &.current-row > td {
        background-color: rgba(0, 255, 255, 0.15) !important;
      }
    }
    
    td, th {
      border-color: rgba(0, 255, 255, 0.1);
    }
  }
  
  .view-btn {
    color: #00ffff;
    font-size: 13px;
    
    &:hover {
      color: #3eefff;
      text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
    }
  }
}

// 全局元素的样式，都使用:deep选择器
:deep(.el-button) {
  background: rgba(0, 255, 255, 0.2) !important;
  border: 1px solid #00ffff !important;
  color: #00ffff !important;
  height: 32px !important;
  padding: 0 20px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  transition: all 0.3s !important;
  
  &:hover {
    background: rgba(0, 255, 255, 0.3) !important;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
    transform: translateY(-1px) !important;
  }
}

:deep(.el-input__inner),
:deep(.el-select .el-input__inner),
:deep(.el-date-editor.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
  transition: all 0.3s;
  
  &:hover, &:focus {
    border-color: rgba(0, 255, 255, 0.5);
  }
}

:deep(.el-date-editor .el-input__icon),
:deep(.el-select .el-input__icon) {
  color: rgba(0, 255, 255, 0.5);
}

:deep(.el-date-editor .el-range-input),
:deep(.el-date-editor .el-range-separator) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-pagination) {
  button, span:not([class*=suffix]) {
    background-color: transparent;
    color: #ffffff;
  }
  
  .btn-prev, .btn-next {
    background-color: rgba(0, 48, 102, 0.5);
    color: rgba(0, 255, 255, 0.7);
    
    &:disabled {
      color: rgba(255, 255, 255, 0.3);
    }
  }
  
  .el-pager li {
    background-color: rgba(0, 48, 102, 0.5);
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 255, 255, 0.1);
    
    &.active {
      background-color: rgba(0, 255, 255, 0.2);
      color: #00ffff;
      border-color: rgba(0, 255, 255, 0.5);
    }
    
    &:hover {
      color: #00ffff;
    }
  }
}

:deep(.el-tag) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: none;
  
  &.el-tag--danger {
    background: linear-gradient(135deg, #f56c6c, #c45656) !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    color: white;
  }
  
  &.el-tag--warning {
    background: linear-gradient(135deg, #e6a23c, #c78833) !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    color: white;
  }
}
</style> 