import io
import re
from datetime import datetime
from typing import Dict

import pdfplumber
from fastapi import UploadFile

from app.core.context_handle import UserContext
from app.db.database import SessionLocal
from app.log.log_utils import LogUtils
from app.models.scene_4_po import Scene4PO
from app.prompts.prompts import TRANSCRIPT_ANALYSIS_PROMPT
from app.services.db_service import DatabaseService

FIELD_MAPPING = {
    'bldd': '笔录地点',
    'blnr': '问答环节'
}


class TranscriptService:
    def __init__(self):
        self.db_service = DatabaseService()

    @staticmethod
    def process_prompt(text: str) -> str:
        # 提示词存放在单独的app/prompts.py文件
        prompt_template = TRANSCRIPT_ANALYSIS_PROMPT

        # 替换提示词中的文本变量
        try:
            result = prompt_template.replace("{input}", text)
            return result
        except Exception as e:
            LogUtils.error(f"提示词处理失败: {str(e)}")
            return None


class TranscriptPDFService:
    """处理笔录PDF文件的服务类"""

    @staticmethod
    async def parse_pdf_to_text(pdf_file: UploadFile) -> str:
        try:
            content = await pdf_file.read()
            pdf_stream = io.BytesIO(content)

            with pdfplumber.open(pdf_stream) as pdf:
                all_lines = []
                for page in pdf.pages:
                    text = page.extract_text(
                        x_tolerance=3,
                        y_tolerance=3,
                        layout=True
                    )
                    if text:
                        # 收集所有非空行
                        lines = text.split('\n')
                        all_lines.extend([line for line in lines if line.strip()])

                # 找出所有行的最小左边距和最大右边界
                if all_lines:
                    left_spaces = [len(line) - len(line.lstrip()) for line in all_lines]
                    min_left = min(left_spaces)

                    # 按照统一边界处理所有行
                    processed_lines = []
                    for line in all_lines:
                        if len(line) > min_left:
                            processed_lines.append(line[min_left:].rstrip())
                        else:
                            processed_lines.append(line.strip())

                    # 合并处理后的行
                    full_text = '\n'.join(processed_lines)
                else:
                    full_text = ""

            # 优化时间格式
            time_pattern = r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日\s*(\d{1,2})\s*时\s*(\d{1,2})\s*分'
            full_text = re.sub(time_pattern, r'\1年\2月\3日\4时\5分', full_text)

            return full_text

        except Exception as e:
            LogUtils.error(f"增强PDF文件处理失败: {str(e)}")
            raise Exception(f"增强PDF文件处理失败: {str(e)}")
        finally:
            await pdf_file.close()
            pdf_stream.close()

    @staticmethod
    async def extract_fields_from_text(text: str) -> Dict:
        """
        从文本中提取关键字段信息

        Args:
            text: 需要处理的文本内容

        Returns:
            Dict: 提取的字段信息，格式如：
            {
                "笔录地点": "办案区询问室1",
                "询问人1": "张三",
                "询问人2": "李四",
                "记录人": "王五",
                "问答环节": "问：...答：...",
                "开始时间": "2025年03月11日15时00分",
                "结束时间": "2025年03月11日15时14分"
            }

        Raises:
            Exception: 文本处理过程中的异常
        """
        try:
            result = {}

            # 提取时间信息（开始时间和结束时间）
            time_pattern = r'时间\s*[:：]?\s*(.*?)\s*至\s*(\d{4}年\d{1,2}月\d{1,2}日\d{1,2}时\d{1,2}分)'
            time_match = re.search(time_pattern, text)
            if time_match:
                start_time = re.search(r'(\d{4}年\d{1,2}月\d{1,2}日\d{1,2}时\d{1,2}分)', time_match.group(1))
                if start_time:
                    result["开始时间"] = start_time.group(1)
                    result["结束时间"] = time_match.group(2)

            # 提取地点信息
            place_pattern = r'地点\s*[:：]?\s*(.*?)\s*(?:\n|$)'
            place_match = re.search(place_pattern, text)
            if place_match:
                result["笔录地点"] = place_match.group(1).strip().replace(' ', '')

            # 提取询问人信息
            xunwen_pattern = r'[询讯]问人[（(]签名[）)]\s*[:：]?\s*(.*?)(?:、|工作单位)'
            xunwen_match = re.search(xunwen_pattern, text)
            if xunwen_match:
                result["询问人1"] = xunwen_match.group(1).strip()

            # 提取询问人2信息
            xunwen2_pattern = r'[询讯]问人[（(]签名[）)]\s*[:：]?\s*.*?、\s*(.*?)\s*工作单位'
            xunwen2_match = re.search(xunwen2_pattern, text)
            if xunwen2_match:
                result["询问人2"] = xunwen2_match.group(1).strip()

            # 提取记录人信息
            jilu_pattern = r'记录人\s*[（(]签名[）)]\s*[:：]?\s*(.*?)\s*工作单位'
            jilu_match = re.search(jilu_pattern, text)
            if jilu_match:
                result["记录人"] = jilu_match.group(1).strip()

            # 匹配现住址
            current_address = re.search(r'现\s*住\s*址\s*(.*?)\s*(联系方式|户籍所在地)', text, re.DOTALL)
            if current_address:
                result["现住址"] = current_address.group(1).strip()

            # 匹配户籍所在地
            registered_address = re.search(r'户籍所在地\s*(.*?)\s*\n', text, re.DOTALL)
            if registered_address:
                result["户籍所在地"] = registered_address.group(1).strip()

            # 提取问答环节
            huji_pattern = r'户籍所在地.*?\n([\s\S]*?)$'
            huji_match = re.search(huji_pattern, text)
            if huji_match:
                qa_content = huji_match.group(1).strip()
                # 找到第一个"问"及其后面的冒号
                qa_match = re.search(r'问\s*[:：]', qa_content)
                if qa_match:
                    qa_start = qa_match.start()
                    qa_content = qa_content[qa_start:]
                    # 去除页码信息（第X页共Y页）
                    page_pattern = r'第\s*\d+\s*页\s*共\s*\d+\s*页'
                    qa_content = re.sub(page_pattern, '', qa_content)
                    # 去除可能产生的多余空行
                    qa_content = '\n'.join(line for line in qa_content.split('\n') if line.strip())
                    # 去除"被询问人"相关文本
                    person_pattern = r'被[询讯]问人\s*[：:]\s*'
                    qa_content = re.sub(person_pattern, '', qa_content)
                    result["问答环节"] = qa_content.strip()
                else:
                    LogUtils.warning("未找到问答内容")
                    result["问答环节"] = ""
            else:
                LogUtils.warning("未找到问答环节内容")
                result["问答环节"] = ""

            # 将字段名称转换为中文
            final_result = {}
            for key, value in result.items():
                final_result[key] = value

            return final_result

        except Exception as e:
            LogUtils.error(f"提取字段信息失败: {str(e)}")
            raise Exception(f"提取字段信息失败: {str(e)}")

    @staticmethod
    async def convert_to_db_format(transcript_json: dict, md5_id: str) -> dict:
        """将解析后的JSON数据转换为数据库字段格式"""

        def convert_time(time_str: str) -> datetime:
            time_str = time_str.replace('年', '-').replace('月', '-').replace('日', ' ').replace('时', ':').replace(
                '分', ':00')
            return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')

        return {
            'md_id': md5_id,
            'bldd': transcript_json.get('笔录地点', ''),
            'mjyxm': transcript_json.get('询问人1', ''),
            'mjexm': transcript_json.get('询问人2', ''),
            'jlrxm': transcript_json.get('记录人', ''),
            'xzz': transcript_json.get('现住址', ''),
            'hjszd': transcript_json.get('户籍所在地', ''),
            'blnr': transcript_json.get('问答环节', ''),
            'kssj': convert_time(transcript_json.get('开始时间', '')),
            'jssj': convert_time(transcript_json.get('结束时间', '')),
            'detection_result': '',
            'analysis_status': 0
        }

    @staticmethod
    async def save_tmp_record(record_data: dict, user_id: int):
        """将转换后的数据保存到数据库"""
        db = SessionLocal()
        try:
            police_number = UserContext.get_current().police_number
            police_number = f"-{police_number}"
            # 创建新记录
            new_record = Scene4PO(**record_data, user_id=user_id,org_code=police_number)
            db.add(new_record)
            db.commit()
            db.refresh(new_record)
            LogUtils.info(f"成功保存笔录记录，ID: {record_data['md_id']}")
            return new_record
        except Exception as e:
            db.rollback()
            LogUtils.error(f"保存笔录记录时发生错误: {str(e)}")
            raise e
        finally:
            db.close()
