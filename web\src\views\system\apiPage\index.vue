<template>
  <div class="api-manage bigscreen">
    <PageHeader title="接口配置" subtitle="API Management" />

    <div class="content-wrapper">
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form common-search" label-width="120px">
          <div class="search-left">
            <el-form-item label="接口名称">
              <el-input v-model="searchForm.name" placeholder="请输入接口名称"></el-input>
            </el-form-item>
            <el-form-item label="接口URL">
              <el-input v-model="searchForm.api" placeholder="请输入接口方法"></el-input>
            </el-form-item>
            <el-form-item label="请求方式">
              <el-select v-model="searchForm.method" placeholder="请选择请求方式" clearable>
                <el-option label="GET" value="GET"></el-option>
                <el-option label="POST" value="POST"></el-option>
                <el-option label="PUT" value="PUT"></el-option>
                <el-option label="DELETE" value="DELETE"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="common-action-button-operations">
        <div class="left-operations">
          <el-button type="primary" @click="handleAdd" class="tool-btn">
            <i class="el-icon-plus"></i>
            新增接口
          </el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          class="el-table--enable-row-hover" height="calc(100vh - 350px)">
          <el-table-column type="index" label="序号" width="70" align="center"></el-table-column>
          <el-table-column prop="name" label="接口名称" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="value" label="接口方法" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="api" label="接口URL" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column prop="method" label="请求方式" width="120" align="center">
            <template slot-scope="scope">
              <el-tag :type="getMethodType(scope.row.method)">{{ scope.row.method }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleEdit(scope.row)" class="edit-btn">
                <i class="el-icon-edit"></i>
                编辑
              </el-button>
              <el-button type="text" size="small" @click="handleDelete(scope.row)" class="delete-btn">
                <i class="el-icon-delete"></i>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible"  :close-on-click-modal="false"
      custom-class="api-dialog screen-dialog">
      <el-form :model="form" :rules="rules" ref="form" label-width="150px">
        <el-form-item label="接口名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入接口名称"></el-input>
        </el-form-item>
        <el-form-item label="接口方法" prop="value">
          <el-input v-model="form.value" placeholder="请输入接口方法"></el-input>
        </el-form-item>
        <el-form-item label="接口URL" prop="api">
          <el-input v-model="form.api" placeholder="请输入接口URL"></el-input>
        </el-form-item>
        <el-form-item label="请求方式" prop="method">
          <el-select v-model="form.method" placeholder="请选择请求方式" style="width: 100%">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联角色" prop="roleIds">
          <el-select
            v-model="form.roleIds"
            placeholder="选择角色"
            style="width: 100%"
            multiple
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联部门" prop="org_codes">
          <treeselect
            v-model="form.org_codes"
            :options="deptOptions"
            :show-count="true"
            :normalizer="normalizerCode"
            :multiple="true"
            placeholder="请选择关联部门"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" class="cancel-btn">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" class="ensure-btn">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PageHeader from "@/components/PageHeader.vue";
import { getApiList, createApi, updateApi, deleteApi, getApiDetail } from '@/api/apiInfo'
import { getDepartment, getRole } from "@/api/department";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "ApiManage",
  components: {
    PageHeader,
    Treeselect
  },
  data() {
    return {
      searchForm: {
        name: "",
        value: "",
        method: "",
        api: "",
      },
      currentPage: 1,
      pageSize: 10,
      tableData: [],
      loading: false,
      total: 0,
      dialogVisible: false,
      dialogTitle: "",
      form: {
        name: "",
        value: "",
        api: "",
        method: "",
        roleIds: [],
        org_codes: []
      },
      rules: {
        name: [
          { required: true, message: "请输入接口名称", trigger: "blur" }
        ],
        value: [
          { required: true, message: "请输入接口方法", trigger: "blur" }
        ],
        api: [
          { required: true, message: "请输入接口URL", trigger: "blur" }
        ],
        method: [
          { required: true, message: "请选择请求方式", trigger: "change" }
        ]
      },
      roleOptions: [],
      deptOptions: [],
    };
  },
  created() {
    this.loadTableData();
    this.getRoleOptions();
    this.getDeptTree();
  },
  methods: {
    headerStyle() {
      return {
        backgroundColor: "#1C3349",
        color: "#FFFFFFcc",
        fontWeight: "bold"
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#FFFFFFb3",
        borderColor: "rgba(0,255,255,0.1)"
      };
    },
    getMethodType(method) {
      const typeMap = {
        GET: "success",
        POST: "primary",
        PUT: "warning",
        DELETE: "danger"
      };
      return typeMap[method] || "info";
    },
    async loadTableData() {
      try {
        this.loading = true;
        const res = await getApiList({
          page: this.currentPage,
          page_size: this.pageSize,
          ...this.searchForm
        });
        this.tableData = res.data.items || [];
        this.total = res.data.total || 0;
      } catch (error) {
        console.error("获取数据失败：", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },
    handleSearch() {
      this.currentPage = 1;
      this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        name: "",
        value: "",
        method: "",
        api:"",
      };
      this.handleSearch();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadTableData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadTableData();
    },
    handleAdd() {
      this.dialogTitle = "新增接口";
      this.form = {
        name: "",
        value: "",
        api: "",
        method: "",
        roleIds: [],
        org_codes: []
      };
      this.dialogVisible = true;
    },
    async handleEdit(row) {
      try {
        const res = await getApiDetail(row.id);
        this.dialogTitle = "编辑接口";
        this.form = { ...res.data };
        this.dialogVisible = true;
      } catch (error) {
        console.error("获取详情失败：", error);
        this.$message.error("获取详情失败");
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm("确认删除该接口配置吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });
        
        await deleteApi(row.id);
        this.$message.success("删除成功");
        this.loadTableData();
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败：", error);
          this.$message.error("删除失败");
        }
      }
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        
        if (this.form.id) {
          await updateApi(this.form);
        } else {
          await createApi(this.form);
        }
        
        this.$message.success(this.form.id ? "更新成功" : "创建成功");
        this.dialogVisible = false;
        this.loadTableData();
      } catch (error) {
        console.error("保存失败：", error);
        this.$message.error("保存失败");
      }
    },
    getRoleOptions() {
      getRole({
        name: "",
        status: "1",
        page_size: 999,
        page: 1,
      })
        .then(res => {
          if (res.code === 200 && res.data) {
            this.roleOptions = res.data.items || [];
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    getDeptTree() {
      getDepartment()
        .then(response => {
          if (response.code === 200) {
            this.deptOptions = this.filterDisabledDept(response.data);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    filterDisabledDept(deptList) {
      return deptList.filter(dept => {
        if (!dept.status) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    normalizerCode(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.org_code,
        label: node.name,
        children: node.children,
      };
    },
  }
};
</script>

<style scoped>
.api-manage {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

.search-section {
  margin-bottom: 20px;
}

.search-left {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.search-right {
  margin-top: 10px;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}

:deep(.el-form-item__label) {
  color: #ffffff !important;
}


.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

.edit-btn,
.delete-btn {
  color: #00ffff;
  margin: 0 5px;
}

.edit-btn:hover {
  color: #ffffff;
}

.delete-btn:hover {
  color: #ff4d4f;
}

:deep(.api-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
}

:deep(.api-dialog .el-dialog__title) {
  color: #00ffff !important;
}

:deep(.api-dialog .el-dialog__body) {
  padding: 20px !important;
}

:deep(.api-dialog .el-dialog__footer) {
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  padding: 15px 20px !important;
}

:deep(.api-dialog .el-select .el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.api-dialog .el-select-dropdown) {
  background-color: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.api-dialog .el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.api-dialog .el-select-dropdown__item.hover),
:deep(.api-dialog .el-select-dropdown__item:hover) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
  height: 32px;
  padding: 0 15px;
}

.tool-btn:hover {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  color: #00ffff;
}

.tool-btn i {
  font-size: 16px;
}

:deep(.vue-treeselect) {
  .vue-treeselect__control {
    background-color: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    color: #ffffff;
  }

  .vue-treeselect__placeholder {
    color: rgba(255, 255, 255, 0.7);
  }

  .vue-treeselect__single-value {
    color: #ffffff;
  }

  .vue-treeselect__menu {
    background-color: rgba(0, 48, 102, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  .vue-treeselect__option {
    color: #ffffff;
  }

  .vue-treeselect__option:hover {
    background-color: rgba(0, 255, 255, 0.1);
  }

  .vue-treeselect__option--highlight {
    background-color: rgba(0, 255, 255, 0.2);
  }

  
}
:deep(.vue-treeselect__control){
        border: 0.0625rem solid rgba(0, 255, 255, 0.2);
  }
</style>
