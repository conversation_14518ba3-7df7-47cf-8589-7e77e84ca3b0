from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.config import settings
from app.models.SacwRecords import SacwBaseBase
from app.models.police_data import PoliceDataBase
from app.models.police_record import Base
from app.models.scene_2_po import Scene2POBase
from app.models.scene_3_record import Scene_3_RecordBase
from app.models.scene_4_po import Scence_4_Base
from app.models.scene_5_record import Scene5Record, Scene5OcrPO
from app.models.scene_6_po import Scene6RecordBase
from app.models.situation_analysis_record import SituationAnalysisBase
from app.models.user import UserBase
from app.models.chat import ChatBase  # 添加新的聊天模型基类

# 配置SQLAlchemy日志
# logging.basicConfig()
# logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URL,
    pool_size=20,  # 连接池大小
    max_overflow=10,  # 超过连接池大小外最多创建的连接数
    pool_timeout=30,  # 池中没有连接时等待的秒数
    pool_recycle=1800,  # 多久之后对连接池中的连接进行一次回收（重置）
    # echo=True  # 启用SQL语句打印
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    db = SessionLocal()
    try:
        yield db
        if db.is_active:  # 检查会话状态
            db.commit()
    except Exception as e:
        if db.is_active:  # 检查是否需要回滚
            db.rollback()
        raise e
    finally:
        if db.is_active:  # 二次确认
            db.close()


# 创建数据库
def create_tables():
    PoliceDataBase.metadata.create_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    # TranscriptBase.metadata.create_all(bind=engine)
    Scence_4_Base.metadata.create_all(bind=engine)
    SituationAnalysisBase.metadata.create_all(bind=engine)
    UserBase.metadata.create_all(bind=engine)
    Scene2POBase.metadata.create_all(bind=engine)
    Scene_3_RecordBase.metadata.create_all(bind=engine)
    Scene6RecordBase.metadata.create_all(bind=engine)
    SacwBaseBase.metadata.create_all(bind=engine)
    Scene5Record.__table__.create(bind=engine, checkfirst=True)
    Scene5OcrPO.__table__.create(bind=engine, checkfirst=True)
    ChatBase.metadata.create_all(bind=engine)  # 添加聊天相关表的创建
