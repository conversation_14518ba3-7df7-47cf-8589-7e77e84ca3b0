<template>
  <div class="situation-analysis bigscreen">
    <PageHeader title="态势分析" subtitle="Situation Analysis" />

    <div class="content-wrapper ">
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="common-search search-form">
          <div class="search-left">
            <el-form-item label="报告名称">
              <el-input v-model="searchForm.reportName" placeholder="请输入报告名称"></el-input>
            </el-form-item>
          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetSearch" class="tool-btn">重置</el-button>
              <el-button type="primary" @click="showGenerateDialog" class="tool-btn">生成报告</el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>

      

      <!-- 添加生成报告弹框 -->
      <el-dialog title="请选择分析的范围" :visible.sync="generateDialogVisible" width="50%" :close-on-click-modal="false"
        custom-class="generate-dialog screen-dialog">
        <div class="generate-form">
          <div class="form-item">
            <div class="form-label">统计时间范围:</div>
            <el-date-picker v-model="generateForm.timeRange" type="monthrange" range-separator="至"
              start-placeholder="开始月份" end-placeholder="结束月份" value-format="yyyy-MM"
              :picker-options="{}"></el-date-picker>
          </div>
          <div class="form-item">
            <div class="form-label">请选择单位:</div>
            <el-select v-model="generateForm.selectedUnits" multiple collapse-tags placeholder="请选择单位"
              style="width: 100%;" :loading="unitSelectLoading" @change="handleUnitSelectionChange">
              <div style="margin: 0 5px;">
                <el-checkbox v-model="selectAllUnits" @change="handleSelectAllUnits"
                  style="padding: 8px 0; border-bottom: 1px solid #eee; width: 100%;">全选</el-checkbox>
              </div>
              <el-option v-for="item in unitOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="form-item">
            <div class="form-label">纳入此次分析的主要执法范围:</div>
            <el-tree ref="analysisTree" :data="analysisTreeData" node-key="id" :show-checkbox="false"
              :default-expanded-keys="['1', '2', '3', '4']" @node-click="handleNodeClick" class="analysis-tree">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="node-content" @click.stop="handleNodeContentClick(data)">
                  <template v-if="!data.developing && !data.children">
                    <el-checkbox v-model="checkedMap[data.id]" @click.native.stop
                      @change="val => handleCheckChange(data, val)" class="node-checkbox"></el-checkbox>
                  </template>
                  <span :class="{ 'node-label': true, 'no-checkbox': data.developing || data.children }">
                    {{ node.label }}
                  </span>
                </span>
                <span v-if="data.developing" class="developing-tag">正在开发中</span>
              </span>
            </el-tree>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="generateDialogVisible = false" class="cancel-btn">取 消</el-button>
          <el-button type="primary" @click="handleGenerate" class="ensure-btn">下一步</el-button>
        </div>
      </el-dialog>

      <el-dialog title="生成报告" :visible.sync="generatingDialogVisible" width="80%" :close-on-click-modal="false"
        :show-close="true" custom-class="generating-dialog screen-dialog">
        <div class="generating-container">
          <!-- 左侧流程区域 -->
          <div class="generating-left">
            <div class="process-title">生成流程</div>
            <div class="process-content">
              <div v-for="item in processes">
                <details class="thinking-details" open v-if="item.type == 'think'">
                  <summary class="thinking-summary">
                    <span>思考过程以及思考结果</span>
                    <i class="el-icon-arrow-down summary-arrow"></i>
                  </summary>
                  <div class="thinking-content">
                    <div class="thinking-text" v-html="item.thinkText"></div>
                    <hr />
                    <div class="thinking-text process-mark-text">
                      <div class="generating-text" v-html="formattedProcessText1(item.resultText)"></div>
                    </div>
                  </div>
                </details>
                <div v-else v-html="item"></div>
              </div>
            </div>
          </div>
          <!-- 右侧结果区域 -->
          <div class="generating-right">
            <div class="result-title">生成结果</div>
            <div class="result-content">
              <div class="generating-text" v-html="formattedResultText"></div>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="generatingDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>

      <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          v-loading="loading">
          <el-table-column type="index" label="序号" width="70">
          </el-table-column>
          <el-table-column prop="report_name" label="报告名称" min-width="350" show-overflow-tooltip></el-table-column>
          <el-table-column prop="start_time" label="统计开始时间" width="120" align="center"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="end_time" label="统计结束时间" width="120" align="center"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="130" align="center"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <!-- <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? "已完成" : "生成中" }}
              </el-tag> -->
              <StatusView :status-option="formatAnalysisStatus(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="scope">
              <el-button v-if="scope.row.status === 1" type="text" size="small" @click="handleView(scope.row)"
                class="view-btn row-operation-btn row-operation-btn-color">
                <!-- <i class="el-icon-view"></i> -->
                查看导出
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>

      <el-dialog title="报告预览" :visible.sync="previewDialogVisible" width="70%" :close-on-click-modal="false"
        custom-class="preview-dialog screen-dialog" append-to-body>
        <div class="preview-container">
          <div class="preview-header">
            <div class="preview-title">{{ currentReport.report_name }}</div>
            <el-button type="primary" @click="handleDownloadInPreview">
              <i class="el-icon-download"></i>
              下载文档
            </el-button>
          </div>
          <div class="preview-content markdown-body">
            <div v-html="markdownContent" class="preview-container-inner"></div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { marked } from "marked";
import "github-markdown-css";
import * as echarts from "echarts";
import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  Table,
  TableRow,
  TableCell,
  WidthType,
  ImageRun,
} from "docx";
import { convert } from "html-to-text";
import html2canvas from "html2canvas";
import MarkdownIt from "markdown-it";
import mdEcharts from "markdown-it-echarts";
import { getReportList, getReportDetail, checkRecord } from "@/api/report";
import { runPoliceSituationWorkflow } from "@/api/chat";
import PageHeader from "@/components/PageHeader.vue";
import StatusView from "@/components/StatusView.vue";
export default {
  name: "SituationAnalysis",
  components: {
    PageHeader,
    StatusView,
  },
  data() {
    return {
      searchForm: {
        reportName: "",
      },
      md: new MarkdownIt({ html: true }).use(mdEcharts),
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      loading: false,
      total: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", start);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", start);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", start);
            },
          },
        ],
      },
      generateDialogVisible: false,
      generateForm: {
        timeRange: "",
        selectedAnalysis: [],
        selectedUnits: [],  // 添加选择的单位
      },
      // 单位数据
      unitOptions: [
        { id: "0", name: "丹江口市公安局" },
        { id: "1", name: "房县公安局" },
        { id: "2", name: "十堰市公安局" },
        { id: "3", name: "十堰市公安局东岳分局" },
        { id: "4", name: "十堰市公安局公共交通安全管理分局" },
        { id: "5", name: "十堰市公安局回龙分局" },
        { id: "6", name: "十堰市公安局茅箭区分局" },
        { id: "7", name: "十堰市公安局十堰经济开发区分局" },
        { id: "8", name: "十堰市公安局武当山机场分局" },
        { id: "9", name: "十堰市公安局郧阳区分局" },
        { id: "10", name: "十堰市公安局张湾区分局" },
        { id: "11", name: "十堰市公安局直属分局" },
        { id: "12", name: "武当山旅游经济特区公安局" },
        { id: "13", name: "郧西县公安局" },
        { id: "14", name: "竹山县公安局" },
        { id: "15", name: "竹溪县公安局" }
      ],
      unitSelectLoading: false,
      selectedUnitMap: {},  // 维护选中单位的状态
      generateTimeOptions: {},
      analysisTreeData: [
        {
          id: "1",
          label: "一、主要执法数据",
          children: [
            {
              id: "police_situation",
              label: "（一）警情数据",
            },
            {
              id: "1-2",
              label: "（二）刑事案件数据",
            },
            {
              id: "1-3",
              label: "（三）行政案件数据",
            },
          ],
        },
        {
          id: "2",
          label: '二、扣好扣法"第一粒扣子"常态巡查情况',
          children: [
            {
              id: "2-1",
              label: "（一）实体性问题",
              developing: true,
            },
            {
              id: "2-2",
              label: "（二）程序性问题",
              developing: true,
            },
          ],
        },
        {
          id: "3",
          label: "三、第三方监督情况",
          children: [
            {
              id: "3-1",
              label: "（一）审判机关行政诉讼情况",
              developing: true,
            },
            {
              id: "3-2",
              label: "（二）地方政府行政复议情况",
              developing: true,
            },
            {
              id: "3-3",
              label: "（三）检察监督及纠违情况",
              developing: true,
            },
            {
              id: "3-4",
              label: "（四）涉法信访情况",
              developing: true,
            },
            {
              id: "3-5",
              label: "（五）监督后追责问责情况",
              developing: true,
            },
          ],
        },
        {
          id: "4",
          label: "四、群众评价情况",
          children: [
            {
              id: "4-1",
              label: "（一）警情处理满意情况",
              developing: true,
            },
            {
              id: "4-2",
              label: "（二）案件处理满意情况",
              developing: true,
            },
          ],
        },
      ],
      originalTreeData: [
        {
          id: "1",
          label: "一、主要执法数据",
          children: [
            {
              id: "police_situation",
              label: "（一）警情数据",
            },
            {
              id: "1-2",
              label: "（二）刑事案件数据",
            },
            {
              id: "1-3",
              label: "（三）行政案件数据",
            },
          ],
        },
        {
          id: "2",
          label: '二、扣好扣法"第一粒扣子"常态巡查情况',
          children: [
            {
              id: "2-1",
              label: "（一）实体性问题",
              developing: true,
            },
            {
              id: "2-2",
              label: "（二）程序性问题",
              developing: true,
            },
          ],
        },
        {
          id: "3",
          label: "三、第三方监督情况",
          children: [
            {
              id: "3-1",
              label: "（一）审判机关行政诉讼情况",
              developing: true,
            },
            {
              id: "3-2",
              label: "（二）地方政府行政复议情况",
              developing: true,
            },
            {
              id: "3-3",
              label: "（三）检察监督及纠违情况",
              developing: true,
            },
            {
              id: "3-4",
              label: "（四）涉法信访情况",
              developing: true,
            },
            {
              id: "3-5",
              label: "（五）监督后追责问责情况",
              developing: true,
            },
          ],
        },
        {
          id: "4",
          label: "四、群众评价情况",
          children: [
            {
              id: "4-1",
              label: "（一）警情处理满意情况",
              developing: true,
            },
            {
              id: "4-2",
              label: "（二）案件处理满意情况",
              developing: true,
            },
          ],
        },
      ],
      previewDialogVisible: false,
      currentReport: {},
      markdownContent: "",
      previewLoading: false,
      chartInstance: [],
      resultChartInstance: [],
      generatingDialogVisible: false,
      processText: "",
      resultText: "",
      currentThinking: "",
      checkedKeys: [],
      checkedMap: {},
      processes: [],
      processesChartInstance: [],
      selectAllUnits: false,
      analysisFormatMap: {
        0: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        1: { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        2: { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
      },
    };
  },
  created() {
    this.loadTableData();
    this.initCheckedMap();
    // 使用实例属性保存定时器ID，便于后续清除
    // this.autoRefreshTimer = setInterval(this.loadTableData, 60000);
  },
  methods: {
    // 分析状态格式化
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.status];
      return status || {};
    },
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchForm.reportName) {
          params.report_name = this.searchForm.reportName;
        }

        const res = await getReportList(params);
        if (res.code === 200 && res.data) {
          this.tableData = res.data.records || [];
          this.total = res.data.total || 0;
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;
      }
    },
    headerStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.8)",
        color: "#ffffffcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
      };
    },
    async handleSearch() {
      this.currentPage = 1;
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        reportName: "",
      };
      this.handleSearch();
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return cellValue;
    },
    goToHome() {
      this.$router.push("/");
    },
    async handleView(row) {
      try {
        this.currentReport = row;
        this.previewDialogVisible = true;
        this.previewLoading = true;

        const res = await getReportDetail({ report_id: row.id });
        if (res.code === 200 && res.data) {
          let md_content = res.data.md_content.replace(/^```markdown\s*\n([\s\S]+?)\n```$/g, "$1");
          console.log(md_content);
          // 移除echarts代码块标记
          md_content = this.removeFunctionsFromString(md_content);
          console.log(md_content);
          // 使用模拟数据
          const mockData = {
            report_name: row.report_name,
            start_time: row.start_time,
            end_time: row.end_time,
            created_at: row.created_at,
            status: row.status,
            md_content: md_content,
          };

          // 更新当前报告信息
          this.currentReport = mockData;
          // 设置markdown内容
          this.markdownContent = marked(mockData.md_content, {
            gfm: true,
            breaks: true,
          });

          // 等待DOM更新后初始化图表
          this.$nextTick(() => {
            this.initChart();
          });
        }
      } catch (error) {
        console.error("获取报告详情失败：", error);
      } finally {
        this.previewLoading = false;
      }
    },
    initChart() {
      // 获取所有包含 echarts 代码块的 pre 元素
      const preElements = document.querySelectorAll(".markdown-body pre");
      if (!preElements.length) return;

      // 销毁所有现有的图表实例
      if (this.chartInstance) {
        this.chartInstance.forEach(instance => instance.dispose());
        this.chartInstance = [];
      }

      // 为每个图表创建新的实例
      preElements.forEach((preElement, index) => {
        try {
          const chartDom = document.createElement("div");
          chartDom.style.width = "100%";
          chartDom.style.height = "400px";
          preElement.parentNode.replaceChild(chartDom, preElement);

          const chartInstance = echarts.init(chartDom);
          const option = JSON.parse(preElement.textContent);
          chartInstance.setOption(option);
          this.chartInstance.push(chartInstance);
        } catch (error) {
          console.error(`初始化第 ${index + 1} 个图表失败:`, error);
        }
      });

      // 监听窗口大小变化，调整所有图表大小
      window.addEventListener("resize", this.handleResize, { passive: true });
    },
    initResultChart() {
      // 获取所有包含 echarts 代码块的 pre 元素
      const preElements = document.querySelectorAll(".result-content pre");
      if (!preElements.length) return;

      // 销毁所有现有的图表实例
      if (this.resultChartInstance) {
        this.resultChartInstance.forEach(instance => instance.dispose());
        this.resultChartInstance = [];
      }

      // 为每个图表创建新的实例
      preElements.forEach((preElement, index) => {
        try {
          const chartDom = document.createElement("div");
          chartDom.style.width = "100%";
          chartDom.style.height = "400px";
          preElement.parentNode.replaceChild(chartDom, preElement);

          const chartInstance = echarts.init(chartDom);
          const option = JSON.parse(preElement.textContent);
          chartInstance.setOption(option);
          this.resultChartInstance.push(chartInstance);
        } catch (error) {
          console.error(`初始化第 ${index + 1} 个结果图表失败:`, error);
        }
      });

      // 监听窗口大小变化，调整所有图表大小
      window.addEventListener("resize", this.handleResultResize, { passive: true });
    },
    initProcessInstanceChart() {
      // 获取所有包含 echarts 代码块的 pre 元素
      const preElements = document.querySelectorAll(".process-mark-text code.language-echarts");
      if (!preElements.length) return;

      // 销毁所有现有的图表实例
      // if (this.processesChartInstance) {
      //   this.processesChartInstance.forEach(instance => instance.dispose());
      //   this.processesChartInstance = [];
      // }

      // 为每个图表创建新的实例
      preElements.forEach((preElement, index) => {
        try {
          const chartDom = document.createElement("div");
          chartDom.style.width = "100%";
          chartDom.style.height = "400px";
          preElement.parentNode.replaceChild(chartDom, preElement);

          const chartInstance = echarts.init(chartDom);
          const option = JSON.parse(preElement.textContent);
          chartInstance.setOption(option);
          this.processesChartInstance.push(chartInstance);
        } catch (error) {
          console.error(`初始化第 ${index + 1} 个结果图表失败:`, error);
        }
      });

      // 监听窗口大小变化，调整所有图表大小
      window.addEventListener("resize", this.handleProcessChartResize, { passive: true });
    },
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.forEach(instance => instance.resize());
      }
    },
    handleResultResize() {
      if (this.resultChartInstance) {
        this.resultChartInstance.forEach(instance => instance.resize());
      }
    },
    handleProcessChartResize() {
      if (this.processesChartInstance) {
        this.processesChartInstance.forEach(instance => instance.resize());
      }
    },
    async handleDownload(row) {
      try {
        // 显示loading
        this.$loading({
          lock: true,
          text: "正在准备下载文档...",
          spinner: "el-icon-loading",
          background: "rgba(0, 24, 51, 0.9)",
          customClass: "custom-loading",
        });

        // 先加载预览内容

        // 等待预览内容加载完成
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 获取预览容器
        const previewContainer = document.querySelector(".preview-container-inner");
        if (!previewContainer) {
          throw new Error("未找到预览内容");
        }

        // 创建文档内容数组
        const children = [];

        // 获取所有段落和图表
        const elements = previewContainer.children;

        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];

          // 处理标题
          if (element.tagName === "H1") {
            children.push(
              new Paragraph({
                text: element.textContent,
                heading: HeadingLevel.HEADING_1,
                spacing: { after: 200 },
              })
            );
          } else if (element.tagName === "H2") {
            children.push(
              new Paragraph({
                text: element.textContent,
                heading: HeadingLevel.HEADING_2,
                spacing: { after: 200 },
              })
            );
          } else if (element.tagName === "H3") {
            children.push(
              new Paragraph({
                text: element.textContent,
                heading: HeadingLevel.HEADING_3,
                spacing: { after: 200 },
              })
            );
          }
          // 处理段落
          else if (element.tagName === "P") {
            children.push(
              new Paragraph({
                text: element.textContent,
                spacing: { after: 200 },
              })
            );
          }
          // 处理列表
          else if (element.tagName === "UL" || element.tagName === "OL") {
            const items = element.getElementsByTagName("LI");
            for (let j = 0; j < items.length; j++) {
              children.push(
                new Paragraph({
                  text: `${j + 1}. ${items[j].textContent}`,
                  indent: { left: 720 }, // 缩进
                  spacing: { after: 200 },
                })
              );
            }
          }
          // 处理图表
          else if (element.tagName === "DIV" && element.querySelector("canvas")) {
            const canvas = element.querySelector("canvas");
            if (canvas) {
              const blob = await new Promise(resolve => canvas.toBlob(resolve, "image/png", 1.0));
              const imageBuffer = await blob.arrayBuffer();
              children.push(
                new Paragraph({
                  children: [
                    new ImageRun({
                      data: imageBuffer,
                      transformation: {
                        width: 600,
                        height: canvas.height * (600 / canvas.width),
                      },
                    }),
                  ],
                  spacing: { after: 200 },
                })
              );
            }
          }
        }

        // 创建 Word 文档
        const doc = new Document({
          sections: [
            {
              properties: {},
              children: children,
            },
          ],
        });

        // 生成 Word 文档
        const docBlob = await Packer.toBlob(doc);

        // 创建下载链接
        const url = window.URL.createObjectURL(docBlob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${row.report_name}.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // 关闭预览对话框
        // this.previewDialogVisible = false;

        this.$message.success("文档导出成功");
      } catch (error) {
        console.error("导出文档失败：", error);
        this.$message.error("导出文档失败：" + error.message);
      } finally {
        // 关闭loading
        this.$loading().close();
      }
    },
    handleDownloadInPreview() {
      this.handleDownload(this.currentReport);
    },
    showGenerateDialog() {
      // 重置表单数据
      this.generateForm = {
        timeRange: "",
        selectedAnalysis: [],
        selectedUnits: [],
      };

      // 重置选中状态
      this.checkedKeys = [];
      this.checkedMap = {};
      this.initCheckedMap();

      // 重置单位全选状态
      this.selectAllUnits = false;

      // 重置生成报告相关数据
      this.processes = [];
      this.processText = "";
      this.resultText = "";
      this.currentThinking = "";

      // 销毁所有图表实例
      if (this.processesChartInstance) {
        this.processesChartInstance.forEach(instance => instance.dispose());
        this.processesChartInstance = [];
      }
      if (this.resultChartInstance) {
        this.resultChartInstance.forEach(instance => instance.dispose());
        this.resultChartInstance = [];
      }

      // 显示对话框
      this.generateDialogVisible = true;
    },
    async handleGenerate() {
      // let resultText = ""
      // let results = {"result": {"result_1_1_1": "```markdown\n\u5404\u53bf\u3001\u5e02\uff08\u533a\uff09\u6709\u6548\u8b66\u60c5\u5206\u5e03\u60c5\u51b5\uff1a\u4e39\u6c5f\u53e3\u5e02\u5c4015,145\u8d77\u3001\u5341\u5830\u5e02\u516c\u5b89\u5c4051,674\u8d77\u3001\u4e1c\u5cb3\u5206\u5c402,005\u8d77\u3001\u516c\u5171\u4ea4\u901a\u5b89\u5168\u7ba1\u7406\u5206\u5c400\u8d77\u3001\u7ecf\u5f00\u533a\u5206\u5c401,276\u8d77\u3001\u56de\u9f99\u5206\u5c409\u8d77\u3001\u5f20\u6e7e\u5206\u5c404,730\u8d77\u3001\u673a\u573a\u5206\u5c409\u8d77\u3001\u76f4\u5c5e\u5206\u5c4075\u8d77\u3001\u8305\u7bad\u5206\u5c408,490\u8d77\u3001\u90e7\u9633\u5206\u5c4011,658\u8d77\u3001\u623f\u53bf\u5c4010,299\u8d77\u3001\u6b66\u5f53\u5c71\u7279\u533a\u5c403,790\u8d77\u3001\u7af9\u5c71\u53bf\u5c409,351\u8d77\u3001\u7af9\u6eaa\u53bf\u5c406,980\u8d77\u3001\u90e7\u897f\u53bf\u5c4010,490\u8d77\u3002\n```", "result_1_1_2": "```echarts\n{\n  \"xAxis\": {\n    \"type\": \"category\",\n    \"data\": [\"\u4e39\u6c5f\u53e3\u5e02\u516c\u5b89\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u4e1c\u5cb3\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u516c\u5171\u4ea4\u901a\u5b89\u5168\u7ba1\u7406\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u5341\u5830\u7ecf\u6d4e\u5f00\u53d1\u533a\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u56de\u9f99\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u5f20\u6e7e\u533a\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u6b66\u5f53\u5c71\u673a\u573a\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u76f4\u5c5e\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u8305\u7bad\u533a\u5206\u5c40\",\"\u5341\u5830\u5e02\u516c\u5b89\u5c40\u90e7\u9633\u533a\u5206\u5c40\",\"\u623f\u53bf\u516c\u5b89\u5c40\",\"\u6b66\u5f53\u5c71\u65c5\u6e38\u7ecf\u6d4e\u7279\u533a\u516c\u5b89\u5c40\",\"\u7af9\u5c71\u53bf\u516c\u5b89\u5c40\",\"\u7af9\u6eaa\u53bf\u516c\u5b89\u5c40\",\"\u90e7\u897f\u53bf\u516c\u5b89\u5c40\"],\n    \"axisLabel\": {\n      \"rotate\": 45,\n      \"interval\": 0\n    }\n  },\n  \"yAxis\": {\n    \"type\": \"value\",\n    \"axisLabel\": {\n      \"formatter\": \"{value}\"\n    }\n  },\n  \"legend\": {\n    \"data\": [\"2025\u5e74\u63a5\u8b66\u6570\"],\n    \"right\": \"5%\",\n    \"orient\": \"vertical\"\n  },\n  \"tooltip\": {\n    \"trigger\": \"axis\",\n    \"formatter\": \"\u8b66\u5c40\uff1a{b}<br/>\u63a5\u8b66\u6570\uff1a{c}\"\n  },\n  \"series\": [{\n    \"name\": \"2025\u5e74\u63a5\u8b66\u6570\",\n    \"type\": \"bar\",\n    \"data\": [15145,51674,2005,0,1276,9,4730,9,75,8490,11658,10299,3790,9351,6980,10490],\n    \"label\": {\n      \"show\": true,\n      \"position\": \"top\",\n      \"formatter\": \"{c}\"\n    },\n    \"itemStyle\": {\n      \"color\": \"#5470c6\"\n    }\n  }],\n  \"grid\": {\n    \"bottom\": \"20%\"\n  }\n}\n```", "result_1_2_1": null, "result_1_2_2": null}}
      // // 获取所有key并排序
      // const sortedKeys = Object.keys(results.result).sort();
      // // 遍历排序后的key，组合value
      // resultText = sortedKeys.map(key => {
      //   const value = results.result[key];
      //   if(!value){
      //     return
      //   }
      //   // 移除markdown代码块标记
      //   return value.replace(/```markdown\n/, '').replace(/```$/, '');
      // }).join('\n\n');

      // this.resultText = resultText;
      // console.log(this.resultText)
      if (!this.generateForm.timeRange || !this.generateForm.selectedAnalysis || !this.generateForm.selectedUnits || this.generateForm.selectedUnits.length === 0) {
        this.$message.warning("请选择统计时间范围、分析范围和单位");
        return;
      }
      // ,type: this.generateForm.selectedAnalysis
      const res1 = await checkRecord({
        start_time: this.generateForm.timeRange[0] + "-01",
        end_time: this.generateForm.timeRange[1] + "-01",
        unit_ids: this.generateForm.selectedUnits,  // 添加选中的单位ID
        police_station: this.generateForm.selectedUnits.join(',')
      });
      if (res1.code === 200 && res1.data) {
        this.generateDialogVisible = true;
      } else if (res1.code === 1001) {
        this.$message.error(res1.message || "生成报告失败");
        return;
      } else {
        this.$message.error(res1.message || "生成报告失败");
        return;
      }

      if (!this.validateGenerateForm()) {
        return;
      }

      // 获取选中的分析类型
      const selectedTypes = this.generateForm.selectedAnalysis;
      // if (!selectedTypes.includes("police_situation")) {
      //   this.$message.warning("请至少选择警情数据分析");
      //   return;
      // }
      if (!this.generateForm.timeRange || !this.generateForm.selectedAnalysis || !this.generateForm.selectedUnits || this.generateForm.selectedUnits.length === 0) {
        this.$message.warning("请选择统计时间范围、分析范围和单位");
        return;
      }

      // 获取时间范围
      const [startMonth, endMonth] = this.generateForm.timeRange;
      // if (!startMonth || !endMonth) {
      //   this.$message.warning("请选择有效的时间范围");
      //   return;
      // }

      // 关闭选择对话框，显示生成对话框
      this.generateDialogVisible = false;
      this.generatingDialogVisible = true;
      this.processText = "";
      this.resultText = "";
      this.currentThinking = "";

      this.loadTableData();

      try {
        // 修改API调用，传递选中的单位ID
        const response = await runPoliceSituationWorkflow(
          startMonth,
          endMonth,
          res1.data.id,
          this.generateForm.selectedUnits,
          this.generateForm.selectedUnits.join(',')
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let partialLine = "";

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          partialLine += chunk;

          const lines = partialLine.split("\n");
          partialLine = lines.pop();

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) continue;

            if (trimmedLine.startsWith("data: ")) {
              try {
                const data = JSON.parse(trimmedLine.slice(6));
                // 解析data.outputs.text的值
                if (
                  data.data &&
                  data.data.outputs &&
                  data.data.outputs.text &&
                  data.data.outputs.text.startsWith("<detail")
                ) {
                  let thinkAllContent = data.data.outputs.text;
                  // 匹配 <details> 标签内的内容，去除 <details> 和 </details> 标签
                  const detailsMatch = thinkAllContent.match(/<details[^>]*>([\s\S]*?)<\/details>/);

                  if (detailsMatch) {
                    // 获取 <details> 标签内的内容
                    let detailsContent = detailsMatch[1];

                    // 移除 <summary> 和 </summary> 标签之间的内容（包括 <summary> 标签和后面的内容）
                    let thinkText = detailsContent
                      .replace(/<summary[^>]*>[\s\S]*?<\/summary>/, "")
                      .trim();

                    // 其它内容是 <details> 标签之外的内容
                    let contentShowText = thinkAllContent.replace(detailsMatch[0], "").trim();

                    if (contentShowText.includes("```markdown")) {
                      contentShowText = contentShowText
                        .replace(/```markdown/, "")
                        .replace(/```$/, "");
                    }
                    this.processes.push({
                      type: "think",
                      thinkText: thinkText,
                      resultText: contentShowText,
                    });

                    this.$nextTick(() => {
                      this.initProcessInstanceChart();
                    });
                  }
                }
                if (data.event === "message" && data.answer) {
                  const content = data.answer;

                  if (
                    content.includes(
                      '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open> <summary> Thinking... </summary>'
                    )
                  ) {
                    this.currentThinking = content.replace(
                      '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open> <summary> Thinking... </summary>',
                      ""
                    );
                  } else if (content.includes("</details>")) {
                    this.currentThinking = this.currentThinking.replace("</details>", "");
                    this.processText += `<think>${this.currentThinking}</think>`;
                    this.currentThinking = "";
                  } else if (this.currentThinking) {
                    this.currentThinking += content;
                    this.processText += `<think>${this.currentThinking}</think>`;
                  } else {
                    this.processText += content;
                  }
                } else if (data.event === "node_started") {
                  const nodeData = data.data;
                  if (nodeData && nodeData.title) {
                    this.currentThinking = `开始执行: ${nodeData.title}...\n`;
                    this.processText += `<think>${this.currentThinking}</think>`;
                    this.processes.push(`<think>${this.currentThinking}</think>`);
                  }
                } else if (data.event === "parallel_branch_finished") {
                  const branchData = data.data;
                  if (branchData && branchData.status === "succeeded") {
                    this.currentThinking = `并行分支执行完成\n`;
                    this.processText += `<think>${this.currentThinking}</think>`;
                    this.processes.push(`<think>${this.currentThinking}</think>`);
                  }
                } else if (data.event === "workflow_finished" && data.data && data.data.outputs) {
                  // 当工作流完成时,获取最终的markdown输出
                  const outputs = data.data.outputs;
                  let resultText = "";

                  // 处理markdown输出
                  // if (outputs.result && outputs.result.result_1_1_1) {
                  //   // 移除markdown代码块标记
                  //   resultText = outputs.result.result_1_1_1.replace(/```markdown\n/, '').replace(/```$/, '');
                  // }

                  // // 处理图表输出
                  // if (outputs.result && outputs.result.result_1_1_2) {
                  //   // 移除echarts代码块标记
                  //   resultText += '\n\n' + outputs.result.result_1_1_2;
                  // }

                  // 处理所有输出结果
                  if (outputs.result) {
                    // 获取所有key并排序
                    const sortedKeys = Object.keys(outputs.result).sort();
                    // 遍历排序后的key，组合value
                    resultText = sortedKeys
                      .map(key => {
                        const value = outputs.result[key];
                        if (!value) {
                          return "";
                        }
                        // 处理markdown内容
                        let valueText = value;
                        // 如果是markdown代码块，保留markdown标记
                        if (valueText.startsWith("```markdown")) {
                          valueText = valueText.replace(/```markdown/, "").replace(/```$/, "");
                        }
                        // 如果是echarts代码块，保留echarts标记
                        else if (valueText.startsWith("```echarts")) {
                          valueText = valueText;
                        }
                        // 其他内容直接返回
                        return valueText;
                      })
                      .filter(text => text)
                      .join("\n\n");
                  }
                  // console.log('处理后的结果文本:', resultText);
                  this.resultText = this.removeFunctionsFromString(resultText);

                  // 等待DOM更新后初始化图表
                  this.$nextTick(() => {
                    try {
                      this.initResultChart();
                    } catch (error) {
                      console.error("初始化结果图表失败:", error);
                    }
                  });
                }
              } catch (e) {
                console.error("解析响应数据失败:", e);
              }
            }
          }
        }
      } catch (error) {
        console.error("生成报告失败:", error);
      } finally {
        // this.generatingDialogVisible = false;
      }
    },
    removeFunctionsFromString(fixedStr) {
      // 1️⃣ **移除 function(params) { ... } 及其所有内容**
      fixedStr = fixedStr.replace(/:\s*function\s*\([^)]*\)\s*\{[\s\S]*?\}\s*(?=,|\})/g, ': ""');

      // 2️⃣ **移除箭头函数 (value) => {} 或 (value) => value + "%"**
      fixedStr = fixedStr.replace(/:\s*\([^)]*\)\s*=>\s*({[\s\S]*?}|\S+)/g, ': ""');

      // 3️⃣ **删除 ECharts 可能的 `formatter: function(params) {...}`**
      fixedStr = fixedStr.replace(
        /"formatter"\s*:\s*""\s*}\);[\s\S]*?return\s+res;\s*}\s*,/g,
        '"formatter": "",'
      );

      // 4️⃣ **修复 JSON key 少引号 (支持 key 含 `-` 和 `_`)**
      fixedStr = fixedStr.replace(/(?<=\{|,)\s*([\w-]+)\s*:/g, '"$1":');

      // 5️⃣ **删除 `/* ... */` 块注释 和 `//` 行注释**
      fixedStr = fixedStr.replace(/\/\*[\s\S]*?\*\//g, "");
      fixedStr = fixedStr.replace(/(^|\s|[,{\[])\s*\/\/.*/g, "$1");

      // 6️⃣ **修复 `+` 连接的多行字符串**
      fixedStr = fixedStr.replace(/"\s*\+\s*"/g, "");

      // 7️⃣ **移除 `,]` 或 `,}`**
      fixedStr = fixedStr.replace(/,(\s*[}\]])/g, "$1");

      // 8️⃣ **修复多个 `,,` 变 `,`**
      fixedStr = fixedStr.replace(/,,+/g, ",");

      return fixedStr;
    },
    handleNodeClick(data) {
      if (data.developing) {
        this.$message({
          message: "该功能正在开发中",
          type: "warning",
          duration: 2000,
        });
        return;
      }

      if (!data.children) {
        // 如果是可选的叶子节点，切换选中状态
        const currentChecked = this.checkedMap[data.id] || false;
        this.handleCheckChange(data, !currentChecked);
      }
    },
    handleCheckChange(data, checked) {
      if (data.developing) return;

      if (data.children) return;

      // 强制更新checkedMap（使用Vue的响应式系统）
      this.$set(this.checkedMap, data.id, checked);

      // 同步更新checkedKeys
      const index = this.checkedKeys.indexOf(data.id);

      if (checked && index === -1) {
        // 添加到选中列表
        this.checkedKeys.push(data.id);
      } else if (!checked && index !== -1) {
        // 从选中列表中移除
        this.checkedKeys.splice(index, 1);
      }

      // 更新表单数据（确保使用正确的值）
      this.generateForm.selectedAnalysis = [...this.checkedKeys];

      // 强制更新视图
      this.$forceUpdate();
    },
    isNodeChecked(id) {
      return !!this.checkedMap[id];
    },
    handleTreeCheck(data, checked) {
      // 这个方法不再使用，保留是为了兼容性
      if (data.developing) {
        this.$message({
          message: "该功能正在开发中",
          type: "warning",
          duration: 2000,
        });
        return;
      }

      if (data.children) {
        return;
      }
    },
    renderContent(h, { node, data, store }) {
      // 如果节点是正在开发中的，则不显示复选框
      if (data.developing) {
        return h(
          "span",
          {
            class: "custom-tree-node",
            style: {
              display: "flex",
              justifyContent: "space-between",
              width: "100%",
            },
          },
          [
            h("span", node.label),
            h(
              "span",
              {
                class: "developing-tag",
              },
              "正在开发中"
            ),
          ]
        );
      } else {
        // 对于正常节点，正常渲染
        return h(
          "span",
          {
            class: "custom-tree-node",
          },
          [h("span", node.label)]
        );
      }
    },
    filterTreeData() {
      // 过滤树数据，移除 developing: true 的节点
      const filterNodes = nodes => {
        if (!nodes) return [];
        return nodes.map(node => {
          const newNode = { ...node };
          if (newNode.children && newNode.children.length > 0) {
            newNode.children = filterNodes(newNode.children).filter(child => !child.developing);
          }
          return newNode;
        });
      };

      this.analysisTreeData = filterNodes(this.originalTreeData);
    },
    validateGenerateForm() {
      if (
        !this.generateForm.timeRange ||
        !this.generateForm.timeRange[0] ||
        !this.generateForm.timeRange[1]
      ) {
        this.$message.warning("请选择统计时间范围");
        return false;
      }
      if (!this.generateForm.selectedAnalysis || this.generateForm.selectedAnalysis.length === 0) {
        this.$message.warning("请至少选择一个分析范围");
        return false;
      }
      if (!this.generateForm.selectedUnits || this.generateForm.selectedUnits.length === 0) {
        this.$message.warning("请至少选择一个单位");
        return false;
      }
      return true;
    },
    initCheckedMap() {
      // 初始化复选框状态映射
      const initMap = nodes => {
        if (!nodes) return;
        nodes.forEach(node => {
          if (!node.children && !node.developing) {
            this.$set(this.checkedMap, node.id, false);
          }
          if (node.children) {
            initMap(node.children);
          }
        });
      };

      initMap(this.analysisTreeData);
    },
    handleNodeContentClick(data) {
      this.handleNodeClick(data);
    },
    formattedProcessText1(text) {
      if (!text) return "";
      let ts = this.removeFunctionsFromString(text);
      // 使用marked转换markdown为HTML，并添加gfm和breaks选项
      const html = marked(ts, {
        gfm: true,
        breaks: true,
      });
      return html;
    },
    handleUnitSelectionChange(value) {
      this.selectAllUnits = value.length === this.unitOptions.length;
    },
    handleSelectAllUnits(value) {
      this.generateForm.selectedUnits = value ? this.unitOptions.map(item => item.id) : [];
      this.selectAllUnits = value;
    },
  },
  computed: {
    formattedProcessText() {
      return marked(this.processText, {
        gfm: true,
        breaks: true,
      });
    },
    formattedResultText() {
      return marked(this.resultText, {
        gfm: true,
        breaks: true,
      });
      // return this.md.render(this.resultText);
    },
  },
  watch: {
    markdownContent: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
    },
    resultText: {
      handler() {
        this.$nextTick(() => {
          this.initResultChart();
        });
      },
      deep: true,
    },
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener("resize", this.handleResize);
    window.removeEventListener("resize", this.handleResultResize);
    window.removeEventListener("resize", this.handleProcessChartResize);

    // 清除定时器
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
    }

    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.forEach(instance => instance.dispose());
    }
    if (this.resultChartInstance) {
      this.resultChartInstance.forEach(instance => instance.dispose());
    }
    if (this.processesChartInstance) {
      this.processesChartInstance.forEach(instance => instance.dispose());
    }
  },
};
</script>

<style scoped>
.situation-analysis {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

.search-form {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
}

.search-right {
  margin-left: auto;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

.view-btn {
  color: #00ffff;
  background: transparent;
  border: none;
  padding: 0;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.view-btn:hover {
  color: #ffffff;
  background: transparent;
}

.view-btn i {
  font-size: 16px;
}

.download-btn {
  color: #00ffff;
  background: transparent;
  border: none;
  padding: 0;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  margin-left: 15px;
}

.download-btn:hover {
  color: #ffffff;
  background: transparent;
}

.download-btn i {
  font-size: 16px;
}

/* Element UI 样式覆盖 */
:deep(.el-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
  height: 0 !important;
}

.pagination-container {
  margin-top: 20px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 1);
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}

:deep(.el-button) {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
  padding: 0 20px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 80px;
}

:deep(.el-button:hover) {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
  color: #00ffff;
}

:deep(.el-table) {
  background-color: transparent !important;
  flex: 1;
  min-height: 0;
}

:deep(.el-table__header) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0, 48, 102, 0.3);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table td, .el-table th.is-leaf) {
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  background-color: rgba(0, 48, 102, 0.3) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-table__row) {
  transition: all 0.3s;
}

:deep(.el-table__body tr:hover > td) {
  background-color: rgba(0, 255, 255, 0.2) !important;
  color: #00ffff !important;
}

:deep(.el-table__header-wrapper) {
  background-color: transparent !important;
}

:deep(.el-table__header tr th) {
  background-color: rgba(0, 48, 102, 0.8) !important;
  border-bottom: 2px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-table__body tr) {
  background-color: transparent !important;
}

:deep(.el-table__body tr td) {
  background-color: rgba(0, 48, 102, 0.3) !important;
}

:deep(.el-table__body tr:hover td) {
  background-color: rgba(0, 255, 255, 0.1) !important;
}

:deep(.el-form-item__label) {
  color: #ffffff !important;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
  margin-bottom: 0px;
}

:deep(.el-date-editor) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-date-editor:hover),
:deep(.el-date-editor:focus) {
  border-color: #00ffff;
}

:deep(.el-form-item.el-form-item--default) {
  margin-bottom: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 10px;
}

:deep(.el-loading-mask) {
  background-color: rgba(0, 24, 51, 0.9) !important;
}

:deep(.el-loading-spinner .el-loading-text) {
  color: #00ffff !important;
}

:deep(.el-loading-spinner .path) {
  stroke: #00ffff !important;
}

:deep(.el-date-editor.el-input__inner) {
  width: 240px;
}

:deep(.el-date-editor--datetime.el-input__inner) {
  width: 240px;
}

:deep(.el-date-editor .el-range-input) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel) {
  background: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-picker-panel__content) {
  color: #ffffff;
}

:deep(.el-date-table th) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-date-table td.available:hover) {
  color: #00ffff;
}

:deep(.el-date-table td.current:not(.disabled)) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
}

:deep(.el-picker-panel__shortcut) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel__shortcut:hover) {
  color: #00ffff;
}

:deep(.el-date-picker__header-label) {
  color: #ffffff;
}

:deep(.el-date-picker__header-label:hover) {
  color: #00ffff;
}

:deep(.el-picker-panel__icon-btn) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel__icon-btn:hover) {
  color: #00ffff;
}

:deep(.el-form--inline .el-form-item__label) {
  padding-right: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  float: inline-start;
}

:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-button) {
  height: 32px;
  padding: 0 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-button [class*="el-icon-"] + span) {
  margin-left: 4px;
}

:deep(.el-select .el-input__inner) {
  padding-right: 25px;
}

:deep(.el-date-editor .el-range__icon) {
  line-height: 24px;
}

.pagination-container {
  margin-top: 20px;
}

:deep(.el-pagination) {
  padding: 0;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}

:deep(.el-select:hover .el-input__inner) {
  border-color: #00ffff;
}

:deep(.el-select .el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.1);
  color: #ffffff;
}

:deep(.el-select-dropdown) {
  background-color: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}

:deep(.el-tooltip__popper.is-light) {
  background: rgba(0, 48, 102, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

:deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow) {
  border-bottom-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow::after) {
  border-bottom-color: rgba(0, 48, 102, 0.95) !important;
}

:deep(.el-dialog) {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 4px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  min-height: 600px;
}

:deep(.el-dialog__title) {
  color: #00ffff !important;
  font-size: 18px;
  font-weight: bold;
}

:deep(.el-dialog__header) {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  background: rgba(0, 48, 102, 0.5);
}

:deep(.el-dialog__body) {
  color: #ffffff !important;
  padding: 30px;
  background: rgba(0, 24, 51, 0.95);
  max-height: calc(84vh - 108px);
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid rgba(0, 255, 255, 0.1);
  background: rgba(0, 48, 102, 0.5);
}

:deep(.el-dialog .el-date-editor) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  border: 1px solid rgba(0, 255, 255, 0.2);
  width: 360px !important;
}

:deep(.el-dialog .el-date-editor .el-range-input) {
  color: #ffffff !important;
  background: transparent;
  font-size: 15px;
}

:deep(.el-dialog .el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-dialog .el-button) {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
}

:deep(.el-dialog .el-button:hover) {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
}

:deep(.el-dialog .el-button--primary) {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
  color: #00ffff;
}

:deep(.el-dialog .el-button--primary:hover) {
  background: rgba(0, 255, 255, 0.3);
  border-color: #00ffff;
}

.generate-form {
  padding: 0;
}

.form-item {
  margin-bottom: 30px;
}

.form-label {
  color: #ffffff;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.form-label::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #00ffff;
  margin-right: 8px;
  border-radius: 2px;
}

.analysis-tree {
  background: rgba(0, 24, 51, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
  max-height: 450px;
  overflow-y: auto;
}

/* 恢复节点基础样式 */
:deep(.analysis-tree .el-tree-node__content) {
  background-color: transparent !important;
  /* 禁用任何默认的轮廓，防止出现浏览器默认的蓝色或白色轮廓 */
  outline: none !important;
  /* 添加过渡效果，使背景色变化更平滑 */
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  /* 恢复被移除的基本样式 */
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  height: 40px;
  padding: 0 8px;
  color: #ffffff;
}

:deep(.analysis-tree .el-tree-node__content:hover) {
  background: rgba(0, 255, 255, 0.1) !important;
}

:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content) {
  background: rgba(0, 24, 51, 0.8) !important;
  color: #00ffff !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
}

:deep(.analysis-tree .el-tree-node.is-focused > .el-tree-node__content) {
  background: rgba(0, 24, 51, 0.8) !important;
  color: #ffffff !important;
}

:deep(.analysis-tree .el-tree-node.is-focused.is-current > .el-tree-node__content) {
  background: rgba(0, 24, 51, 0.8) !important;
  color: #00ffff !important;
}

:deep(.analysis-tree .el-tree-node.is-focused > .el-tree-node__content:hover) {
  background: rgba(0, 24, 51, 0.8) !important;
}

:deep(.analysis-tree .el-tree-node.is-disabled > .el-tree-node__content) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  cursor: not-allowed;
  pointer-events: none;
}

:deep(.analysis-tree .el-tree-node.is-disabled > .el-tree-node__content:hover),
:deep(.analysis-tree .el-tree-node.is-disabled.is-current > .el-tree-node__content),
:deep(.analysis-tree .el-tree-node.is-disabled.is-checked > .el-tree-node__content),
:deep(.analysis-tree .el-tree-node.is-disabled.is-focused > .el-tree-node__content),
:deep(.analysis-tree .el-tree-node.is-disabled > .el-tree-node__content:focus) {
  background-color: rgba(0, 48, 102, 0.3) !important;
  color: rgba(255, 255, 255, 0.5) !important;
}

:deep(.analysis-tree .el-tree-node.is-disabled .el-tree-node__content *) {
  pointer-events: none;
}

:deep(.analysis-tree .el-tree-node.is-disabled .el-checkbox) {
  pointer-events: none;
}

:deep(.analysis-tree .el-tree-node__label) {
  color: inherit;
  font-size: 15px;
  padding-left: 6px;
}

:deep(.analysis-tree .el-tree-node.is-expanded > .el-tree-node__content) {
  background: rgba(0, 255, 255, 0.05);
  font-weight: bold;
}

:deep(.analysis-tree .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #00ffff !important;
  border-color: #00ffff !important;
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  transition: all 0.3s;
}

:deep(.analysis-tree .el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #00ffff !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
}

:deep(.analysis-tree .el-checkbox__inner) {
  background-color: transparent;
  border-color: rgba(0, 255, 255, 0.5);
  border-width: 2px;
  width: 18px;
  height: 18px;
}

:deep(.analysis-tree .el-checkbox__inner::after) {
  border-width: 2px;
  height: 9px;
  left: 6px;
  top: 2px;
  width: 4px;
  border-color: #003366;
}

:deep(.analysis-tree .el-checkbox__inner:hover) {
  border-color: #00ffff;
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.3);
}

:deep(.analysis-tree .el-checkbox__input.is-indeterminate .el-checkbox__inner) {
  background-color: rgba(0, 255, 255, 0.3) !important;
  border-color: #00ffff !important;
}

:deep(.analysis-tree .el-checkbox__input.is-indeterminate .el-checkbox__inner::before) {
  background-color: #00ffff !important;
}

:deep(.analysis-tree .el-tree-node.is-checked > .el-tree-node__content) {
  background: rgba(0, 24, 51, 0.6) !important;
}

:deep(.analysis-tree .el-tree-node.is-checked.is-current > .el-tree-node__content) {
  background: rgba(0, 24, 51, 0.8) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
}

:deep(.analysis-tree .el-tree-node__expand-icon) {
  color: #00ffff;
}

:deep(.analysis-tree .el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg);
}

:deep(.analysis-tree .el-tree-node__expand-icon.is-leaf) {
  color: transparent;
}

:deep(.analysis-tree::-webkit-scrollbar) {
  width: 6px;
}

:deep(.analysis-tree::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 3px;
}

:deep(.analysis-tree::-webkit-scrollbar-track) {
  background: rgba(0, 48, 102, 0.3);
}

.preview-dialog {
  min-height: 90vh !important;
  display: flex;
  flex-direction: column;
}

:deep(.preview-dialog .el-dialog) {
  margin-top: 2vh !important;
  margin-bottom: 2vh !important;
  height: 96vh !important;
  width: 70% !important;
  display: flex;
  flex-direction: column;
}

:deep(.preview-dialog .el-dialog__body) {
  flex: 1;
  padding: 0;
  min-height: 0;
  overflow: auto;
}

:deep(.preview-dialog .el-dialog__body::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.preview-dialog .el-dialog__body::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.preview-dialog .el-dialog__body::-webkit-scrollbar-thumb:hover) {
  background: rgba(0, 255, 255, 0.6);
}

.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  background: rgba(0, 48, 102, 0.3);
}

.preview-title {
  font-size: 18px;
  color: #00ffff;
  font-weight: bold;
}

.preview-content {
  flex: 1;
  min-height: 0;
  background: rgb(0 31 66);
  position: relative;
  overflow: auto;
  padding: 20px;
}

.preview-container-inner {
  background: #ffffff;
  color: #000000;
  padding: 40px;
  min-height: 100%;
  box-sizing: border-box;
}

:deep(.markdown-body) {
  background: transparent;
  color: #000000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
}

:deep(.markdown-body h1) {
  color: #000000;
  font-size: 28px;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #eaecef;
}

:deep(.markdown-body h2) {
  color: #000000;
  font-size: 24px;
  margin: 24px 0 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eaecef;
}

:deep(.markdown-body h3) {
  color: #000000;
  font-size: 20px;
  margin: 20px 0 12px;
}

:deep(.markdown-body p) {
  margin: 12px 0;
  line-height: 1.8;
}

:deep(.markdown-body ul),
:deep(.markdown-body ol) {
  padding-left: 24px;
  margin: 12px 0;
}

:deep(.markdown-body li) {
  margin: 8px 0;
}

:deep(.markdown-body code) {
  background: rgba(0, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: Consolas, Monaco, "Andale Mono", monospace;
  color: #00ffff;
}

:deep(.markdown-body pre) {
  background: rgba(0, 48, 102, 0.5);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

:deep(.markdown-body pre code) {
  background: transparent;
  padding: 0;
  color: #ffffff;
}

:deep(.markdown-body blockquote) {
  margin: 16px 0;
  padding: 0 16px;
  color: rgba(255, 255, 255, 0.7);
  border-left: 4px solid rgba(0, 255, 255, 0.3);
}

:deep(.markdown-body table) {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

:deep(.markdown-body th),
:deep(.markdown-body td) {
  border: 1px solid rgba(0, 255, 255, 0.2);
  padding: 8px 12px;
  text-align: left;
}

:deep(.markdown-body th) {
  background: rgba(0, 48, 102, 0.5);
  color: #00ffff;
}

:deep(.markdown-body tr:nth-child(even)) {
  background: rgba(0, 48, 102, 0.3);
}

:deep(.markdown-body tr:hover) {
  background: rgba(0, 255, 255, 0.1);
}

:deep(.markdown-body #policeChart) {
  background: rgba(0, 48, 102, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.developing-tag {
  font-size: 12px;
  color: rgba(0, 255, 255, 0.5);
  background: rgba(0, 48, 102, 0.5);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.generating-container {
  display: flex;
  height: 60vh;
  gap: 20px;
}

.generating-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.generating-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  overflow: hidden;
}

.process-title,
.result-title {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
  color: #00ffff;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 48, 102, 0.5);
}

.process-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: rgba(0, 48, 102, 0.3);
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.4) rgba(0, 48, 102, 0.3);
}

.process-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.process-content::-webkit-scrollbar-track {
  background: rgba(0, 48, 102, 0.3);
  border-radius: 4px;
}

.process-content::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 4px;
}

.process-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.6);
}

.result-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #ffffff;
  color: #000000;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 48, 102, 0.4) #f0f0f0;
}

.result-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.result-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.result-content::-webkit-scrollbar-thumb {
  background: rgba(0, 48, 102, 0.4);
  border-radius: 4px;
}

.result-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 48, 102, 0.6);
}

:deep(.result-content .markdown-body) {
  background: #ffffff;
  color: #000000;
}

:deep(.result-content .markdown-body h1),
:deep(.result-content .markdown-body h2),
:deep(.result-content .markdown-body h3) {
  color: #000000;
}

:deep(.result-content .markdown-body p) {
  color: #000000;
}

:deep(.result-content .markdown-body li) {
  color: #000000;
}

:deep(.generating-dialog .el-dialog__body) {
  padding: 20px;
  height: 60vh;
  overflow: hidden;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-checkbox {
  margin-right: 5px;
}

.node-label {
  margin-left: 3px;
}

.node-label.no-checkbox {
  margin-left: 25px;
  /* 为没有复选框的节点添加左侧间距，保持对齐 */
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #00ffff !important;
  border-color: #00ffff !important;
}

:deep(.el-checkbox__inner) {
  background-color: transparent;
  border-color: rgba(0, 255, 255, 0.5);
}

:deep(.el-checkbox__inner:hover) {
  border-color: #00ffff;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(0, 255, 255, 0.1);
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
  padding: 5px 0;
}

.node-checkbox {
  margin-right: 8px;
  cursor: pointer !important;
}

.node-label {
  margin-left: 3px;
  cursor: pointer;
}

.node-label.no-checkbox {
  margin-left: 25px;
  /* 为没有复选框的节点添加左侧间距，保持对齐 */
}

:deep(.el-checkbox) {
  cursor: pointer !important;
}

:deep(.el-checkbox__inner) {
  cursor: pointer !important;
}

:deep(.el-checkbox__input) {
  cursor: pointer !important;
}

.thinking-details {
  background: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.thinking-summary {
  padding: 12px 15px;
  font-size: 15px;
  color: #00ffff;
  background: rgba(0, 48, 102, 0.5);
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.thinking-summary:hover {
  background: rgba(0, 48, 102, 0.7);
}

.thinking-summary::marker,
.thinking-summary::-webkit-details-marker {
  display: none;
}

.summary-arrow {
  transition: transform 0.3s ease;
}

details[open] .summary-arrow {
  transform: rotate(180deg);
}

.thinking-content {
  padding: 15px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.6;
}

.thinking-content hr {
  border: none;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  margin: 15px 0;
}

.thinking-text {
  white-space: pre-wrap;
}

.thinking-text :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
}

.thinking-text :deep(h1),
.thinking-text :deep(h2),
.thinking-text :deep(h3) {
  color: #00ffff;
  margin: 16px 0 8px;
}

.thinking-text :deep(ul),
.thinking-text :deep(ol) {
  padding-left: 20px;
  margin: 8px 0;
}

.thinking-text :deep(li) {
  margin: 4px 0;
}

.thinking-text :deep(code) {
  background: rgba(0, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  color: #00ffff;
  font-family: Consolas, Monaco, "Andale Mono", monospace;
}

.thinking-text :deep(pre) {
  background: rgba(0, 48, 102, 0.3);
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 12px 0;
}

.thinking-text :deep(pre code) {
  background: transparent;
  padding: 0;
  color: #ffffff;
}

.thinking-text :deep(blockquote) {
  border-left: 3px solid rgba(0, 255, 255, 0.3);
  padding-left: 12px;
  margin: 12px 0;
  color: rgba(255, 255, 255, 0.7);
}

.thinking-text :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
}

.thinking-text :deep(th),
.thinking-text :deep(td) {
  border: 1px solid rgba(0, 255, 255, 0.2);
  padding: 8px;
  text-align: left;
}

.thinking-text :deep(th) {
  background: rgba(0, 48, 102, 0.5);
  color: #00ffff;
}

.thinking-text :deep(tr:nth-child(even)) {
  background: rgba(0, 48, 102, 0.3);
}

.thinking-text :deep(tr:hover) {
  background: rgba(0, 255, 255, 0.1);
}

.thinking-text :deep(think) {
  display: block;
  padding: 10px;
  margin: 8px 0;
  background: rgba(0, 255, 255, 0.05);
  border-left: 3px solid rgba(0, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content .node-label) {
  color: #00ffff !important;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content .custom-tree-node) {
  color: #00ffff !important;
}

/* 确保选中状态下的checkbox也显示正确 */
:deep(.analysis-tree .el-tree-node.is-current .el-checkbox__label) {
  color: #00ffff !important;
}

/* 修复元素获得焦点时的背景色问题 */
:deep(.analysis-tree .el-tree-node__content:focus),
:deep(.analysis-tree .el-tree-node__content:focus-visible),
:deep(.analysis-tree .el-tree-node__content:focus-within) {
  background-color: rgba(0, 24, 51, 0.8) !important;
  color: #00ffff !important;
  outline: none !important;
  box-shadow: 0 0 0 1px rgba(0, 255, 255, 0.3) !important;
}

/* 确保任何情况下都不出现白色背景 */
:deep(.analysis-tree .el-tree-node__content),
:deep(.analysis-tree .el-tree-node__content:hover),
:deep(.analysis-tree .el-tree-node__content:focus),
:deep(.analysis-tree .el-tree-node__content:active) {
  background-color: transparent;
}

:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content),
:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content:focus) {
  background: rgba(0, 24, 51, 0.8) !important;
  color: #00ffff !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  outline: none !important;
}

/* 覆盖可能的浏览器默认样式，防止白色背景出现 */
:deep(.analysis-tree .el-tree-node__content *) {
  outline: none !important;
}

/* 添加一个全局样式，确保整个树组件中没有元素会出现白色背景 */
:deep(.analysis-tree *) {
  background-color: transparent;
}

/* 修复元素获得焦点时的背景色问题 */
:deep(.analysis-tree .el-tree-node__content:focus),
:deep(.analysis-tree .el-tree-node__content:focus-visible),
:deep(.analysis-tree .el-tree-node__content:focus-within) {
  background-color: rgba(0, 24, 51, 0.8) !important;
  color: #00ffff !important;
  outline: none !important;
  box-shadow: 0 0 0 1px rgba(0, 255, 255, 0.3) !important;
}

/* 确保任何情况下都不出现白色背景 */
:deep(.analysis-tree .el-tree-node__content),
:deep(.analysis-tree .el-tree-node__content:hover),
:deep(.analysis-tree .el-tree-node__content:focus),
:deep(.analysis-tree .el-tree-node__content:active) {
  background-color: transparent;
}

:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content),
:deep(.analysis-tree .el-tree-node.is-current > .el-tree-node__content:focus) {
  background: rgba(0, 24, 51, 0.8) !important;
  color: #00ffff !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  outline: none !important;
}

/* 最终防御 - 禁用任何可能导致白色背景的浏览器默认行为 */
:deep(.analysis-tree) * {
  -webkit-tap-highlight-color: transparent !important;
}

/* 特别针对焦点/激活状态的防御 */
:deep(.analysis-tree .el-tree-node__content:-moz-focusring),
:deep(.analysis-tree .el-tree-node__content:focus-visible),
:deep(.analysis-tree .el-tree-node__content:focus-within),
:deep(.analysis-tree .el-tree-node:focus .el-tree-node__content),
:deep(.analysis-tree .el-tree-node__content:-webkit-focus-ring),
:deep(.analysis-tree .el-tree-node.is-focusable .el-tree-node__content) {
  background-color: rgba(0, 24, 51, 0.8) !important;
  outline: none !important;
}
</style>


<style lang="scss" scoped>
// 保持现有的样式不变

// 添加单位选择相关样式
.generate-form {
  .form-item {
    margin-bottom: 20px;
  }

  .form-label {
    margin-bottom: 10px;
    font-weight: bold;
  }

  .el-select {
    width: 100%;
  }
}

// 自定义el-select-dropdown的样式（需要放在非scoped的style中）</style>
