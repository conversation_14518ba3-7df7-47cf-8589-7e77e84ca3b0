import request from '@/utils/request'

// 获取笔录列表
export function getSpyingList(data) {
  return request({
    url: '/api/scene_2/search',
    method: 'post',
    data: data
  })
}

export function syncSpying(data) {
    return request({
      url: '/api/scene_2/sync',
      method: 'post',
      params: data
    })
  }

  export function batchAnalysis(data) {
    return request({
      url: '/api//scene_2/analyze',
      method: 'post',
      data: {
        ajbhes: data
      }
    })
  }

export function exportSpyingList(params) {
  return request({
    url: '/api/scene_2/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}