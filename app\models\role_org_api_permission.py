
from sqlalchemy import Column, Integer, String, BigInteger, ForeignKey
from app.db.database import Base

class RoleOrgApiPermission(Base):
    __tablename__ = "role_org_api_permission"

    id = Column(BigInteger, primary_key=True, index=True, autoincrement=True)
    role_id = Column(Integer, nullable=False, index=True)
    org_code = Column(String(50), nullable=False, index=True)
    api_id = Column(Integer, nullable=False, index=True)