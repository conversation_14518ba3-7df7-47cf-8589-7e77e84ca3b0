<template>
  <div class="case-analyze bigscreen">
    <PageHeader title="重复警情治理" />
    <div class="tabs-section">
      <el-tabs v-model="activeTab" class="common-tabs">
        <el-tab-pane label="周期统计" name="statistics">
          <div class="statistics-container">
            <div class="search-section">
              <el-form
                :inline="true"
                :model="statisticsSearchForm"
                class="common-search search-form"
                label-width="120px"
              >
                <div class="search-left">
                  <el-form-item label="统计周期">
                    <el-select v-model="statisticsSearchForm.period" placeholder="请选择周期">
                      <el-option label="周统计" value="week" />
                      <el-option label="月统计" value="month" />
                      <el-option label="年统计" value="year" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="日期范围" class="dateWidth">
                    <el-date-picker
                      v-model="statisticsSearchForm.dateRange"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :default-time="['00:00:00', '23:59:59']"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="警情分类">
                  <el-select v-model="statisticsSearchForm.caseType" placeholder="全部分类">
                    <el-option label="全部分类" value="" />
                    <el-option label="盗窃" value="theft" />
                    <el-option label="诈骗" value="fraud" />
                  </el-select>
                </el-form-item> -->
                </div>
                <div class="search-right">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-refresh"
                      :loading="loading"
                      @click="handleStatisticsSearch"
                    >
                      更新统计
                    </el-button>
                    <el-button type="primary" icon="el-icon-refresh" @click="handleSync">
                      同步数据
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>

            <div class="statistics-cards" v-loading="loading" element-loading-text="数据加载中...">
              <div class="stat-card" @click="showPersonDetailPanelHandler" style="cursor: pointer">
                <div class="stat-title">一人多次</div>
                <div class="stat-value">{{ statisticsData.cluster_entities_num }}</div>
                <div class="stat-desc">涉及人员数</div>
              </div>
              <div class="stat-card" @click="showEventDetailPanelHandler" style="cursor: pointer">
                <div class="stat-title">一事多发</div>
                <div class="stat-value">{{ statisticsData.cluster_cases_num }}</div>
                <div class="stat-desc">案件数量</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">一地多起</div>
                <div class="stat-value">{{ statisticsData.cluster_locations_num }}</div>
                <div class="stat-desc">地点数量</div>
              </div>
            </div>
            <PersonClusterDetail
              :visible="showPersonDetailPanel && !showEventDetailPanel"
              :startTime="statisticsSearchForm.dateRange && statisticsSearchForm.dateRange[0]"
              :endTime="statisticsSearchForm.dateRange && statisticsSearchForm.dateRange[1]"
              @close="showPersonDetailPanel = false"
            />
            <EventClusterDetail
              :visible="showEventDetailPanel && !showPersonDetailPanel"
              :startTime="statisticsSearchForm.dateRange && statisticsSearchForm.dateRange[0]"
              :endTime="statisticsSearchForm.dateRange && statisticsSearchForm.dateRange[1]"
              @close="showEventDetailPanel = false"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="一人多次" name="person">
          <person-multi-case />
        </el-tab-pane>
        <el-tab-pane label="一事多发" name="event">
          <event-multi-case />
        </el-tab-pane>
        <el-tab-pane label="一地多起" name="multi">
          <location-multi-case />
        </el-tab-pane>
      </el-tabs>
    </div>
    <DateRangeDialog
      :visible.sync="dateRangeDialogVisible"
      @confirm="handleDateRangeConfirm"
      @cancel="handleDateRangeCancel"
      @close="handleDateRangeClose"
    ></DateRangeDialog>
  </div>
</template>

<script>
import PageHeader from "@/components/PageHeader.vue";
import PersonMultiCase from "./components/PersonMultiCase.vue";
import LocationMultiCase from "./components/LocationMultiCase.vue";
import EventMultiCase from "./components/EventMultiCase.vue";
import PersonClusterDetail from "./components/PersonClusterDetail.vue";
import EventClusterDetail from "./components/EventClusterDetail.vue";
import DateRangeDialog from "@/components/DateRangeDialog.vue";
import {
  getClusterEntities,
  getPeriodicStats,
  getPersonClusterEntities,
  syncSceneData,
} from "@/api/caseAnalyze";

export default {
  name: "CaseAnalyze",
  components: {
    PageHeader,
    PersonMultiCase,
    LocationMultiCase,
    EventMultiCase,
    PersonClusterDetail,
    EventClusterDetail,
    DateRangeDialog,
  },
  data() {
    return {
      searchForm: {
        id_card: "",
        name: "",
        phone: "",
        role: "",
        caseType: "",
        location: "",
      },
      activeTab: "statistics",
      statisticsTimeRange: "month",
      statisticsData: {
        cluster_cases_num: 0,
        cluster_entities_num: 0,
        cluster_locations_num: 0,
      },
      statisticsSearchForm: {
        period: "week",
        dateRange: [],
        caseType: "",
      },
      eventList: [],
      showPersonDetailPanel: false,
      showEventDetailPanel: false,
      dateRangeDialogVisible: false,
      loading: false,
    };
  },
  methods: {
    // 格式化日期为 yyyy-MM-dd HH:mm:ss
    formatDateTime(date) {
      if (!date) return "";
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      const hours = String(d.getHours()).padStart(2, "0");
      const minutes = String(d.getMinutes()).padStart(2, "0");
      const seconds = String(d.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 获取一人多事数据
    async fetchPersonList() {
      try {
        const params = {
          ...this.searchForm,
          page: this.page,
          page_size: this.pageSize,
        };

        const response = await getClusterEntities(params);
        if (response.code === 200) {
          console.log(response.data);
          const { items, total } = response.data;
          this.total = total;

          // 按身份证号分组处理数据
          const groupedData = {};
          items.forEach(item => {
            if (!groupedData[item.id_card]) {
              groupedData[item.id_card] = {
                id_card: item.id_card,
                name: item.name,
                cases: [],
              };
            }
            groupedData[item.id_card].cases.push({
              case: item.case,
              police_time: item.police_time,
              role: item.role,
            });
          });

          this.personList = Object.values(groupedData);
        }
      } catch (error) {
        console.error("获取一人多事数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchPersonList();
    },

    handleCurrentChange(val) {
      this.page = val;
      this.fetchPersonList();
    },

    handleSearch() {
      this.page = 1;
      this.fetchPersonList();
    },

    resetSearch() {
      this.searchForm = {
        id_card: "",
        name: "",
        phone: "",
        role: "",
        caseType: "",
        location: "",
      };
      this.page = 1;
      this.fetchPersonList();
    },
    async handleStatisticsSearch() {
      try {
        if (
          !this.statisticsSearchForm.dateRange ||
          this.statisticsSearchForm.dateRange.length !== 2
        ) {
          this.$message.warning("请选择日期范围");
          return;
        }
        this.loading = true;
        const [start_time, end_time] = this.statisticsSearchForm.dateRange;
        const response = await getPeriodicStats({ start_time, end_time });
        if (response.code === 200) {
          this.statisticsData = response.data;
        } else {
          this.$message.error(response.message || "获取统计数据失败");
        }
      } catch (error) {
        console.error("获取统计数据失败:", error);
        this.$message.error("获取统计数据失败");
      } finally {
        this.loading = false;
      }
    },
    handleExportReport() {
      // 这里写导出报告逻辑
    },
    // 设置日期范围
    setDateRangeByPeriod(period) {
      const end = new Date();
      let start = new Date();

      switch (period) {
        case "week":
          start.setDate(end.getDate() - 7);
          break;
        case "month":
          start.setMonth(end.getMonth() - 1);
          break;
        case "year":
          start.setFullYear(end.getFullYear() - 1);
          break;
      }

      // 设置开始时间为当天的开始
      start.setHours(0, 0, 0, 0);
      // 设置结束时间为当天的结束
      end.setHours(23, 59, 59, 999);

      this.statisticsSearchForm.dateRange = [this.formatDateTime(start), this.formatDateTime(end)];
    },
    async showPersonDetailPanelHandler() {
      this.showPersonDetailPanel = true;
      this.showEventDetailPanel = false;
    },
    async showEventDetailPanelHandler() {
      console.log("点击一事多警卡片");
      if (
        !this.statisticsSearchForm.dateRange ||
        this.statisticsSearchForm.dateRange.length !== 2
      ) {
        this.$message.warning("请先选择时间范围");
        return;
      }
      console.log("时间范围:", this.statisticsSearchForm.dateRange);
      this.showEventDetailPanel = true;
      this.showPersonDetailPanel = false;
    },
    // 同步功能
    async handleSync() {
      this.dateRangeDialogVisible = true;
    },
    async handleDateRangeConfirm(dateRange) {
      try {
        this.loading = true;
        const params = {
          start_date: dateRange[0],
          end_date: dateRange[1],
        };
        const res = await syncSceneData(params);
        if (res.code === 200) {
          this.$message.success("数据同步成功");
          await this.handleStatisticsSearch();
        } else {
          throw new Error(res.message || "数据同步失败");
        }
      } catch (error) {
        console.error("数据同步失败:", error);
        this.$message.error(error.message || "数据同步失败");
      } finally {
        this.loading = false;
        this.dateRangeDialogVisible = false;
      }
    },
    handleDateRangeCancel() {
      this.dateRangeDialogVisible = false;
    },
    handleDateRangeClose() {
      this.dateRangeDialogVisible = false;
    },
  },
  watch: {
    "statisticsSearchForm.period": {
      handler(newPeriod) {
        this.setDateRangeByPeriod(newPeriod);
      },
      immediate: true,
    },
    "statisticsSearchForm.dateRange": {
      handler() {
        if (this.activeTab === "person") {
          // this.fetchPersonList();
        }
      },
    },
    showEventDetailPanel: {
      handler(val) {
        console.log("showEventDetailPanel changed:", val);
      },
    },
  },
  created() {
    this.handleStatisticsSearch();
  },
};
</script>

<style lang="less" scoped>
.dateWidth {
  width: 600px !important;
}
.case-analyze {
  .person-detail-panel {
    margin-top: 24px;
    border: none;
    color: rgb(255, 255, 255, 0.8);
    background-color: transparent;
    :deep(tr th) {
      background-color: transparent !important;
    }
  }
  .person-detail-scroll {
    // max-height: 420px;
    overflow-y: auto;
    padding-right: 8px;
  }
  :deep(.el-pagination .el-input__inner, .btn-prev, .btn-next),
  :deep(.btn-prev),
  :deep(.btn-next) {
    background-color: transparent;
    color: rgb(255, 255, 255, 0.8);
    border: 1px solid rgb(255, 255, 255, 0.8);
  }
  :deep(.el-pagination) {
    color: rgb(255, 255, 255, 0.8) !important;
  }
  min-height: 100vh;
  color: #fff;
  .search-section {
    padding: 24px 32px 8px 32px;
    border-radius: 8px;
    margin-bottom: 18px;
    .el-form-item {
      margin-right: 24px;
      .el-input,
      .el-select {
        color: #fff;
      }
    }
  }
  .tabs-section {
    // background: #18233a;
    border-radius: 8px;
    padding: 16px 24px;
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    flex: 1;

    :deep(.el-tabs) {
      height: 100%;
    }
    :deep(.el-tabs__content) {
      flex: 1;
      overflow: hidden;
      position: relative;
      height: 100%;
    }

    :deep(.el-tab-pane) {
      height: 100%;
      overflow-y: auto;
      padding-right: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 3px;
      }
    }

    .el-tabs__item {
      color: #bfc8e2;
    }
    .el-tabs__item.is-active {
      color: #fff;
      font-weight: bold;
    }
    .case-card {
      background: #22304a;
      border-radius: 8px;
      margin-bottom: 24px;
      padding: 18px 24px 12px 24px;
      .card-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.08);
        }

        .card-title {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          flex: 1;
          min-width: 300px;
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: "";
            display: inline-block;
            width: 4px;
            height: 16px;
            background: #409eff;
            border-radius: 2px;
          }
        }

        .card-id {
          color: #bfc8e2;
          font-size: 14px;
          padding: 4px 12px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 4px;
        }
      }
      .card-body {
        display: flex;
        gap: 32px;
        .case-table {
          flex: 2;
        }
        .relation-graph {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          .relation-placeholder {
            background: #18233a;
            color: #bfc8e2;
            border-radius: 6px;
            padding: 32px 16px;
            text-align: center;
            font-size: 15px;
            min-width: 180px;
          }
        }
      }
    }
    .empty-tab {
      color: #bfc8e2;
      text-align: center;
      padding: 48px 0;
    }
  }
  .sub-total {
    color: #bfc8e2;
    font-size: 14px;
    margin-bottom: 20px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    display: inline-block;
  }

  .statistics-container {
    padding: 24px;

    .statistics-header {
      margin-bottom: 32px;

      .time-filter {
        .el-radio-group {
          background: #22304a;
          padding: 4px;
          border-radius: 6px;

          .el-radio-button__inner {
            background: transparent;
            border: none;
            color: #bfc8e2;

            &:hover {
              color: #fff;
            }
          }

          .el-radio-button__original-radio:checked + .el-radio-button__inner {
            background: #409eff;
            color: #fff;
            box-shadow: none;
          }
        }
      }
    }

    .statistics-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;

      .stat-card {
        background: #0f2336;
        border-radius: 8px;
        padding: 24px;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .stat-title {
          color: #bfc8e2;
          font-size: 16px;
          margin-bottom: 16px;
        }

        .stat-value {
          color: #fff;
          font-size: 36px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        .stat-desc {
          color: #bfc8e2;
          font-size: 14px;
        }
      }
    }
  }
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding: 0 20px;
}

:deep(.el-loading-mask) {
  background-color: rgba(0, 0, 0, 0.7);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: #fff;
    margin: 3px 0;
    font-size: 14px;
  }
  .circular {
    height: 42px;
    width: 42px;
  }
}
</style>
