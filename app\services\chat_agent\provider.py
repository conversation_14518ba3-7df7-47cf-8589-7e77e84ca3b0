"""
智能体服务提供者
"""
from fastapi import Depends
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.services.chat_agent.service import AgentService
from app.services.chat_agent.service_factory import AgentServiceFactory
from app.services.dify.provider import get_dify_service
from app.services.dify.service import DifyService


# 这里假设已经有了这些服务的提供者
# 如果没有，需要创建相应的服务和提供者
class KnowledgeBaseService:
    """知识库服务"""
    
    def __init__(self, db: Session, dify_service: DifyService):
        self.db = db
        self.dify_service = dify_service
    
    async def create_handler_for_agent(self, agent_config):
        """为智能体创建知识库处理器"""
        from app.services.chat_agent.handlers.factory import KnowledgeBaseHandlerFactory
        return await KnowledgeBaseHandlerFactory.create_handler(self.db, self.dify_service, agent_config)


class QueryProcessingService:
    """查询处理服务"""
    
    def __init__(self, dify_service: DifyService):
        self.dify_service = dify_service
    
    async def create_processor_for_agent(self, agent_config):
        """为智能体创建查询处理器"""
        from app.services.chat_agent.handlers.factory import QueryProcessingHandlerFactory
        return await QueryProcessingHandlerFactory.create_handler(self.dify_service, agent_config)


class SessionService:
    """会话服务"""
    
    def __init__(self, db: Session, dify_service: DifyService):
        self.db = db
        self.dify_service = dify_service

    async def create_handler_for_agent(self, agent_config):
        """为智能体创建会话管理器"""
        from app.services.chat_agent.handlers.factory import SessionHandlerFactory
        return await SessionHandlerFactory.create_handler(self.db, self.dify_service ,agent_config)


def get_knowledge_base_service(
        db: Session = Depends(get_db)
) -> KnowledgeBaseService:
    """获取知识库服务"""
    new_dify_service = get_dify_service()
    return KnowledgeBaseService(db, new_dify_service)


def get_query_processing_service(
) -> QueryProcessingService:
    """获取查询处理服务"""
    new_dify_service = get_dify_service()
    return QueryProcessingService(new_dify_service)


def get_session_service(
        db: Session = Depends(get_db)
) -> SessionService:
    """获取会话服务"""
    new_dify_service = get_dify_service()
    return SessionService(db, new_dify_service)


def get_agent_service(
    db: Session = Depends(get_db),
    kb_service: KnowledgeBaseService = Depends(get_knowledge_base_service),
    query_service: QueryProcessingService = Depends(get_query_processing_service),
    session_service: SessionService = Depends(get_session_service)
) -> AgentService:
    """获取智能体服务"""
    return AgentServiceFactory.create_service(db, kb_service, query_service, session_service)
