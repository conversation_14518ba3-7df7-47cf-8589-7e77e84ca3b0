import contextvars
from typing import Optional, Dict, Any, List

from app.log.log_utils import LogUtils
from app.models.user import User


class UserContext:
    current_user = contextvars.ContextVar('current_user', default=None)  # 定义ContextVar变量
    current_user_info_expand = contextvars.ContextVar('user_info_expand', default=None)  # 定义ContextVar变量  获取用户所有信息

    @staticmethod
    def get_current() -> Optional[User]:
        """获取当前用户"""
        return UserContext.current_user.get()

    @staticmethod
    def get_current_id() -> Optional[str]:
        """获取当前用户"""
        user:User|None = UserContext.current_user.get()
        return None if user is None else user.id

    @staticmethod
    def get_current_user_expand() -> Optional[Dict[str, Any]] :
        return UserContext.current_user_info_expand.get();

    @staticmethod
    def get_current_user_org() -> Optional[Dict[str, Any]]:
        """获取当前用户的第一个组织信息"""
        user_info = UserContext.current_user_info_expand.get()
        if not user_info:
            return None
        org_list = user_info.get("userOrg", [])
        return org_list[0] if org_list else None

    @staticmethod
    def get_current_user_role() -> Optional[Dict[str, Any]]:
        """获取当前用户角色信息"""
        try:
            # 获取当前用户ID
            user = UserContext.current_user.get()
            if not user:
                return None

            # 获取用户扩展信息
            user_info = UserContext.current_user_info_expand.get()
            if not user_info:
                return None

            # 获取用户角色列表
            user_roles = user_info.get("userRole", [])

            # 组装返回数据
            return {
                "id": user.id,
                "name":user.username,
                "user_roles": user_roles
            }

        except Exception as e:
            LogUtils.error(f"获取用户角色信息失败: {str(e)}")
            return None
