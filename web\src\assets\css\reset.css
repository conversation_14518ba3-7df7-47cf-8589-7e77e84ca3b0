/* html,
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
p,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
input,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
textarea,
article,
aside,
audio,
canvas,
figure,
footer,
header,
mark,
menu,
nav,
section,
time,
video {
    margin: 0;
    padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
    font-weight: normal
}

article,
aside,
dialog,
figure,
footer,
header,
hgroup,
nav,
section,
blockquote {
    display: block;
}

ul,
ol {
    list-style: none;
}

img {
    border: 0 none;
    vertical-align: top;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

strong,
em,
i {
    font-style: normal;
    font-weight: normal;
}

ins {
    text-decoration: underline;
}

del {
    text-decoration: line-through;
}

mark {
    background: none;
}

input::-ms-clear {
    display: none !important;
}

body {
    font: 12px/1.5 \5FAE\8F6F\96C5\9ED1, \5B8B\4F53, "Hiragino Sans GB", STHeiti, "WenQuanYi Micro Hei", "Droid Sans Fallback", SimSun, sans-serif;
    background: #fff;
}

a {
    text-decoration: none;
    color: #333;
}

a:hover {
    text-decoration: none;
}

body,
html,
.main {
    width: 100%;
    height: 100%;
    font-size: 12px;
} 

.el-scrollbar__wrap {
    margin-bottom: 0px !important;
}

.el-table__fixed-right::before, .el-table__fixed::before {
    background-color: transparent;
} */

*{
    margin: 0;
    padding: 0;
  }