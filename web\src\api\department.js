import request from "@/utils/request";

// 部门树形数据
export function getDepartment() {
  return request({
    url: "/api/org/tree",
    method: "get",
  });
}

// 添加机构
export function addDepartment(params) {
  return request({
    url: "/api/org/addOrg",
    method: "post",
    data: params,
  });
}
// 修改机构
export function updateDepartment(params) {
  return request({
    url: "/api/org/updateOrg",
    method: "put",
    data: params,
  });
}

// 删除机构
export function deleteDepartment(id) {
  return request({
    url: `/api/org/delete/${id}`,
    method: "delete",
  });
}

// 查询组织详情
export function getDepartmentDetail(id) {
  return request({
    url: `/api/org/byid/${id}`,
    method: "get",
  });
}

// 获取角色列表
export function getRole(params) {
  return request({
    url: "/api/role/list",
    method: "post",
    data: params,
  });
}

// 获取用户列表
export function getUserList(params) {
  return request({
    url: "/api/user/backend/list",
    method: "post",
    data: params,
  });
}

// 用户新增
export function addUser(params) {
  return request({
    url: "/api/user/backend/register",
    method: "post",
    data: params,
  });
}

// 用户修改
export function updateUser(params) {
  return request({
    url: "/api/user/backend/update_user",
    method: "post",
    data: params,
  });
}

// 用户删除
export function deleteUser(id) {
  return request({
    url: `/api/user/backend/delete_user/${id}`,
    method: "GET",
  });
}

// 根据id获取用户信息
export function getUserById(id) {
  return request({
    url: `/api/user/backend/byId/${id}`,
    method: "GET",
  });
}

// 重置密码
export function resetUserPwd(data) {
  return request({
    url: `/api/user/backend/resetPwd`,
    method: "POST",
    data,
  });
}
