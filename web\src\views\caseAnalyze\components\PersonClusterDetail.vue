<template>
  <el-card v-if="visible" class="person-detail-panel">
    <div
      class="panel-header"
      style="display: flex; align-items: center; justify-content: space-between"
    >
      <span style="font-weight: bold">一人多次详情</span>
      <el-button type="text" icon="el-icon-close" @click="$emit('close')" />
    </div>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else class="person-detail-scroll common-scroll">
      <div v-for="item in personList" :key="item.id_card" class="case-card">
        <div class="card-header">
          <span class="card-title">{{ item.name }} 涉案{{ item.cases.length }}起</span>
          <span class="card-id">身份证: {{ item.id_card }}</span>
        </div>
        <div class="card-body">
          <el-table :data="item.cases" style="width: 100%" size="small">
            <el-table-column prop="case" label="处警编号" width="280" />
            <el-table-column prop="case_type" label="类别" width="180" />
            <el-table-column prop="location" label="案件地点" />
            <el-table-column prop="police_time" label="报警时间" width="200">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.police_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="role" label="角色类型" />
          </el-table>
        </div>
      </div>
    </div>
    <el-pagination
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      style="margin-top: 8px; text-align: right"
    />
  </el-card>
</template>

<script>
import { getPersonClusterEntities } from "@/api/caseAnalyze";

export default {
  name: "PersonClusterDetail",
  props: {
    visible: Boolean,
    startTime: String,
    endTime: String,
  },
  data() {
    return {
      personList: [],
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.page = 1;
        this.fetchList();
      }
    },
    startTime() {
      if (this.visible) {
        this.page = 1;
        this.fetchList();
      }
    },
    endTime() {
      if (this.visible) {
        this.page = 1;
        this.fetchList();
      }
    },
  },
  methods: {
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "";
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    async fetchList() {
      if (!this.startTime || !this.endTime) return;
      this.loading = true;
      const res = await getPersonClusterEntities({
        start_time: this.startTime,
        end_time: this.endTime,
        page: this.page,
        page_size: this.pageSize,
      });
      if (res.code === 200) {
        this.personList = res.data.items;
        this.total = res.data.total;
      }
      this.loading = false;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.page = 1;
      this.fetchList();
    },
    handleCurrentChange(val) {
      this.page = val;
      this.fetchList();
    },
  },
};
</script>

<style lang="less" scoped>
.person-detail-panel {
  margin-top: 24px;
  border: none;
  color: rgb(255, 255, 255, 0.8);
  background-color: transparent;
  :deep(tr th) {
    background-color: transparent !important;
  }
}
.person-detail-scroll {
  // max-height: 420px;
  overflow-y: auto;
  padding-right: 8px;
}

.case-card {
  background: #22304a;
  border-radius: 8px;
  margin-bottom: 24px;
  padding: 18px 24px 12px 24px;
  .card-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    &:hover {
      background: rgba(255, 255, 255, 0.08);
    }
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #fff;
      flex: 1;
      min-width: 300px;
      display: flex;
      align-items: center;
      gap: 8px;
      &::before {
        content: "";
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #409eff;
        border-radius: 2px;
      }
    }
    .card-id {
      color: #bfc8e2;
      font-size: 14px;
      padding: 4px 12px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
    }
  }
  .card-body {
    // 可根据需要添加额外样式
  }
}
</style>
