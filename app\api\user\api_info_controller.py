from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.permission_check import require_permissions
from app.db.database import get_db
from app.schemas.api_info_schema import ApiInfoCreateRequest, ApiInfoUpdateRequest, ApiInfoQueryRequest
from app.services.user.api_info_service import ApiInfoService

router = APIRouter(prefix="/api-info", tags=["api-info"])


@router.post("/list")
@require_permissions(permission_key="apiInfo:list")
def get_api_infos(request:ApiInfoQueryRequest, db: Session = Depends(get_db)):
    try:
        """获取API信息列表"""
        api_info_service = ApiInfoService(db)
        data = api_info_service.get_api_infos(request)
        return create_response(code=200, data=data)
    except Exception as e:
        return create_response(
            message=f"异常: {str(e)}",
            code=500
        )



@router.get("/byid/{api_id}")
@require_permissions(permission_key="apiInfo:byId")
def get_api_info(api_id: int, db: Session = Depends(get_db)):
        """获取API信息详情"""
        api_info_service = ApiInfoService(db)
        return api_info_service.get_api_info(api_id)




@router.post("/save")
@require_permissions(permission_key="apiInfo:save")
def create_api_info(request: ApiInfoCreateRequest, db: Session = Depends(get_db)):
        """创建API信息"""
        api_info_service = ApiInfoService(db)
        return api_info_service.create_api_info(request)



@router.put("/update")
@require_permissions(permission_key="apiInfo:update")
def update_api_info(api_info: ApiInfoUpdateRequest, db: Session = Depends(get_db)):
        """更新API信息"""
        api_info_service = ApiInfoService(db)
        return api_info_service.update_api_info(api_info)



@router.delete("/delete/{api_id}")
@require_permissions(permission_key="apiInfo:delete")
def delete_api_info(api_id: int, db: Session = Depends(get_db)):
        """删除API信息"""
        api_info_service = ApiInfoService(db)
        return api_info_service.delete_api_info(api_id)

