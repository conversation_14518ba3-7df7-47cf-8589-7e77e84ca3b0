"""
聊天智能体基类实现
"""
import json
from abc import ABC, abstractmethod
from typing import Dict, Any

from sqlalchemy.orm import Session

from app.log.log_utils import LogUtils
from app.models.chat import ChatAgentsMsg
from app.services.chat_agent.actions.base import ActionHandler
from app.services.chat_agent.base import AgentOperation
from app.services.chat_agent.handlers.interfaces import (
    KnowledgeBaseHandler,
    QueryProcessingHandler,
    SessionHandler
)


class BaseAgentTemplate(ABC):
    """智能体基类，实现模板方法模式"""

    def __init__(self,
                 db: Session,
                 agent_config: Dict[str, Any],
                 kb_handler: KnowledgeBaseHandler,
                 query_processor: QueryProcessing<PERSON>and<PERSON>,
                 session_handler: SessionHandler):
        self.db = db
        self.agent_config = agent_config
        self.agent_id = agent_config.get("agent_id", "")
        self.kb_handler = kb_handler
        self.query_processor = query_processor
        self.session_handler = session_handler
        self.action_chains = self._build_action_chains()

    @abstractmethod
    def _build_action_chains(self) -> Dict[AgentOperation, ActionHandler]:
        """
        构建动作链
        子类必须实现此方法，为每种操作类型定义对应的动作链
        
        Returns:
            操作类型到动作链头部的映射
        """
        pass

    async def _execute_action_chain(self,
                                    operation: AgentOperation,
                                    context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行动作链
        
        Args:
            operation: 操作类型
            context: 上下文信息
            
        Returns:
            处理结果
        """
        if operation not in self.action_chains:
            error_msg = f"不支持的操作类型: {operation}"
            LogUtils.error(error_msg)
            raise ValueError(error_msg)

        chain_head = self.action_chains[operation]
        return await chain_head.handle(context)

    async def process_chat_request(self,
                                   user_input: Dict[str, Any],
                                   user_context: Dict[str, Any],
                                   conversation_id: str) -> Dict[str, Any]:
        """
        处理聊天请求
        
        Args:
            user_input: 用户输入，可能包含附件+文本
            user_context: 用户上下文
            conversation_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            LogUtils.info(f"处理聊天请求: conversation_id={conversation_id}, agent_id={self.agent_id}")

            # 准备上下文
            context = {
                "query": user_input,
                "user_profile": user_context,
                "conversation_id": conversation_id,
                "agent_id": self.agent_id,
                "processing_time": 0
            }

            # 执行动作链
            result = await self._execute_action_chain(AgentOperation.PROCESS_CHAT, context)

            # 格式化响应
            response = self._format_chat_response(result)

            return response
        except Exception as e:
            LogUtils.error(f"处理聊天请求失败: {str(e)}")
            return {
                "success": False,
                "content": f"处理失败: {str(e)}",
                "metadata": {}
            }

    async def handle_document_upload(self,
                                     document_data: Dict[str, Any],
                                     user_context: Dict[str, Any],
                                     ) -> Dict[str, Any]:
        """
        处理文档上传
        
        Args:
            document_data: 文档数据
            user_context: 用户上下文
            
        Returns:
            处理结果
        """
        try:
            LogUtils.info(f"处理文档上传: agent_id={self.agent_id}")
            # 处理重复文件上传
            try:
                segment_id,existing_doc = await self.kb_handler.check_duplicate_upload(document_data, user_context,self.agent_id)
            except Exception as e:
                return {
                    "success": False,
                    "message": f"重复文件上传校验失败: {str(e)}",
                }
            # 若无知识库，则先创建知识库
            dataset_id, document_id, local_dataset = await self.kb_handler.get_or_create_dataset(
                agent_id=self.agent_id,
                user=user_context
            )

            # 知识库信息添加到上下文
            # 准备上下文
            context = {
                "document_data": document_data,
                "user_profile": user_context,
                "local_dataset": local_dataset,
                "agent_id": self.agent_id,
                "processing_time": 0,
                "existing_doc":  existing_doc
            }

            # 执行动作链
            result = await self._execute_action_chain(AgentOperation.HANDLE_DOCUMENT_UPLOAD, context)
            local_dataset = await self.kb_handler.push_document_to_dataset(
                dataset_id=dataset_id,
                document_id=document_id,
                context=result,
                user=user_context
            )

            segment_id = await self.kb_handler.push_segment_to_document(
                local_dataset.dataset_id,
                local_dataset.document_id,
                result,
                segment_id,
            )

            result.update({
                "success": True,
                "agent_type": self.agent_config.get("type"),
                "document_id": document_id,
                "segment_id": segment_id,
                "dataset_id": dataset_id,
                "local_dataset": local_dataset
            })

            return result
        except Exception as e:
            LogUtils.error(f"处理文档上传失败: {str(e)}")
            return {
                "success": False,
                "message": f"上传处理失败: {str(e)}",
                "data": {}
            }

    def _format_chat_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化聊天响应
        子类可以重写此方法以自定义响应格式
        
        Args:
            result: 动作链执行结果
            
        Returns:
            格式化的响应
        """
        if "error" in result:
            return {
                "success": False,
                "content": f"处理失败: {result['error']}",
                "metadata": {}
            }

        return {
            "success": True,
            "content": result.get("generated_answer", {}).get("text", "无法生成回答"),
            "metadata": {
                "sources": result.get("retrieved_documents", ""),
                "processing_time": result.get("processing_time", 0)
            }
        }

    def _format_upload_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化上传响应
        子类可以重写此方法以自定义响应格式
        
        Args:
            result: 动作链执行结果
            
        Returns:
            格式化的响应
        """
        if "error" in result:
            return {
                "success": False,
                "message": f"上传处理失败: {result['error']}",
                "data": {}
            }

        return {
            "success": True,
            "message": "文档处理成功",
            "data": {
                "document_id": result.get("document_id", ""),
                "features": result.get("document_features", {})
            }
        }

    async def start_new_session(self, user_id: int) -> str:
        """
        创建会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话ID
        """
        return await self.session_handler.start_new_session(user_id, self.agent_id)

    async def get_chat_history(self, conversation_id: str, user_id: int, first_id: str = None, limit: int = 20):
        """
        获取会话历史
        
        Args:
            conversation_id: 会话ID
            user_id: 用户ID
            limit: 限制数量
            
        Returns:
            会话历史
        """
        return await self.session_handler.get_recent_history(conversation_id, user_id, first_id, limit)

    async def user_chat_session(self, user_id: int):
        """
        获取用户会话列表
        
        Args:
            user_id: 用户ID

        Returns:
            会话列表
        """
        agent_session = self._find_agent_session(user_id)
        return None if agent_session is None else agent_session.msg_session_id

    async def list_datasets(self, user_context: Dict[str, Any], page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取与此Agent关联的知识库列表
        
        Args:
            user_context: 用户上下文
            is_personal: 是否只返回个人知识库
            limit: 返回数量限制
            
        Returns:
            知识库列表及相关信息
        """
        try:
            #
            # 将agent_type添加到user_context
            context = user_context.copy()
            context["agent_type"] = self.agent_config.get("type")

            # 直接调用knowledge_base_handler获取知识库列表
            datasets_result = await self.kb_handler.list_datasets(
                agent_id=self.agent_id,
                user_context=context,
                page=page,
                limit=limit
            )

            return {
                "success": True,
                "datasets": datasets_result.get("datasets", []),
                "has_more": datasets_result.get("has_more", False),
                "message": None
            }
        except Exception as e:
            LogUtils.error(f"获取知识库列表失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取知识库列表失败: {str(e)}",
                "datasets": [],
                "has_more": False
            }

    async def list_user_chat_conversations(self, session_id, limit, last_id: str = None):
        """
        获取用户会话列表

        Args:
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        #  中间层，协议转换
        return await self.session_handler.get_conversations(session_id, limit, last_id)

    async def update_conversation_name(self, conversation_id, name, user_id):
        """
        获取用户会话列表

        Args:
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        #  中间层，协议转换
        agent_session = self._find_agent_session(user_id)
        return await self.session_handler.update_conversation_name(conversation_id, name, agent_session.msg_session_id)

    async def handle_file_upload(self,
                                 document_data: Dict[str, Any],
                                 session_id: str):
        """
        获取用户会话列表

        Args:
            session_id: 会话ID
            limit: 限制数量

        Returns:
            会话列表
        """
        return await self.session_handler.file_upload(document_data, session_id)

    async def process_chat_request_stream(self,
                                          user_input: Dict[str, Any],
                                          user_context: Dict[str, Any],
                                          conversation_id: str):
        """
        流式处理聊天请求
        
        Args:
            user_input: 用户输入，可能包含附件+文本
            user_context: 用户上下文
            conversation_id: 会话ID
            
        Yields:
            处理过程中的响应块
        """
        try:
            LogUtils.info(f"流式处理聊天请求: conversation_id={conversation_id}, agent_id={self.agent_id}")

            agent_session = self._find_agent_session(user_context.get("id"))
            # 准备上下文
            context = {
                "query": user_input,
                "user_profile": user_context,
                "conversation_id": conversation_id,
                "agent_id": self.agent_id,
                "session_id": agent_session.msg_session_id,
                "processing_time": 0,
                "streaming": True  # 标记流式处理
            }

            # 执行流式动作链
            async for chunk in self._execute_action_chain_stream(AgentOperation.PROCESS_CHAT, context):
                formatted_chunk = self._format_stream_chunk(chunk)
                yield formatted_chunk

        except Exception as e:
            LogUtils.error(f"流式处理聊天请求失败: {str(e)}")
            yield json.dumps({
                "success": False,
                "content": f"处理失败: {str(e)}",
                "metadata": {}
            })

    async def _execute_action_chain_stream(self,
                                           operation: AgentOperation,
                                           context: Dict[str, Any]):
        """
        执行流式动作链
        
        Args:
            operation: 操作类型
            context: 上下文信息
            
        Yields:
            处理过程中产生的数据块
        """
        if operation not in self.action_chains:
            error_msg = f"不支持的操作类型: {operation}"
            LogUtils.error(error_msg)
            yield json.dumps({"error": error_msg})
            return

        chain_head = self.action_chains[operation]
        # 使用支持流式处理的handle_stream方法
        async for chunk in chain_head.handle_stream(context):
            yield chunk

    def _format_stream_chunk(self, chunk: Dict[str, Any]) -> str:
        """
        格式化流式块响应
        
        Args:
            chunk: 响应块数据
            "event": "message",
            "conversation_id": "f4ca48e4-743a-4dcd-b56a-dcca1e392daf",
            "message_id": "3ab8cff5-7d54-436b-909f-44e67cec3b2d",
            "created_at": 1747882941,
            "answer": "！",
            
        Returns:
            格式化后的响应字符串
        """
        # 处理各种可能的块类型
        if "text_chunk" in chunk:
            return json.dumps({
                "type": "text",
                "content": chunk["text_chunk"]
            })
        elif "citation" in chunk:
            return json.dumps({
                "type": "citation",
                "content": chunk["citation"]
            })
        elif "error" in chunk:
            return json.dumps({
                "type": "error",
                "content": chunk["error"]
            })
        elif "end" in chunk:
            # 处理结束信号，可能包含完整元数据
            return json.dumps({
                "type": "end",
                "metadata": chunk.get("metadata", {})
            })
        else:
            # 其他类型的块原样传递
            return json.dumps(chunk)

    def _find_agent_session(self, user_id):
        return self.db.query(ChatAgentsMsg).filter(
            ChatAgentsMsg.agent_id == self.agent_id,
            ChatAgentsMsg.user_id == user_id
        ).first()
