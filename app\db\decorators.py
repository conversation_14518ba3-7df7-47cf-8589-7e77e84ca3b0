from contextlib import contextmanager
from functools import wraps
from typing import Callable

from sqlalchemy.orm import Session

from .database import SessionLocal
from ..log.log_utils import LogUtils


@contextmanager
def get_db_session():
    """
    数据库会话上下文管理器

    用法示例:
    ```python
    with get_db_session() as db:
        # 数据库操作
        db.query(Model).all()
    ```
    """
    db = SessionLocal()
    try:
        yield db
        if db.is_active:  # 检查会话状态
            db.commit()
    except Exception as e:
        if db.is_active:  # 检查是否需要回滚
            db.rollback()
        raise e
    finally:
        if db.is_active:  # 二次确认
            db.close()


def transactional(func: Callable) -> Callable:
    """
    数据库事务装饰器

    用法示例:
    ```python
    @transactional
    async def my_db_function(db: Session):  # db参数可选
        # 数据库操作
        pass
    ```

    装饰器会自动处理:
    1. 事务提交
    2. 错误回滚
    3. 会话关闭
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 检查args中是否已有SessionLocal类型的参数
        db_in_args = False
        for arg in args:
            if isinstance(arg, Session):
                db_in_args = True
                break
        if db_in_args:
            # args中已有SessionLocal类型参数，直接执行
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                LogUtils.error(f"Function execution failed: {str(e)}")
                raise
        else:
            # 创建新的db会话
            with get_db_session() as db:
                try:
                    # 将db添加到kwargs中
                    kwargs['db'] = db
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    LogUtils.error(f"Function execution failed: {str(e)}")
                    raise

    return wrapper
