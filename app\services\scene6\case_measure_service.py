from datetime import datetime, timedelta

from sqlalchemy import tuple_
from sqlalchemy.orm import Session

from app.models.SacwRecords import SacwDjrkjl, SacwClck, SacwBgyq
from app.models.scene_6_po import scene_6_record


def match_finance(db: Session, case_number: str = None):
    query = db.query(scene_6_record)
    if case_number:
        query = query.filter(scene_6_record.case_number == case_number)
    results = query.all()

    for result in results:
        result.finance_status = '未入库'
        result.money_status = '未入库'
        existed_SacwDjrkjl = db.query(SacwDjrkjl).filter(SacwDjrkjl.ajbh == result.case_number,
                                                         SacwDjrkjl.cwmc == result.measure_object_name).first()
        updateFlag = False
        if existed_SacwDjrkjl:
            updateFlag = True
            result.ajbh = existed_SacwDjrkjl.ajbh
            result.ajmc = existed_SacwDjrkjl.ajmc
            result.cwbh = existed_SacwDjrkjl.cwbh
            result.cwmc = existed_SacwDjrkjl.cwmc
            result.cwsl = existed_SacwDjrkjl.cwsl
            result.fh_rksj = existed_SacwDjrkjl.fh_rksj
            result.finance_status = '无问题'
            if result.measure_object_name == '人民币':
                result.money_status = '涉案款未及时处置'

        existedSacwClck = db.query(SacwClck).filter(SacwClck.ajbh == result.case_number,
                                                    SacwClck.cwmc == result.measure_object_name).first()
        if existedSacwClck:
            updateFlag = True
            result.ajbh = existedSacwClck.ajbh
            result.ajmc = existedSacwClck.ajmc
            result.cwbh = existedSacwClck.cwbh
            result.cwmc = existedSacwClck.cwmc
            result.fh_rksj = existedSacwClck.fh_rksj
            result.finance_status = '无问题'
            if result.measure_object_name == '人民币':
                result.money_status = '处理出库'

        existedSacwBgyq = db.query(SacwBgyq).filter(SacwBgyq.ajbh == result.case_number,
                                                    SacwBgyq.cwmc == result.measure_object_name).first()
        if existedSacwBgyq:
            updateFlag = True
            result.ajbh = existedSacwBgyq.ajbh
            result.ajmc = existedSacwBgyq.ajmc
            result.cwbh = existedSacwBgyq.cwbh
            result.cwmc = existedSacwBgyq.cwmc
            result.fh_rksj = existedSacwBgyq.fh_rksj
            result.finance_status = '无问题'
            if result.measure_object_name == '人民币':
                result.money_status = '保管延期'
        if updateFlag:
            db.add(result)
            db.commit()
            db.refresh(result)


def finance_object_analysis(db: Session):
    # 每日通过省警综、十堰市涉案财物管理系统查询前3日全市公安机关扣押涉案财物入库情况,对未入库涉案财物进行预警。
    # 获取昨天的开始和结束时间
    yesterday = datetime.now().date() - timedelta(days=1)
    start_of_yesterday = datetime.combine(yesterday, datetime.min.time())
    end_of_yesterday = datetime.combine(yesterday, datetime.max.time()).replace(microsecond=0)
    # 查询昨日所有批准的案件措施记录
    results = db.query(scene_6_record).filter(
        scene_6_record.measure_approve_date <= end_of_yesterday,
        scene_6_record.measure_approve_date >= start_of_yesterday
    ).all()

    # 3. 收集查询条件
    case_cw_pairs = {(result.case_number, result.measure_object_name) for result in results}

    # 4. 批量查询三个相关表的记录
    # 4.1 查询入库记录
    existing_records_sacwdjrkjl = db.query(SacwDjrkjl).filter(
        tuple_(SacwDjrkjl.ajbh, SacwDjrkjl.cwmc).in_(case_cw_pairs)
    ).all()
    existing_map_sacwdjrkjl = {(record.ajbh, record.cwmc): record for record in existing_records_sacwdjrkjl}

    # 4.2 查询出库记录
    existing_records_sacwclck = db.query(SacwClck).filter(
        tuple_(SacwClck.ajbh, SacwClck.cwmc).in_(case_cw_pairs)
    ).all()
    existing_map_sacwclck = {(record.ajbh, record.cwmc): record for record in existing_records_sacwclck}

    # 4.3 查询保管延期记录
    existing_records_sacwbgyq = db.query(SacwBgyq).filter(
        tuple_(SacwBgyq.ajbh, SacwBgyq.cwmc).in_(case_cw_pairs)
    ).all()
    existing_map_sacwbgyq = {(record.ajbh, record.cwmc): record for record in existing_records_sacwbgyq}

    processed_results = []
    try:
        for result in results:
            # 5. 检查每条记录在三个表中的状态
            sacwdjrkjl_record = existing_map_sacwdjrkjl.get((result.case_number, result.measure_object_name))
            sacwclck_record = existing_map_sacwclck.get((result.case_number, result.measure_object_name))
            sacwbgyq_record = existing_map_sacwbgyq.get((result.case_number, result.measure_object_name))

            # 6. 根据不同情况更新状态
            if sacwdjrkjl_record:
                # 6.1 有入库记录
                fields = ['ajbh', 'ajmc', 'cwbh', 'cwmc', 'cwsl', 'fh_rksj']
                for field in fields:
                    setattr(result, field, getattr(sacwdjrkjl_record, field))
                result.finance_status = '无问题'
                result.finance_analysis_state = 2
            elif sacwclck_record:
                # 6.2 有出库记录
                fields = ['ajbh', 'ajmc', 'cwbh', 'cwmc', 'fh_rksj', 'czsj']
                for field in fields:
                    setattr(result, field, getattr(sacwclck_record, field))
                result.finance_status = '无问题'
                result.finance_analysis_state = 2
            elif sacwbgyq_record:
                # 6.3 有延期记录
                fields = ['ajbh', 'ajmc', 'cwbh', 'cwmc', 'fh_rksj', 'czsj', 'clqx']
                for field in fields:
                    setattr(result, field, getattr(sacwbgyq_record, field))
                result.finance_status = '无问题'
                result.finance_analysis_state = 2
            else:
                # 6.4 三个表都没有记录
                result.finance_status = '未入库'
                result.finance_analysis_state = 2

            db.add(result)
            processed_results.append(result)

        # 7. 提交事务
        db.commit()
        return processed_results
    except Exception as e:
        db.rollback()
        raise e


def finance_money_analysis(db: Session):
    # 每日通过省警综、十堰市涉案财物管理系统查询12个月前扣押涉案款明细，对涉案款未及时处置情况进行预警。
    # 获取去年今天结束时间
    current_date = datetime.now().date()
    try:
        # 计算去年同日，自动处理闰年异常
        last_year_date = current_date.replace(year=current_date.year - 1)
    except ValueError:
        # 处理闰年2月29日特殊情况
        last_year_date = current_date.replace(year=current_date.year - 1, day=28)
    end_of_last_year_date = datetime.combine(last_year_date, datetime.max.time()).replace(microsecond=0)
    # 查询昨日所有批准的案件措施记录
    results = db.query(scene_6_record).filter(
        scene_6_record.measure_approve_date <= end_of_last_year_date,
        scene_6_record.measure_object_name == '人民币'
    ).all()

    # 批量收集查询条件
    case_cw_pairs = {(result.case_number, result.measure_object_name) for result in results}
    # 单次批量查询所有匹配记录
    existing_records_sacwdjrkjl = db.query(SacwDjrkjl).filter(
        tuple_(SacwDjrkjl.ajbh, SacwDjrkjl.cwmc).in_(case_cw_pairs)
    ).all()
    # 批量获取已存在的财物入库记录，建立映射关系用于快速查找
    existing_map_sacwdjrkjl = {(record.ajbh, record.cwmc): record for record in existing_records_sacwdjrkjl}

    # 单次批量查询所有匹配记录
    existing_records_sacwclck = db.query(SacwClck).filter(
        tuple_(SacwClck.ajbh, SacwClck.cwmc).in_(case_cw_pairs)
    ).all()
    # 批量获取已存在的财物出库记录，建立映射关系用于快速查找
    existing_map_sacwclck = {(record.ajbh, record.cwmc): record for record in existing_records_sacwclck}

    # 单次批量查询所有匹配记录
    existing_records_sacwbgyq = db.query(SacwBgyq).filter(
        tuple_(SacwBgyq.ajbh, SacwBgyq.cwmc).in_(case_cw_pairs)
    ).all()
    # 批量获取已存在的财物入库记录，建立映射关系用于快速查找
    existing_map_sacwbgyq = {(record.ajbh, record.cwmc): record for record in existing_records_sacwbgyq}

    processed_results = []
    try:
        for result in results:
            sacwdjrkjl_record = existing_map_sacwdjrkjl.get((result.case_number, result.measure_object_name))
            sacwclck_record = existing_map_sacwclck.get((result.case_number, result.measure_object_name))
            sacwbgyq_record = existing_map_sacwbgyq.get((result.case_number, result.measure_object_name))

            if sacwdjrkjl_record:
                # 动态字段赋值
                fields = ['ajbh', 'ajmc', 'cwbh', 'cwmc', 'cwsl', 'fh_rksj']
                for field in fields:
                    setattr(result, field, getattr(sacwdjrkjl_record, field))
                result.money_status = '涉案款未及时处置'
                result.money_analysis_state = 2
                db.add(result)
                processed_results.append(result)
            elif sacwclck_record:
                # 动态字段赋值
                fields = ['ajbh', 'ajmc', 'cwbh', 'cwmc', 'fh_rksj', 'czsj']
                for field in fields:
                    setattr(result, field, getattr(sacwclck_record, field))
                result.money_status = '处理出库'
                result.money_analysis_state = 2
                db.add(result)
                processed_results.append(result)
            elif sacwbgyq_record:
                # 动态字段赋值
                fields = ['ajbh', 'ajmc', 'cwbh', 'cwmc', 'fh_rksj', 'czsj']
                for field in fields:
                    setattr(result, field, getattr(sacwbgyq_record, field))
                result.money_status = '保管延期'
                result.money_analysis_state = 2
                db.add(result)
                processed_results.append(result)

        # 统一提交事务
        db.commit()
        # 批量刷新处理过的对象
        for result in processed_results:
            db.refresh(result)
        return processed_results
    except Exception as e:
        db.rollback()
        raise e
