from sqlalchemy import Column, Integer, String, Text
from sqlalchemy.ext.declarative import declarative_base

PoliceDataBase = declarative_base()

class PoliceData(PoliceDataBase):
    """
    警务数据表模型
    """
    __tablename__ = 'police_data'

    id = Column(Integer, primary_key=True, autoincrement=True)
    police_station = Column(String(100), nullable=False, comment='警局')
    month = Column(Integer, nullable=False, comment='月份')
    received_alerts = Column(Integer, default=0, comment='接警数')
    handled_alerts = Column(Integer, default=0, comment='处警数')
    case_received_alerts = Column(Integer, default=0, comment='案件类案件接警数')
    case_register_alerts = Column(Integer, default=0, comment='案件类案件立案数')
    # case_type_cases = Column(Text, default='[]', comment='案件类案件')
    # criminal_cases = Column(Text, default='[]', comment='刑事案件')
    # administrative_cases = Column(Text, default='[]', comment='行政案件')
    # coercive_measure_rate = Column(String(50), default='', comment='强制措施转诉率')
    # substantive_issues = Column(Text, default='', comment='执法实体性问题')
    # procedural_issues = Column(Text, default='', comment='执法程序性问题')
    # alert_handling_satisfaction = Column(Text, default='', comment='警情处理满意情况')
    # case_handling_satisfaction = Column(Text, default='', comment='案件处理满意情况')

    def __repr__(self):
        return f"<PoliceData(police_station='{self.police_station}', month={self.month})>" 