from app.config import settings


class DifyDatasetParams:
    def __init__(self):
        super().__init__()

    # 创建空知识库
    @staticmethod
    def create_dataset_default_params(name, description: str = None):
        data = {
            "name": name,
            "permission": "only_me"
        }
        if not description:
            data['description'] = description
        return data

    # 根据文本在已有知识库中创建文档
    @staticmethod
    def create_document_by_text_default_params(
            name='特征文档',  # 文档名称，必填
    ):
        return {
            'name': name,
            'text': '',
            'indexing_technique': 'high_quality',
            'process_rule': {"mode": "custom", "rules": {
                "pre_processing_rules": [{"id": "remove_extra_spaces", "enabled": True}],
                "segmentation": {"separator": "\n", "max_tokens": 4000},
                "parent_mode": "full-doc"}},
            'retrieval_model': {"search_method": "hybrid_search", "reranking_enable": True,
                                "reranking_model": {
                                    "reranking_provider_name": settings.RERANKING_PROVIDER_NAME,
                                    "reranking_model_name": settings.RERANKING_MODEL_NAME}, "top_k": 1,
                                "score_threshold_enabled": False},
            "embedding_model": settings.EMBEDDING_MODEL,
            "embedding_model_provider": settings.EMBEDDING_MODEL_PROVIDER}

    # 在已有知识库的已有文档中创建片段,返回结果中的id为,可以同时创建多个片段
    @staticmethod
    def create_segments_default_params(
            content: str,
    ):
        segments = [{"content": f'{content}'}]

        return {
            "segments": segments
        }

    # 更新已有的分段内容(覆盖)
    @staticmethod
    def update_segment_default_params(
            content,  # 片段内容 不能为空
    ):
        content_update = {"content": content}
        return {
            'segment': content_update
        }

    # 检索知识库
    @staticmethod
    def retrieve_dataset_default_params(
            query):
        return {
            'query': query,
            'retrieval_model': {"search_method": "hybrid_search", "reranking_enable": True,
                                "reranking_model": {
                                    "reranking_provider_name": settings.RERANKING_PROVIDER_NAME,
                                    "reranking_model_name": settings.RERANKING_MODEL_NAME}, "weights": 0.3,
                                "top_k": 1, "score_threshold_enabled": False, "score_threshold": None}
        }
    # 通过文件在已有知识库中创建文档
    @staticmethod
    def create_document_by_file_default_params():
        return {"indexing_technique": "high_quality", "process_rule": {"mode": "custom", "rules": {
            "pre_processing_rules": [{"id": "remove_extra_spaces", "enabled": True}],
            "segmentation": {"separator": "\n", "max_tokens": 4000}, "parent_mode": "full-doc"}},
                "retrieval_model": {"search_method": "hybrid_search", "reranking_enable": True,
                                    "reranking_model": {
                                        "reranking_provider_name": settings.RERANKING_PROVIDER_NAME,
                                        "reranking_model_name": settings.RERANKING_MODEL_NAME}, "top_k": 1,
                                    "score_threshold_enabled": False},
                "embedding_model": settings.EMBEDDING_MODEL,
                "embedding_model_provider": settings.EMBEDDING_MODEL_PROVIDER}
