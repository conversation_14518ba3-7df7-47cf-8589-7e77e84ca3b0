.el-tooltip__popper{
  max-width: 300px;
}
.el-tooltip__popper.is-dark{
  color: rgb(255,255,255,0.7);
  font-size: 12px;
  line-height: 18px;
}
.screen-dialog {
  .el-dialog__header{
    background-image: linear-gradient(270deg, #122F46 0%, #234E71 100%);
  }
  .cancel-btn{
    width: 88px;
    height: 34px;
    font-size: 14px;
    margin: 0 20px 0 0px;
    background-color: #23415D;
    color: rgb(0,255,255,0.8);
    border: transparent;
    padding: 0px;
  }
  .ensure-btn:hover {
    background: rgb(0,255,255,0.8) !important;
    color: #071827 !important;
    border: none;
  }
  .ensure-btn{
    width: 88px;
    height: 34px;
    background: #00FFFF;
    border-radius: 2px;
    padding: 0;
    color: #071827;
    border: none;
  }
  .el-dialog__title{
    color: #00ffff !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
  }
  .el-dialog__body{
    padding: 20px !important;
    background: #112A3F;
  }
  .el-dialog__footer{
    padding: 15px 20px !important;
    background: #112A3F;
  }
  .el-form-item__label{
    color: rgb(255,255,255,0.8);
  }
  .el-input__inner,.vue-treeselect__control{
    height: 34px;
    background-color: #152D43;
    color: rgb(255,255,255,0.8);
    ::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }

  }
  .vue-treeselect__single-value{
    color: rgba(255, 255, 255, 0.5)
  }
  .el-input-number__increase,.el-input-number__decrease{
    background-color: transparent;
    color: white;
  }
}
// 修改drawer的样式
.screen-drawer{
  background: #112A3F;
  .el-drawer__header{
    background-image: linear-gradient(270deg, #122F46 0%, #234E71 100%);
    color: rgb(255,255,255,0.8);
  }
  .cancel-btn{
    width: 88px;
    height: 34px;
    font-size: 14px;
    margin: 0 20px 0 0px;
    background-color: #23415D;
    color: rgb(0,255,255,0.8);
    border: transparent;
    padding: 0px;
  }
  .ensure-btn:hover {
    background: rgb(0,255,255,0.8) !important;
    color: #071827 !important;
    border: none;
  }
  .ensure-btn{
    width: 88px;
    height: 34px;
    background: #00FFFF;
    border-radius: 2px;
    padding: 0;
    color: #071827;
    border: none;
  }
  .el-dialog__title{
    color: #00ffff !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
  }
  .drawer-content{
    padding: 20px !important;
    background: #112A3F;
    .el-checkbox{
      color: rgb(255,255,255,0.8) !important;
    }
  }
  .drawer-footer{
    padding: 15px 20px !important;
    background: #112A3F !important;
    border-radius: 0 0 8px 8px !important;
    border: none !important;
  }
  .vue-treeselect__single-value{
    color: rgba(255, 255, 255, 0.5)
  }
  .el-tree{
    background-color: transparent;
    color: rgb(255,255,255,0.8);
    border: none !important;
    .el-tree-node__content:hover {
      background: #213e5be0 !important;
    }
    .el-tree-node.is-current > .el-tree-node__content {
      background: #213e5be0 !important;
    }
  }

  .el-input__inner{
    background-color: rgba(0, 48, 102, 0.3);
    border: 0.0625rem solid rgba(0, 255, 255, 0.2);
    color: #ffffff;
  }


}
.bigscreen{
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 1.25rem;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
  .content-wrapper{
    display: flex;
    flex: 1;
    flex-direction: column;
    min-height: 0;
    padding: 0;
  }
  .tool-btn{
    width: 110px;
    height: 34px;
    font-size: 14px;
    margin: 0 20px 0 0px;
    background-color: #23415D;
    color: rgb(0,255,255,0.8);
    border: none;
  }

  .el-table .el-table__expand-icon{
    color: #ffffff;
  }
  .el-table .el-table__body-wrapper {
    height: 100%;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 255, 255, 0.4);
      border-radius: 8px;
      width: 8px;
    }

  }
  

  .el-tabs--card>.el-tabs__header .el-tabs__nav {
    border: 1px solid #003066 !important;
  }

  .el-tabs--card>.el-tabs__header {
    border-bottom: 1px solid rgba(0, 255, 255, 0.4) !important;
  }

  .el-table--border::after,
  .el-table--group::after,
  .el-table::before {
    background-color: transparent !important;
  }

  .el-table td.el-table__cell,
  .el-table th.el-table__cell.is-leaf {
    // border-bottom: 1px solid transparent !important;
    background-color: transparent;
  }
  .el-table__header-wrapper tr th{
    border: none !important;
  }

  // 搜索表单
  .el-form-item {
    .el-form-item__content {
      .el-input{
        .el-input__inner {
          border: 0.0625rem solid rgba(0, 255, 255, 0.2);
          color: #ffffff;
          ::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }

      .el-date-editor .el-range-input::placeholder {
        color: rgba(255, 255, 255, 0.8) !important;
      }
    }
  }
  .el-date-editor .el-range-input{
    background-color: transparent;
    color: #ffffff;
  }
  // 公共的滚动条样式
  .common-scroll,.detail-text {
    overflow: auto;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
      border-radius: 8px;
      width: 8px;
    }
  }

  .common-scroll:hover::-webkit-scrollbar-thumb, .detail-text:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 255, 255, 0.4);
  }


  .search-section {
    background: #0F2336;
    border-radius: 4px;
    padding: 12px 20px;
    margin-bottom: 20px;
  }

  .common-action-button-operations {
    background: #0F2336;
    border-radius: 4px;
    padding: 18px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-operations {
      display: flex;
      align-items: center;
    }

    .el-button {
      background: #23415D;
      border-color: #23415D;
      // padding: 0 20px;
      height: 34px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      min-width: 80px;
    }

    .el-button:hover {
      background: #23415Dd3;
    }

    .el-button.is-disabled {
      background: #23415D !important;
      border-color: #23415D;
      color: rgba(0, 255, 255, 0.3) !important;
      cursor: not-allowed;
      box-shadow: none !important;
      transform: none !important;
    }

  }

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    min-height: 0;
    padding-bottom: 10px;
    .check-result-error{
      width: 70px;
      margin: 0 auto;
      background: #ff292926;
      border: 1px solid #FF2929;
      border-radius: 2px;
      color: #FF2929;
      padding: 4px 9px;
    }
    .check-result-success{
      width: 70px;
      margin: 0 auto;
      background: #38a36626;
      border: 1px solid #1E5937;
      border-radius: 2px;
      color: #38A366;
      padding: 4px 9px;
    }

    .el-table__header tr th {
      background-color: #1C3349 !important;
      border: none;
    }

    // .el-table__body tr {
    //   background: #091C2D;
    //   box-shadow: inset 0 -1px 0 0 #1D3449;
    // }

    .el-table__fixed-right {
      right: 0px !important;
    }
  }

  // 公共的筛选条件样式
  .common-search.search-form {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    width: 100%;
    align-items: flex-end;


    .el-date-editor.el-input__inner {
      width: 100% !important;
      align-items: baseline;
    }

    .el-form-item__content {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      margin-left: 0 !important;
      flex: 1;
      align-items: baseline;

      .el-select {
        width: 100% !important;

        .el-input .el-input--suffix {
          width: 100% !important;

          input {
            width: 100%;
          }
        }
      }
    }

    .search-left {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;
      flex: 3;
      transition: all 0.35s linear;
      overflow: visible;

      .el-form-item {
        margin-right: 0;
        margin-bottom: 10px;
        display: flex;
        flex-direction: row;
        margin-right: 20px;
      }

      .el-form-item__label {
        line-height: 34px;
        color: rgb(255,255,255,0.8);
      }

      .el-input__inner {
        height: 34px;
        background-color: #152D43;
        background-color: rgba(0, 48, 102, 0.3);
        border: 0.0625rem solid rgba(0, 255, 255, 0.2);
        color: #ffffff;
      }

      >* {
        width: calc(100% / 3 - (45px / 2));
      }

      > :nth-child(n + 4) {
        opacity: 1;
        transition: all 0.2s linear;
      }

      > :nth-child(3n) {
        margin-right: 0px;
      }

      .el-range-editor.el-input__inner {
        padding: 0px 12px;

        .el-range-separator {
          color: rgba($color: #ffffff, $alpha: 0.7);
        }
      }
      .el-date-editor--daterange{
        border: 0.0625rem solid rgba(0, 255, 255, 0.2);
      }
    }

    .search-right {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-left: 10px;
      flex: 1;
      justify-content: end;
      transform: translateY(-3px);


      .el-form-item {
        margin-right: 0;
        margin-bottom: 10px;
        display: flex;
        flex-direction: row;
      }

      .el-button {
        background: #23415D;
        border-color: #23415D;
        color: #00FFFF;
        // padding: 0 20px;
        height: 34px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        min-width: 80px;
      }

      .el-button:hover {
        background: #23415Dcc;
      }

      .el-button_text {
        height: 32px;
        margin-left: 10px;
        cursor: pointer;
        color: rgb(255,255,255,0.7);
      }

      .el-form-item__content {
        height: 34px;
        >:first-child {
          color: #071827;
          background-color: #00ffff;
          border-color: #00ffff;
          margin-right: 14px;
        }

        >:first-child:hover {
          color: #071827 !important;
          background-color: #00ffffcc !important;
          border-color: #00ffffcc !important;
        }
      }
    }

    .has-height {
      height: 50px !important;
      overflow: hidden;

      > :nth-child(n + 4) {
        opacity: 0;
      }
    }
  }

  // 公共的标签页样式
  .common-tabs {
    >.el-tabs__header .el-tabs__item {
      color: #ffffff;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      transition: all 0.3s;
      font-weight: bolder;
    }

    >.el-tabs__header .el-tabs__item.is-active {
      color: #00ffff;
    }

    >.el-tabs__header .el-tabs__item:hover {
      color: #00ffff;
    }

    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }
  }

  // 公共的表格表头选择框居中
  .el-table__header {
    .el-table-column--selection {
      th.is-center {
        .cell {
          text-align: center;
        }
      }
    }
  }

  :root {
    --custom-button: 10px;
  }

  *+.el-button {
    margin-left: var(--custom-button);
  }

  .el-button+* {
    margin-left: var(--custom-button);
  }
  .table-container{
    background-color: #0F2336;
  }
  .el-table {
    background-color: transparent;
    color: rgb(255,255,255,0.8);
    tr td{
      background-color: transparent !important; 
      border-bottom: 1px solid #1D3449 !important;
      
    }
    tr .el-table__expanded-cell {
      background-color: #091C2D !important;
    }
    tr{
      background-color: transparent !important; 
      .row-operation-btn {
        background: none ;
        border: none;
      }
      .row-operation-btn-color {
        color: #00ffff;
      }
    }
    
    .el-checkbox {
      margin-right: 0;
    }

    .el-checkbox__inner {
      background-color: rgba(0, 48, 102, 0.3);
      border-color: rgba(0, 255, 255, 0.2);
    }

    .el-checkbox__inner:hover {
      border-color: #00ffff;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: rgba(0, 255, 255, 0.2);
      border-color: #00ffff;
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: rgba(0, 255, 255, 0.2);
      border-color: #00ffff;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #00ffff;
    }

    .el-checkbox__input.is-disabled .el-checkbox__inner {
      background-color: rgba(0, 48, 102, 0.1);
      border-color: rgba(0, 255, 255, 0.1);
      cursor: not-allowed;
    }
    
    /* 查看结果按钮 */
    .view-btn {
      color: #00ffff;
      background: transparent;
      border: none;
      padding: 0;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      margin-left: 16px;
    }

    .view-btn:hover {
      color: #ffffff;
      background: transparent;
    }

    .view-btn i {
      font-size: 16px;
    }

  }

  .el-table--enable-row-hover {
    .el-table__body {
      tr:hover{
        >td {
          background: #213e5be0 !important;
          box-shadow: inset 0 -1px 0 0 #213e5be0;
          color: #FFFFFFB3 !important;
          .el-table__expand-icon {
            color: #FFFFFFB3 !important;
          }
        }
      }
    }
  }

  .el-tabs__item+.el-tabs__item {
    padding: 0px 10px;
  }

  .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
    padding-left: 0;
    padding-right: 10px;
  }

  // 公共的密码框尾部图标
  .pass-input__suffix {
    display: inline-block;
    height: 100%;
    padding: 0px 5px;
    cursor: pointer;

    i {
      line-height: 300%;
      font-size: 16px;
    }
  }

  .not_view {
    position: relative;

    &::after {
      display: inline-block;
      content: '';
      position: absolute;
      width: 18px;
      height: 1px;
      background-color: currentColor;
      transform: rotate(-45deg);
      top: 50%;
      left: 50%;
      margin-left: -9px;
      margin-top: -0.5px;
    }
  }

  i.el-input__icon.el-icon-view.el-input__clear {
    display: none !important;
  }

  .el-button--primary,
  .el-button--default {
    
    // color: #00FFFF;
  }

  .el-button--primary:hover,
  .el-button--default:hover {
    background: #23415d79 ;
    border-color: #23415d79;
    color: #00FFFF;
  }

  .submit-button {
    color: #071827;
    background-color: #00ffff;
    border-color: #00ffff;

    &:hover {
      color: #071827 !important;
      background-color: #00ffffcc !important;
      border-color: #00ffffcc !important;
    }
  }

  .submit-button.is-disabled {
    background: #23415D !important;
    border-color: #23415D !important;
    color: rgba(0, 255, 255, 0.3) !important;

    &:hover {
      background: #23415D !important;
      border-color: #23415D !important;
      color: rgba(0, 255, 255, 0.3) !important;
    }
  }

  .el-message-box {
    background-color: #112a3f !important;
    border: 0px !important;

    .el-message-box__header {
      background-image: linear-gradient(270deg, #122F46 0%, #234E71 100%);

      .el-message-box__title {
        color: #FFFFFF;
      }
    }

    .el-message-box__content {
      color: #ffffffa9;
    }
  }
  
  /**table详情的展示*/
  .detail-container {
    background-color: #091C2D;
    border-radius: 4px;
    margin: 10px 20px;
  }

  .detail-header {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    margin-bottom: 20px;
    // padding-bottom: 10px;
    // border-bottom: 1px solid rgba(0, 255, 255, 0.2);

    .detail-title {
      font-size: 16px;
      color: #fff;
      font-weight: bold;
    }

    .detail-number {
      background: #1B354E;
      color: rgba(0, 255, 255, 0.7);
      font-size: 14px;
      margin-left: 8px;
      padding: 3px 8px;
      border-radius: 2px;
    }
  }


  .detail-content {
    display: grid;
    flex-direction: column;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(1, 1fr);
    gap: 12px;

    .detail-section {
      background-color: #0D2235;
      border: 1px solid rgba(0, 255, 255, 0.1);
      border-radius: 4px;
      padding: 15px;

      .detail-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 15px;
        max-height: 250px;
        overflow-y: scroll;

        /* 设置滚动条整体样式 */
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        /* 滚动条轨道 */
        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 3px;
        }

        /* 滚动条滑块 */
        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }

        /* 滚动条两端按钮 */
        &::-webkit-scrollbar-button {
          display: none;
        }

        /* 设置滚动条交叉处样式 */
        &::-webkit-scrollbar-corner {
          background: transparent;
        }
      }

      .detail-scroll {
        max-height: 260px;
        color: rgb(255,255,255,0.8);
      }
    }

    .section-title {
      color: rgb(0,255,255,0.7);
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 15px;
      position: relative;
      padding-left: 12px;
    }

    .section-title::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #00ffff;
      border-radius: 2px;
    }

    
    .detail-item {
      display: flex;
      align-items: center;
      color: rgb(255,255,255,0.8);
    }
    .detail-text {
      color: rgb(255,255,255,0.8);
      font-size: 14px;
      line-height: 1.6;
      text-align: justify;
      max-height: 250px;
      overflow-y: scroll;
    }
    .item-label {
      color: rgba(255, 255, 255, 0.7);
      margin-right: 8px;
      font-size: 14px;
      white-space: nowrap;
    }
    
    .item-value {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
    }
    
  }



}