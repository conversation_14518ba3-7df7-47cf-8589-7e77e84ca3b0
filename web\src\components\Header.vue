<template>
  <div class="header">
    <div class="header-title">政务服务大数据可视化监管平台</div>
    <div class="header-time">{{ currentTime }}</div>
  </div>
</template>

<script>
export default {
  name: 'Header',
  data() {
    return {
      currentTime: ''
    }
  },
  mounted() {
    this.updateTime()
    setInterval(this.updateTime, 1000)
  },
  methods: {
    updateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      this.currentTime = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style scoped>
.header {
  width: 100%;
  height: 80px;
  background: linear-gradient(to right, rgba(0,48,102,0.8), rgba(0,48,102,0.3));
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.header-title {
  font-size: 36px;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0,255,255,0.5);
  letter-spacing: 2px;
}

.header-time {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  font-size: 16px;
}
</style> 