import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data 登录信息
 * @returns {Promise}
 */
export function login(data) {
  return request({
    url: '/api/userlogin',
    method: 'post',
    data
  })
}

export function logoutInfo(token){
  return request({
    url: '/api/logout',
    method: 'post',
    params: {token: token}
  })
}

/**
 * 用户注册
 * @param {Object} data 注册信息
 * @returns {Promise}
 */
export function register(data) {
  return request({
    url: '/api/register',
    method: 'post',
    data
  })
}

export function getOrgByPoliceCode(policeCode) {
  return request({
    url: '/api/getOrgByPoliceCode/'+policeCode,
    method: 'get',
  })
}


/**
 * 重置密码
 * @param {Object} data 重置密码信息
 * @returns {Promise}
 */
export function resetPassword(data) {
  return request({
    url: '/api/changepassword',
    method: 'post',
    data
  })
}
