from typing import List, Optional, Dict, Set


from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.log.log_utils import LogUtils
from app.models.org import Org, UserToOrg, OrgToRole
from app.models.temp_org_info import temp_org_map
from app.schemas.org_schema import OrgCreate, OrgUpdate, UserOrgCreate, OrgRoleCreate
from collections import defaultdict


class OrgService:
    def __init__(self, db: Session):
        self.db = db
        self._org_cache = {}  # 缓存组织信息
        self._org_children_cache = defaultdict(list)  # 缓存组织的子组织

    def _build_org_cache(self):
        """构建组织缓存"""
        if not self._org_cache:
            orgs = self.db.query(Org).all()
            for org in orgs:
                self._org_cache[org.id] = org
                if org.parent_id != -1:
                    self._org_children_cache[org.parent_id].append(org.id)

    def get_org(self, org_id: int) -> Dict:
        # 查询组织基本信息
        org = self.db.query(Org).filter(Org.id == org_id).first()
        if not org:
            return None

        # 查询组织关联的角色ID列表
        role_ids = [
            role[0] for role in self.db.query(OrgToRole.role_id)
            .filter(OrgToRole.org_id == org_id)
            .all()
        ]

        # 组合返回数据
        # 将 org 对象转换为字典并添加 role_ids
        org_dict = {
            "id": org.id,
            "name": org.name,
            "org_code": org.org_code,
            "sort": org.sort,
            "owner_user_id": org.owner_user_id,
            "status": org.status,
            "parent_id": org.parent_id,
            "parent_org_code": org.parent_org_code,
            "can_sub_query": org.can_sub_query,
            "role_ids": role_ids
        }
        return {"org_info": org_dict}

    def get_org_by_code(self, org_code: str) -> Optional[Org]:
        return self.db.query(Org).filter(Org.org_code == org_code).first()

    def get_orgs(self, skip: int = 0, limit: int = 100) -> List[Org]:
        return self.db.query(Org).offset(skip).limit(limit).all()

    def create_org(self, org: OrgCreate) -> Org:
        try:
            # 查询是否是初始化数据
            initialize = self.db.query(Org).count()

            if initialize == 0:
                # 初始化根组织
                org_data = org.dict(exclude={"role_ids"})
                org_data.update({
                    "org_code": "4203",
                    "parent_id": -1,
                    "parent_org_code": "-1",
                    "owner_user_id": UserContext.get_current_id()
                })
            else:
                # 查询父组织
                org_parent_exists = self.db.query(Org).filter(
                    Org.org_code == org.parent_org_code
                ).first()

                if not org_parent_exists:
                    raise Exception("父组织不存在")

                # 计算后缀编码
                child_count = self.db.query(Org).filter(
                    Org.parent_org_code == org_parent_exists.org_code
                ).count()

                # 生成4位后缀编码
                suffix = str(child_count + 1).zfill(4)

                org_data = org.dict(exclude={"role_ids"})
                org_data.update({
                    "org_code": f"{org_parent_exists.org_code}{suffix}",
                    "parent_org_code": org_parent_exists.org_code,
                    "owner_user_id": UserContext.get_current_id(),
                    "parent_id": org_parent_exists.id
                })

            # 创建组织
            db_org = Org(**org_data)
            self.db.add(db_org)
            self.db.commit()
            self.db.refresh(db_org)
            orgId = db_org.id
            roleIds = org.role_ids
            if roleIds:
                org_role_mappings = [
                    OrgRoleCreate(org_code=db_org.org_code, role_id=role_id, org_id=orgId)
                    for role_id in org.role_ids
                ]
                self.batch_role_to_org(org_role_mappings)

            return create_response(code=500, data=db_org)
        except Exception as e:
            self.db.rollback()
            return create_response(code=500,message={str(e)})


    def update_org(self, org_id: int, org: OrgUpdate) -> Optional[Org]:
        db_org = self.db.query(Org).filter(Org.id == org_id).first()
        if db_org:
            for key, value in org.dict(exclude={"role_ids"}).items():
                setattr(db_org, key, value)
            self.db.commit()
            self.db.refresh(db_org)
            # 删除原有角色关联
            self.db.query(OrgToRole).filter(
                OrgToRole.org_id == org_id
            ).delete()

            # 创建新的角色关联
            if org.role_ids:
                org_role_mappings = [
                    OrgRoleCreate(
                        org_id=db_org.id,
                        org_code=db_org.org_code,
                        role_id=role_id
                    )
                    for role_id in org.role_ids
                ]
                self.batch_role_to_org(org_role_mappings)

            self.db.commit()

        return db_org

    def delete_org(self, org_id: int) -> bool:
        try:
            # 检查组织是否存在
            db_org = self.db.query(Org).filter(Org.id == org_id).first()
            if not db_org:
               raise Exception("组织不存在")

            # 检查用户绑定关系
            db_user_to_org = self.db.query(UserToOrg).filter(UserToOrg.org_code == db_org.org_code).first()
            if db_user_to_org:
                raise Exception("该组织存在用户绑定关系，请先解绑后再删除")

            # 先删除组织的角色关联
            self.db.query(OrgToRole).filter(
                OrgToRole.org_id == org_id
            ).delete()

            # 再删除组织本身
            self.db.delete(db_org)
            self.db.commit()
            return create_response(code=200)

        except Exception as he:
            self.db.rollback()
            return create_response(code=500, data=f"{str(he)}")


    def batch_role_to_org(self, org_roles: List[OrgRoleCreate]) -> List[OrgToRole]:
        # 创建 OrgToRole 对象列表
        org_role_objects = [
            OrgToRole(**org_role.dict())
            for org_role in org_roles
        ]

        # 批量添加
        self.db.add_all(org_role_objects)
        self.db.commit()

        return org_role_objects



    def get_org_tree(self, name: str = None):
        """获取组织树结构"""
        try:
            # 获取所有组织数据
            query = self.db.query(Org)
            all_orgs = query.order_by(Org.org_code).all()
            # 构建组织字典
            org_dict = {}
            root_nodes = []
            # 第一次遍历：构建所有节点
            for org in all_orgs:
                org_data = {
                    "id": org.id,
                    "name": org.name,
                    "org_code": org.org_code,
                    "sort": org.sort,
                    "owner_user_id": org.owner_user_id,
                    "status": org.status,
                    "parent_id": org.parent_id,
                    "parent_org_code": org.parent_org_code,
                    "can_sub_query": org.can_sub_query,
                    "children": []
                }
                org_dict[org.org_code] = org_data

            # 第二次遍历：构建树结构
            for org_code, org_data in org_dict.items():
                if org_data["parent_org_code"] == "-1" or org_data["org_code"] == "4203":
                    # 根节点
                    root_nodes.append(org_data)
                elif org_data["parent_org_code"] in org_dict:
                    # 将当前节点添加到父节点的children中
                    parent = org_dict[org_data["parent_org_code"]]
                    parent["children"].append(org_data)

            return create_response(code=200, data=root_nodes)
        except Exception as e:
            return create_response(code=500, message="获取组织树失败")


    def get_org_tree2(self, name: str = None) -> List[Dict]:
        """获取组织树结构"""
        try:
            # 获取所有组织数据
            u=UserContext.get_current_user_expand()
            org_code= u.get("userOrg")
            query = self.db.query(Org).filter(Org.org_code.like(f"{org_code[0]}%")).order_by(Org.org_code)
            all_orgs = query.all()
            # 构建组织字典
            org_dict = {}
            root_nodes = []
            # 第一次遍历：构建所有节点
            for org in all_orgs:
                org_data = {
                    "id": org.id,
                    "name": org.name,
                    "org_code": org.org_code,
                    "sort": org.sort,
                    "owner_user_id": org.owner_user_id,
                    "status": org.status,
                    "parent_id": org.parent_id,
                    "parent_org_code": org.parent_org_code,
                    "can_sub_query": org.can_sub_query,
                    "children": []
                }
                org_dict[org.org_code] = org_data

            # 第二次遍历：构建树结构
            for org_code, org_data in org_dict.items():
                if org_data["parent_org_code"] == "-1" or org_data["org_code"] == "4203":
                    # 根节点
                    root_nodes.append(org_data)
                elif org_data["parent_org_code"] in org_dict:
                    # 将当前节点添加到父节点的children中
                    parent = org_dict[org_data["parent_org_code"]]
                    parent["children"].append(org_data)
            return root_nodes
        except Exception as e:
            return create_response(code=500, data="获取组织树失败")

       ## 获取主部门机构列表 及是否可查子子集列表
    def getUserToOrg(db: Session, org_code: str) -> List[Dict]:
        try:
            # 查询机构信息
            org_info = db.query(Org).filter(Org.org_code == org_code).first()
            if not org_info:
                return create_response(code=500, data="机构不存在")

            # 判断是否可以查询子集
            if org_info.can_sub_query == 1:
                # 可以查询子集，使用like查询所有子节点
                org_codes = db.query(Org).filter(
                    Org.org_code.like(f"{org_code}%")
                ).all()
                return [code[0] for code in org_codes]
            else:
                # 不可查询子集，只返回当前节点
                return [org_code]
        except Exception as e:
            return create_response(code=500, data=f"获取机构列表失败: {str(e)}")



    def getCurrentOrgList(self, db: Session) -> List[str]:
        try:
            # 获取用户机构信息
            org = UserContext.get_current_user_org()
            if not org:
                return create_response(code=500, data=f"未获取到用户机构信息")


            org_code = org.get("org_code")
            if not org_code:
                return create_response(code=500, data=f"未获取到机构编码")


            # 查询机构权限
            org_info = db.query(Org).filter(Org.org_code == org_code).first()
            if not org_info:
                return []

            # 获取所有相关机构编码并映射
            if org_info.can_sub_query == 1:
                # 可以查询子部门，先获取所有子机构
                sub_orgs = db.query(Org.org_code).filter(
                    Org.org_code.like(f"{org_code}%")
                ).all()
                if not sub_orgs:
                    return []

                org_codes = [code[0] for code in sub_orgs]

                # 通过机构映射表获取原始机构编码
                origin_org_codes = db.query(temp_org_map.origin_code).filter(
                    temp_org_map.org_code.in_(org_codes)
                ).all()
                return [code[0] for code in origin_org_codes] if origin_org_codes else []
            else:
                # 只查询当前机构的映射
                origin_org_code = db.query(temp_org_map.origin_code).filter(
                    temp_org_map.org_code == org_code
                ).first()
                return [origin_org_code[0]] if origin_org_code else []

        except Exception as e:
            LogUtils.error(f"获取机构列表失败: {str(e)}")
            return create_response(code=500, data=f"获取机构列表失败: {str(e)}")

    def get_org_map (self, db: Session) -> List[Dict]:
        records = db.query(temp_org_map).all()
        return {record.origin_code: record.org_code for record in records}