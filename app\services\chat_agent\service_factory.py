"""
智能体服务工厂
"""
from typing import Any
from sqlalchemy.orm import Session

from app.services.chat_agent.registry import AgentRegistry
from app.services.chat_agent.service import AgentService
from app.services.chat_agent.init_registry import init_agent_registry


class AgentServiceFactory:
    """智能体服务工厂"""

    @staticmethod
    def create_service(
        db: Session,
        kb_service: Any,  # KnowledgeBaseService
        query_service: Any,  # QueryProcessingService
        session_service: Any  # SessionService
    ) -> AgentService:
        """
        创建智能体服务

        Args:
            db: 数据库会话
            kb_service: 知识库服务
            query_service: 查询处理服务
            session_service: 会话服务

        Returns:
            智能体服务
        """
        # 初始化智能体注册表
        registry = init_agent_registry()

        # 创建智能体服务
        return AgentService(db, registry, kb_service, query_service, session_service)
