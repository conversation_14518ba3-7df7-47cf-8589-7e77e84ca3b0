<template>
  <div class="case-management bigscreen">
    <PageHeader title="案件类警情流失巡查" subtitle="Police Case Management Platform" />

    <div class="content-wrapper">
      <!-- 添加Tab页切换组件 -->
      <div class="tabs-section">
        <div class="tabs-header">
          <el-tabs v-model="activeTab" class="el-tabs-card" :class="[activeTab == 0 ? 'active1' : 'active2']"
            @tab-click="handleTabClick">
            <el-tab-pane label="刑事类警情" name="0">
            </el-tab-pane>
            <el-tab-pane label="行政（治安）类警情" name="1">
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form common-search">
          <div class="search-left" :class="[isReverse && 'has-height']">
            <el-form-item label="处警时间">
              <el-date-picker v-model="searchForm.timeRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"></el-date-picker>
            </el-form-item>
            <el-form-item label="处警编号">
              <el-input v-model="searchForm.police_number" placeholder="请输入处警编号"></el-input>
            </el-form-item>
            <el-form-item label="处警单位">
              <el-input v-model="searchForm.police_unit" placeholder="请输入处警单位"></el-input>
            </el-form-item>
            <el-form-item label="报警内容">
              <el-input v-model="searchForm.alarm_content" placeholder="请输入报警内容"></el-input>
            </el-form-item>
            <el-form-item label="核查结果">
              <el-select v-model="searchForm.check_results" placeholder="请选择核查结果" clearable>
                <el-option label="有案不立" value="有案不立"></el-option>
                <el-option label="降格处理" value="降格处理"></el-option>
                <el-option label="待回访案件" value="待回访案件"></el-option>
                <el-option label="无问题" value="无问题"></el-option>
                <el-option label="有争议需关注" value="有争议需关注"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分析状态">
              <el-select v-model="searchForm.analysis_status" placeholder="请选择分析状态" clearable>
                <el-option v-for="(item, key) in analysisStatusMap" :key="key" :label="item.text" :value="key">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="search-right">
            <el-form-item>
              <el-button type="primary" @click="handleSearch" class="search-button">查询</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <div class="el-button_text" @click="isReverse = !isReverse">
                {{ !isReverse ? '收起' : '展开' }}
                <i v-show="isReverse" class="el-icon-arrow-down"></i>
                <i v-show="!isReverse" class="el-icon-arrow-up"></i>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <div class="common-action-button-operations">
        <div class="left-operations">
          <el-upload class="upload-excel" action="1233122312" :http-request="handleUpload" :show-file-list="false"
            :before-upload="beforeUpload" accept=".xlsx,.xls">
            <el-button type="primary" class="tool-btn">
              <i class="el-icon-upload2"></i>
              <span>导入数据</span>
            </el-button>
          </el-upload>
          <el-button type="primary" @click="exportData" class="tool-btn">
            <i class="el-icon-download"></i>
            <span>导出数据</span>
          </el-button>
          <el-button type="primary" @click="handleSync" class="tool-btn">
            <i class="el-icon-refresh"></i>
            <span>同步数据</span>
          </el-button>
          <el-button type="primary" @click="handleBatchAnalyze" :disabled="!selectedRows.length" class="tool-btn">
            <i class="el-icon-data-analysis"></i>
            <span>批量分析</span>
          </el-button>
          <!-- <el-upload
            class="upload-excel"
            action="1233122312"
            :http-request="handleManualImport"
            :show-file-list="false"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls"
          >
            <el-button type="primary">
              <i class="el-icon-upload"></i>
              <span>手动导入</span>
            </el-button>
          </el-upload>
          <el-button type="primary" @click="showImportList">
            <i class="el-icon-document"></i>
            <span>查看导入</span>
          </el-button> -->
        </div>
      </div>

      <div class="table-container">
        <el-table :data="tableData" style="width: 100%" :header-cell-style="headerStyle" :cell-style="cellStyle"
          v-loading="loading" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="60" header-align="center" align="center"
            :selectable="checkSelectable"></el-table-column>
          <el-table-column type="expand" width="25" align="center" header-align="center">
            <template slot-scope="props">
              <div class="detail-container">
                <div class="detail-header">
                  <span class="detail-title">警情详细信息</span>
                  <span class="detail-number">{{ props.row.police_number }}</span>
                </div>
                <div class="detail-content">
                  <div class="detail-section">
                    <div class="section-title">基本信息</div>
                    <div class="detail-grid">
                      <div class="detail-item">
                        <span class="item-label">处警时间：</span>
                        <span class="item-value">
                          {{ formatDate(props.row, null, props.row.police_time) }}
                        </span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">处警编号：</span>
                        <span class="item-value">{{ props.row.police_number }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">处警单位：</span>
                        <span class="item-value">{{ props.row.police_unit }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">报警形式：</span>
                        <span class="item-value">{{ props.row.alarm_form }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">警情类别：</span>
                        <span class="item-value">{{ props.row.alarm_category }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">处警类别：</span>
                        <span class="item-value">{{ props.row.police_category }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="item-label">处警结果：</span>
                        <span class="item-value">{{ props.row.police_results }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="detail-section">
                    <div class="section-title">报警内容</div>
                    <div class="common-scroll detail-scroll">{{ props.row.alarm_content }}</div>
                  </div>
                  <div class="detail-section">
                    <div class="section-title">处警详细情况</div>
                    <div class="common-scroll detail-scroll">{{ props.row.alarm_details }}</div>
                  </div>
                  <div class="detail-section">
                    <div class="section-title">辅助分析</div>
                    <div class="common-scroll detail-scroll">
                      {{ props.row.analysis_details }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column type="index" label="序号" width="70" align="center" header-align="center">
          </el-table-column>
          <el-table-column prop="police_time" label="处警时间" min-width="80" align="left" show-overflow-tooltip
            :formatter="formatDate"></el-table-column>
          <el-table-column prop="police_number" label="处警编号" min-width="180" show-overflow-tooltip
            align="left"></el-table-column>
          <el-table-column prop="police_unit" label="处警单位" min-width="180" show-overflow-tooltip
            align="left"></el-table-column>
          <el-table-column prop="police_results" label="处警结果" min-width="100" show-overflow-tooltip
            align="left"></el-table-column>
          <el-table-column prop="alarm_content" label="报警内容" min-width="180" show-overflow-tooltip
            align="left"></el-table-column>
          <el-table-column prop="alarm_details" label="处警详细情况" min-width="180" show-overflow-tooltip
            align="left"></el-table-column>
          <el-table-column prop="check_results" label="核查结果" min-width="80" align="left">
            <template slot-scope="scope">
              <StatusView :statusOption="formatCheckResults(scope.row)"></StatusView>
            </template>
          </el-table-column>
          <el-table-column prop="analysis_details" label="辅助分析理由" min-width="200" align="left"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="analysis_status" label="分析状态" min-width="100" align="center" header-align="center">
            <template slot-scope="scope">
              <StatusView :statusOption="formatAnalysisStatus(scope.row)"></StatusView>
              <!-- <el-tooltip :content="formatAnalysisStatus(scope.row)" placement="top">
                <div class="status-icon">
                  <i v-if="scope.row.analysis_status === 1" class="el-icon-loading"></i>
                  <i
                    v-else-if="scope.row.analysis_status === 2 || scope.row.analysis_status === 4"
                    class="el-icon-check success-icon"
                  ></i>
                  <i
                    v-else-if="scope.row.analysis_status === 3"
                    class="el-icon-close error-icon"
                  ></i>
                  <i v-else class="el-icon-more waiting-icon"></i>
                </div>
              </el-tooltip> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" background></el-pagination>
        </div>
      </div>

      <!-- 月份范围选择弹框 -->
      <DateRangeDialog :visible.sync="dateRangeDialogVisible" @confirm="handleDateRangeConfirm"
        @cancel="handleDateRangeCancel" @close="handleDateRangeClose" />

      <!-- 导入文件列表对话框 -->
      <ImportFilesDialog :visible.sync="importListDialogVisible" :defaultCaseType="activeTab"
        @view-detail="handleViewImportDetail" />
    </div>
  </div>
</template>

<script>
import { getCaseList, exportCases, uploadCases, syncCaseData, batchAnalyzeCases, manualImportCases } from "@/api/case";
import XLSX from "xlsx";
import AIChatBox from "@/components/AIChatBox.vue";
import PageHeader from "@/components/PageHeader.vue";
import DateRangeDialog from "@/components/DateRangeDialog.vue";
import ImportFilesDialog from "@/components/ImportFilesDialog.vue";
import StatusView from "@/components/StatusView.vue";


export default {
  name: "CaseManagement",
  components: {
    AIChatBox,
    PageHeader,
    DateRangeDialog,
    ImportFilesDialog,
    StatusView
  },
  data() {
    return {
      activeTab: "0", // 默认选中刑事类警情tab
      searchForm: {
        police_number: "",
        police_unit: "",
        timeRange: [],
        check_results: "",
        alarm_content: "",
        analysis_status: "",
      },
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      loading: false,
      total: 0,
      analysisStatusMap: {
        0: { text: "未分析", color: "#909399", iconType: '', bgColor: "rgba(144,147,153,0.1)" },
        1: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        2: { text: "分析完成", color: "#67C23A", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "分析失败", color: "#F56C6C", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
      },
      checkResultsColorMap: {
        有案不立: "有案不立",
        降格处理: "降格处理",
        待回访案件: "待回访案件",
        无问题: "无问题",
        有争议需关注: "有争议需关注",
      },
      analysisFormatMap: {
        0: { text: "未分析", color: "#909399", iconType: '', bgColor: "rgba(144,147,153,0.1)" },
        1: { text: "分析中", color: "#EEEEEE", iconType: 'loading', bgColor: "rgba(64,158,255,0.1)" },
        2: { text: "分析完成", color: "#38A366", iconType: 'check', bgColor: "rgba(103,194,58,0.1)" },
        3: { text: "分析失败", color: "#FF2F2F", iconType: 'close', bgColor: "rgba(245,108,108,0.1)" },
        4: { text: "已同步", color: "#409EFF", iconType: 'refresh', bgColor: "rgba(245,108,108,0.1)" },
        5: { text: "待分析", color: "#E6A23C", iconType: 'clock', bgColor: "rgba(245,108,108,0.1)" },
      },
      checkFormatMap: {
        '无问题': { iconColor: "#999999", iconType: 'speck' },
        '待回访案件': { iconColor: "#E6A23C", iconType: 'speck' },
        '有案不立': { iconColor: "#F56C6C", iconType: 'speck' },
        '有争议需关注': { iconColor: "#409EFF", iconType: 'speck' },
        '降格处理': { iconColor: "#F56C6C", iconType: 'speck' }, 
      },
      analysisLevelColorMap: {
        red: "#F56C6C",
        yellow: "#F56C6C",
        white: "#999999",
        blue: "#409EFF",
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      selectedFile: null,
      selectedRows: [], // 新增选中行数据
      dateRangeDialogVisible: false,

      // 导入文件列表相关
      importListDialogVisible: false, // 导入文件列表对话框可见性

      isReverse: false, // 控制是否展开收起
    };
  },
  created() {
    // 检查 URL 参数中是否有 analysis_level 和 case_type
    const analysisLevel = this.$route.query.analysis_level;
    const caseType = this.$route.query.case_type;
    const fileId = this.$route.query.file_id;
    const importDate = this.$route.query.import_date;

    if (caseType) {
      this.activeTab = caseType === 1 ? 1 : 0;
    }

    if (analysisLevel) {
      this.searchForm.analysis_level = analysisLevel;
    }

    // 如果有文件ID，添加到搜索条件中
    if (fileId) {
      this.searchForm.file_id = fileId;
    }

    // 如果有导入日期，设置时间范围
    if (importDate) {
      this.searchForm.timeRange = [importDate, importDate];
    }

    this.loadTableData();
  },
  methods: {
    // 处理tab切换
    handleTabClick() {
      this.currentPage = 1;
      this.loadTableData();
    },

    // 加载表格数据
    async loadTableData() {
      try {
        this.loading = true;
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
          case_type: this.activeTab, // 添加案件类型参数
        };

        // 添加搜索条件
        if (this.searchForm.police_number) {
          params.police_number = this.searchForm.police_number;
        }
        if (this.searchForm.police_unit) {
          params.police_unit = this.searchForm.police_unit;
        }
        if (this.searchForm.timeRange && this.searchForm.timeRange.length === 2) {
          params.start_time = this.searchForm.timeRange[0];
          params.end_time = this.searchForm.timeRange[1];
        }
        if (this.searchForm.analysis_level) {
          params.analysis_level = this.searchForm.analysis_level;
        }
        if (this.searchForm.alarm_content) {
          params.alarm_content = this.searchForm.alarm_content;
        }
        if (this.searchForm.check_results) {
          params.check_results = this.searchForm.check_results;
        }
        if (this.searchForm.analysis_status) {
          params.analysis_status = this.searchForm.analysis_status;
        }
        // 添加文件ID参数
        if (this.searchForm.file_id) {
          params.file_id = this.searchForm.file_id;
        }

        const res = await getCaseList(params);
        if (res.code === 200 && res.data && Array.isArray(res.data.records)) {
          this.tableData = res.data.records;
          this.total = res.data.total || res.data.records.length;
        }
      } catch (error) {
        console.error("获取数据失败：", error);
      } finally {
        this.loading = false;
      }
    },
    headerStyle() {
      return {
        backgroundColor: "#1C3349",
        color: "#FFFFFFcc",
        fontWeight: "bold",
      };
    },
    cellStyle() {
      return {
        backgroundColor: "rgba(0,48,102,0.3)",
        color: "#ffffffb3",
        borderColor: "rgba(0,255,255,0.1)",
      };
    },
    async handleSearch() {
      this.currentPage = 1;
      await this.loadTableData();
    },
    resetSearch() {
      this.searchForm = {
        police_number: "",
        police_unit: "",
        timeRange: [],
        check_results: "",
        alarm_content: "",
        analysis_status: "",
      };
      this.handleSearch();
    },
    async exportData() {
      try {
        this.loading = true;
        const params = {
          // page: this.currentPage,
          // page_size: this.pageSize,
          case_type: this.activeTab, // 添加案件类型参数
        };

        // 添加搜索条件
        if (this.searchForm.police_number) {
          params.police_number = this.searchForm.police_number;
        }
        if (this.searchForm.police_unit) {
          params.police_unit = this.searchForm.police_unit;
        }
        if (this.searchForm.timeRange && this.searchForm.timeRange.length === 2) {
          params.start_time = this.searchForm.timeRange[0];
          params.end_time = this.searchForm.timeRange[1];
        }
        if (this.searchForm.check_results) {
          params.check_results = this.searchForm.check_results;
        }
        if (this.searchForm.alarm_content) {
          params.alarm_content = this.searchForm.alarm_content;
        }
        if (this.searchForm.analysis_status) {
          params.analysis_status = this.searchForm.analysis_status;
        }

        const res = await exportCases(params);
        if (
          res.type === "application/vnd.ms-excel" ||
          res.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ) {
          // 创建Blob对象
          const blob = new Blob([res], { type: res.type });

          // 创建下载链接
          const downloadElement = document.createElement("a");
          const href = window.URL.createObjectURL(blob);
          downloadElement.href = href;
          const caseTypeText = this.activeTab + "" === "0" ? "刑事类" : "行政（治安）类";
          downloadElement.download = `${caseTypeText}警情数据_${new Date().toLocaleDateString()}.xlsx`;

          // 触发下载
          document.body.appendChild(downloadElement);
          downloadElement.click();
          document.body.removeChild(downloadElement);
          window.URL.revokeObjectURL(href);

          this.$message.success("导出成功");
        } else {
          throw new Error(res.message || "导出失败");
        }
      } catch (error) {
        console.error("导出失败：", error);
      } finally {
        this.loading = false;
      }
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      await this.loadTableData();
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      await this.loadTableData();
    },
    beforeUpload(file) {
      const isExcel =
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error("只能上传 Excel 文件!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    async handleUpload(options) {
      try {
        this.selectedFile = options.file;
        const caseTypeText = this.activeTab === "0" ? "刑事类警情" : "行政（治安）类警情";
        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              <i class="el-icon-warning"></i>当前选择的案件类型为: <strong>${caseTypeText}</strong>
            </div>
            <div class="confirm-item">
              警情数据导入成功后DeepSeek会自动智能分析并展示打标结果，确认导入警情数据吗
            </div>
          </div>
        `,
          "导入确认",
          {
            confirmButtonText: "确定导入",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const formData = new FormData();
          formData.append("file", this.selectedFile);
          formData.append("case_type", this.activeTab); // 添加案件类型参数

          // 设置请求头
          const config = {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          };

          const res = await uploadCases(formData, config);
          if (res.code === 200 && res.data && res.data.processed) {
            this.$message.success("文件上传成功");
            // 刷新数据列表
            await this.loadTableData();
          } else {
            throw new Error(res.message || "文件上传失败");
          }
        } else {
          this.$message.info("已取消导入");
          return;
        }
      } catch (error) {
        // 只有在实际上传过程中出现错误时才提示
        if (error.toString() !== "cancel") {
          console.error("文件上传失败:", error);

        }
      } finally {
        this.loading = false;
        this.selectedFile = null;
      }
    },
    // 获取分析等级颜色
    getAnalysisLevelColor(level) {
      return this.analysisLevelColorMap[level] || "#FFFFFF";
    },
    getCheckResults(result) {
      return this.checkResultsColorMap[result] || "";
    },
    // 分析等级样式
    analysisLevelStyle({ row }) {
      return this.cellStyle();
    },
    // 格式化日期
    formatDate(row, column, cellValue) {
      if (!cellValue) return "";
      const date = new Date(cellValue);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 分析状态格式化
    formatAnalysisStatus(row) {
      const status = this.analysisFormatMap[row.analysis_status];
      return status || {};
    },
    formatCheckResults(row) {
      let status = this.checkFormatMap[row.check_results];
      status = status || {};
      status.text = row.check_results
      return status || {};
    },
    goToHome() {
      this.$router.push("/");
    },
    async handleSync() {
      this.dateRangeDialogVisible = true;
    },
    async handleDateRangeConfirm(dateRange) {
      try {
        this.loading = true;
        const params = {
          start_date: dateRange[0], // 添加日期为当月第一天
          end_date: dateRange[1]    // 添加日期为当月第一天
        };
        const res = await syncCaseData(params);
        if (res.code === 200) {
          this.$message.success("数据同步成功");
          await this.loadTableData();
        } else {
          throw new Error(res.message || "数据同步失败");
        }
      } catch (error) {
        console.error("数据同步失败:", error);
      } finally {
        this.loading = false;
        this.dateRangeDialogVisible = false;
      }
    },
    handleDateRangeCancel() {
      this.dateRangeDialogVisible = false;
    },
    handleDateRangeClose() {
      this.dateRangeDialogVisible = false;
    },
    // 新增方法：判断是否可选
    checkSelectable(row) {
      return row.analysis_status === 0 || row.analysis_status === 3; // 只有未分析状态可选
    },
    // 新增方法：处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 新增方法：批量分析
    async handleBatchAnalyze() {
      try {
        if (this.selectedRows.length === 0) {
          this.$message.warning('请选择要分析的案件');
          return;
        }

        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              确认对选中的 ${this.selectedRows.length} 个案件进行批量分析吗？
            </div>
          </div>
        `,
          "分析确认",
          {
            confirmButtonText: "确定分析",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const caseIds = this.selectedRows.map(row => row.id);
          const res = await batchAnalyzeCases(caseIds);
          if (res.code === 200) {
            this.$message.success("批量分析任务已启动");
            await this.loadTableData();
            this.selectedRows = [];
          } else {
            throw new Error(res.message || "批量分析失败");
          }
        } else {
          this.$message.info("已取消分析");
        }
      } catch (error) {
        if (error.toString() !== "cancel" && error.toString() !== "close") {
          console.error("批量分析失败:", error.toString());

        }
      } finally {
        this.loading = false;
      }
    },
    // 手动导入文件处理
    async handleManualImport(options) {
      try {
        this.selectedFile = options.file;
        const caseTypeText = this.activeTab === "0" ? "刑事类警情" : "行政（治安）类警情";
        const confirmResult = await this.$confirm(
          `
          <div class="import-confirm-content">
            <div class="confirm-item">
              <i class="el-icon-warning"></i>当前选择的案件类型为: <strong>${caseTypeText}</strong>
            </div>
            <div class="confirm-item">
              手动导入警情数据后需要单独进行分析，确认导入警情数据吗？
            </div>
          </div>
        `,
          "手动导入确认",
          {
            confirmButtonText: "确定导入",
            cancelButtonText: "取消",
            type: "warning",
            center: true,
            dangerouslyUseHTMLString: true,
            distinguishCancelAndClose: true,
            showClose: true,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            customClass: "import-confirm-dialog",
          }
        );

        if (confirmResult === "confirm") {
          this.loading = true;
          const formData = new FormData();
          formData.append("file", this.selectedFile);
          formData.append("case_type", this.activeTab); // 添加案件类型参数

          // 设置请求头
          const config = {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          };

          const res = await manualImportCases(formData, config);
          if (res.code === 200) {
            this.$message.success({
              message: "文件上传成功，是否查看导入文件列表？",
              duration: 5000,
              showClose: true,
              onClick: () => {
                this.showImportList();
              }
            });
            // 刷新数据列表
            await this.loadTableData();
          } else {
            throw new Error(res.message || "文件上传失败");
          }
        } else {
          this.$message.info("已取消导入");
          return;
        }
      } catch (error) {
        // 只有在实际上传过程中出现错误时才提示
        if (error.toString() !== "cancel") {
          console.error("文件上传失败:", error);

        }
      } finally {
        this.loading = false;
        this.selectedFile = null;
      }
    },

    // 显示导入文件列表
    async showImportList() {
      this.importListDialogVisible = true;
    },

    // 查看导入详情
    handleViewImportDetail(detail) {
      // 根据文件ID跳转到对应的查询页面
      this.$router.push({
        path: '/case-management',
        query: {
          file_id: detail.id,
          case_type: detail.case_type,
          import_date: detail.import_date ? detail.import_date.substring(0, 10) : ''
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>



.case-management {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  flex-direction: column;
}

:deep(.el-tooltip__popper.is-light) {
  background: rgba(0, 48, 102, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

:deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow) {
  border-bottom-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow::after) {
  border-bottom-color: rgba(0, 48, 102, 0.95) !important;
}

.search-left {
  // display: flex;
  // align-items: center;
  // gap: 10px;
  // flex-wrap: wrap;
  // flex: 3;
  // justify-content: space-between;
  height: 110px;

  >* {
    width: calc(100% / 3 - (70px / 3));
  }
}

.has-height {
  height: 50px;
  overflow: hidden;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 10px;
  flex: 1;
  justify-content: end;

  .el-button_text {
    height: 32px;
    margin-left: 10px;
    cursor: pointer;
  }
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0;
}

:deep(.el-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.el-table__header-wrapper) {
  flex-shrink: 0;
}

:deep(.el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto !important;
  height: 0 !important;
}

.pagination-container {
  margin-top: 20px;
  margin-right: 60px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}

/* Element UI 样式覆盖 */
:deep(.el-input__inner) {
  background-color: rgba(0, 48, 102, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.5);
}

:deep(.el-date-editor .el-range-input::placeholder) {
  color: rgba(255, 255, 255, 1) !important;
}

:deep(.el-date-editor .el-range-input) {
  color: #ffffff !important;
  background-color: transparent !important;
}

:deep(.el-input__inner:hover),
:deep(.el-input__inner:focus) {
  border-color: #00ffff;
}


:deep(.el-table) {
  background-color: transparent !important;
  flex: 1;
  min-height: 0;
}

:deep(.el-table__header) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: rgba(0, 255, 255, 0.4);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: rgba(0, 48, 102, 0.3);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: rgba(0, 48, 102, 0.5) !important;
}

:deep(.el-table td, .el-table th.is-leaf) {
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  background-color: rgba(0, 48, 102, 0.3) !important;
}

// :deep(.el-table__row) {
//   transition: all 0.3s;
// }

// :deep(.el-table__body tr:hover > td) {
//   background-color: rgba(0, 255, 255, 0.2) !important;
//   color: #00ffff !important;
// }

// :deep(.el-table__header-wrapper) {
//   background-color: transparent !important;
// }

// :deep(.el-table__header tr th) {
//   background-color: rgba(0, 48, 102, 0.8) !important;
//   border-bottom: 2px solid rgba(0, 255, 255, 0.2);
// }

// :deep(.el-table__body tr) {
//   background-color: transparent !important;
// }

// :deep(.el-table__body tr td) {
//   background-color: rgba(0, 48, 102, 0.3) !important;
// }

// :deep(.el-table__body tr:hover td) {
//   background-color: rgba(0, 255, 255, 0.1) !important;
// }

.detail-container {
  // padding: 20px;
  // background: rgba(0, 48, 102, 0.2);
  border-radius: 4px;
  margin: 10px 20px;
}

.detail-header {
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  margin-bottom: 20px;
  // padding-bottom: 10px;
  // border-bottom: 1px solid rgba(0, 255, 255, 0.2);

  .detail-title {
    font-size: 16px;
    color: #fff;
    font-weight: bold;
  }

  .detail-number {
    background: #1B354E;
    color: rgba(0, 255, 255, 0.7);
    font-size: 14px;
    margin-left: 8px;
    padding: 3px 8px;
    border-radius: 2px;
  }
}


.detail-content {
  display: grid;
  flex-direction: column;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(1, 1fr);
  gap: 12px;

  .detail-section {
    // background: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.1);
    border-radius: 4px;
    padding: 15px;

    .detail-grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 15px;
    }

    .detail-scroll {
      max-height: 260px;
      color: #f8f8f8;
    }
  }

  .section-title {
    color: #04c2c2;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
    position: relative;
    padding-left: 12px;
  }

  .section-title::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: #00ffff;
    border-radius: 2px;
  }
}



.item-label {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
  font-size: 14px;
  white-space: nowrap;
}

.item-value {
  color: #ffffff;
  font-size: 14px;
}



.pagination-container {
  margin-top: 20px;
}

:deep(.el-pagination) {
  padding: 0;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
  border: 1px solid #00ffff;
}

:deep(.el-pagination.is-background .el-pager li) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
  margin: 0 3px;
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
  background-color: rgba(0, 48, 102, 0.3);
  color: #ffffff;
  border: 1px solid rgba(0, 255, 255, 0.1);
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover) {
  color: #00ffff;
  border-color: #00ffff;
}

:deep(.el-pagination__total),
:deep(.el-pagination__jump),
:deep(.el-pagination__sizes) {
  color: #ffffff;
}



:deep(.el-select-dropdown) {
  background-color: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}

.button-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.upload-excel {
  display: inline-block;
}

:deep(.el-upload) {
  display: inline-block;
}

:deep(.el-icon-upload2),
:deep(.el-icon-download) {
  font-size: 16px;
}

/* 添加tooltip样式 */
:deep(.el-tooltip__popper) {
  background-color: rgba(0, 48, 102, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  color: #ffffff !important;
  font-size: 12px !important;
  padding: 8px 12px !important;
  max-width: 400px !important;
  line-height: 1.5 !important;
}

:deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow) {
  border-top-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow::after) {
  border-top-color: rgba(0, 48, 102, 0.95) !important;
}

:deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow) {
  border-bottom-color: rgba(0, 255, 255, 0.2) !important;
}

:deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow::after) {
  border-bottom-color: rgba(0, 48, 102, 0.95) !important;
}


:deep(.el-form--inline .el-form-item) {
  margin-right: 0;
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  // gap: 10px;
}

:deep(.el-form-item__content) {
  display: flex;
  flex-direction: row;
  // gap: 10px;
  flex-wrap: nowrap;
  margin-left: 0 !important;
  flex: 1;
  align-items: baseline;

  .el-select {
    width: 100%;

    .el-input .el-input--suffix {
      width: 100%;

      input {
        width: 100%;
      }
    }
  }
}

/* Element UI 样式覆盖 */
:deep(.el-loading-mask) {
  background-color: rgba(0, 24, 51, 0.9) !important;
}

:deep(.el-loading-spinner .el-loading-text) {
  color: #00ffff !important;
}

:deep(.el-loading-spinner .path) {
  stroke: #00ffff !important;
}

.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 48, 102, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-icon i {
  font-size: 14px;
}

.success-icon {
  color: #67c23a;
  text-shadow: 0 0 4px rgba(103, 194, 58, 0.3);
}

.error-icon {
  color: #f56c6c;
  text-shadow: 0 0 4px rgba(245, 108, 108, 0.3);
}

.waiting-icon {
  color: #909399;
  border: 1px solid #909399;
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 0 4px rgba(144, 147, 153, 0.3);
}

:deep(.el-icon-loading) {
  text-shadow: 0 0 4px rgba(64, 158, 255, 0.3);
}

:deep(.el-date-editor.el-input__inner) {
  align-items: baseline;
}

:deep(.el-date-editor--daterange.el-input__inner) {
  align-items: baseline;
}

:deep(.el-range-editor--medium.el-input__inner) {
  align-items: baseline;
}

:deep(.el-date-editor .el-range-input) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-date-editor .el-range-separator) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel) {
  background: rgba(0, 48, 102, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.2);
}

:deep(.el-picker-panel__content) {
  color: #ffffff;
}

:deep(.el-date-table th) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-date-table td.available:hover) {
  color: #00ffff;
}

:deep(.el-date-table td.current:not(.disabled)) {
  background-color: rgba(0, 255, 255, 0.2);
  color: #00ffff;
}

:deep(.el-picker-panel__shortcut) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel__shortcut:hover) {
  color: #00ffff;
}

:deep(.el-date-picker__header-label) {
  color: #ffffff;
}

:deep(.el-date-picker__header-label:hover) {
  color: #00ffff;
}

:deep(.el-picker-panel__icon-btn) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-picker-panel__icon-btn:hover) {
  color: #00ffff;
}

/* 导入确认对话框全局样式 */
.el-message-box.import-confirm-dialog {
  background: rgba(0, 24, 51, 0.85) !important;
  border: 1px solid rgba(0, 255, 255, 0.4) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
  min-width: 420px !important;
}

.import-confirm-content {
  padding: 15px 0;
}

.confirm-item {
  margin: 8px 0;
  color: #ffffff;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1.5;
}

.confirm-item .el-icon-warning {
  color: #e6a23c;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(230, 162, 60, 0.4);
}

.el-message-box.import-confirm-dialog .el-message-box__header {
  padding: 12px 16px 10px !important;
  background: linear-gradient(180deg,
      rgba(0, 48, 102, 0.8) 0%,
      rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

.el-message-box.import-confirm-dialog .el-message-box__title {
  color: #00ffff !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

.el-message-box.import-confirm-dialog .el-message-box__content {
  padding: 20px 16px !important;
  background: rgba(0, 24, 51, 0.4) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.15) !important;
}

.el-message-box.import-confirm-dialog .el-message-box__btns {
  padding: 12px 16px !important;
  background: linear-gradient(0deg,
      rgba(0, 48, 102, 0.8) 0%,
      rgba(0, 48, 102, 0.6) 100%) !important;
  border-radius: 0 0 8px 8px !important;
}

.el-message-box.import-confirm-dialog .el-button {
  height: 32px !important;
  padding: 0 20px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  transition: all 0.3s !important;
}

.el-message-box.import-confirm-dialog .el-button--primary {
  background: rgba(0, 255, 255, 0.2) !important;
  border: 1px solid #00ffff !important;
  color: #00ffff !important;
}

.el-message-box.import-confirm-dialog .el-button--primary:hover {
  background: rgba(0, 255, 255, 0.3) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
}

.el-message-box.import-confirm-dialog .el-button--default {
  background: rgba(144, 147, 153, 0.1) !important;
  border: 1px solid rgba(144, 147, 153, 0.3) !important;
  color: #ffffff !important;
}

.el-message-box.import-confirm-dialog .el-button--default:hover {
  background: rgba(144, 147, 153, 0.2) !important;
  border-color: rgba(144, 147, 153, 0.4) !important;
  transform: translateY(-1px) !important;
}

.el-message-box.import-confirm-dialog .el-message-box__close {
  color: rgba(0, 255, 255, 0.7) !important;
  font-size: 16px !important;
  transition: all 0.3s !important;
}

.el-message-box.import-confirm-dialog .el-message-box__close:hover {
  color: #00ffff !important;
  transform: rotate(90deg) !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.4) !important;
}

.tabs-section {
  margin-bottom: 20px;
}

.sync-button {
  position: absolute;
  right: 20px;
  margin-top: -6.8rem;
  height: 40px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 5px;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.4);
  color: #00ffff;
  transition: all 0.3s;
}

.sync-button:hover {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  transform: translateY(-1px);
}

.sync-button i {
  font-size: 16px;
}

.sync-button span {
  font-size: 14px;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  border-bottom: none;
}

:deep(.el-tabs__nav-wrap) {
  margin-bottom: 0;
}

:deep(.el-tabs-card > .el-tabs__header .el-tabs__item) {
  color: #ffffffcc;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  transition: all 0.3s;
  font-weight: bolder;
}




:deep(.el-tabs-card > .el-tabs__header .el-tabs__item.is-active) {
  color: #00ffffcc;
}

:deep(.el-tabs-card > .el-tabs__header .el-tabs__item:hover) {
  color: #00ffffcc;
}

:deep(.el-tabs__active-bar) {
  background-color: #00ffff;
  height: 3px;
}

:deep(.active1) .el-tabs__active-bar {
  width: 16% !important;
  margin-left: 16px;
}

:deep(.active2) .el-tabs__active-bar {
  width: 30% !important;
  margin-left: 24px;
}

:deep(.el-tabs__nav-next),
:deep(.el-tabs__nav-prev) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-tabs__nav-next:hover),
:deep(.el-tabs__nav-prev:hover) {
  color: #00ffff;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: transparent;
}

.tab-indicator {
  background-color: rgba(0, 48, 102, 0.1);
  padding: 8px 16px;
  border-radius: 0 0 4px 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  margin-top: 6px;
}

.tab-type {
  color: #00ffff;
  font-weight: bold;
  margin-left: 5px;
}

:deep(.el-button.is-disabled i) {
  color: rgba(0, 255, 255, 0.3) !important;
}

:deep(.el-button.is-disabled span) {
  color: rgba(0, 255, 255, 0.3) !important;
}

/* 导入列表对话框样式 */
.import-list-dialog {
  background: rgba(0, 24, 51, 0.95) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2), inset 0 0 10px rgba(0, 255, 255, 0.1) !important;
}

.import-list-content {
  padding: 10px 0;
}

.import-list-dialog .el-dialog__header {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.8) 0%, rgba(0, 48, 102, 0.6) 100%) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px 8px 0 0 !important;
}

.import-list-dialog .el-dialog__title {
  color: #00ffff !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.3) !important;
}

.import-list-dialog .el-dialog__body {
  padding: 20px !important;
  background: rgba(0, 24, 51, 0.4) !important;
}

.import-list-dialog .el-dialog__footer {
  padding: 15px 20px !important;
  background: linear-gradient(180deg, rgba(0, 48, 102, 0.6) 0%, rgba(0, 48, 102, 0.4) 100%) !important;
  border-top: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 0 0 8px 8px !important;
}

.import-date-filter {
  margin-bottom: 15px;
}

.date-label {
  color: #ffffff;
  font-size: 14px;
  margin-right: 10px;
}
</style>
