import hashlib
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Body
from fastapi import UploadFile, File
from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.db.database import get_db
from app.log.log_utils import LogUtils
from app.models.org import Org
from app.models.scene_4_po import scene4_search_order, Scene4PO
from app.models.user import User
from app.services.db_service import DatabaseService
from app.services.scene_4_service import Scene4Service
from app.services.transcript_service import TranscriptPDFService

router = APIRouter()

# 创建全局线程池执行器
executor = ThreadPoolExecutor()


@router.post("/transcript/search")
async def search_records(
        kssj: Optional[str] = None,
        jssj: Optional[str] = None,
        asker: Optional[str] = None,
        bldd: Optional[str] = None,
        analysis_status: Optional[str] = None,
        check_status: Optional[str] = None,
        ajbh: Optional[str] = None,
        ajmc: Optional[str] = None,
        md_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        db: Session = Depends(get_db)
):
    # 使用 DatabaseService 构建查询
    query = DatabaseService.build_transcript_search_query(
        db, kssj, jssj, asker, bldd, analysis_status, ajbh, md_id, check_status, ajmc
    )
    # 获取当前用户信息
    current_org_code = UserContext.get_current_user_org().get("org_code")
    police_number = UserContext.current_user.get().police_number
    # 查询当前用户机构权限
    org_info = db.query(Org).filter(Org.org_code == current_org_code).first()
    # 构建机构和警号的查询条件
    org_condition = (
        Scene4PO.org_code.like(f"{current_org_code}%")
        if org_info and org_info.can_sub_query == 1
        else Scene4PO.org_code == current_org_code
    )
    # 组合查询条件
    query = query.filter(
        or_(
            org_condition,
            Scene4PO.org_code == f"-{police_number}"
        )
    )

    # 计算总记录数
    total_count = query.count()

    # 添加分页
    skip = (page - 1) * page_size
    query = query.order_by(scene4_search_order(), Scene4PO.kssj.desc()).offset(skip).limit(page_size)

    results = query.all()
    return create_response(data={
        "records": results,
        "total": total_count,
        "page": page,
        "page_size": page_size,
        "total_pages": (total_count + page_size - 1) // page_size
    })


@router.post("/upload_transcript_pdf")
async def parse_transcript_pdf(
        db: Session = Depends(get_db),
        pdf_file: UploadFile = File(...)
):
    """
    仅解析PDF文件内容，不进行后续分析处理
    """
    try:
        # 计算PDF文件的MD5值
        content = await pdf_file.read()
        md5_id = hashlib.md5(content).hexdigest()
        LogUtils.info(f"PDF文件 {pdf_file.filename} 的MD5值: {md5_id}")
        await pdf_file.seek(0)

        record = DatabaseService.check_tmp_transcript_exists(db, md5_id)
        if record:
            LogUtils.info(f"数据已存在")
            return create_response(
                message="该PDF文件已存在",
                data=record.md_id
            )

        else:
            # 解析PDF文件内容
            transcript_text = await TranscriptPDFService.parse_pdf_to_text(pdf_file)
            transcript_json = await TranscriptPDFService.extract_fields_from_text(transcript_text)

            # 转换数据格式
            record_data = await TranscriptPDFService.convert_to_db_format(transcript_json, md5_id)
            user = UserContext.get_current()
            new_record = await TranscriptPDFService.save_tmp_record(record_data, user.id)
            LogUtils.info(f"数据完成解析并入库: {new_record.md_id}")

            return create_response(
                message="笔录PDF文件解析成功",
                data=new_record.md_id
            )

    except Exception as e:
        LogUtils.error(f"PDF文件解析异常: {str(e)}")
        db.rollback()
        return create_response(
            code=500,
            message=f"PDF文件解析失败"
        )


@router.post("/scene_4/sync")
async def scene_4_data_sync(
        current_user: User = Depends(UserContext.get_current),
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    立而不侦场景数据同步接口

    Returns:
        Dict[str, Any]: 同步结果
    """
    res = await Scene4Service.scene_4_data_sync(current_user.id, start_date, end_date)
    if not res:
        return create_response(
            message="同步异常",
            code=500
        )
    return create_response(
        message="同步成功",
        data=res
    )


@router.post("/scene_4/analyze")
async def scene_4_summary_analyze(ids: List[int] = Body(..., embed=True)) -> Dict[str, Any]:
    """
    笔录场景分析接口

    Args:
        md_ids: 笔录记录md_id

    Returns:
        Dict[str, Any]: 分析结果
    """
    try:
        # 异步调用，指令发送完成即可
        res = await Scene4Service.scene_4_summary_analyze(ids=ids)
        return create_response(
            message="分析指令已发送",
            data=res
        )
    except Exception as e:
        return create_response(
            message=f"分析异常: {str(e)}",
            code=500
        )
