"""
聊天智能体基础类型定义
"""
from enum import Enum, auto


class AgentOperation(Enum):
    """智能体操作类型枚举"""
    PROCESS_CHAT = auto()  # 处理聊天
    HANDLE_DOCUMENT_UPLOAD = auto()  # 处理文档上传

class AgentType(Enum):
    """问答智能体类型枚举"""
    GENERAL = auto()  # 通用问答
    LEGAL_CODE = auto()  # 法条运用（通用）
    SECRETARIAL = auto()  # 文秘办公（通用）
    DETENTION_CENTRE = auto()  # 法律法规（看守所）

class UserRoleType(Enum):
    """问答智能体类型枚举"""
    SUPER_ADMIN = auto()  # 超级管理员
    ADMIN = auto()  # 管理员
    NORMAL_USER = auto()  # 普通用户
    DATASET_MANAGER = auto()  # 知识库管理员


class ActionResult(Enum):
    """动作执行结果枚举"""
    SUCCESS = auto()  # 成功
    FAILURE = auto()  # 失败
    CONTINUE = auto()  # 继续执行链中的下一个动作
