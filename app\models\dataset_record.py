from sqlalchemy import Column, Integer, String, DateTime, Text ,Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

DataSetBase = declarative_base()

class DataSetRecord(DataSetBase):
    """数据集信息数据模型"""
    __tablename__ = 'dataset_record'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    # 数据集字段
    question = Column(Text, comment='数据集Q')
    answer = Column(Text, comment='数据集A')
    cot = Column(Text, comment='数据集思维链cot')
    reasoning_content = Column(Text, comment='模型思考过程内容reasoning_content')

    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=func.now(),
                         onupdate=func.now(), comment='更新时间')

    __table_args__ = (
        Index("idx_question_answer", question, answer, unique=True),
        {'comment': '数据集信息表'}
    )

class DataSetTextRecord(DataSetBase):
    """文本信息数据模型"""
    __tablename__ = 'dataset_text_record'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    # 数据集字段
    md5id = Column(String, comment='文本md5值')
    text = Column(Text, comment='文本信息')

    create_time = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=func.now(),
                         onupdate=func.now(), comment='更新时间')

    __table_args__ = (
        Index("idx_md5", md5id),
        {'comment': '文本切片信息表'}
    )

