from typing import Dict

import requests
from ..config import settings

class AuthService:
    def __init__(self):
        super().__init__()

    @staticmethod
    def dify_login() -> Dict:

        # headers = {
        #     'Host': '*************:9901',
        #     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        #     # Already added when you pass json=
        #     # 'content-type': 'application/json',
        #     'authorization': 'Bearer',
        #     'Accept': '*/*',
        #     'Origin': 'http://*************:9901',
        #     'Referer': 'http://*************:9901/signin',
        #     'Accept-Language': 'zh-CN,zh;q=0.9',
        # }

        json_data = {
            'email': settings.DIFY_USER,
            'password': settings.DIFY_USER_PASSWD,
            'language': 'zh-Hans',
            'remember_me': True,
        }

        response = requests.post(settings.DIFY_LOGIN_API_URL, headers={}, json=json_data)
        # 如果目标API返回非200状态码，抛出异常
        response.raise_for_status()
        return response.json()

