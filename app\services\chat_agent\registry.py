"""
智能体注册表实现
"""
from typing import Type, Optional, Dict, Any

from sqlalchemy.orm import Session

from app.services.chat_agent.agents.base_agent import BaseAgentTemplate
from app.services.chat_agent.base import AgentType
from app.services.chat_agent.handlers.interfaces import (
    KnowledgeBaseHandler,
    QueryProcessingHandler,
    SessionHandler
)


class AgentRegistry:
    """智能体注册表，实现注册表模式和工厂模式"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._agent_types = {}
        return cls._instance

    def register_agent_type(self,
                            agent_type: str,
                            agent_class: Type[BaseAgentTemplate]) -> None:
        """
        注册智能体类型
        
        Args:
            agent_type: 智能体类型名称
            agent_class: 智能体类
        """
        self._agent_types[agent_type] = agent_class

    def get_agent_class(self, agent_type: str) -> Optional[Type[BaseAgentTemplate]]:
        """
        获取智能体类
        
        Args:
            agent_type: 智能体类型名称
            
        Returns:
            智能体类或None
        """
        return self._agent_types.get(agent_type)

    def create_agent(self,
                     agent_config:  Dict[str, Any],
                     kb_handler: KnowledgeBaseHandler,
                     query_processor: QueryProcessingHandler,
                     session_handler: SessionHandler,
                     db: Session,
                     ) -> Optional[BaseAgentTemplate]:
        """
        创建智能体实例
        
        Args:
            agent_config: agent配置参数
            kb_handler: 知识库处理器
            query_processor: 查询处理器
            session_handler: 会话管理器
            db: 数据库管理器

        Returns:
            智能体实例或None
        """
        agent_type = agent_config.get("type", AgentType.GENERAL.name)
        agent_class = self.get_agent_class(agent_type)

        if agent_class is None:
            return None
        return agent_class(db,agent_config, kb_handler, query_processor, session_handler)
