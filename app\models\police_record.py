from datetime import datetime  # 添加这行导入语句

from sqlalchemy import Column, Integer, String, DateTime, Text, case
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class PoliceRecord(Base):
    """警察记录数据模型
    
    用于存储警察相关的警情记录信息
    """
    __tablename__ = 'police_records'

    # User ID
    user_id = Column(Integer, index=True)
    # 记录ID，主键
    id = Column(Integer, primary_key=True)
    # 案件类型：0：刑事案件 1：行政类案件
    case_type = Column(Integer, default=0, nullable=False, index=True)
    # 处警编号 一个表可以存在两条police_number,一个是上传的数据,一个是 同步的数据,同步数据police_number不可以重复,上传的police_number也不可以重复
    police_number = Column(String(50), index=True)
    # 处警单位
    police_unit = Column(String(100), index=True)
    # 处警日期时间
    police_time = Column(DateTime, index=True)
    # 接警时间
    alarm_time = Column(DateTime, index=True)
    # 报警人姓名
    call_name = Column(String(100), index=True)
    # 报警形式
    alarm_form = Column(String(50), index=True)
    # 报警电话
    alarm_call = Column(String(20), index=True)
    # 接警类别
    alarm_category = Column(String(50), index=True)
    # 处警类别
    police_category = Column(String(50), index=True)
    # 处警人
    police_name = Column(String(100), index=True)
    # 处警结果
    police_results = Column(String(200), index=True, comment='处警结果')
    # 报警内容
    alarm_content = Column(Text, comment='报警内容')
    # 处警详细情况
    alarm_details = Column(Text, comment='处警详细情况')
    # 上传时间
    upload_time = Column(DateTime, nullable=False, default=datetime.now, index=True)
    # 核查结果：有案不立;降格处理;待回访；无问题;有争议
    check_results = Column(String(50), index=True)
    # 分析状态 0:未分析 1:分析中 2:分析完成 3:分析失败 4:无需分析
    analysis_status = Column(Integer, default=0, nullable=False, index=True)
    # AI分析等级 红：red 黄：yellow 蓝：blue 无色: white
    analysis_level = Column(String(10), default='white', index=True)
    # AI分析详情
    analysis_details = Column(Text, comment='AI分析详情')

    fh_rksj = Column(DateTime, index=True, comment='元数据_入库时间')
    source = Column(Integer, default=0,
                    comment="数据来源: 0-数据同步,1-文件上传")
    org_code = Column(String(50), index=True)


ANALYSIS_STATUS_MAPPING = {
    0: '未分析',
    1: '分析中',
    2: '分析完成',
    3: '分析失败',
    4: '无需分析'
}


def police_record_search_order():
    # 定义分析状态的排序优先级
    analysis_status_order = {
        1: 1,  # 分析中
        2: 2,  # 已分析
        0: 3,  # 未分析
        3: 4  # 分析失败
    }

    # 使用case语句进行自定义排序
    return case(
        {status: order for status, order in analysis_status_order.items()},
        value=PoliceRecord.analysis_status
    )
