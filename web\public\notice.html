<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法制监督智能助手 - 提示信息</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .security-notice {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .notice-header {
            background-color: #1e88e5;
            color: white;
            padding: 15px 30px;
            border-radius: 4px;
            font-size: 18px;
            margin-bottom: 20px;
        }

        .notice-content {
            background-color: white;
            padding: 30px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .notice-content p {
            margin: 0;
            font-size: 16px;
            color: #333;
            line-height: 1.6;
        }

        .notice-footer {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="security-notice">
        <div class="notice-header">
            法制监督智能助手提示信息
        </div>
        <div class="notice-content">
            <p>你所访问的信息资源，尚未授权，对外开放。</p>
            <div class="notice-footer">
                请下次再来！
            </div>
        </div>
    </div>
</body>
</html> 