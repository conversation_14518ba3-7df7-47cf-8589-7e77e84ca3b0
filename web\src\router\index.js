import Vue from "vue";
import Router from 'vue-router'
import Home from "../views/Home/index.vue";
import CaseManagement from "../views/CaseManagement.vue";
import RecordCheck from "../views/RecordCheck.vue";
import SituationAnalysis from "../views/SituationAnalysis.vue";
import Login from "../views/Login.vue";
import Register from "../views/Register.vue";
import ResetPassword from "../views/ResetPassword.vue";
import StandFirmWithoutSpying from "../views/StandFirmWithoutSpying.vue";
import LegalRecordCheck from "../views/LegalRecordCheck.vue";
import SecurityNotice from "../views/SecurityNotice.vue";
import store from "@/store";

Vue.use(Router);

// 静态路由
export const constantRoutes = [
  {
    path: "/login",
    name: "Login",
    component: Login,
    meta: { requiresAuth: false },
  },
  {
    path: "/register",
    name: "Register",
    component: Register,
    meta: { requiresAuth: false },
  },
  {
    path: "/reset-password",
    name: "ResetPassword",
    component: ResetPassword,
    meta: { requiresAuth: false },
  },
  {
    path: "/menu",
    name: "menu",
    component: ()=>import("@/views/system/menu/index.vue"),
    meta: { requiresAuth: false },
  },
  {
    path: "/role",
    name: "role",
    component: ()=>import("@/views/system/role/index.vue"),
    meta: { requiresAuth: false },
  },
  {
    path: "/security-notice",
    name: "SecurityNotice",
    component: SecurityNotice,
    meta: { requiresAuth: false },
  },
  {
    path: "/file-review/preview",
    name: "fileReviewPreview",
    component: ()=>import("@/views/FileReview/preview.vue"),
    meta: { requiresAuth: false },
  },
  // {
  //   path: '/404',
  //   component: () => import('@/views/error-page/404'),
  //   hidden: true
  // }
];

// 动态路由
export const asyncRoutes = [
  {
    path: "/",
    name: "Layout",
    component: () => import('@/views/Layout/index.vue'),
    redirect: '/index',
    children: []
  }
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch(err => err)
}

const createRouter = () => new Router({
  routes: constantRoutes
})

const router = createRouter()

// 重置路由方法
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router

// const router = new VueRouter({
//   routes,
// });

// // 路由守卫
// router.beforeEach((to, from, next) => {
//   const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
//   const isAuthenticated = store.getters.isAuthenticated;

//   console.log("导航守卫:", {
//     to: to.path,
//     from: from.path,
//     requiresAuth,
//     isAuthenticated,
//     token: store.state.token,
//     user: store.state.user,
//   });

//   // 避免重定向循环：如果已经在登录页且未认证，直接放行
//   if (to.path === "/login" && !isAuthenticated) {
//     return next();
//   }

//   // 检查认证状态
//   if (requiresAuth && !isAuthenticated) {
//     console.log("未认证，重定向到登录页");
//     next("/login");
//   } else {
//     console.log("导航通过");
//     next();
//   }
// });

// export default router;
