import request from '@/utils/request'

// 获取未入库列表
export function getYetWarehousedList(data) {
  return request({
    url: '/api/scene6/search',
    method: 'post',
    data: {...data, finance_type:0}
  })
}

export function getYetWarehousedAnalysis() {
    return request({
        url: '/api/finance/object/analysis',
        method: 'post'   
    })
}


export function uploadWarehousingExcl(data, config = {}) {
      return request({
        url: 'api/finance/file/upload',
        method: 'post',
        data,
        ...config
      })
} 

export function getYetDisposedList(data) {
      return request({
        url: '/api/scene6/search',
        method: 'post',
        data: {...data, finance_type:1}
      })
        
    }
export function getYetDisposedAnalysis() {
    return request({
        url: '/api/finance/money/analysis',
        method: 'post'   
    })
}
export function uploadDisposedExcl(data, config = {}) {
        //   return request({
        //     url: '/api/upload_transcript_pdf',
        //     method: 'post',
        //     data,
        //     ...config
        //   })
            return {
                code: 200,
                data: {}
            }
    } 

    // 同步涉案财物数据
export function syncPropertyData(data) {
  return request({
    url: '/api/scene6/sync',
    method: 'post',
    data
  })
} 